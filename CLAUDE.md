# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is **panel4-templates**, a Vue.js component constructor system that generates distributable bundles for integration into a legacy PHP system. This is NOT a standalone application that runs with `npm run dev` - it's a **component builder** that produces optimized bundles for the legacy system to consume.

### Key Architecture Points

- **Decoupled Frontend**: Vue 3 + TypeScript components that compile to distributable bundles
- **DDP Integration**: Real-time connectivity to Meteor backend via SimpleDDP 
- **Multi-Entry Build System**: Each component has its own entry point and bundle
- **Parent System Integration**: Components mount automatically when bundles are loaded

## Development Commands

### Build Commands
```bash
# Development build (with source maps and console.log)
npm run build

# Production build (minified, no debug)
npm run prod

# Pre-production build
npm run pre-prod

# Preview build output
npm run preview
```

### Development Workflow
```bash
# Install dependencies
npm install

# Build for development (with source maps and debug)
npm run build

# Build for production (minified, optimized)
npm run prod

# Preview built components
npm run preview
# Then test at http://localhost:4173/panel.html
```

**IMPORTANT**: This project does NOT use `npm run dev` for development. It's designed as a **component constructor** that generates distributable bundles for integration into a legacy PHP system.

### Testing Integration
The `panel.html` file serves as a testing environment for built components. Always run `npm run build` first, then `npm run preview` to test at `http://localhost:4173/panel.html`.

## Architecture

### Component Structure
```
src/components/
├── [component-name]/
│   ├── ComponentName.vue     # Main Vue component
│   ├── index.js             # Entry point for build
│   └── ComponentName-DOCS.md # Component documentation
```

### Build Configuration (vite.config.ts)
- **Multi-entry setup**: Each component has its own entry point
- **Conditional naming**: Main components (like `muroInmobiliarioSocial`) have no hash for direct integration
- **Environment-based builds**: Different optimizations for dev/prod/pre-prod

### Key Entry Points (from vite.config.ts)
- `muroInmobiliarioSocial`: Main social feed component (no hash in filename)
- `inmuebleBolsaInmobiliaria`: Property exchange component (no hash)
- `dashboard`: Dashboard components (with hash)
- `notificaciones`: Notifications system (with hash)
- `sidebarCardInmuebles`: Property sidebar (with hash)
- `sidebarMultibolsa`: Multi-exchange sidebar (with hash)
- `multipsCard`: Multips card component (with hash)
- `microdash`: MicroDash component (with hash)
- `panel` and `guest`: HTML testing files (with hash)

## Services Architecture

### DDP Service (`src/services/ddpService.ts`)
- **Singleton pattern** for DDP connection management
- **Auto-reconnection** with exponential backoff (max 5 attempts)
- **Authentication state management** with automatic re-authentication on reconnect
- **Token-based authentication** using Meteor's resume token system
- **Event-driven architecture** with auth change listeners
- **Connection diagnostics** and status monitoring

Key methods:
- `connect(token?)`: Establishes DDP connection and authenticates
- `getCurrentUserId()`: Returns authenticated user ID
- `isAuthenticatedUser()`: Checks authentication status
- `forceReauthenticate()`: Forces re-authentication
- `getConnectionStatus()`: Returns detailed connection diagnostics

### Real-time Services
Services are found in `src/services/` but actual implementation may vary. Check directory for available services.

## Component Integration Pattern

Components auto-initialize using DOM attributes:
```html
<!-- In parent system -->
<div id="component-name" data-token="auth-token" data-config="{}"></div>
<script src="dist/assets/componentName.js"></script>
```

### Authentication Flow
1. Parent system provides auth token via `data-token` attribute
2. Component connects to Meteor via DDP with token
3. Real-time subscriptions established automatically

## TypeScript Types

### Core Domain Types (`src/types/inmobiliario.ts`)
- `PostInmobiliario`: Main post interface with real estate data, comments, favorites
- `Socio`: Partner/colleague information 
- `Autor`: Author information for posts and comments
- `Comentario`: Comment structure
- `NuevoPost`: Interface for creating new posts
- `FavoritoPost` & `FavoritosResponse`: Favorites system types
- `Filtros`: Post filtering options

**Note**: Some type files mentioned may not exist - check `src/types/` directory for actual available types.

## Styling

- **Tailwind CSS**: Configured with custom Mulbin color palette
- **Ion Icons**: Used throughout for iconography
- **Custom CSS**: Component-specific styles in Vue SFCs

### Tailwind Configuration
Custom colors include `mulbin-{50,600,700}` variants for brand consistency.

## External Dependencies

### Backend Integration
- **Meteor Backend**: WebSocket connection via DDP at `ws://localhost:3000/websocket`
- **REST API (msi-v5)**: HTTP requests via axios, proxied through nginx
- **Parent System (Panel4)**: PHP-based orchestrator that renders templates

### Environment Variables
- `VITE_METEOR_URL`: Meteor DDP endpoint (defaults to ws://localhost:3000/websocket)
- `NODE_ENV`: Controls build optimizations (development/production/pre-production)

## Important Development Notes

### Build-Only Workflow
**CRITICAL**: This project does NOT run as a development server. The workflow is:
1. Make changes to Vue components
2. Run `npm run build` (development) or `npm run prod` (production)
3. Test in legacy system or use `npm run preview` with `panel.html`

### Build Output
- **Development builds**: Include source maps, preserve console.log/debugger statements
- **Production builds**: Minified, strip all console.log and debugger statements, no source maps
- **Pre-production builds**: Similar to production but with different optimizations
- Assets output to `dist/assets/` with conditional naming:
  - Main components (`muroInmobiliarioSocial`, `inmuebleBolsaInmobiliaria`): No hash for direct integration
  - Other components: Include hash for cache busting

### Component Lifecycle
1. Legacy system includes bundle via `<script>` tag
2. Component auto-mounts when DOM element with matching ID exists
3. Extracts configuration from data attributes
4. Establishes DDP connection if auth token provided
5. Subscribes to real-time data streams
6. Renders reactive UI

### Testing
- Use `panel.html` for isolated component testing (after building)
- Full integration testing requires legacy system stack
- DDP connection testing requires Meteor backend running
- **Never use `npm run dev`** - components must be built to function

## Development Environment Setup

This component system is part of a larger microservices architecture:

1. **Parent System (Panel4)**: PHP orchestrator
2. **This System (panel4-templates)**: Vue component builder
3. **Meteor Backend**: Real-time data layer
4. **REST API (msi-v5)**: HTTP API layer

For full development, ensure all systems are running. See `ARQUITECTURA_SISTEMA_MULBIN.md` for complete setup instructions.

## Available Documentation

The codebase contains extensive documentation:

### System Architecture
- `ARQUITECTURA_SISTEMA_MULBIN.md`: Complete system architecture
- `INTEGRACION-METEOR-POSTS.md`: Meteor integration details
- `README.md`: Comprehensive project documentation

### Component-Specific Docs
- Component documentation found in respective component directories (e.g., `*-DOCS.md` files)
- Integration guides for specific features
- API documentation for DDP methods

### Legacy System Integration
- PHP template files in `src/entries/` with integration examples
- HTML testing files (`panel.html`, `guest.html`) for component testing

## Common Patterns

### New Component Creation
1. Create component directory in `src/components/`
2. Add Vue SFC with TypeScript
3. Create `index.js` entry point
4. Add to `vite.config.ts` input configuration
5. Build with `npm run build`
6. Test integration in legacy system

**Remember**: Always build first, never use `npm run dev` - this is a component constructor, not a dev server.

### DDP Service Usage
```typescript
import ddpService from '@/services/ddpService';

// Connect with authentication
await ddpService.connect(authToken);

// Subscribe to data
ddpService.subscribe('collectionName', ...args);

// Call server methods
const result = await ddpService.call('methodName', ...args);
```

### Error Handling
- Components should gracefully handle DDP connection failures
- Provide loading states for async operations
- Log errors appropriately based on build environment

## Internationalization and Character Encoding

### Spanish Language Support
This project is primarily developed in **Spanish**, with all user-facing content, comments, and documentation in Spanish. When working with templates and files:

#### Character Encoding Guidelines
- **Always use UTF-8 encoding** for all files containing Spanish text
- **Watch for character corruption**: Common issues include:
  - `ó` becomes `�` 
  - `ñ` becomes `�`
  - `é` becomes `�`
  - `á` becomes `�`
  - `í` becomes `�`
  - `ú` becomes `�`

#### Best Practices for Spanish Content
1. **Template Files (.mustache)**: Ensure proper UTF-8 encoding when creating/editing
2. **PHP Files**: Use `setlocale(LC_ALL, "es_MX");` for Spanish locale support
3. **Database Content**: Verify UTF-8 collation for Spanish characters
4. **File Creation**: Always save files with UTF-8 encoding, not ASCII or Latin-1

#### Common Spanish Terms in Codebase
- **Temas** = Themes
- **Genéricos** = Generic  
- **Personalizados** = Personalized/Custom
- **Configuración** = Configuration
- **Logotipo** = Logo
- **Apariencia** = Appearance

**IMPORTANT**: When creating or modifying templates, always verify that Spanish characters display correctly and are properly encoded.