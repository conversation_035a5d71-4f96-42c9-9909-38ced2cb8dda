# Prompt para Separación de Responsabilidades: Backend/UI con Mustache

## Contexto del Problema

Este prompt está diseñado para refactorizar archivos PHP legacy que mezclan lógica de backend con presentación HTML inline, separándolos en templates Mustache siguiendo el patrón establecido en `importExportFromTo.php`.

## Identificación de Archivos Candidatos

Busca archivos PHP que contengan estas características:

### ✅ Indicadores de Refactorización Necesaria:
- **HTML inline mezclado con PHP**: Código como `echo "<div>...<?php echo $variable ?></div>";`
- **Lógica de presentación en PHP**: Generación de HTML usando concatenación de strings
- **Motor Mustache disponible**: Presencia de `$mustache->render()` en el archivo
- **Función `cm_lee_archivo()`**: Disponible para leer templates
- **Formularios complejos**: Con múltiples campos, selects, y lógica condicional
- **Mensajes de éxito/error**: Renderizados directamente en PHP
- **JavaScript inline**: Mezclado con HTML en el PHP

### 🎯 Ejemplo de Código Candidato:
```php
if (trim($tipo_tema) == '') {
    ?>
    <script language=javascript>
      var themeConfigList = <?php echo json_encode($themeWithConf); ?>;
      function chequeo(vRadio) { ... }
    </script>
    <div align="center">
      <form action="<?php echo $PHP_SELF; ?>" method="POST">
        <select name="tema_generico">
          <?php
          while ($fic) {
              echo "<option selected>{$fic}</option>\n";
          }
          ?>
        </select>
      </form>
    </div>
    <?php
} else {
    echo '<p align="center">La configuración ha sido actualizada.</p>';
}
```

## Proceso de Refactorización

### PASO 1: Análisis del Archivo Original

1. **Identifica las secciones principales**:
   - Lógica de inicialización
   - Procesamiento de formularios (POST)
   - Generación de datos para vista
   - Renderizado HTML
   - Mensajes de éxito/error

2. **Mapea las variables dinámicas**:
   - Valores de campos de formulario
   - Opciones de selects generadas dinámicamente
   - Contenido condicional (mostrar/ocultar secciones)
   - Estados de UI (checked, selected, disabled)

### PASO 2: Crear Template Mustache Principal

**📁 Ubicación**: `src/entries/templates/[categoria]/[nombre].mustache`

**🎨 Estructura del Template**:

```mustache
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet"/>

<div class="container px-2 mx-auto sm:px-4 md:py-8">
  <!-- Header -->
  <div class="px-4 mb-4 text-center sm:mb-8">
    <div class="inline-flex justify-center items-center mb-3 w-12 h-12 bg-gradient-to-r to-indigo-600 rounded-full sm:mb-4 sm:w-16 sm:h-16 from-mulbin-500">
      <i class="text-lg text-white sm:text-2xl fas fa-[icono-apropiado]"></i>
    </div>
    <h1 class="mb-2 text-2xl font-bold leading-tight text-gray-800 sm:text-3xl lg:text-4xl">
      [Título de la Página]
    </h1>
    <p class="mx-auto max-w-md text-sm text-gray-600 sm:text-base">
      [Descripción breve]
    </p>
  </div>

  <!-- Main Card -->
  <div class="mx-auto max-w-4xl">
    <div class="overflow-hidden bg-white rounded-2xl border border-gray-100 shadow-xl">
      <!-- Card Header -->
      <div class="px-6 py-4 bg-gradient-to-r to-indigo-600 from-mulbin-500">
        <div class="flex items-center space-x-3">
          <div class="flex justify-center items-center w-10 h-10 rounded-lg bg-white/20">
            <i class="text-lg text-white fas fa-[icono]"></i>
          </div>
          <div>
            <h2 class="text-xl font-semibold text-white">[Título Sección]</h2>
            <p class="text-sm text-blue-100">[Descripción sección]</p>
          </div>
        </div>
      </div>

      <!-- Card Body -->
      <div class="p-6">
        <form method="POST" enctype="multipart/form-data" class="space-y-8">
          <!-- [Contenido del formulario] -->
          <input type="hidden" name="paso" value="2">
        </form>
      </div>
    </div>

    <!-- Error Messages -->
    {{#MENSAJE_ERROR}}
    <div class="p-6 mt-6 bg-red-50 rounded-xl border border-red-200">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <div class="flex justify-center items-center w-8 h-8 bg-red-100 rounded-lg">
            <i class="text-red-600 fas fa-exclamation-triangle"></i>
          </div>
        </div>
        <div>
          <h3 class="text-sm font-medium text-red-800">Error en la configuración</h3>
          <p class="text-sm text-red-600">{{MENSAJE_ERROR}}</p>
        </div>
      </div>
    </div>
    {{/MENSAJE_ERROR}}
  </div>
</div>

<script>
  // JavaScript necesario - usar variables Mustache con triple llaves {{{VARIABLE}}}
  var configData = {{{CONFIG_DATA}}};
  
  function miFuncion() {
    // Lógica JavaScript
  }
  
  document.addEventListener('DOMContentLoaded', function() {
    // Inicialización
  });
</script>
```

### PASO 3: Manejo Correcto de Selects y Opciones

**❌ EVITAR** (genera HTML escapado):
```php
$options = '';
while ($row) {
    $selected = ($current == $row['value']) ? 'selected' : '';
    $options .= "<option $selected>{$row['label']}</option>\n";
}
// Pasar: 'OPTIONS_HTML' => $options
```

**✅ MÉTODO CORRECTO** (arrays de objetos):
```php
$opciones = array();
while ($row) {
    $opciones[] = array(
        'value' => $row['value'],
        'label' => $row['label'], 
        'selected' => ($current == $row['value'])
    );
}
// Pasar: 'OPCIONES' => $opciones
```

**Template Mustache correspondiente**:
```mustache
<select name="campo" class="[clases-tailwind]">
  {{#OPCIONES}}
  <option value="{{value}}" {{#selected}}selected{{/selected}}>{{label}}</option>
  {{/OPCIONES}}
</select>
```

### PASO 4: Crear Template de Éxito Separado

**📁 Crear**: `src/entries/templates/[categoria]/[nombre]_exito.mustache`

```mustache
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet"/>

<div class="container px-2 mx-auto sm:px-4 md:py-8">
  <!-- Success Header -->
  <div class="px-4 mb-4 text-center sm:mb-8">
    <div class="inline-flex justify-center items-center mb-3 w-12 h-12 bg-gradient-to-r to-green-600 rounded-full sm:mb-4 sm:w-16 sm:h-16 from-green-500">
      <i class="text-lg text-white sm:text-2xl fas fa-check-circle"></i>
    </div>
    <h1 class="mb-2 text-2xl font-bold leading-tight text-gray-800 sm:text-3xl lg:text-4xl">
      ¡[Acción] Exitosa!
    </h1>
    <p class="mx-auto max-w-md text-sm text-gray-600 sm:text-base">
      Los cambios se han aplicado correctamente
    </p>
  </div>

  <!-- Success Card -->
  <div class="mx-auto max-w-2xl">
    <div class="overflow-hidden bg-white rounded-2xl border border-gray-100 shadow-xl">
      <div class="px-6 py-4 bg-gradient-to-r from-green-500 to-green-600">
        <div class="flex items-center space-x-3">
          <div class="flex justify-center items-center w-10 h-10 rounded-lg bg-white/20">
            <i class="text-lg text-white fas fa-[icono]"></i>
          </div>
          <div>
            <h2 class="text-xl font-semibold text-white">Cambios Aplicados</h2>
            <p class="text-sm text-green-100">La operación se completó satisfactoriamente</p>
          </div>
        </div>
      </div>

      <div class="p-8 text-center">
        <div class="mb-8">
          <div class="inline-flex justify-center items-center mb-4 w-20 h-20 bg-green-100 rounded-full">
            <i class="text-3xl text-green-600 fas fa-check"></i>
          </div>
          <h3 class="mb-3 text-xl font-semibold text-gray-800">¡Éxito!</h3>
          <p class="mb-4 text-gray-600 leading-relaxed">{{MENSAJE_EXITO}}</p>
          
          {{#INFO_ADICIONAL}}
          <div class="p-4 mb-6 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex items-center justify-center space-x-2">
              <i class="text-blue-600 fas fa-info-circle"></i>
              <span class="text-sm font-medium text-blue-800">{{INFO_ADICIONAL}}</span>
            </div>
          </div>
          {{/INFO_ADICIONAL}}
        </div>

        <div class="flex flex-col gap-4 sm:flex-row sm:justify-center">
          <a href="[archivo].php" class="inline-flex justify-center items-center px-8 py-3 text-lg font-semibold text-white bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:shadow-xl">
            <i class="mr-3 fas fa-arrow-left"></i>
            Regresar a [Sección]
          </a>
          <a href="index.php" class="inline-flex justify-center items-center px-8 py-3 text-lg font-semibold text-gray-700 bg-white rounded-lg border-2 border-gray-300 shadow-sm transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <i class="mr-3 fas fa-home"></i>
            Ir al Inicio
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
```

### PASO 5: Refactorizar el Archivo PHP

**📝 Estructura del PHP refactorizado**:

```php
<?php
// [Includes originales mantenidos]
setlocale(LC_ALL, "es_MX");
include_once("languages/esp.php");
include_once("funciones.php");
// ... otros includes

// [Lógica de negocio mantenida]
// Variables de configuración
// Funciones auxiliares si las hay

// Sección de inicialización y validaciones
$plantilla = cm_extrae_plantilla_mto();
include_once("enc_mto.php");

//////////////
// Vista inicial - mostrar formulario
if (trim($campo_indicador) == '') {
    
    // 1. Preparar datos para selects usando ARRAYS DE OBJETOS
    $opciones_select1 = array();
    $directorio = opendir('./ruta');
    $archivo = readdir($directorio);
    while ($archivo) {
        if (strpos($archivo, '.') !== 0) {
            $opciones_select1[] = array(
                'value' => $archivo,
                'label' => $archivo,
                'selected' => ($config['campo'] == $archivo)
            );
        }
        $archivo = readdir($directorio);
    }
    
    // 2. Preparar otros datos dinámicos
    $datos_adicionales = // ... lógica específica
    
    // 3. Configurar variables para template
    $render = array(
        // Estados de formulario
        'CAMPO_CHECKED' => ($config['campo'] == 'valor') ? 'checked' : '',
        'SELECT_DISABLED' => ($condicion) ? 'disabled' : '',
        
        // Datos para selects (SIEMPRE arrays)
        'OPCIONES_SELECT1' => $opciones_select1,
        'OPCIONES_SELECT2' => $opciones_select2,
        
        // Contenido condicional (booleanos)
        'MOSTRAR_SECCION' => (count($datos) > 0),
        
        // Datos simples
        'CAMPO1' => $valor1,
        'CAMPO2' => $valor2,
        
        // JavaScript data (triple llaves en template)
        'JS_CONFIG' => json_encode($configuracion_js)
    );
    
    // 4. Renderizar template principal
    $salida = cm_lee_archivo(__DIR__.'/templates/[categoria]/[nombre].mustache');
    echo $mustache->render($salida, $render);

//////////////
// Procesamiento POST - lógica de negocio
} else {
    
    // [Toda la lógica de procesamiento mantenida]
    // Validaciones
    // Actualizaciones de base de datos
    // Procesamiento de archivos
    // etc.
    
    // Variables para template de éxito
    $render = array(
        'MENSAJE_EXITO' => 'Operación completada correctamente.',
        'INFO_ADICIONAL' => ($cambio_importante) ? 'Información relevante' : null
    );
    
    // Renderizar template de éxito
    $salida = cm_lee_archivo(__DIR__.'/templates/[categoria]/[nombre]_exito.mustache');
    echo $mustache->render($salida, $render);
}

// [Footer original mantenido]
echo $mustache->render($plantilla['pie'], $render);
exit;
?>
```

## Puntos Críticos y Soluciones

### 🚨 Problema: Caracteres Especiales Corruptos

**Síntomas**: `ó` se muestra como `�`, `ñ` como `�`

**✅ Solución**:
1. **Verificar encoding**: Todos los archivos en UTF-8
2. **Configurar PHP**: `setlocale(LC_ALL, "es_MX");` al inicio
3. **Corregir caracteres manualmente** en templates:
   ```
   Gen�ricos → Genéricos
   Configuraci�n → Configuración
   ```

### 🚨 Problema: HTML Escapado en Selects

**Síntomas**: `<option>` se renderiza como `&lt;option&gt;`

**✅ Solución**:
- **NUNCA** generar HTML como string
- **SIEMPRE** usar arrays de objetos
- Usar sintaxis Mustache nativa para opciones

### 🚨 Problema: Variables JavaScript

**❌ Incorrecto**:
```php
'JS_VARIABLE' => json_encode($data)  // Se escapa el JSON
```

**✅ Correcto**:
```php
'JS_VARIABLE' => json_encode($data)
```

**En template usar triple llaves**:
```mustache
<script>
  var myData = {{{JS_VARIABLE}}};  // Triple llaves = sin escapar
</script>
```

### 🚨 Problema: Condicionales Booleanas

**❌ Incorrecto**:
```php
'MOSTRAR_SECCION' => 'true'  // String, no booleano
```

**✅ Correcto**:
```php
'MOSTRAR_SECCION' => (count($items) > 0)  // Booleano real
```

## Verificación Final

### ✅ Checklist de Calidad:

- [ ] **Separación completa**: Cero HTML inline en PHP
- [ ] **Template principal**: Solo formulario, sin mensajes de éxito
- [ ] **Template éxito**: Pantalla dedicada con navegación
- [ ] **Caracteres especiales**: Todos los acentos correctos
- [ ] **Selects**: Usan arrays de objetos, no HTML strings
- [ ] **JavaScript**: Variables con triple llaves `{{{VAR}}}`
- [ ] **Navegación**: Enlaces directos, no `history.back()`
- [ ] **Responsivo**: Diseño Tailwind adaptable
- [ ] **Consistencia visual**: Sigue patrón de `importExportFromTo.php`
- [ ] **Funcionalidad**: Todos los features originales funcionan

### 🧪 Pruebas Requeridas:

1. **Formulario inicial**: Se carga correctamente
2. **Validaciones**: Errores se muestran apropiadamente  
3. **Envío exitoso**: Redirige a template de éxito
4. **Navegación**: Botones llevan a URLs correctas
5. **JavaScript**: Funciones interactivas operan normalmente
6. **Responsive**: Se ve bien en móvil y desktop

## Beneficios de Esta Refactorización

1. **Mantenibilidad**: HTML separado de lógica PHP
2. **Reutilización**: Templates pueden ser reutilizados
3. **Diseño consistente**: Patrón visual unificado
4. **UX mejorada**: Navegación clara post-acción
5. **Código limpio**: Responsabilidades bien definidas
6. **Escalabilidad**: Fácil agregar nuevas características

---

**⚡ IMPORTANTE**: Antes de aplicar este patrón, asegúrate de que el archivo tenga acceso a `$mustache` y `cm_lee_archivo()`. Si no los tiene, es probable que no sea candidato para esta refactorización.