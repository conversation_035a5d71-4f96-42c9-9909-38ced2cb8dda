# Arquitectura del Sistema Mulbin - Guía para Desarrolladores

## 📋 Índice
1. [Visión General](#visión-general)
2. [Componentes del Sistema](#componentes-del-sistema)
3. [Flujo de Datos y Comunicación](#flujo-de-datos-y-comunicación)
4. [Configuración de Nginx](#configuración-de-nginx)
5. [Autenticación y Sesiones](#autenticación-y-sesiones)
6. [Desarrollo Local](#desarrollo-local)
7. [Deployment y Producción](#deployment-y-producción)
8. [Casos de Uso Específicos](#casos-de-uso-específicos)
9. [Workflows de Desarrollo Común](#workflows-de-desarrollo-común)
10. [Quick Reference para Desarrolladores](#quick-reference-para-desarrolladores)

## 🎯 Visión General

El sistema Mulbin utiliza una arquitectura de microservicios orquestada por **Panel4** como el núcleo principal, con múltiples backends especializados y componentes Vue.js compilados que se integran mediante proxies nginx.

### Tecnologías Principales
- **Panel4**: PHP (Orquestador principal)
- **panel4-templates**: Vue.js + TypeScript + Vite
- **msi-v5**: PHP Slim Framework v4 (API REST)
- **MulbinComponents**: Meteor.js (WebSockets y DDP)
- **Nginx**: Proxy reverso y balanceador
- **Docker**: Contenedorización (Dev) y Rancher (Producción)

## 🏗️ Componentes del Sistema

### 1. Panel4 (Orquestador Principal)
**Ubicación**: `/panel4/`
**Función**: Sistema principal que renderiza templates HTML y orquesta toda la aplicación.

```php
// Ejemplo de renderizado de template
// panel4 renderiza templates con componentes Vue compilados
```

**Características**:
- Manejo de sesiones de usuario
- Renderizado de templates con Mustache
- Integración de componentes Vue compilados
- Gestión de autenticación principal

### 2. panel4-templates (Frontend Vue.js)
**Ubicación**: `/panel4-templates/`
**Función**: Componentes Vue.js que se compilan y se integran en Panel4.

**Estructura**:
```
panel4-templates/
├── src/
│   ├── components/
│   │   ├── muro-inmobiliario-social/
│   │   │   ├── MisSocios.vue
│   │   │   └── MuroInmobiliarioSocial.vue
│   │   └── ui/
│   ├── services/
│   └── types/
├── vite.config.ts
└── package.json
```

**Proceso de Build**:
```bash
npm run build  # Compila componentes Vue
# Los bundles se sirven desde Panel4
```

### 3. msi-v5 (API Backend)
**Ubicación**: `/msi-v5/`
**Función**: API REST con Slim Framework v4 para operaciones específicas.

**Estructura de Rutas**:
```php
// /msi-v5/app/routes.php
$app->group('/owner/socios', function (Group $group) {
    $group->get('', ListSociosOwnerAction::class);
    $group->post('/{id}/autorizar', AutorizarSocioOwnerAction::class);
    // ... más rutas
});
```

### 4. MulbinComponents (Meteor Backend)
**Ubicación**: `/MulbinComponents/`
**Función**: Backend en tiempo real con WebSockets y DDP para reactividad.

**Colecciones Principales**:
```javascript
// /MulbinComponents/app/imports/api/index.js
export { PostsInmobiliarios } from "./posts-inmobiliarios/collection.js";
export { Notifications } from "./notifications/collection.js";
export { HilosInteres } from "./hilos-interes/collection.js";
```

## 🔄 Flujo de Datos y Comunicación

### Flujo Principal: Vue → API REST

```mermaid
graph TD
    A[Componente Vue] -->|axios.get| B[nginx proxy]
    B -->|Validación sesión| C[/auth-session]
    C -->|Headers auth| D[msi-v5 Backend]
    D -->|Response| E[ListSociosOwnerAction]
    E -->|JSON| A
```

**Ejemplo práctico - MisSocios.vue**:
```typescript
// 1. Componente Vue hace llamada
const response = await axios.get("/msi-v5/owner/socios");

// 2. nginx intercepta y valida sesión
location /msi-v5 {
    auth_request /auth-session;
    proxy_pass http://msi-v5/;
}

// 3. Backend PHP procesa
class ListSociosOwnerAction extends OwnerAction {
    protected function action(): Response {
        $socios = $this->getSociosDirectos();
        return $this->respondWithData(['socios' => $socios]);
    }
}
```

### Flujo Reactivo: Meteor → Vue

```mermaid
graph TD
    A[Meteor DDP] -->|WebSocket| B[ddpService.ts]
    B -->|Reactive Data| C[Componente Vue]
    C -->|UI Update| D[Template HTML]
```

## ⚙️ Configuración de Nginx

**Archivo**: `/website2025/server/panel4/conf.d/default.nginx.dev.conf`

### Proxy para msi-v5
```nginx
location /msi-v5 {
    auth_request /auth-session;
    
    # Headers de autenticación
    auth_request_set $auth_bearer_token $upstream_http_x_auth_bearer_token;
    auth_request_set $auth_type $upstream_http_x_auth_type;
    auth_request_set $auth_contrato $upstream_http_x_auth_contrato;
    
    # Proxy headers
    proxy_set_header X-Auth-Bearer-Token $auth_bearer_token;
    proxy_set_header X-Auth-Type $auth_type;
    proxy_set_header X-Auth-Contrato $auth_contrato;
    
    proxy_pass http://msi-v5/;
}
```

### Autenticación Interna
```nginx
location ~ ^/auth-session$ {
    internal;
    fastcgi_pass php-fpm;
    fastcgi_param SCRIPT_FILENAME "/var/www/auth-session/iSession.php";
}
```

## 🔐 Autenticación y Sesiones

### Métodos de Autenticación

1. **Sesión de Panel4** (Principal)
   - Sesión PHP tradicional
   - Cookies de larga duración
   - Validación mediante `/auth-session`

2. **Bearer Token** (API)
   - Token JWT almacenado en DB
   - Para pruebas directas de API
   - Headers de autorización

### Flujo de Autenticación
```php
// 1. nginx valida sesión
auth_request /auth-session;

// 2. iSession.php procesa
// Extrae datos de sesión y los pasa como headers

// 3. msi-v5 recibe headers
$this->auth->getContratoId(); // ID del contrato autenticado
```

## 💻 Desarrollo Local

### Arquitectura de Contenedores Docker

El sistema se ejecuta en **dos stacks de Docker Compose** separados:

#### 1. Stack Principal (website2025/docker-compose.yml)
Contiene todos los servicios principales del sistema:

```yaml
services:
  website2025:      # Landing page y activación de servicios
    ports: ["8000:80"]
    
  mariadb:          # Base de datos principal
    ports: ["3306:3306"]
    
  phpmyadmin:       # Administrador de DB
    ports: ["8081:80"]
    
  interno:          # Sistema interno legacy
    ports: ["8080:80"]
    
  msi-v5:           # API REST Slim v4
    ports: ["8082:80"]
    
  panel4:           # Orquestador principal
    ports: ["8020:80"]
    
  photos:           # Servicio de gestión de fotos
    ports: ["8030:80"]
```

#### 2. Stack Meteor (MulbinComponents/docker-compose.yml)
Contiene el backend en tiempo real:

```yaml
services:
  meteor-dev:       # Desarrollo Meteor
    ports: ["3000:3000"]
    
  meteor-prod:      # Producción Meteor
    ports: ["3000:3000"]
    profiles: ["prod"]
    
  mongo:            # Base de datos MongoDB
    ports: ["27017:27017"]
```

### Comandos de Inicio

#### Iniciar Stack Principal
```bash
# En directorio: website2025/
docker-compose up -d

# Ver todos los servicios
docker-compose ps

# Ver logs específicos
docker-compose logs -f panel4
docker-compose logs -f msi-v5
docker-compose logs -f website2025
```

#### Iniciar Stack Meteor
```bash
# En directorio: MulbinComponents/
docker-compose up -d meteor-dev mongo

# Ver logs de Meteor
docker-compose logs -f meteor-dev

# Ver logs de MongoDB
docker-compose logs -f mongo
```

#### Compilar Componentes Vue
```bash
# En directorio: panel4-templates/
npm install
npm run build

# El build se monta automáticamente en panel4 via volumen:
# /dist -> /var/www/html/public/dist
```

### URLs de Desarrollo Específicas

| Servicio | URL | Función |
|----------|-----|---------|
| **website2025** | `http://localhost:8000` | Landing page y activación |
| **panel4** | `http://localhost:8020` | Panel principal orquestador |
| **msi-v5** | `http://localhost:8082` | API REST directa |
| **photos** | `http://localhost:8030` | Gestión de imágenes |
| **interno** | `http://localhost:8080` | Sistema legacy |
| **phpmyadmin** | `http://localhost:8081` | Admin base de datos |
| **meteor-dev** | `http://localhost:3000` | Backend Meteor DDP |

### Comandos de Debug Específicos

#### Logs por Servicio
```bash
# Panel principal
docker-compose logs -f panel4

# API REST
docker-compose logs -f msi-v5

# Meteor (en directorio MulbinComponents/)
docker-compose logs -f meteor-dev

# Base de datos
docker-compose logs -f mariadb

# Servicio de fotos
docker-compose logs -f photos

# Landing page
docker-compose logs -f website2025
```

#### Acceso a Contenedores
```bash
# Entrar al contenedor de panel4
docker-compose exec panel4 sh

# Entrar al contenedor de msi-v5
docker-compose exec msi-v5 sh

# Entrar al contenedor de Meteor
cd MulbinComponents/
docker-compose exec meteor-dev sh

# Acceso a base de datos
docker-compose exec mariadb mysql -u root -p
```

#### Reiniciar Servicios Específicos
```bash
# Reiniciar solo panel4
docker-compose restart panel4

# Reiniciar API REST
docker-compose restart msi-v5

# Reiniciar Meteor completo
cd MulbinComponents/
docker-compose restart meteor-dev

# Rebuild de un servicio específico
docker-compose up -d --build panel4
```

## 🚀 Deployment y Producción

### Ambiente de Producción

#### Orquestación con Rancher
En producción, los mismos servicios de Docker Compose se orquestan con **Rancher**, manteniendo la misma estructura pero con configuraciones optimizadas:

```yaml
# Equivalencia Desarrollo → Producción
Dev (docker-compose)     → Prod (Rancher)
───────────────────────────────────────────
meteor-dev               → meteor-prod (profile: prod)
website2025:8000         → website2025 (load balanced)
panel4:8020             → panel4 (múltiples instancias)
msi-v5:8082             → msi-v5 (múltiples instancias)
photos:8030             → photos (múltiples instancias)
mariadb:3306            → MariaDB cluster
mongo:27017             → MongoDB replica set
```

#### Servicios Críticos en Producción

| Servicio | Instancias | Load Balancer | Base de Datos |
|----------|------------|---------------|---------------|
| **website2025** | 3+ | nginx/Rancher | MariaDB cluster |
| **panel4** | 3+ | nginx/Rancher | MariaDB cluster |
| **msi-v5** | 2+ | nginx/Rancher | MariaDB cluster |
| **meteor-prod** | 2+ | Rancher | MongoDB replica |
| **photos** | 2+ | nginx/Rancher | MariaDB cluster |

### Pipeline de Deployment

#### 1. Preparación de Assets
```bash
# Compilar componentes Vue para producción
cd panel4-templates/
npm run build

# Los assets se incluyen en la imagen panel4
# via volumen: /dist -> /var/www/html/public/dist
```

#### 2. Build de Imágenes Docker
```bash
# Panel4 (orquestador principal)
docker build -t registry.mulbin.com/panel4:latest ./panel4

# msi-v5 (API REST)
docker build -t registry.mulbin.com/msi-v5:latest ./msi-v5

# Meteor para producción
cd MulbinComponents/
docker-compose build meteor-prod

# Photos service
docker build -t registry.mulbin.com/photos:latest ./photos

# Website2025 (landing)
docker build -t registry.mulbin.com/website2025:latest ./website2025
```

#### 3. Deploy con Rancher
```bash
# Deploy stack completo
rancher app install mulbin-stack

# Deploy servicios específicos
rancher service up panel4
rancher service up msi-v5
rancher service up meteor-prod

# Verificar deployment
rancher ps
```

#### 4. Variables de Entorno Críticas

**Producción vs Desarrollo**:
```bash
# Desarrollo
APP_ENV=development
METEOR_API_URL=http://meteor-dev:3000
DB_HOST=mariadb

# Producción  
APP_ENV=production
METEOR_API_URL=https://ws-si-cft.mulb.in/api/
DB_HOST=mariadb-cluster.internal
```

### Monitoreo y Logs en Producción

#### Comando Equivalentes Rancher
```bash
# Equivalente a: docker-compose logs -f panel4
rancher logs -f panel4

# Equivalente a: docker-compose logs -f meteor-dev  
rancher logs -f meteor-prod

# Equivalente a: docker-compose restart msi-v5
rancher service restart msi-v5

# Ver todos los servicios activos
rancher ps
```

#### Health Checks y Scaling
```yaml
# Configuración típica en Rancher
healthcheck:
  test: ["CMD-SHELL", "curl -f http://localhost/ping || exit 1"]
  interval: 30s
  timeout: 10s
  retries: 3

scaling:
  min: 2
  max: 10
  target_cpu: 70%
```

### Dominios y SSL en Producción

```bash
# Dominios de producción
website2025.cft.mulbin.com     → website2025 service
*.panel.cft.mulbin.com         → panel4 service  
ws-si-cft.mulb.in             → meteor-prod service
pics.server                    → photos service
```

## 📚 Casos de Uso Específicos

### Caso 1: Carga de Lista de Socios

**Archivo**: `MisSocios.vue`

```typescript
// 1. Componente Vue monta
onMounted(() => {
    fetchSocios();
});

// 2. Llamada a API
const fetchSocios = async () => {
    const response = await axios.get("/msi-v5/owner/socios");
    socios.value = response.data.data.socios;
};
```

**Backend**: `ListSociosOwnerAction.php`
```php
protected function action(): Response {
    // Obtiene socios del usuario autenticado
    $socios = $this->getSociosDirectos();
    
    // Procesa tags y tipos
    $tags = SISociosTags::where('contrato_id', $this->auth->getContratoId())
        ->select('id', 'tag', 'description', 'style')
        ->get();
    
    return $this->respondWithData(['socios' => $response, 'tags' => $tags]);
}
```

### Caso 2: Integración en Template Principal

**Archivo**: `panel.htm`
```html
<!-- 1. Contenedor del componente -->
<div 
    data-muro-inmobiliario-social 
    data-token="{{ meteor_auth_token }}"
    class="w-full">
</div>

<!-- 2. Script compilado de Vue -->
<script 
    type="module" 
    src="/assets/muroInmobiliarioSocial.js?t={{ tkt }}">
</script>
```

### Caso 3: Comunicación Reactiva con Meteor

**Servicio**: `ddpService.ts`
```typescript
// Conexión DDP
const ddp = new DDP({
    endpoint: 'ws://localhost:3000/websocket',
    SocketConstructor: WebSocket,
});

// Suscripción reactiva
ddp.subscribe('posts.inmobiliarios', {
    onReady: () => {
        // Datos disponibles
    }
});
```

## 🛠️ Workflows de Desarrollo Común

### Escenario 1: Desarrollo Frontend (Vue.js)

```bash
# 1. Asegurar que los servicios están corriendo
cd website2025/
docker-compose ps

# 2. Desarrollar componentes Vue
npm run dev  # Hot reload para desarrollo

# 3. Compilar y probar en panel4
npm run build
# Abrir: http://localhost:8020

# 4. Ver logs del panel principal
docker-compose logs -f panel4
```

### Escenario 2: Desarrollo Backend API (msi-v5)

```bash
# 1. Modificar código en msi-v5/src/
# 2. Reiniciar servicio para aplicar cambios
docker-compose restart msi-v5

# 3. Probar endpoint directamente
curl http://localhost:8082/owner/socios

# 4. Ver logs de API
docker-compose logs -f msi-v5

# 5. Debug de base de datos
docker-compose exec mariadb mysql -u root -p
```

### Escenario 3: Desarrollo Backend Meteor

```bash
# 1. Cambiar al directorio Meteor
cd MulbinComponents/

# 2. Verificar que meteor-dev está corriendo
docker-compose ps

# 3. Ver logs en tiempo real
docker-compose logs -f meteor-dev

# 4. Acceder al contenedor para debug
docker-compose exec meteor-dev sh

# 5. Restart completo de Meteor
docker-compose restart meteor-dev
```

### Escenario 4: Testing Full Stack

```bash
# 1. Iniciar todos los servicios
cd website2025/
docker-compose up -d

cd ../MulbinComponents/
docker-compose up -d meteor-dev mongo

# 2. Verificar conectividad
curl http://localhost:8020  # panel4
curl http://localhost:8082  # msi-v5  
curl http://localhost:3000  # meteor
curl http://localhost:8000  # website2025

# 3. Probar flujo completo Vue → API
# Desde MisSocios.vue: axios.get("/msi-v5/owner/socios")
# nginx proxy → auth-session → msi-v5 → respuesta
```

### Escenario 5: Reset Completo del Ambiente

```bash
# 1. Parar todos los servicios
cd website2025/
docker-compose down

cd ../MulbinComponents/
docker-compose down

# 2. Limpiar volúmenes (¡CUIDADO! Borra datos)
docker-compose down -v

# 3. Rebuild completo
docker-compose up -d --build

# 4. Verificar que todo funciona
docker-compose ps
```

### Escenario 6: Migración de Base de Datos

```bash
# 1. Acceder a MariaDB
docker-compose exec mariadb mysql -u root -p

# 2. Ejecutar migrations (ejemplo)
USE sistemainmobiliario;
SOURCE /var/lib/mysql/migrations/nueva_migration.sql;

# 3. Verificar cambios en msi-v5
docker-compose logs -f msi-v5

# 4. Probar endpoints que usan nuevas tablas
curl http://localhost:8082/owner/socios
```

## 🔧 Herramientas de Debug

### Logs de Nginx
```bash
# Ver logs de acceso
tail -f /var/log/nginx/access.log

# Ver logs de error
tail -f /var/log/nginx/error.log
```

### Debug de APIs
```bash
# Probar endpoint directamente
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost/msi-v5/owner/socios
```

### Debug de Componentes Vue
```javascript
// En desarrollo, usar Vue DevTools
// Console logs en servicios
console.log('🔍 Debug data:', response.data);
```

## 📝 Convenciones de Código

### PHP (msi-v5)
- PSR-4 autoloading
- Actions pattern para endpoints
- Traits para funcionalidad compartida

### Vue.js (panel4-templates)
- Composition API
- TypeScript interfaces
- Componentes Single File

### Meteor (MulbinComponents)
- Publications/Subscriptions pattern
- Collections con schemas
- Methods para operaciones

## 🚨 Troubleshooting Común

### Error de Autenticación
```
Status: 403 Forbidden
```
**Solución**: Verificar configuración de `/auth-session` en nginx

### Error de CORS
```
Access-Control-Allow-Origin
```
**Solución**: Configurar headers CORS en nginx o PHP

### Componente Vue no carga
**Solución**: 
1. Verificar build: `npm run build`
2. Revisar ruta del asset en template
3. Verificar configuración de Vite

---

## 📞 Soporte

Para dudas específicas sobre la arquitectura:
1. Revisar logs de nginx
2. Verificar autenticación de sesión
3. Probar endpoints directamente
4. Revisar configuración de proxies

**¡Importante!**: Siempre probar en ambiente de desarrollo antes de hacer cambios en producción.

---

## 🚀 Quick Reference para Desarrolladores

### Comandos Esenciales

```bash
# 🏃‍♂️ INICIO RÁPIDO
# Stack principal
cd website2025/ && docker-compose up -d

# Stack Meteor  
cd MulbinComponents/ && docker-compose up -d meteor-dev mongo

# 📋 VERIFICAR SERVICIOS
docker-compose ps  # Ver servicios activos
curl http://localhost:8020  # Panel4
curl http://localhost:8082  # msi-v5 API

# 🔍 LOGS IMPORTANTES
docker-compose logs -f panel4    # Orquestador principal
docker-compose logs -f msi-v5    # API REST
docker-compose logs -f meteor-dev # Backend tiempo real

# 🔄 RESTART SERVICIOS
docker-compose restart panel4
docker-compose restart msi-v5
docker-compose restart meteor-dev

# 🛠️ BUILD COMPONENTES VUE
cd panel4-templates/
npm run build  # Para producción
npm run dev    # Para desarrollo

# 🗃️ ACCESO A BASES DE DATOS
docker-compose exec mariadb mysql -u root -p
# Usuario: root | Password: password
```

### URLs de Desarrollo

| Puerto | Servicio | URL |
|--------|----------|-----|
| `:8000` | **website2025** | Landing page y activación |
| `:8020` | **panel4** | Panel principal (nginx proxy incluido) |
| `:8082` | **msi-v5** | API REST directa |
| `:3000` | **meteor-dev** | Backend DDP/WebSockets |
| `:8081` | **phpmyadmin** | Admin base de datos |

### Flujo de Debugging Típico

1. **Verificar servicios**: `docker-compose ps`
2. **Ver logs**: `docker-compose logs -f [servicio]`
3. **Probar endpoints**: `curl http://localhost:[puerto]`
4. **Reiniciar si es necesario**: `docker-compose restart [servicio]`
5. **Rebuild si hay cambios**: `docker-compose up -d --build [servicio]`

### Contactos de Emergencia
- **Arquitectura**: Ver este documento
- **Docker issues**: Verificar que Docker Desktop esté corriendo
- **Base de datos**: Acceso via phpMyAdmin en `:8081`
- **Logs completos**: `docker-compose logs > debug.log` 
