import { ref, computed, nextTick } from "vue";
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  arrow,
  type Placement,
} from "@floating-ui/vue";

export interface TooltipOptions {
  placement?: Placement;
  offset?: number;
  delay?: number;
  disabled?: boolean;
  theme?: "dark" | "light" | "mulbin";
  maxWidth?: number;
  arrow?: boolean;
}

export function useTooltip(options: TooltipOptions = {}) {
  const {
    placement = "top",
    offset: offsetValue = 8,
    delay = 300,
    disabled = false,
    theme = "dark",
    maxWidth = 200,
    arrow: showArrow = true,
  } = options;

  // Referencias para el elemento trigger y el tooltip
  const reference = ref<HTMLElement>();
  const floating = ref<HTMLElement>();
  const arrowRef = ref<HTMLElement>();

  // Estado del tooltip
  const isVisible = ref(false);
  const content = ref("");

  // Temporizadores para delay
  let showTimer: ReturnType<typeof setTimeout> | null = null;
  let hideTimer: ReturnType<typeof setTimeout> | null = null;

  // Configuración de Floating UI
  const middleware = computed(() => [
    offset(offsetValue),
    flip(),
    shift({ padding: 5 }),
    ...(showArrow ? [arrow({ element: arrowRef })] : []),
  ]);

  const { floatingStyles, middlewareData } = useFloating(reference, floating, {
    placement,
    middleware,
    whileElementsMounted: autoUpdate,
  });

  // Estilos para la flecha
  const arrowStyles = computed(() => {
    if (!showArrow || !middlewareData.value.arrow) return {};

    const { x, y } = middlewareData.value.arrow;
    const staticSide = {
      top: "bottom",
      right: "left",
      bottom: "top",
      left: "right",
    }[placement.split("-")[0]];

    return {
      left: x != null ? `${x}px` : "",
      top: y != null ? `${y}px` : "",
      right: "",
      bottom: "",
      [staticSide as string]: "-4px",
    };
  });

  // Clases CSS para el tema
  const tooltipClasses = computed(() => {
    const baseClasses =
      "px-3 py-2 text-sm rounded-lg shadow-lg z-50 pointer-events-none transition-opacity duration-200";

    const themeClasses = {
      dark: "bg-gray-900 text-white border border-gray-700",
      light: "bg-white text-gray-900 border border-gray-200",
      mulbin: "bg-mulbin-600 text-white border border-mulbin-700",
    };

    return `${baseClasses} ${themeClasses[theme]}`;
  });

  // Clases para la flecha
  const arrowClasses = computed(() => {
    const themeClasses = {
      dark: "fill-gray-900",
      light: "fill-white",
      mulbin: "fill-mulbin-600",
    };

    return `absolute w-2 h-2 ${themeClasses[theme]}`;
  });

  // Funciones para mostrar/ocultar
  const show = (text: string) => {
    if (disabled) return;

    content.value = text;

    if (hideTimer) {
      clearTimeout(hideTimer);
      hideTimer = null;
    }

    if (delay > 0) {
      showTimer = setTimeout(() => {
        isVisible.value = true;
      }, delay);
    } else {
      isVisible.value = true;
    }
  };

  const hide = () => {
    if (showTimer) {
      clearTimeout(showTimer);
      showTimer = null;
    }

    if (delay > 0) {
      hideTimer = setTimeout(() => {
        isVisible.value = false;
      }, delay);
    } else {
      isVisible.value = false;
    }
  };

  const toggle = (text: string) => {
    if (isVisible.value) {
      hide();
    } else {
      show(text);
    }
  };

  // Limpiar temporizadores
  const cleanup = () => {
    if (showTimer) clearTimeout(showTimer);
    if (hideTimer) clearTimeout(hideTimer);
  };

  return {
    // Referencias
    reference,
    floating,
    arrowRef,

    // Estado
    isVisible,
    content,

    // Estilos
    floatingStyles,
    arrowStyles,
    tooltipClasses,
    arrowClasses,
    maxWidth,

    // Métodos
    show,
    hide,
    toggle,
    cleanup,
  };
}
