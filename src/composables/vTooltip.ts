import { type App, type DirectiveBinding } from "vue";
import { createApp } from "vue";
import Tooltip from "../components/ui/Tooltip.vue";
import type { TooltipProps } from "../components/ui/Tooltip.vue";

interface TooltipElement extends HTMLElement {
  _tooltipInstance?: any;
  _tooltipApp?: any;
}

interface TooltipDirectiveBinding extends DirectiveBinding {
  value: string | TooltipProps;
  modifiers: {
    left?: boolean;
    right?: boolean;
    top?: boolean;
    bottom?: boolean;
    light?: boolean;
    mulbin?: boolean;
    click?: boolean;
    focus?: boolean;
    noArrow?: boolean;
    html?: boolean;
  };
}

const vTooltip = {
  mounted(el: TooltipElement, binding: TooltipDirectiveBinding) {
    createTooltip(el, binding);
  },

  updated(el: TooltipElement, binding: TooltipDirectiveBinding) {
    if (binding.value !== binding.oldValue) {
      destroyTooltip(el);
      createTooltip(el, binding);
    }
  },

  unmounted(el: TooltipElement) {
    destroyTooltip(el);
  },
};

function createTooltip(el: TooltipElement, binding: TooltipDirectiveBinding) {
  const { value, modifiers } = binding;

  // Parsear el valor
  let tooltipProps: TooltipProps;

  if (typeof value === "string") {
    tooltipProps = {
      content: value,
    };
  } else if (typeof value === "object" && value !== null) {
    tooltipProps = { ...value };
  } else {
    return; // No hay contenido válido
  }

  // Aplicar modificadores
  if (modifiers.left) tooltipProps.placement = "left";
  if (modifiers.right) tooltipProps.placement = "right";
  if (modifiers.top) tooltipProps.placement = "top";
  if (modifiers.bottom) tooltipProps.placement = "bottom";

  if (modifiers.light) tooltipProps.theme = "light";
  if (modifiers.mulbin) tooltipProps.theme = "mulbin";

  if (modifiers.click) tooltipProps.trigger = "click";
  if (modifiers.focus) tooltipProps.trigger = "focus";

  if (modifiers.noArrow) tooltipProps.arrow = false;
  if (modifiers.html) tooltipProps.isHtml = true;

  // Crear wrapper div
  const wrapper = document.createElement("div");
  wrapper.style.display = "inline-block";
  wrapper.style.width = "100%";

  // Insertar wrapper antes del elemento
  el.parentNode?.insertBefore(wrapper, el);
  wrapper.appendChild(el);

  // Crear aplicación Vue para el tooltip
  const tooltipApp = createApp(Tooltip, tooltipProps);
  const tooltipInstance = tooltipApp.mount(wrapper);

  // Guardar referencias para limpieza
  el._tooltipApp = tooltipApp;
  el._tooltipInstance = tooltipInstance;
}

function destroyTooltip(el: TooltipElement) {
  if (el._tooltipApp) {
    el._tooltipApp.unmount();
    el._tooltipApp = undefined;
  }

  if (el._tooltipInstance) {
    el._tooltipInstance = undefined;
  }
}

// Plugin para instalar la directiva
export default {
  install(app: App) {
    app.directive("tooltip", vTooltip);
  },
};

export { vTooltip };
