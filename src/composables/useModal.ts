import { reactive } from "vue";
import type { ModalType, ModalSize } from "../components/ui/BaseModal.vue";

export interface ModalConfig {
  type?: ModalType;
  size?: ModalSize;
  title?: string;
  subtitle?: string;
  message?: string;
  showIcon?: boolean;
  showCloseButton?: boolean;
  showActions?: boolean;
  showCancelButton?: boolean;
  showConfirmButton?: boolean;
  cancelText?: string;
  confirmText?: string;
  closeOnBackdrop?: boolean;
  persistent?: boolean;
}

export interface ModalState extends ModalConfig {
  show: boolean;
  loading: boolean;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void | Promise<void>;
  onClose?: () => void | Promise<void>;
}

// Estado global del modal
const modalState = reactive<ModalState>({
  show: false,
  loading: false,
  type: "info",
  size: "md",
  title: "",
  subtitle: "",
  message: "",
  showIcon: true,
  showCloseButton: true,
  showActions: true,
  showCancelButton: true,
  showConfirmButton: true,
  cancelText: "Cancelar",
  confirmText: "Aceptar",
  closeOnBackdrop: true,
  persistent: false,
});

export function useModal() {
  // Función para mostrar modal básico
  const showModal = (
    config: ModalConfig & {
      onConfirm?: () => void | Promise<void>;
      onCancel?: () => void | Promise<void>;
      onClose?: () => void | Promise<void>;
    } = {}
  ) => {
    Object.assign(modalState, {
      show: true,
      loading: false,
      type: "info",
      size: "md",
      showIcon: true,
      showCloseButton: true,
      showActions: true,
      showCancelButton: true,
      showConfirmButton: true,
      cancelText: "Cancelar",
      confirmText: "Aceptar",
      closeOnBackdrop: true,
      persistent: false,
      ...config,
    });
  };

  // Función para mostrar modal de información
  const showInfo = (
    title: string,
    message: string,
    config: Partial<ModalConfig> = {}
  ) => {
    showModal({
      type: "info",
      title,
      message,
      showCancelButton: false,
      confirmText: "Entendido",
      ...config,
    });
  };

  // Función para mostrar modal de éxito
  const showSuccess = (
    title: string,
    message: string,
    config: Partial<ModalConfig> = {}
  ) => {
    showModal({
      type: "success",
      title,
      message,
      showCancelButton: false,
      confirmText: "Perfecto",
      ...config,
    });
  };

  // Función para mostrar modal de advertencia
  const showWarning = (
    title: string,
    message: string,
    config: Partial<ModalConfig> = {}
  ) => {
    showModal({
      type: "warning",
      title,
      message,
      showCancelButton: false,
      confirmText: "Entendido",
      ...config,
    });
  };

  // Función para mostrar modal de error
  const showError = (
    title: string,
    message: string,
    config: Partial<ModalConfig> = {}
  ) => {
    showModal({
      type: "error",
      title,
      message,
      showCancelButton: false,
      confirmText: "Cerrar",
      ...config,
    });
  };

  // Función para mostrar modal de confirmación
  const showConfirm = (
    title: string,
    message: string,
    onConfirm: () => void | Promise<void>,
    config: Partial<ModalConfig> = {}
  ) => {
    showModal({
      type: "confirm",
      title,
      message,
      confirmText: "Confirmar",
      onConfirm,
      ...config,
    });
  };

  // Función para cerrar modal
  const closeModal = () => {
    modalState.show = false;
    modalState.loading = false;

    // Ejecutar callback de cierre si existe
    if (modalState.onClose) {
      modalState.onClose();
    }

    // Limpiar callbacks después de un breve delay
    setTimeout(() => {
      modalState.onConfirm = undefined;
      modalState.onCancel = undefined;
      modalState.onClose = undefined;
    }, 300);
  };

  // Manejar confirmación
  const handleConfirm = async () => {
    if (modalState.onConfirm) {
      try {
        modalState.loading = true;
        await modalState.onConfirm();

        // Solo cerrar si no es persistente
        if (!modalState.persistent) {
          closeModal();
        }
      } catch (error) {
        console.error("Error en confirmación del modal:", error);
        // En caso de error, mostrar modal de error
        showError(
          "Error",
          "Ocurrió un error inesperado. Por favor, inténtalo de nuevo."
        );
      } finally {
        modalState.loading = false;
      }
    } else {
      closeModal();
    }
  };

  // Manejar cancelación
  const handleCancel = async () => {
    if (modalState.onCancel) {
      try {
        await modalState.onCancel();
      } catch (error) {
        console.error("Error en cancelación del modal:", error);
      }
    }
    closeModal();
  };

  // Funciones de conveniencia para casos comunes
  const alert = (message: string, title: string = "Información") => {
    showInfo(title, message);
  };

  const confirm = (
    message: string,
    onConfirm: () => void | Promise<void>,
    title: string = "Confirmar acción"
  ) => {
    showConfirm(title, message, onConfirm);
  };

  const success = (message: string, title: string = "¡Éxito!") => {
    showSuccess(title, message);
  };

  const error = (message: string, title: string = "Error") => {
    showError(title, message);
  };

  const warning = (message: string, title: string = "Advertencia") => {
    showWarning(title, message);
  };

  return {
    // Estado
    modalState,

    // Funciones principales
    showModal,
    closeModal,
    handleConfirm,
    handleCancel,

    // Funciones específicas por tipo
    showInfo,
    showSuccess,
    showWarning,
    showError,
    showConfirm,

    // Funciones de conveniencia (compatibilidad con alert/confirm)
    alert,
    confirm,
    success,
    error,
    warning,
  };
}
