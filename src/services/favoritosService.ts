/**
 * Servicio para manejar favoritos de inmuebles
 * Utiliza localStorage para persistencia local
 *
 * 🔄 SISTEMA DE REACTIVIDAD:
 * Implementa un patrón de eventos personalizado para notificar cambios en tiempo real.
 *
 * Uso en componentes:
 * ```typescript
 * const unsubscribe = favoritosService.onChange(() => {
 *   // Actualizar UI cuando cambien los favoritos
 * });
 *
 * // IMPORTANTE: Desuscribirse en onUnmounted para evitar memory leaks
 * onUnmounted(() => unsubscribe());
 * ```
 *
 * ⚠️ PARA DESARROLLADORES:
 * NO crear sistemas de eventos paralelos. Usar este patrón para otros servicios.
 */

export interface FavoritoInmueble {
  id: string;
  titulo: string;
  fechaAgregado: string;
  precio: number;
  operacion: string;
  ubicacion: string;
}

class FavoritosService {
  private readonly STORAGE_KEY = "mulbin_favoritos_inmuebles";
  private readonly MAX_FAVORITOS = 100; // Límite máximo de favoritos

  // 🔄 SISTEMA DE EVENTOS PARA REACTIVIDAD
  // Array de callbacks que se ejecutan cuando cambian los favoritos
  private listeners: Array<() => void> = [];

  /**
   * 🎯 SUSCRIBIRSE A CAMBIOS EN FAVORITOS
   *
   * Permite que componentes Vue reaccionen automáticamente a cambios en favoritos.
   *
   * @param callback Función que se ejecuta cuando cambian los favoritos
   * @returns Función para desuscribirse (IMPORTANTE: llamar en onUnmounted)
   *
   * @example
   * ```typescript
   * // En componente Vue
   * let unsubscribe: (() => void) | null = null;
   *
   * onMounted(() => {
   *   unsubscribe = favoritosService.onChange(() => {
   *     totalFavoritos.value = favoritosService.getFavoritos().length;
   *   });
   * });
   *
   * onUnmounted(() => {
   *   if (unsubscribe) unsubscribe(); // ← CRÍTICO para evitar memory leaks
   * });
   * ```
   */
  onChange(callback: () => void): () => void {
    this.listeners.push(callback);

    // Retorna función para desuscribirse
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 📢 NOTIFICAR CAMBIOS A TODOS LOS LISTENERS
   *
   * Método privado que se llama automáticamente cuando se modifican favoritos.
   * Ejecuta todos los callbacks registrados con onChange().
   *
   * ⚠️ IMPORTANTE: Llamar este método en TODOS los métodos que modifiquen favoritos.
   */
  private notifyChange(): void {
    this.listeners.forEach((callback) => {
      try {
        callback();
      } catch (error) {
        console.error("❌ Error en listener de favoritos:", error);
      }
    });
  }

  /**
   * Obtiene todos los favoritos del localStorage
   */
  getFavoritos(): FavoritoInmueble[] {
    try {
      const favoritosJson = localStorage.getItem(this.STORAGE_KEY);
      if (!favoritosJson) return [];

      const favoritos = JSON.parse(favoritosJson);
      return Array.isArray(favoritos) ? favoritos : [];
    } catch (error) {
      console.error("❌ Error al obtener favoritos del localStorage:", error);
      return [];
    }
  }

  /**
   * Obtiene solo los IDs de los favoritos (para consultas rápidas)
   */
  getFavoritosIds(): string[] {
    return this.getFavoritos().map((fav) => fav.id);
  }

  /**
   * Verifica si un inmueble está en favoritos
   */
  esFavorito(inmuebleId: string): boolean {
    return this.getFavoritosIds().includes(inmuebleId);
  }

  /**
   * Agrega un inmueble a favoritos
   */
  agregarFavorito(inmueble: {
    id: string;
    titulo: string;
    precio: number;
    operacion: string;
    ubicacion: string;
  }): boolean {
    try {
      const favoritos = this.getFavoritos();

      // Verificar si ya existe
      if (this.esFavorito(inmueble.id)) {
        console.log("⚠️ El inmueble ya está en favoritos:", inmueble.id);
        return false;
      }

      // Verificar límite máximo
      if (favoritos.length >= this.MAX_FAVORITOS) {
        console.warn(
          "⚠️ Límite máximo de favoritos alcanzado:",
          this.MAX_FAVORITOS
        );
        return false;
      }

      // Crear nuevo favorito
      const nuevoFavorito: FavoritoInmueble = {
        id: inmueble.id,
        titulo: inmueble.titulo,
        precio: inmueble.precio,
        operacion: inmueble.operacion,
        ubicacion: inmueble.ubicacion,
        fechaAgregado: new Date().toISOString(),
      };

      // Agregar al inicio del array (más recientes primero)
      favoritos.unshift(nuevoFavorito);

      // Guardar en localStorage
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(favoritos));

      // Notificar cambios
      this.notifyChange();

      console.log("⭐ Inmueble agregado a favoritos:", inmueble.id);
      return true;
    } catch (error) {
      console.error("❌ Error al agregar favorito:", error);
      return false;
    }
  }

  /**
   * Remueve un inmueble de favoritos
   */
  removerFavorito(inmuebleId: string): boolean {
    try {
      const favoritos = this.getFavoritos();
      const favoritosFiltrados = favoritos.filter(
        (fav) => fav.id !== inmuebleId
      );

      if (favoritosFiltrados.length === favoritos.length) {
        console.log("⚠️ El inmueble no estaba en favoritos:", inmuebleId);
        return false;
      }

      // Guardar en localStorage
      localStorage.setItem(
        this.STORAGE_KEY,
        JSON.stringify(favoritosFiltrados)
      );

      // Notificar cambios
      this.notifyChange();

      console.log("🗑️ Inmueble removido de favoritos:", inmuebleId);
      return true;
    } catch (error) {
      console.error("❌ Error al remover favorito:", error);
      return false;
    }
  }

  /**
   * Toggle favorito (agregar/remover)
   */
  toggleFavorito(inmueble: {
    id: string;
    titulo: string;
    precio: number;
    operacion: string;
    ubicacion: string;
  }): boolean {
    if (this.esFavorito(inmueble.id)) {
      return this.removerFavorito(inmueble.id);
    } else {
      return this.agregarFavorito(inmueble);
    }
  }

  /**
   * Limpia todos los favoritos
   */
  limpiarFavoritos(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);

      // Notificar cambios
      this.notifyChange();

      console.log("🧹 Todos los favoritos han sido limpiados");
    } catch (error) {
      console.error("❌ Error al limpiar favoritos:", error);
    }
  }

  /**
   * Obtiene estadísticas de favoritos
   */
  getEstadisticas(): {
    total: number;
    porOperacion: Record<string, number>;
    fechaUltimoAgregado?: string;
  } {
    const favoritos = this.getFavoritos();

    const porOperacion = favoritos.reduce((acc, fav) => {
      acc[fav.operacion] = (acc[fav.operacion] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const fechaUltimoAgregado =
      favoritos.length > 0 ? favoritos[0].fechaAgregado : undefined;

    return {
      total: favoritos.length,
      porOperacion,
      fechaUltimoAgregado,
    };
  }

  /**
   * Exporta favoritos como JSON (para backup)
   */
  exportarFavoritos(): string {
    return JSON.stringify(this.getFavoritos(), null, 2);
  }

  /**
   * Importa favoritos desde JSON (para restore)
   */
  importarFavoritos(favoritosJson: string): boolean {
    try {
      const favoritos = JSON.parse(favoritosJson);
      if (!Array.isArray(favoritos)) {
        throw new Error("Formato de favoritos inválido");
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(favoritos));

      // Notificar cambios
      this.notifyChange();

      console.log("📥 Favoritos importados exitosamente:", favoritos.length);
      return true;
    } catch (error) {
      console.error("❌ Error al importar favoritos:", error);
      return false;
    }
  }

  /**
   * Limpia favoritos obsoletos (opcional, para mantenimiento)
   */
  limpiarFavoritosObsoletos(diasMaximos: number = 90): number {
    try {
      const favoritos = this.getFavoritos();
      const fechaLimite = new Date();
      fechaLimite.setDate(fechaLimite.getDate() - diasMaximos);

      const favoritosValidos = favoritos.filter((fav) => {
        const fechaFavorito = new Date(fav.fechaAgregado);
        return fechaFavorito >= fechaLimite;
      });

      const removidos = favoritos.length - favoritosValidos.length;

      if (removidos > 0) {
        localStorage.setItem(
          this.STORAGE_KEY,
          JSON.stringify(favoritosValidos)
        );

        // Notificar cambios
        this.notifyChange();

        console.log(`🧹 ${removidos} favoritos obsoletos limpiados`);
      }

      return removidos;
    } catch (error) {
      console.error("❌ Error al limpiar favoritos obsoletos:", error);
      return 0;
    }
  }
}

// Exportar instancia singleton
export const favoritosService = new FavoritosService();
export default favoritosService;
