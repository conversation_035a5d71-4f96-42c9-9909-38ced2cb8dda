import ddpService from "./ddpService";
import type {
  PostInmobiliario,
  Filtros,
  NuevoPost,
  FavoritoPost,
  FavoritosResponse,
  FavoritosOptions,
} from "../types/inmobiliario";

class InmobiliarioService {
  private static instance: InmobiliarioService;
  private readonly COLLECTION_NAME = "postsInmobiliarios";
  // private reactivePosts: any = null;

  private constructor() {}

  public static getInstance(): InmobiliarioService {
    if (!InmobiliarioService.instance) {
      InmobiliarioService.instance = new InmobiliarioService();
    }
    return InmobiliarioService.instance;
  }

  public async connect() {
    await ddpService.connect();
  }

  // Nuevo método para conectar con autenticación
  public async connectWithToken(token: string) {
    await ddpService.connect(token);
  }

  // 🚀 SCROLL INFINITO: Suscripción optimizada con límite incremental
  public async subscribeToPosts(
    filtros?: Filtros,
    limitOrPage?: number,
    limit?: number
  ) {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de suscribirse
      if (!ddpService.isAuthenticatedUser()) {
        console.log("🔄 Forzando reautenticación antes de suscribirse...");
        await ddpService.forceReauthenticate();
      }

      // 🚀 DETERMINISTIC: Detectar si es scroll infinito (solo 2 parámetros) o paginación tradicional (3 parámetros)
      let finalLimit: number;
      let page: number;

      if (limit !== undefined) {
        // Modo paginación tradicional: (filtros, page, limit)
        page = limitOrPage || 1;
        finalLimit = limit;
        console.log("📄 Modo paginación tradicional:", {
          page,
          limit: finalLimit,
        });
      } else {
        // Modo scroll infinito: (filtros, limit)
        page = 1; // Siempre página 1 para scroll infinito
        finalLimit = limitOrPage || 10;
        console.log("🔄 Modo scroll infinito:", { limit: finalLimit });
      }

      // 🔧 OPTIMIZADO: Confiar en la autenticación DDP nativa - el servidor ya conoce al usuario
      console.log("📡 Suscribiéndose con filtros:", {
        ...filtros,
        authenticated: ddpService.isAuthenticatedUser(),
        mode: limit !== undefined ? "pagination" : "infinite-scroll",
      });

      const subscription = ddpService.subscribe(
        "postsInmobiliarios",
        filtros,
        page,
        finalLimit
      );
      await subscription.ready();
      return true;
    } catch (error) {
      console.error("❌ Error al suscribirse a los posts:", error);
      throw error;
    }
  }

  public getPosts(reactive: boolean = false, filtros?: Filtros) {
    try {
      let collection = ddpService.collection(this.COLLECTION_NAME);

      // Obtener todos los posts primero
      let allPosts = collection.fetch();

      // Debug: Log para verificar los datos que llegan
      console.log("📊 Posts obtenidos de DDP:", {
        total: allPosts.length,
        connectionStatus: ddpService.getConnectionStatus(),
      });

      // Normalizar y filtrar posts que no tengan ID válido
      allPosts = allPosts
        .map((post: any) => {
          // Normalizar: usar id como _id si _id no existe
          if (!post._id && post.id) {
            post._id = post.id;
          }
          return post;
        })
        .filter((post: any) => {
          if (!post._id || typeof post._id !== "string") {
            console.warn("⚠️ Post sin ID válido encontrado:", post);
            return false;
          }
          return true;
        });

      console.log("✅ Posts con ID válido:", allPosts.length);

      // Aplicar filtros si existen
      if (filtros) {
        if (filtros.type) {
          allPosts = allPosts.filter((post: any) => post.type === filtros.type);
        }
        if (filtros.location) {
          allPosts = allPosts.filter(
            (post: any) => post.location === filtros.location
          );
        }
        if (filtros.maxPrice) {
          allPosts = allPosts.filter(
            (post: any) => post.price <= filtros.maxPrice!
          );
        }
      }

      // 🔄 ORDENAR POR FECHA - MÁS RECIENTES PRIMERO
      allPosts = allPosts.sort((a: any, b: any) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA; // Orden descendente (más recientes primero)
      });

      if (reactive) {
        // Para colección reactiva, necesitamos recrear la colección filtrada
        // Por ahora devolvemos los posts filtrados y ordenados
        console.log(
          "📱 Devolviendo posts reactivos filtrados y ordenados:",
          allPosts.length
        );
        return allPosts;
      } else {
        // Devolver posts filtrados y ordenados
        console.log(
          "📋 Devolviendo posts no reactivos filtrados y ordenados:",
          allPosts.length
        );
        return allPosts;
      }
    } catch (error) {
      console.error("❌ Error al obtener posts:", error);
      throw error;
    }
  }

  public async createPost(post: NuevoPost): Promise<string> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de crear post
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de crear post...");
        await ddpService.forceReauthenticate();
      }

      return await ddpService.call("postsInmobiliarios.create", post);
    } catch (error) {
      console.error("❌ Error al crear post:", error);
      throw error;
    }
  }

  // 🔧 REMOVIDO: Sistema de interés toggleInterested eliminado

  public async addComment(postId: string, text: string): Promise<string> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de comentar
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de comentar...");
        await ddpService.forceReauthenticate();
      }

      return await ddpService.call("comentariosPost.create", postId, text);
    } catch (error) {
      console.error("❌ Error al agregar comentario:", error);
      throw error;
    }
  }

  public async getComments(postId: string): Promise<any[]> {
    try {
      // Validar que postId sea un string válido y no esté vacío
      if (!postId || typeof postId !== "string" || postId.trim() === "") {
        throw new Error(
          "postId debe ser un string válido y no puede estar vacío"
        );
      }

      // 🔧 VERIFICAR AUTENTICACIÓN antes de cargar comentarios
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de cargar comentarios...");
        await ddpService.forceReauthenticate();
      }

      const subscription = ddpService.subscribe("comentariosPost", postId);
      await subscription.ready();
      return ddpService
        .collection("comentariosPost")
        .filter((c: any) => c.postId === postId && c.active !== false)
        .fetch();
    } catch (error) {
      console.error("❌ Error al obtener comentarios:", error);
      throw error;
    }
  }

  public async removeComment(commentId: string): Promise<boolean> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de eliminar comentario
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de eliminar comentario...");
        await ddpService.forceReauthenticate();
      }

      await ddpService.call("comentariosPost.remove", commentId);
      return true;
    } catch (error) {
      console.error("❌ Error al eliminar comentario:", error);
      throw error;
    }
  }

  // 🔧 MEJORADO: Observer con manejo de reconexión automática
  public onPostsChange(
    callback: (posts: PostInmobiliario[]) => void
  ): () => void {
    const collection = ddpService.collection(this.COLLECTION_NAME);
    const observer = collection.onChange(() => {
      const allPosts = collection.fetch();

      // Verificar estado de conexión
      const connectionStatus = ddpService.getConnectionStatus();
      console.log("🔄 onChange callback - Estado de conexión:", {
        connected: connectionStatus.connected,
        authenticated: connectionStatus.authenticated,
        postsCount: allPosts.length,
      });

      // Normalizar y filtrar posts con ID válido
      const validPosts = allPosts
        .map((post: any) => {
          // Normalizar: usar id como _id si _id no existe
          if (!post._id && post.id) {
            post._id = post.id;
          }
          return post;
        })
        .filter((post: any) => {
          if (!post._id || typeof post._id !== "string") {
            console.warn("⚠️ Post sin ID válido en onChange:", post);
            return false;
          }
          return true;
        })
        // 🔄 ORDENAR POR FECHA - MÁS RECIENTES PRIMERO
        .sort((a: any, b: any) => {
          const dateA = new Date(a.createdAt).getTime();
          const dateB = new Date(b.createdAt).getTime();
          return dateB - dateA; // Orden descendente (más recientes primero)
        });

      console.log(
        `📊 onChange: ${allPosts.length} posts totales, ${validPosts.length} posts válidos y ordenados`
      );
      callback(validPosts);
    });

    return () => {
      if (observer && typeof observer.stop === "function") {
        observer.stop();
      }
    };
  }

  public async getTotalPostsCount(): Promise<number> {
    // 🔧 VERIFICAR AUTENTICACIÓN antes de contar posts
    if (!ddpService.isAuthenticatedUser()) {
      console.warn("⚠️ Reautenticando antes de contar posts...");
      await ddpService.forceReauthenticate();
    }

    return ddpService.call("postsInmobiliarios.count");
  }

  // 🆕 NUEVO: Crear post con destinatarios específicos
  public async createPostWithTargets(
    post: NuevoPost,
    targetUserIds: string[] = []
  ): Promise<string> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de crear post con destinatarios
      if (!ddpService.isAuthenticatedUser()) {
        console.warn(
          "⚠️ Reautenticando antes de crear post con destinatarios..."
        );
        await ddpService.forceReauthenticate();
      }

      const postWithTargets = {
        ...post,
        targetUserIds,
      };

      console.log("📤 Enviando post con destinatarios:", {
        title: postWithTargets.title,
        type: postWithTargets.type,
        price: postWithTargets.price,
        priceType: typeof postWithTargets.price,
        targetUserIds: postWithTargets.targetUserIds,
        isPublic: targetUserIds.length === 0,
        connectionStatus: ddpService.getConnectionStatus(),
      });

      return await ddpService.call(
        "postsInmobiliarios.create",
        postWithTargets
      );
    } catch (error) {
      console.error("❌ Error al crear post con destinatarios:", error);
      console.error("Datos del post:", {
        title: post.title,
        type: post.type,
        targetUserIds,
      });
      throw error;
    }
  }

  // 🆕 NUEVO: Eliminar post inmobiliario
  public async removePost(postId: string): Promise<boolean> {
    try {
      if (!postId || typeof postId !== "string" || postId.trim() === "") {
        throw new Error(
          "postId debe ser un string válido y no puede estar vacío"
        );
      }

      // 🔧 VERIFICAR AUTENTICACIÓN antes de eliminar post
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de eliminar post...");
        await ddpService.forceReauthenticate();
      }

      console.log("🗑️ Eliminando post:", postId);
      await ddpService.call("postsInmobiliarios.remove", postId);
      console.log("✅ Post eliminado exitosamente");
      return true;
    } catch (error) {
      console.error("❌ Error al eliminar post:", error);
      throw error;
    }
  }

  // 🆕 NUEVO: Actualizar post inmobiliario
  public async updatePost(
    postId: string,
    updateData: Partial<NuevoPost>
  ): Promise<boolean> {
    try {
      if (!postId || typeof postId !== "string" || postId.trim() === "") {
        throw new Error(
          "postId debe ser un string válido y no puede estar vacío"
        );
      }

      // 🔧 VERIFICAR AUTENTICACIÓN antes de actualizar post
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de actualizar post...");
        await ddpService.forceReauthenticate();
      }

      console.log("✏️ Actualizando post:", postId, updateData);
      await ddpService.call("postsInmobiliarios.update", postId, updateData);
      console.log("✅ Post actualizado exitosamente");
      return true;
    } catch (error) {
      console.error("❌ Error al actualizar post:", error);
      throw error;
    }
  }

  // Método genérico para llamar a cualquier método Meteor
  public call(method: string, ...args: any[]): Promise<any> {
    return ddpService.call(method, ...args);
  }

  // 🔧 MEJORADO: Método de diagnóstico con información de conexión
  public diagnosticCollection(): void {
    try {
      const collection = ddpService.collection(this.COLLECTION_NAME);
      const allPosts = collection.fetch();
      const connectionStatus = ddpService.getConnectionStatus();

      console.log("=== 🔍 DIAGNÓSTICO DE COLECCIÓN MEJORADO ===");
      console.log("📊 Estado de conexión:", connectionStatus);
      console.log(`📄 Total de posts en colección: ${allPosts.length}`);

      allPosts.forEach((post: any, index: number) => {
        console.log(`📄 Post ${index}:`, {
          _id: post._id,
          id: post.id,
          _id_type: typeof post._id,
          id_type: typeof post.id,
          title: post.title || "Sin título",
          type: post.type,
          isPublic: post.isPublic,
          targetUserIds: post.targetUserIds?.length || 0,
          hasValidId: !!(post._id && typeof post._id === "string"),
          hasValidIdField: !!(post.id && typeof post.id === "string"),
          canNormalize: !post._id && post.id,
        });
      });

      // Aplicar normalización para diagnóstico
      const normalizedPosts = allPosts.map((post: any) => {
        if (!post._id && post.id) {
          return { ...post, _id: post.id };
        }
        return post;
      });

      const postsWithoutId = normalizedPosts.filter(
        (post: any) => !post._id || typeof post._id !== "string"
      );
      console.log(
        `⚠️ Posts sin ID válido después de normalización: ${postsWithoutId.length}`
      );

      if (postsWithoutId.length > 0) {
        console.log(
          "🚨 Posts problemáticos después de normalización:",
          postsWithoutId
        );
      }

      // Verificar posts privados vs públicos
      const publicPosts = normalizedPosts.filter(
        (post: any) => post.isPublic === true
      );
      const privatePosts = normalizedPosts.filter(
        (post: any) => post.isPublic === false
      );
      console.log(`🌍 Posts públicos: ${publicPosts.length}`);
      console.log(`🔒 Posts privados: ${privatePosts.length}`);

      console.log("=== 🏁 FIN DIAGNÓSTICO ===");
    } catch (error) {
      console.error("❌ Error en diagnóstico:", error);
    }
  }

  // 🆕 NUEVO: Método para verificar y reparar conexión
  public async checkAndRepairConnection(): Promise<boolean> {
    try {
      const status = ddpService.getConnectionStatus();
      console.log("🔍 Verificando estado de conexión:", status);

      if (!status.connected) {
        console.log("🔌 Reconectando...");
        const token = ddpService.getCurrentToken();
        if (token) {
          await ddpService.connect(token);
        } else {
          await ddpService.connect();
        }
      }

      if (status.hasToken && !status.authenticated) {
        console.log("🔑 Reautenticando...");
        await ddpService.forceReauthenticate();
      }

      const newStatus = ddpService.getConnectionStatus();
      console.log("✅ Estado después de reparación:", newStatus);

      return (
        newStatus.connected && (newStatus.authenticated || !newStatus.hasToken)
      );
    } catch (error) {
      console.error("❌ Error al reparar conexión:", error);
      return false;
    }
  }

  // 🆕 NUEVO: Alternar favorito en un post
  public async toggleFavorite(
    postId: string
  ): Promise<{ favorited: boolean; favoritesCount: number }> {
    try {
      if (!postId || typeof postId !== "string" || postId.trim() === "") {
        throw new Error(
          "postId debe ser un string válido y no puede estar vacío"
        );
      }

      // 🔧 VERIFICAR AUTENTICACIÓN antes de marcar favorito
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de marcar favorito...");
        await ddpService.forceReauthenticate();
      }

      console.log("⭐ Alternando favorito para post:", postId);
      const result = await ddpService.call(
        "postsInmobiliarios.toggleFavorite",
        postId
      );
      console.log("✅ Favorito alternado exitosamente:", result);
      return result;
    } catch (error) {
      console.error("❌ Error al alternar favorito:", error);
      throw error;
    }
  }

  // 🆕 NUEVO: Verificar si un post está en favoritos
  public async isFavorited(postId: string): Promise<boolean> {
    try {
      if (!postId || typeof postId !== "string" || postId.trim() === "") {
        return false;
      }

      // 🔧 VERIFICAR AUTENTICACIÓN antes de verificar favorito
      if (!ddpService.isAuthenticatedUser()) {
        return false;
      }

      return await ddpService.call("postsInmobiliarios.isFavorited", postId);
    } catch (error) {
      console.error("❌ Error al verificar favorito:", error);
      return false;
    }
  }

  // 🚀 NUEVO: Verificar favoritos por lotes (OPTIMIZADO)
  public async getFavoritedStatus(
    postIds: string[]
  ): Promise<{ [postId: string]: boolean }> {
    try {
      if (!postIds || postIds.length === 0) {
        return {};
      }

      // 🔧 VERIFICAR AUTENTICACIÓN antes de verificar favoritos
      if (!ddpService.isAuthenticatedUser()) {
        return {};
      }

      console.log(
        "⭐ Verificando favoritos por lotes:",
        postIds.length,
        "posts"
      );

      // Suscribirse a favoritos para estos posts
      const subscription = ddpService.subscribe(
        "favoritosPost.forPosts",
        postIds
      );
      await subscription.ready();

      // Obtener favoritos de la colección local
      const favoritesCollection = ddpService.collection("favoritosPost");
      const favorites = favoritesCollection.fetch();

      // Mapear estado por postId
      const statusMap: { [postId: string]: boolean } = {};
      postIds.forEach((postId) => {
        statusMap[postId] = favorites.some((fav: any) => fav.postId === postId);
      });

      console.log("✅ Estado de favoritos obtenido por lotes:", {
        totalPosts: postIds.length,
        favoritedPosts: Object.values(statusMap).filter(Boolean).length,
      });

      return statusMap;
    } catch (error) {
      console.error("❌ Error al verificar favoritos por lotes:", error);
      return {};
    }
  }

  // 🚀 NUEVO: Configurar observer optimizado de favoritos para posts específicos
  public onFavoritesChangeForPosts(
    postIds: string[],
    callback: (favoritedStatus: { [postId: string]: boolean }) => void
  ): () => void {
    try {
      if (!postIds || postIds.length === 0) {
        return () => {};
      }

      console.log(
        "👂 Configurando observer optimizado de favoritos para",
        postIds.length,
        "posts"
      );

      const collection = ddpService.collection("favoritosPost");
      const observer = collection.onChange(() => {
        // Obtener favoritos actuales
        const favorites = collection.fetch();

        // Mapear estado por postId
        const statusMap: { [postId: string]: boolean } = {};
        postIds.forEach((postId) => {
          statusMap[postId] = favorites.some(
            (fav: any) => fav.postId === postId
          );
        });

        console.log("🔄 Favoritos actualizados via observer optimizado:", {
          totalPosts: postIds.length,
          favoritedPosts: Object.values(statusMap).filter(Boolean).length,
        });

        callback(statusMap);
      });

      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
          console.log("🛑 Observer optimizado de favoritos detenido");
        }
      };
    } catch (error) {
      console.error(
        "❌ Error configurando observer optimizado de favoritos:",
        error
      );
      return () => {};
    }
  }

  // 🆕 NUEVO: Obtener favoritos del usuario
  public async getFavorites(
    options: FavoritosOptions = {}
  ): Promise<FavoritosResponse> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de obtener favoritos
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de obtener favoritos...");
        await ddpService.forceReauthenticate();
      }

      console.log("⭐ Obteniendo favoritos con opciones:", options);
      const result = await ddpService.call(
        "postsInmobiliarios.getFavorites",
        options
      );
      console.log("✅ Favoritos obtenidos exitosamente:", {
        count: result.favorites?.length || 0,
        totalCount: result.totalCount,
        currentPage: result.currentPage,
      });
      return result;
    } catch (error) {
      console.error("❌ Error al obtener favoritos:", error);
      throw error;
    }
  }

  // 🆕 NUEVO: Suscribirse a favoritos del usuario
  public async subscribeToFavorites(
    filtros: any = {},
    page: number = 1,
    limit: number = 10
  ): Promise<boolean> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de suscribirse
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de suscribirse a favoritos...");
        await ddpService.forceReauthenticate();
      }

      console.log("⭐ Suscribiéndose a favoritos con filtros:", filtros);
      const subscription = ddpService.subscribe(
        "favoritosPost.byUser",
        filtros,
        page,
        limit
      );
      await subscription.ready();
      console.log("✅ Suscripción a favoritos exitosa");
      return true;
    } catch (error) {
      console.error("❌ Error al suscribirse a favoritos:", error);
      throw error;
    }
  }

  // 🆕 NUEVO: Obtener favoritos de la colección DDP
  public getFavoritesFromCollection(): FavoritoPost[] {
    try {
      const collection = ddpService.collection("favoritosPost");
      const favorites = collection.fetch();

      console.log("⭐ Favoritos obtenidos de colección DDP:", favorites.length);
      return favorites;
    } catch (error) {
      console.error("❌ Error al obtener favoritos de colección:", error);
      return [];
    }
  }

  // 🆕 NUEVO: Observer de cambios en favoritos
  public onFavoritesChange(
    callback: (favorites: FavoritoPost[]) => void
  ): () => void {
    try {
      const collection = ddpService.collection("favoritosPost");
      const observer = collection.onChange(() => {
        const favorites = this.getFavoritesFromCollection();
        callback(favorites);
      });

      console.log("👂 Observer de favoritos configurado");
      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
          console.log("🛑 Observer de favoritos detenido");
        }
      };
    } catch (error) {
      console.error("❌ Error configurando observer de favoritos:", error);
      return () => {};
    }
  }

  // 🚀 NUEVO: Limpiar favoritos obsoletos del usuario
  public async cleanupFavorites(): Promise<{ cleanedCount: number }> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de limpiar favoritos
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de limpiar favoritos...");
        await ddpService.forceReauthenticate();
      }

      console.log("🧹 Limpiando favoritos obsoletos...");
      const result = await ddpService.call(
        "postsInmobiliarios.cleanupFavorites"
      );
      console.log("✅ Favoritos limpiados exitosamente:", result);
      return result;
    } catch (error) {
      console.error("❌ Error al limpiar favoritos:", error);
      throw error;
    }
  }

  // 🚀 NUEVO: Sincronizar contadores de favoritos (solo administradores)
  public async syncFavoritesCounters(): Promise<{
    totalPosts: number;
    inconsistenciesFound: number;
    syncedCount: number;
    message: string;
  }> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de sincronizar
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de sincronizar contadores...");
        await ddpService.forceReauthenticate();
      }

      console.log("🔄 Sincronizando contadores de favoritos...");
      const result = await ddpService.call(
        "postsInmobiliarios.syncFavoritesCounters"
      );
      console.log("✅ Contadores sincronizados exitosamente:", result);
      return result;
    } catch (error) {
      console.error("❌ Error al sincronizar contadores:", error);
      throw error;
    }
  }

  // 🚀 NUEVO: Obtener estadísticas de favoritos (solo administradores)
  public async getFavoritesStats(): Promise<{
    totalPosts: number;
    totalFavorites: number;
    postsWithFavorites: number;
    averageFavoritesPerPost: string;
    topFavoritedPosts: any[];
    topActiveUsers: any[];
  }> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de obtener estadísticas
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de obtener estadísticas...");
        await ddpService.forceReauthenticate();
      }

      console.log("📊 Obteniendo estadísticas de favoritos...");
      const result = await ddpService.call(
        "postsInmobiliarios.getFavoritesStats"
      );
      console.log("✅ Estadísticas obtenidas exitosamente:", result);
      return result;
    } catch (error) {
      console.error("❌ Error al obtener estadísticas:", error);
      throw error;
    }
  }

  // 🆕 NUEVO: Métodos para hilos de interés reactivos
  // SIGUIENDO PRINCIPIOS METEOR-DDP-REACTIVIDAD-GUIA.md

  /**
   * Suscribirse reactivamente a mensajes de un hilo específico
   * UNA SOLA SUSCRIPCIÓN que se mantiene activa automáticamente
   */
  public async subscribeToThreadMessages(hiloId: string): Promise<boolean> {
    try {
      console.log(
        `💬 Suscribiéndose REACTIVAMENTE a mensajes del hilo: ${hiloId}`
      );

      // 🔧 VERIFICAR AUTENTICACIÓN
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de suscribirse a mensajes...");
        await ddpService.forceReauthenticate();
      }

      // Suscribirse UNA SOLA VEZ - la subscription se mantiene activa automáticamente
      // Usar la publication 'mensajesHilo' que acabamos de crear en el backend
      const subscription = ddpService.subscribe("mensajesHilo", hiloId);
      await subscription.ready();

      console.log(
        `✅ Subscription reactiva a mensajes del hilo ${hiloId} establecida`
      );
      return true;
    } catch (error) {
      console.error("❌ Error al suscribirse a mensajes del hilo:", error);

      // Si la subscription falla, intentar con el nombre alternativo
      try {
        console.log("🔄 Intentando con subscription alternativa...");
        const altSubscription = ddpService.subscribe(
          "mensajesHilo.byHilo",
          hiloId
        );
        await altSubscription.ready();
        console.log(`✅ Subscription alternativa exitosa para hilo ${hiloId}`);
        return true;
      } catch (altError) {
        console.error("❌ Error con subscription alternativa:", altError);
        throw error; // Lanzar el error original
      }
    }
  }

  /**
   * Obtener mensajes de un hilo (snapshot no reactivo)
   * Para estado inicial solamente - NO USAR PARA POLLING
   */
  public getThreadMessages(hiloId: string): any[] {
    try {
      const mensajesCollection = ddpService.collection("mensajesHilo");

      const mensajes = mensajesCollection
        .filter((m: any) => m.hiloId === hiloId && m.active !== false)
        .fetch()
        .sort(
          (a: any, b: any) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

      console.log(
        `📊 ${mensajes.length} mensajes obtenidos para hilo ${hiloId}`
      );
      return mensajes;
    } catch (error) {
      console.error("❌ Error al obtener mensajes del hilo:", error);
      return [];
    }
  }

  /**
   * Obtener contadores de mensajes no leídos para hilos
   */
  public async getUnreadCounts(
    hiloIds: string[]
  ): Promise<Record<string, number>> {
    try {
      if (hiloIds.length === 0) return {};

      await this.connect();
      const contadores = await this.call(
        "hilosInteres.getUnreadCounts",
        hiloIds
      );

      console.log(`📊 Contadores de mensajes no leídos obtenidos:`, contadores);
      return contadores;
    } catch (error) {
      console.error(
        "❌ Error obteniendo contadores de mensajes no leídos:",
        error
      );
      return {};
    }
  }

  /**
   * Marcar mensajes como leídos en un hilo
   */
  public async markMessagesAsRead(hiloId: string): Promise<any> {
    try {
      await this.connect();
      const result = await this.call("hilosInteres.markAsRead", hiloId);

      console.log(`✅ Mensajes marcados como leídos en hilo ${hiloId}`);
      return result;
    } catch (error) {
      console.error("❌ Error marcando mensajes como leídos:", error);
      throw error;
    }
  }

  /**
   * 🆕 NUEVO: Ocultar una publicación para el usuario actual
   * Agrega al usuario al array hiddenByUsers para que no vea la publicación
   */
  public async hideForUser(postId: string): Promise<{
    success: boolean;
    message: string;
    postId: string;
    hiddenByUsersCount: number;
  }> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de ocultar
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de ocultar publicación...");
        await ddpService.forceReauthenticate();
      }

      console.log(
        `👁️‍🗨️ Ocultando publicación ${postId} para el usuario actual...`
      );

      // Llamar al método del backend
      const result = await this.call("postsInmobiliarios.hideForUser", postId);

      console.log("✅ Publicación ocultada exitosamente:", result);
      return result;
    } catch (error) {
      console.error("❌ Error al ocultar publicación:", error);

      // Interpretar errores específicos del backend
      let errorMessage = "Error al ocultar la publicación";

      if (error instanceof Error) {
        if (error.message.includes("not-allowed")) {
          errorMessage = "No puedes ocultar tus propias publicaciones";
        } else if (error.message.includes("already-hidden")) {
          errorMessage = "Esta publicación ya está oculta para ti";
        } else if (error.message.includes("not-found")) {
          errorMessage = "Publicación no encontrada";
        } else if (error.message.includes("not-authorized")) {
          errorMessage = "Debes iniciar sesión para ocultar publicaciones";
        } else {
          errorMessage = error.message;
        }
      }

      // Re-lanzar error con mensaje interpretado
      const interpretedError = new Error(errorMessage);
      (interpretedError as any).originalError = error;
      throw interpretedError;
    }
  }

  /**
   * 🆕 NUEVO: Des-ocultar una publicación para el usuario actual
   * Remueve al usuario del array hiddenByUsers para que vuelva a ver la publicación
   */
  public async unhideForUser(postId: string): Promise<{
    success: boolean;
    message: string;
    postId: string;
    hiddenByUsersCount: number;
  }> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de des-ocultar
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de des-ocultar publicación...");
        await ddpService.forceReauthenticate();
      }

      console.log(
        `👁️ Des-ocultando publicación ${postId} para el usuario actual...`
      );

      // Llamar al método del backend
      const result = await this.call(
        "postsInmobiliarios.unhideForUser",
        postId
      );

      console.log("✅ Publicación des-ocultada exitosamente:", result);
      return result;
    } catch (error) {
      console.error("❌ Error al des-ocultar publicación:", error);

      // Interpretar errores específicos del backend
      let errorMessage = "Error al restaurar la publicación";

      if (error instanceof Error) {
        if (error.message.includes("not-hidden")) {
          errorMessage = "Esta publicación no está oculta para ti";
        } else if (error.message.includes("not-found")) {
          errorMessage = "Publicación no encontrada";
        } else if (error.message.includes("not-authorized")) {
          errorMessage = "Debes iniciar sesión para restaurar publicaciones";
        } else {
          errorMessage = error.message;
        }
      }

      // Re-lanzar error con mensaje interpretado
      const interpretedError = new Error(errorMessage);
      (interpretedError as any).originalError = error;
      throw interpretedError;
    }
  }

  /**
   * 🆕 NUEVO: Verificar si una publicación está visible para el usuario actual
   */
  public async isVisibleForUser(postId: string): Promise<boolean> {
    try {
      // 🔧 VERIFICAR AUTENTICACIÓN antes de verificar visibilidad
      if (!ddpService.isAuthenticatedUser()) {
        console.warn("⚠️ Reautenticando antes de verificar visibilidad...");
        await ddpService.forceReauthenticate();
      }

      console.log(`🔍 Verificando visibilidad de publicación ${postId}...`);

      // Llamar al método del backend
      const isVisible = await this.call(
        "postsInmobiliarios.isVisibleForUser",
        postId
      );

      console.log(
        `✅ Visibilidad verificada para publicación ${postId}: ${isVisible}`
      );
      return isVisible;
    } catch (error) {
      console.error("❌ Error al verificar visibilidad:", error);
      return false; // En caso de error, asumir que no es visible
    }
  }

  /**
   * Observador reactivo para cambios en mensajes de hilo
   * CONFIGURAR UNA SOLA VEZ y Meteor manejará las actualizaciones automáticamente
   */
  public onThreadMessagesChange(
    hiloId: string,
    callback: (mensajes: any[]) => void
  ): () => void {
    console.log(
      `🔄 Configurando observer reactivo para mensajes del hilo: ${hiloId}`
    );

    const mensajesCollection = ddpService.collection("mensajesHilo");
    const observer = mensajesCollection.onChange(() => {
      console.log(
        "💬 Cambio detectado en colección de mensajes - REACTIVIDAD METEOR"
      );

      // Filtrar mensajes de este hilo específico
      const mensajesDelHilo = mensajesCollection
        .filter((m: any) => m.hiloId === hiloId && m.active !== false)
        .fetch()
        .sort(
          (a: any, b: any) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

      // Ejecutar callback REACTIVAMENTE
      callback(mensajesDelHilo);

      console.log(
        `🔄 Observer ejecutado - ${mensajesDelHilo.length} mensajes actualizados`
      );
    });

    // Retornar función de limpieza para evitar memory leaks
    return () => {
      if (observer && typeof observer.stop === "function") {
        observer.stop();
        console.log(`🧹 Observer de mensajes para hilo ${hiloId} limpiado`);
      }
    };
  }

  // 🔧 REMOVIDO: Todo el sistema de leads eliminado
}

export default InmobiliarioService.getInstance();
