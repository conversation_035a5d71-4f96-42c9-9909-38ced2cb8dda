// Este archivo sirve como wrapper para simpleDDP para evitar problemas de inicialización
import simpleDDP from "simpleddp";
import * as loginPluginModule from "simpleddp-plugin-login";
import ws from "isomorphic-ws";

// Acceso directo al plugin de login (puede estar en default o en otra propiedad)
const loginPlugin =
  typeof loginPluginModule.default === "function"
    ? loginPluginModule.default
    : typeof loginPluginModule === "function"
    ? loginPluginModule
    : null;

// Log para diagnóstico
console.log("Plugin login encontrado:", !!loginPlugin);
console.log("Tipo del plugin:", typeof loginPlugin);

// Exportar los módulos
export { simpleDDP, ws };

// Exportamos loginPlugin como una función que siempre devuelve algo válido
export function getLoginPlugin() {
  if (typeof loginPlugin === "function") {
    return loginPlugin();
  }
  // Fallback: devolvemos un plugin vacío si no se cargó correctamente
  return {
    name: "mock-login-plugin",
    init: () => {},
  };
}

// También creamos funciones de fábrica para facilitar la creación de instancias
export function createDDP(options) {
  // Aseguramos que las opciones sean válidas
  const validOptions = options || {};

  // Usamos try/catch para garantizar que no falle la creación
  try {
    return new simpleDDP(validOptions);
  } catch (error) {
    console.error("Error al crear instancia de simpleDDP:", error);
    // Devolvemos un objeto mock para que no falle todo
    return {
      connect: () => Promise.resolve(),
      on: () => {},
      collection: () => ({ filter: () => ({ fetch: () => [] }) }),
      call: () => Promise.resolve(null),
      subscribe: () => ({ ready: () => Promise.resolve() }),
    };
  }
}

// Crear una instancia de DDP con plugins
export function createDDPWithPlugins(options, usePlugins = true) {
  // Aseguramos que las opciones sean válidas
  const validOptions = { ...options };

  // Si se desean usar plugins, los incorporamos
  if (usePlugins) {
    try {
      // Añadir el plugin de login independientemente de si se exportó correctamente
      validOptions.plugins = [getLoginPlugin()];
    } catch (e) {
      console.error("Error al inicializar plugins:", e);
      validOptions.plugins = [];
    }
  }

  return createDDP(validOptions);
}
