import type {
  HiloInter<PERSON>,
  MensajeHilo,
  UnreadCountsByPosts,
  UnreadCountsOptions,
  UnreadCountByPost,
  UnreadCountersState,
  HilosStats,
} from "../types/hilosInteres";
import ddpService from "./ddpService";

/**
 * Servicio optimizado para gestión de hilos de interés
 * Incluye contadores globales reactivos y métodos de alto rendimiento
 */
class HilosInteresService {
  // 🆕 Estado reactivo de contadores
  private countersState: UnreadCountersState = {
    counters: {},
    loading: false,
    lastUpdated: null,
    error: null,
  };

  private countersObservers: Map<string, () => void> = new Map();

  // 🆕 MÉTODOS OPTIMIZADOS PARA CONTADORES GLOBALES

  /**
   * Suscribirse a contadores de mensajes no leídos para múltiples posts
   */
  async subscribeToUnreadCountsByPosts(postIds: string[]): Promise<void> {
    try {
      if (postIds.length === 0) return;

      const subscription = ddpService.subscribe(
        "unreadCounts.byPosts",
        postIds
      );
      await subscription.ready();

      console.log(`✅ Suscrito a contadores para ${postIds.length} posts`);
    } catch (error) {
      console.error("❌ Error en suscripción a contadores:", error);
      throw error;
    }
  }

  /**
   * Obtener contadores globales de mensajes no leídos por posts (OPTIMIZADO)
   */
  async getUnreadCountsByPosts(
    options: UnreadCountsOptions
  ): Promise<UnreadCountsByPosts> {
    try {
      this.countersState.loading = true;
      this.countersState.error = null;

      const { postIds, forceRecalculate = false } = options;

      if (postIds.length === 0) {
        return {};
      }

      console.log(`📊 Obteniendo contadores para ${postIds.length} posts...`);

      // Usar método optimizado del backend
      const counters = forceRecalculate
        ? await ddpService.call(
            "hilosInteres.recalculateCountersForPosts",
            postIds
          )
        : await ddpService.call("hilosInteres.getUnreadCountsByPosts", postIds);

      // Actualizar estado reactivo
      this.countersState.counters = {
        ...this.countersState.counters,
        ...counters,
      };
      this.countersState.lastUpdated = new Date();

      console.log(`✅ Contadores globales obtenidos:`, counters);
      return counters;
    } catch (error) {
      console.error("❌ Error obteniendo contadores globales:", error);
      this.countersState.error =
        error instanceof Error ? error.message : "Error desconocido";
      throw error;
    } finally {
      this.countersState.loading = false;
    }
  }

  /**
   * Obtener contadores desde el estado reactivo local
   */
  getCountersFromState(): UnreadCountsByPosts {
    return { ...this.countersState.counters };
  }

  /**
   * Obtener contador para un post específico
   */
  getCounterForPost(postId: string): number {
    return this.countersState.counters[postId] || 0;
  }

  /**
   * Observer reactivo para cambios en contadores globales
   */
  onCountersChange(
    callback: (counters: UnreadCountsByPosts) => void
  ): () => void {
    try {
      const observerId = `counters-${Date.now()}-${Math.random()}`;

      const collection = ddpService.collection("unreadCountsByPost");

      const observer = collection.onChange(() => {
        // Obtener contadores actualizados de la colección
        const countersArray = collection.fetch() as UnreadCountByPost[];

        // Convertir a formato de mapa
        const countersMap: UnreadCountsByPosts = {};
        countersArray.forEach((counter) => {
          countersMap[counter.postId] = counter.totalUnreadMessages;
        });

        // Actualizar estado interno
        this.countersState.counters = countersMap;
        this.countersState.lastUpdated = new Date();

        // Notificar cambios
        callback(countersMap);

        console.log(
          "🔄 Contadores globales actualizados reactivamente:",
          countersMap
        );
      });

      // Guardar referencia para limpieza
      this.countersObservers.set(observerId, () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
        }
      });

      console.log("👂 Observer de contadores globales configurado");

      return () => {
        const cleanup = this.countersObservers.get(observerId);
        if (cleanup) {
          cleanup();
          this.countersObservers.delete(observerId);
        }
        console.log("🛑 Observer de contadores globales detenido");
      };
    } catch (error) {
      console.error("❌ Error configurando observer de contadores:", error);
      return () => {};
    }
  }

  /**
   * Forzar actualización de contadores para posts específicos
   */
  async forceUpdateCountersForPosts(
    postIds: string[]
  ): Promise<UnreadCountsByPosts> {
    try {
      console.log(
        `🔄 Forzando actualización de contadores para ${postIds.length} posts...`
      );

      const counters = await ddpService.call(
        "hilosInteres.recalculateCountersForPosts",
        postIds
      );

      // Actualizar estado local
      this.countersState.counters = {
        ...this.countersState.counters,
        ...counters,
      };
      this.countersState.lastUpdated = new Date();

      console.log("✅ Contadores forzados actualizados:", counters);
      return counters;
    } catch (error) {
      console.error("❌ Error forzando actualización de contadores:", error);
      throw error;
    }
  }

  // 🆕 MÉTODOS PARA FEED DE HILOS (SIGUIENDO MEJORES PRÁCTICAS DDP)

  /**
   * 🚀 Suscribirse al feed de hilos para un usuario (REACTIVO)
   * Siguiendo principios DDP: Una sola suscripción mantiene datos sincronizados
   */
  async subscribeToFeedForUser(
    userId: string,
    options: {
      limit?: number;
      sortBy?: "lastMessage" | "created" | "activity" | "alphabetical";
      filterUnread?: boolean;
    } = {}
  ): Promise<void> {
    try {
      console.log(`📋 Suscribiéndose al feed de hilos para usuario: ${userId}`);

      const subscription = ddpService.subscribe(
        "hilosFeed.forUser",
        userId,
        options
      );
      await subscription.ready();

      console.log(`✅ Suscrito al feed de hilos para usuario: ${userId}`);
    } catch (error) {
      console.error("❌ Error suscribiéndose al feed:", error);
      throw error;
    }
  }

  /**
   * 🔄 Observer reactivo para cambios en el feed de hilos
   * NO hace polling - confía en la reactividad nativa de DDP
   */
  onFeedChange(
    userId: string,
    callback: (hilos: HiloInteres[]) => void
  ): () => void {
    try {
      const collection = ddpService.collection("hilosInteres");

      const observer = collection.onChange(() => {
        // 🚀 DDP mantiene la colección sincronizada automáticamente
        const feedHilos = collection
          .fetch()
          .filter(
            (hilo: any) =>
              hilo.sociosIds && hilo.sociosIds.includes(userId) && hilo.active
          ) as HiloInteres[];

        // Ordenar por última actividad (cliente puede hacer esto eficientemente)
        feedHilos.sort(
          (a, b) =>
            new Date(b.lastMessageAt).getTime() -
            new Date(a.lastMessageAt).getTime()
        );

        callback(feedHilos);
        console.log(
          `🔄 Feed actualizado automáticamente: ${feedHilos.length} hilos`
        );
      });

      console.log(`👂 Observer de feed configurado para usuario: ${userId}`);

      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
        }
        console.log(`🛑 Observer de feed detenido para usuario: ${userId}`);
      };
    } catch (error) {
      console.error("❌ Error configurando observer de feed:", error);
      return () => {};
    }
  }

  /**
   * 📊 Obtener feed de hilos desde colección local (NO hace llamada al servidor)
   * DDP mantiene la colección sincronizada automáticamente
   */
  getFeedFromCollection(userId: string): HiloInteres[] {
    try {
      const collection = ddpService.collection("hilosInteres");

      const feedHilos = collection
        .fetch()
        .filter(
          (hilo: any) =>
            hilo.sociosIds && hilo.sociosIds.includes(userId) && hilo.active
        ) as HiloInteres[];

      // Ordenar por última actividad
      feedHilos.sort(
        (a, b) =>
          new Date(b.lastMessageAt).getTime() -
          new Date(a.lastMessageAt).getTime()
      );

      console.log(
        `📋 Feed obtenido desde colección local: ${feedHilos.length} hilos`
      );
      return feedHilos;
    } catch (error) {
      console.error("❌ Error obteniendo feed desde colección:", error);
      return [];
    }
  }

  /**
   * 🔢 Obtener estadísticas del feed (llamada única al servidor)
   */
  async getFeedStats(userId: string): Promise<{
    totalHilos: number;
    hilosConMensajesNoLeidos: number;
    totalMensajesNoLeidos: number;
    hilosCreados: number;
    hilosComoAutorPost: number;
    porcentajeActividad: number;
    timestamp: Date;
  }> {
    try {
      console.log(`📊 Obteniendo estadísticas de feed para usuario: ${userId}`);

      const stats = await ddpService.call("hilosInteres.getFeedStats", userId);

      console.log(`✅ Estadísticas de feed obtenidas:`, stats);
      return stats;
    } catch (error) {
      console.error("❌ Error obteniendo estadísticas de feed:", error);
      throw error;
    }
  }

  /**
   * 🎯 Obtener solo hilos con mensajes no leídos (desde servidor)
   */
  async getUnreadFeedForUser(userId: string): Promise<HiloInteres[]> {
    try {
      console.log(`🔔 Obteniendo hilos no leídos para usuario: ${userId}`);

      const unreadHilos = await ddpService.call(
        "hilosInteres.getFeedForUser",
        userId,
        {
          filterUnread: true,
          sortBy: "lastMessage",
          limit: 50,
        }
      );

      console.log(`✅ Hilos no leídos obtenidos: ${unreadHilos.length}`);
      return unreadHilos;
    } catch (error) {
      console.error("❌ Error obteniendo hilos no leídos:", error);
      return [];
    }
  }

  /**
   * 🚀 Suscripción completa para dashboard (hilos + contadores)
   * Una sola suscripción para datos relacionados
   */
  async subscribeToFeedWithCounters(userId: string): Promise<void> {
    try {
      console.log(
        `📋📊 Suscribiéndose al feed completo para usuario: ${userId}`
      );

      const subscription = ddpService.subscribe(
        "hilosFeed.withCounters",
        userId
      );
      await subscription.ready();

      console.log(`✅ Suscrito al feed completo para usuario: ${userId}`);
    } catch (error) {
      console.error("❌ Error suscribiéndose al feed completo:", error);
      throw error;
    }
  }

  /**
   * 🔔 Suscribirse a notificaciones de nuevos hilos
   */
  async subscribeToHilosNotifications(userId: string): Promise<void> {
    try {
      console.log(`🔔 Suscribiéndose a notificaciones para usuario: ${userId}`);

      const subscription = ddpService.subscribe(
        "hilosNotifications.forUser",
        userId
      );
      await subscription.ready();

      console.log(`✅ Suscrito a notificaciones para usuario: ${userId}`);
    } catch (error) {
      console.error("❌ Error suscribiéndose a notificaciones:", error);
      throw error;
    }
  }

  /**
   * 🔄 Observer para notificaciones de nuevos hilos
   */
  onHilosNotificationsChange(
    userId: string,
    callback: (notificaciones: HiloInteres[]) => void
  ): () => void {
    try {
      const collection = ddpService.collection("hilosInteres");

      const observer = collection.onChange(() => {
        // Filtrar solo notificaciones recientes (últimas 24h)
        const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);

        const notificaciones = collection.fetch().filter(
          (hilo: any) =>
            hilo.sociosIds &&
            hilo.sociosIds.includes(userId) &&
            hilo.active &&
            new Date(hilo.createdAt) >= last24Hours &&
            hilo.creatorId !== userId // Excluir hilos que el usuario creó
        ) as HiloInteres[];

        callback(notificaciones);
        console.log(`🔔 Notificaciones actualizadas: ${notificaciones.length}`);
      });

      console.log(
        `👂 Observer de notificaciones configurado para usuario: ${userId}`
      );

      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
        }
        console.log(
          `🛑 Observer de notificaciones detenido para usuario: ${userId}`
        );
      };
    } catch (error) {
      console.error("❌ Error configurando observer de notificaciones:", error);
      return () => {};
    }
  }

  // MÉTODOS EXISTENTES (mantenidos para compatibilidad)

  /**
   * Obtener hilos desde la colección local
   */
  getHilosFromCollection(): HiloInteres[] {
    try {
      const collection = ddpService.collection("hilosInteres");
      return collection.fetch() as HiloInteres[];
    } catch (error) {
      console.error("❌ Error obteniendo hilos de colección:", error);
      return [];
    }
  }

  /**
   * Obtener mensajes desde la colección local
   */
  getMensajesFromCollection(hiloId?: string): MensajeHilo[] {
    try {
      const collection = ddpService.collection("mensajesHilo");
      const mensajes = collection.fetch() as MensajeHilo[];

      return hiloId ? mensajes.filter((m) => m.hiloId === hiloId) : mensajes;
    } catch (error) {
      console.error("❌ Error obteniendo mensajes de colección:", error);
      return [];
    }
  }

  /**
   * Observer para cambios en hilos
   */
  onHilosChange(callback: (hilos: HiloInteres[]) => void): () => void {
    try {
      const collection = ddpService.collection("hilosInteres");
      const observer = collection.onChange(() => {
        const hilos = this.getHilosFromCollection();
        callback(hilos);
      });

      console.log("👂 Observer de hilos configurado");
      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
          console.log("🛑 Observer de hilos detenido");
        }
      };
    } catch (error) {
      console.error("❌ Error configurando observer de hilos:", error);
      return () => {};
    }
  }

  /**
   * Observer para cambios en hilos de un post específico
   */
  onHilosChangeByPost(
    postId: string,
    callback: (hilos: HiloInteres[]) => void
  ): () => void {
    try {
      const collection = ddpService.collection("hilosInteres");
      const observer = collection.onChange(() => {
        const hilos = this.getHilosFromCollection().filter(
          (h) => h.postId === postId
        );
        callback(hilos);
      });

      console.log(`👂 Observer de hilos configurado para post ${postId}`);
      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
          console.log(`🛑 Observer de hilos detenido para post ${postId}`);
        }
      };
    } catch (error) {
      console.error("❌ Error configurando observer de hilos por post:", error);
      return () => {};
    }
  }

  /**
   * Observer para cambios en mensajes
   */
  onMensajesChange(
    hiloId: string,
    callback: (mensajes: MensajeHilo[]) => void
  ): () => void {
    try {
      const collection = ddpService.collection("mensajesHilo");
      const observer = collection.onChange(() => {
        const mensajes = this.getMensajesFromCollection(hiloId);
        callback(mensajes);
      });

      console.log(`👂 Observer de mensajes configurado para hilo ${hiloId}`);
      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
          console.log(`🛑 Observer de mensajes detenido para hilo ${hiloId}`);
        }
      };
    } catch (error) {
      console.error("❌ Error configurando observer de mensajes:", error);
      return () => {};
    }
  }

  // MÉTODOS DE ACCIÓN

  /**
   * Crear un nuevo hilo de interés
   */
  async createHilo(hiloData: {
    postId: string;
    titulo: string;
    referenciaPrivada: string;
    mensajeInicial: string;
  }) {
    try {
      const result = await ddpService.call("hilosInteres.create", hiloData);
      console.log("✅ Hilo creado exitosamente");
      return result;
    } catch (error) {
      console.error("❌ Error creando hilo:", error);
      throw error;
    }
  }

  /**
   * Enviar mensaje a un hilo
   */
  async sendMessage(hiloId: string, mensaje: string) {
    try {
      const result = await ddpService.call(
        "hilosInteres.sendMessage",
        hiloId,
        mensaje
      );
      console.log("✅ Mensaje enviado exitosamente");
      return result;
    } catch (error) {
      console.error("❌ Error enviando mensaje:", error);
      throw error;
    }
  }

  /**
   * Marcar mensajes como leídos
   */
  async markAsRead(hiloId: string) {
    try {
      const result = await ddpService.call("hilosInteres.markAsRead", hiloId);
      console.log("✅ Mensajes marcados como leídos");
      return result;
    } catch (error) {
      console.error("❌ Error marcando mensajes como leídos:", error);
      throw error;
    }
  }

  /**
   * Obtener contadores de mensajes no leídos para múltiples hilos
   */
  async getUnreadCounts(hiloIds: string[]): Promise<Record<string, number>> {
    try {
      if (hiloIds.length === 0) return {};

      const contadores = await ddpService.call(
        "hilosInteres.getUnreadCounts",
        hiloIds
      );
      console.log(
        `📊 Contadores de no leídos obtenidos para ${hiloIds.length} hilos:`,
        contadores
      );
      return contadores;
    } catch (error) {
      console.error("❌ Error obteniendo contadores de no leídos:", error);
      return {};
    }
  }

  /**
   * Eliminar un hilo
   */
  async removeHilo(hiloId: string) {
    try {
      const result = await ddpService.call("hilosInteres.remove", hiloId);
      console.log("✅ Hilo eliminado exitosamente");
      return result;
    } catch (error) {
      console.error("❌ Error eliminando hilo:", error);
      throw error;
    }
  }

  /**
   * Obtener resumen de hilos del usuario
   */
  async getSummary() {
    try {
      const result = await ddpService.call("hilosInteres.getSummary");
      console.log("✅ Resumen de hilos obtenido");
      return result;
    } catch (error) {
      console.error("❌ Error obteniendo resumen:", error);
      throw error;
    }
  }

  // 🆕 MÉTODOS DE ADMINISTRACIÓN

  /**
   * Recalcular todos los contadores (solo administradores)
   */
  async adminRecalculateAllCounters(): Promise<{ recalculatedCount: number }> {
    try {
      const result = await ddpService.call(
        "hilosInteres.admin.recalculateAllCounters"
      );
      console.log("✅ Recálculo completo ejecutado");
      return result;
    } catch (error) {
      console.error("❌ Error en recálculo completo:", error);
      throw error;
    }
  }

  /**
   * Limpiar contadores obsoletos (solo administradores)
   */
  async adminCleanupCounters(): Promise<{ cleanedCount: number }> {
    try {
      const result = await ddpService.call(
        "hilosInteres.admin.cleanupCounters"
      );
      console.log("✅ Limpieza de contadores ejecutada");
      return result;
    } catch (error) {
      console.error("❌ Error en limpieza de contadores:", error);
      throw error;
    }
  }

  /**
   * Obtener estadísticas del sistema (solo administradores)
   */
  async adminGetStats(): Promise<HilosStats> {
    try {
      const result = await ddpService.call("hilosInteres.admin.getStats");
      console.log("✅ Estadísticas obtenidas");
      return result;
    } catch (error) {
      console.error("❌ Error obteniendo estadísticas:", error);
      throw error;
    }
  }

  // MÉTODOS DE LIMPIEZA

  /**
   * Limpiar todos los observers activos
   */
  cleanup(): void {
    console.log("🧹 Limpiando observers de HilosInteresService...");

    // Limpiar observers de contadores
    this.countersObservers.forEach((cleanup, id) => {
      try {
        cleanup();
        console.log(`✅ Observer ${id} limpiado`);
      } catch (error) {
        console.error(`❌ Error limpiando observer ${id}:`, error);
      }
    });
    this.countersObservers.clear();

    // Resetear estado de contadores
    this.countersState = {
      counters: {},
      loading: false,
      lastUpdated: null,
      error: null,
    };

    console.log("✅ HilosInteresService limpiado completamente");
  }

  /**
   * Obtener estado actual de contadores (para debugging)
   */
  getCountersState(): UnreadCountersState {
    return { ...this.countersState };
  }
}

const hilosInteresService = new HilosInteresService();
export default hilosInteresService;
