import { createDDPWithPlugins, ws } from "./simpleddpWrapper";

// Configuración para la conexión
const opts = {
  endpoint: import.meta.env.VITE_METEOR_URL || "ws://localhost:3000/websocket",
  SocketConstructor: ws,
  reconnectInterval: 5000,
};

// Clase singleton para gestionar la conexión DDP
class DDPService {
  private static instance: DDPService;
  public ddp: any;
  private isConnecting: boolean = false;
  private connectionPromise: Promise<void> | null = null;
  private currentToken: string | null = null;
  private isAuthenticated: boolean = false;
  private reconnectionAttempts: number = 0;
  private maxReconnectionAttempts: number = 5;
  private reconnectionTimer: NodeJS.Timeout | null = null;

  // 🆕 NUEVO: Sistema de eventos para reactividad
  private authChangeListeners: Set<() => void> = new Set();

  private constructor() {
    try {
      // Usar la nueva función que maneja los plugins internamente
      this.ddp = createDDPWithPlugins(opts);

      // Configurar eventos de conexión mejorados
      this.setupConnectionEvents();
    } catch (error) {
      console.error("Error crítico al inicializar DDP:", error);
      // Crear un mock para evitar errores fatales
      this.ddp = {
        connected: false,
        connect: () => Promise.resolve(),
        on: () => {},
        collection: () => ({ filter: () => ({ fetch: () => [] }) }),
        call: () => Promise.resolve(null),
        subscribe: () => ({ ready: () => Promise.resolve() }),
      };
    }
  }

  public static getInstance(): DDPService {
    if (!DDPService.instance) {
      DDPService.instance = new DDPService();
    }
    return DDPService.instance;
  }

  // 🆕 MEJORADO: Configurar eventos de conexión con reautenticación automática
  private setupConnectionEvents(): void {
    this.ddp.on("connected", async () => {
      console.log("🔌 Conectado al servidor Meteor");
      this.reconnectionAttempts = 0;

      // 🔑 REAUTENTICAR AUTOMÁTICAMENTE al reconectarse
      if (this.currentToken && !this.isAuthenticated) {
        console.log("🔑 Reautenticando automáticamente tras reconexión...");
        try {
          await this.authenticateWithToken();
        } catch (error) {
          console.error("❌ Error en reautenticación automática:", error);
        }
      }
    });

    this.ddp.on("disconnected", () => {
      console.log("🔌 Desconectado del servidor Meteor");
      this.isAuthenticated = false; // Marcar como no autenticado

      // 🔄 PROGRAMAR RECONEXIÓN INTELIGENTE
      this.scheduleReconnection();
    });

    this.ddp.on("error", (e: any) => {
      console.error("❌ Error en la conexión DDP:", e);
      this.isAuthenticated = false;
    });

    // 🆕 NUEVO: Escuchar eventos de autenticación
    this.ddp.on("login", (loginResult: any) => {
      console.log("🔑 Evento login DDP recibido:", {
        userId: loginResult?.id || "N/A",
        hasId: !!loginResult?.id,
      });
      this.isAuthenticated = true;

      // 🔧 ASEGURAR que el userId se guarde en el evento login también
      if (loginResult?.id) {
        this.ddp.userId = loginResult.id;
        console.log("✅ UserId guardado desde evento login:", loginResult.id);
      }

      // 🆕 NUEVO: Notificar cambio de autenticación
      this.notifyAuthChange();
    });

    this.ddp.on("logout", () => {
      console.log("🔑 Logout DDP detectado");
      this.isAuthenticated = false;
      this.currentToken = null;
      this.ddp.userId = null; // Limpiar userId

      // 🆕 NUEVO: Notificar cambio de autenticación
      this.notifyAuthChange();
    });
  }

  // 🆕 NUEVO: Programar reconexión inteligente
  private scheduleReconnection(): void {
    if (this.reconnectionTimer) {
      clearTimeout(this.reconnectionTimer);
    }

    if (this.reconnectionAttempts < this.maxReconnectionAttempts) {
      const delay = Math.min(
        1000 * Math.pow(2, this.reconnectionAttempts),
        30000
      ); // Backoff exponencial
      this.reconnectionAttempts++;

      console.log(
        `🔄 Programando reconexión automática en ${delay}ms (intento ${this.reconnectionAttempts}/${this.maxReconnectionAttempts})`
      );

      this.reconnectionTimer = setTimeout(async () => {
        if (!this.ddp.connected && this.currentToken) {
          console.log("🔄 Intentando reconexión automática...");
          try {
            await this.connect(this.currentToken);
          } catch (error) {
            console.error("❌ Error en reconexión automática:", error);
          }
        }
      }, delay);
    } else {
      console.warn("⚠️ Máximo número de intentos de reconexión alcanzado");
    }
  }

  // Método para establecer el token de autenticación
  public setAuthToken(token: string): void {
    this.currentToken = token;
  }

  public async connect(token?: string): Promise<void> {
    // Si se proporciona un token, actualizarlo
    if (token) {
      this.currentToken = token;
    }

    if (this.ddp.connected) {
      // Si ya estamos conectados pero tenemos un nuevo token, autenticar
      if (this.currentToken && !this.isAuthenticated) {
        await this.authenticateWithToken();
      }
      return Promise.resolve();
    }

    if (this.isConnecting) {
      return this.connectionPromise as Promise<void>;
    }

    this.isConnecting = true;
    this.connectionPromise = this.ddp
      .connect()
      .then(async () => {
        this.isConnecting = false;
        console.log("✅ Conexión DDP establecida");

        // Autenticar automáticamente si tenemos token
        if (this.currentToken) {
          await this.authenticateWithToken();
        }
      })
      .catch((error: any) => {
        this.isConnecting = false;
        console.error("❌ Error al conectar DDP:", error);
        throw error;
      });

    // Solución al problema de tipo - garantizamos que no es null
    return this.connectionPromise || Promise.resolve();
  }

  // 🔧 MEJORADO: Método para autenticar con token usando el sistema estándar de Meteor
  private async authenticateWithToken(): Promise<void> {
    if (!this.currentToken) {
      console.warn("⚠️ No hay token disponible para autenticación DDP");
      return;
    }

    try {
      console.log("🔑 Autenticando conexión DDP con token de sesión...");
      // Usar el método login estándar de Meteor con resume token
      const result = await this.ddp.call("login", {
        resume: this.currentToken,
      });

      this.isAuthenticated = true;

      // 🔧 GUARDAR EL USERID del resultado de la autenticación
      if (result?.id) {
        this.ddp.userId = result.id;
        console.log("✅ Autenticación DDP exitosa:", {
          userId: result.id,
          tokenType: result?.tokenExpires ? "Con expiración" : "Permanente",
        });
      } else {
        console.warn(
          "⚠️ Autenticación exitosa pero sin userId en respuesta:",
          result
        );
      }

      // 🆕 NUEVO: Notificar cambio de autenticación
      this.notifyAuthChange();
    } catch (error) {
      console.error("❌ Error al autenticar con token DDP:", error);
      this.isAuthenticated = false;
      throw error;
    }
  }

  // 🆕 NUEVO: Verificar estado de autenticación
  public isAuthenticatedUser(): boolean {
    return this.ddp.connected && this.isAuthenticated && !!this.currentToken;
  }

  // 🆕 NUEVO: Forzar reautenticación
  public async forceReauthenticate(): Promise<void> {
    if (!this.currentToken) {
      throw new Error("No hay token para reautenticar");
    }

    console.log("🔄 Forzando reautenticación...");
    this.isAuthenticated = false;

    if (!this.ddp.connected) {
      await this.connect(this.currentToken);
    } else {
      await this.authenticateWithToken();
    }
  }

  // 🆕 NUEVO: Obtener ID del usuario actual (si está autenticado)
  public getCurrentUserId(): string | null {
    try {
      // Verificación rápida de estado de autenticación
      if (!this.isAuthenticated) {
        console.warn("🔒 getCurrentUserId: Usuario no autenticado");
        return null;
      }

      if (!this.ddp.connected) {
        console.warn("🔒 getCurrentUserId: DDP no conectado");
        return null;
      }

      // Intentar obtener el userId desde el estado de DDP
      const userId = this.ddp.userId || null;

      console.log("🔒 getCurrentUserId resultado:", {
        isAuthenticated: this.isAuthenticated,
        connected: this.ddp.connected,
        hasToken: !!this.currentToken,
        userId: userId ? "***" + userId.slice(-4) : null,
        ddpUserIdExists: !!this.ddp.userId,
      });

      return userId;
    } catch (error) {
      console.error("❌ Error obteniendo userId:", error);
      return null;
    }
  }

  public collection(name: string) {
    return this.ddp.collection(name);
  }

  public subscribe(name: string, ...args: any[]) {
    // 🔧 VERIFICAR AUTENTICACIÓN antes de suscribirse
    if (this.currentToken && !this.isAuthenticated) {
      console.warn(
        "⚠️ Intentando suscribirse sin autenticación válida, reautenticando..."
      );
      this.forceReauthenticate().catch((err) =>
        console.error("Error en reautenticación automática:", err)
      );
    }

    return this.ddp.subscribe(name, ...args);
  }

  public call(method: string, ...args: any[]) {
    return this.ddp.call(method, ...args);
  }

  public async login(credentials: {
    username?: string;
    email?: string;
    password: string;
  }): Promise<any> {
    try {
      const result = await this.ddp.login(credentials);
      this.isAuthenticated = true;
      return result;
    } catch (error) {
      console.error("Error al autenticar:", error);
      this.isAuthenticated = false;
      throw error;
    }
  }

  public async logout(): Promise<void> {
    try {
      await this.ddp.logout();
      this.currentToken = null;
      this.isAuthenticated = false;

      // Limpiar timer de reconexión
      if (this.reconnectionTimer) {
        clearTimeout(this.reconnectionTimer);
        this.reconnectionTimer = null;
      }
    } catch (error) {
      console.error("Error al cerrar sesión:", error);
      throw error;
    }
  }

  // Método para obtener el token actual
  public getCurrentToken(): string | null {
    return this.currentToken;
  }

  // 🆕 NUEVO: Método para diagnóstico de conexión
  public getConnectionStatus(): {
    connected: boolean;
    authenticated: boolean;
    hasToken: boolean;
    userId: string | null;
    reconnectionAttempts: number;
  } {
    return {
      connected: this.ddp.connected,
      authenticated: this.isAuthenticated,
      hasToken: !!this.currentToken,
      userId: this.getCurrentUserId(),
      reconnectionAttempts: this.reconnectionAttempts,
    };
  }

  // 🆕 NUEVO: Sistema de eventos para reactividad de autenticación
  public onAuthChange(callback: () => void): () => void {
    this.authChangeListeners.add(callback);

    // Retornar función de limpieza
    return () => {
      this.authChangeListeners.delete(callback);
    };
  }

  // 🆕 NUEVO: Notificar cambios de autenticación
  private notifyAuthChange(): void {
    this.authChangeListeners.forEach((callback) => {
      try {
        callback();
      } catch (error) {
        console.error("❌ Error en callback de auth change:", error);
      }
    });
  }
}

export default DDPService.getInstance();
