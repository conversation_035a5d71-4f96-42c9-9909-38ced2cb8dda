import ddpService from "./ddpService";
import type {
  Notification,
  NotificationResponse,
} from "../types/notifications";

class NotificationService {
  private static instance: NotificationService;
  private readonly COLLECTION_NAME = "notifications";
  private reactiveNotifications: any = null;

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  public async connect(): Promise<void> {
    await ddpService.connect();
  }

  public async subscribeToNotifications(limit: number = 20): Promise<boolean> {
    try {
      const subscription = ddpService.subscribe("userNotifications", limit);
      await subscription.ready();
      return true;
    } catch (error) {
      console.error("Error al suscribirse a las notificaciones:", error);
      throw error;
    }
  }

  public getNotifications(reactive: boolean = false): Notification[] {
    try {
      const collection = ddpService.collection(this.COLLECTION_NAME);

      if (reactive) {
        // Devolver una colección reactiva
        this.reactiveNotifications = collection.reactive();
        return this.reactiveNotifications;
      } else {
        // Devolver un snapshot no reactivo
        return collection.fetch();
      }
    } catch (error) {
      console.error("Error al obtener notificaciones:", error);
      return [];
    }
  }

  public onNotificationsChange(
    callback: (notifications: Notification[]) => void
  ): () => void {
    try {
      const collection = ddpService.collection(this.COLLECTION_NAME);
      const observer = collection.onChange(() => {
        callback(collection.fetch());
      });

      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
        }
      };
    } catch (error) {
      console.error("Error al observar cambios en notificaciones:", error);
      return () => {};
    }
  }

  public async getUnreadCount(): Promise<number> {
    try {
      return await ddpService.call("notifications.getUnreadCount");
    } catch (error) {
      console.error(
        "Error al obtener conteo de notificaciones no leídas:",
        error
      );
      return 0;
    }
  }

  public async markAsRead(notificationId: string): Promise<boolean> {
    try {
      return await ddpService.call("notifications.markAsRead", notificationId);
    } catch (error) {
      console.error("Error al marcar notificación como leída:", error);
      throw error;
    }
  }

  public async markAllAsRead(): Promise<boolean> {
    try {
      return await ddpService.call("notifications.markAllAsRead");
    } catch (error) {
      console.error(
        "Error al marcar todas las notificaciones como leídas:",
        error
      );
      throw error;
    }
  }

  public async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      return await ddpService.call("notifications.remove", notificationId);
    } catch (error) {
      console.error("Error al eliminar notificación:", error);
      throw error;
    }
  }

  public async generateExampleNotifications(
    count: number = 5
  ): Promise<NotificationResponse> {
    try {
      return await ddpService.call("notifications.generateDemo", count);
    } catch (error) {
      console.error("Error al generar notificaciones de ejemplo:", error);
      throw error;
    }
  }

  public async clearExampleNotifications(): Promise<NotificationResponse> {
    try {
      return await ddpService.call("notifications.clearDemo");
    } catch (error) {
      console.error("Error al limpiar notificaciones de ejemplo:", error);
      throw error;
    }
  }
}

export default NotificationService.getInstance();
