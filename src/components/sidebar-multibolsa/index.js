import { createApp } from "vue";
import SidebarMultibolsa from "./SidebarMultibolsa.vue";

function mountSidebarMultibolsa(elementSelector = "[data-sidebar-multibolsa]") {
  const targetElement = document.querySelector(elementSelector);
  if (!targetElement) {
    console.error(`No se encontró el elemento: ${elementSelector}`);
    return;
  }

  const props = {
    apiSociosEndpoint:
      targetElement.getAttribute("data-api-socios") || "/msi-v5/owner/socios",
    apiKpisEndpoint:
      targetElement.getAttribute("data-api-kpis") || "/api/club/kpis",
    apiFeedEndpoint:
      targetElement.getAttribute("data-api-feed") || "/api/club/feed",
  };

  const appContainer = document.createElement("div");
  targetElement.innerHTML = "";
  targetElement.appendChild(appContainer);

  const app = createApp(SidebarMultibolsa, props);
  app.mount(appContainer);
  return app;
}

const tryMount = () => {
  const el = document.querySelector("[data-sidebar-multibolsa]");
  if (el) {
    mountSidebarMultibolsa();
  }
};

if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", tryMount);
} else {
  tryMount();
}

window.SidebarMultibolsaMount = mountSidebarMultibolsa;

export default SidebarMultibolsa;
