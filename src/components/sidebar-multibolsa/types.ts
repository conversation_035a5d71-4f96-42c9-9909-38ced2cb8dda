export interface SidebarMultibolsaProps {
  apiSociosEndpoint?: string;
  apiKpisEndpoint?: string;
  apiFeedEndpoint?: string;
}

export interface PartnerRequest {
  contractId: string;
  name: string;
  company?: string;
  avatar?: string;
  email?: string;
  phone?: string;
  whatsapp?: string;
  location?: string;
  user?: string; // slug/usuario
  id?: string | number; // meteor_id u otro identificador
}

export interface SidebarKpis {
  totalProps?: number;
  totalPropsDelta?: number;
  myProps?: number;
  myPropsInBolsa?: number;
  buyerRequests?: number;
  buyerRequestsActive?: number;
  activePartners?: number;
  ampiPartners?: number;
}

export interface FeedItem {
  id: string | number;
  title: string;
  subtitle?: string;
  type?: "prop" | "buyer";
}
