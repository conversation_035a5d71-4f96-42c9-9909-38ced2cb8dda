import type { PartnerRequest, SidebarKpis, FeedItem } from "../types";

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export class SidebarMultibolsaService {
  static async fetchPendingRequests(
    endpoint: string
  ): Promise<PartnerRequest[]> {
    try {
      const url = endpoint.includes("?")
        ? `${endpoint}`
        : `${endpoint}?tipo=porAutorizar`;
      const response = await fetch(url, { credentials: "include" });
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();
      // Normalizar desde /owner/socios → puede venir como { data: { socios: [...] } } o { socios: [...] }
      const socios = (data?.socios || data?.data?.socios || []) as any[];
      const pendientes = socios.filter((s: any) => s.tipo === "porAutorizar");
      return pendientes.map((s: any) => ({
        contractId: String(s.contrato),
        name: String(s.nombre || "Socio"),
        company: s.empresa || undefined,
        avatar: s.avatar || undefined,
        email: s.email || undefined,
        phone: s.telefono || undefined,
        whatsapp: s.wa || undefined,
        location: s.ubicacion || undefined,
        user: s.usuario || undefined,
        id: s.id || undefined,
      }));
    } catch (error) {
      console.warn("Falling back to dummy pending requests:", error);
      await delay(200);
      return [];
    }
  }

  static async fetchKpis(endpoint: string): Promise<SidebarKpis> {
    try {
      const response = await fetch(endpoint, { credentials: "include" });
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();
      return {
        totalProps: data.totalProps ?? data.numprops ?? undefined,
        totalPropsDelta: data.totalPropsDelta ?? undefined,
        myProps: data.myProps ?? data.mis_props ?? undefined,
        myPropsInBolsa: data.myPropsInBolsa ?? undefined,
        buyerRequests: data.buyerRequests ?? data.numsol ?? undefined,
        buyerRequestsActive: data.buyerRequestsActive ?? undefined,
        activePartners: data.activePartners ?? data.numsocios ?? undefined,
        ampiPartners: data.ampiPartners ?? data.numSociosAmpi ?? undefined,
      };
    } catch (error) {
      console.warn("Falling back to dummy KPIs:", error);
      await delay(200);
      return {};
    }
  }

  static async fetchFeed(endpoint: string): Promise<FeedItem[]> {
    try {
      const response = await fetch(endpoint, { credentials: "include" });
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();
      return (data?.items || data || []).map((it: any, idx: number) => ({
        id: it.id ?? idx,
        title: String(it.title ?? it.titulo ?? "Nueva actividad"),
        subtitle: it.subtitle ?? it.descripcion ?? undefined,
        type: it.type ?? undefined,
      }));
    } catch (error) {
      console.warn("Falling back to empty feed:", error);
      await delay(200);
      return [];
    }
  }

  static async acceptRequest(contractId: string): Promise<void> {
    const url = `/msi-v5/owner/socios/${encodeURIComponent(
      contractId
    )}/autorizar`;
    const response = await fetch(url, {
      method: "POST",
      credentials: "include",
      headers: { "Content-Type": "application/json" },
    });
    if (!response.ok) {
      const text = await response.text().catch(() => "");
      throw new Error(`Error autorizando socio (${response.status}): ${text}`);
    }
  }

  static async rejectRequest(contractId: string): Promise<void> {
    const url = `/msi-v5/owner/socios/${encodeURIComponent(
      contractId
    )}/rechazar`;
    const response = await fetch(url, {
      method: "POST",
      credentials: "include",
      headers: { "Content-Type": "application/json" },
    });
    if (!response.ok) {
      const text = await response.text().catch(() => "");
      throw new Error(`Error rechazando socio (${response.status}): ${text}`);
    }
  }
}
