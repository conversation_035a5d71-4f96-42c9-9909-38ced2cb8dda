<template>
  <div class="space-y-5">
    <!-- Solicitudes de sociedad pendientes -->
    <section class="bg-white rounded-xl border border-gray-200 shadow-sm">
      <header class="flex justify-between items-center px-4 py-3">
        <div class="flex gap-2 items-center">
          <div
            class="flex justify-center items-center w-8 h-8 rounded-full bg-mulbin-100 text-mulbin-700"
          >
            <ion-icon name="person-add-outline" class="text-lg"></ion-icon>
          </div>
          <h3 class="text-sm font-semibold text-gray-800">
            Solicitudes de sociedad
          </h3>
        </div>
        <span
          v-if="pendingRequests.length > 0"
          class="px-2 py-0.5 text-xs font-medium text-white bg-red-600 rounded-full"
        >
          {{ pendingRequests.length }}
        </span>
      </header>
      <div class="px-3 pb-3">
        <div
          v-if="pendingRequests.length === 0"
          class="flex gap-3 items-center p-3 text-xs text-gray-600 bg-gray-50 rounded-lg border border-gray-200 border-dashed"
        >
          <ion-icon
            name="sparkles-outline"
            class="text-base text-gray-400"
          ></ion-icon>
          No tienes solicitudes pendientes por ahora.
        </div>

        <ul v-else class="divide-y divide-gray-100">
          <li
            v-for="req in pendingRequests"
            :key="req.contractId"
            class="py-3 border-t border-gray-100"
          >
            <!-- Contenedor principal -->
            <div class="flex gap-3 items-center">
              <!-- Avatar -->
              <div class="relative shrink-0">
                <img
                  :src="
                    req.avatar || 'https://assets.mulbin.com/images/avatar.png'
                  "
                  alt="avatar"
                  class="object-cover w-11 h-11 rounded-full border"
                  @error="handleAvatarError"
                />
                <button
                  type="button"
                  class="flex absolute -right-1 -bottom-1 justify-center items-center w-5 h-5 text-white rounded-full ring-2 ring-white shadow bg-mulbin-700 hover:bg-mulbin-800"
                  title="Opciones de contacto"
                  @click.stop="toggleContactMenu(req.contractId)"
                >
                  <ion-icon name="chevron-down" class="text-[12px]"></ion-icon>
                </button>
                <div
                  v-if="openContactFor === req.contractId"
                  data-contact-menu-container
                  class="absolute -left-1 z-10 p-1.5 mt-1 w-36 bg-white rounded-lg border border-gray-200 shadow-lg origin-top-left"
                  @click.stop
                >
                  <a
                    v-if="req.email"
                    :href="`mailto:${req.email}`"
                    class="flex items-center gap-2 rounded-md px-2 py-1 text-[12px] text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon
                      name="mail-outline"
                      class="text-sm text-gray-500"
                    ></ion-icon>
                    Email
                  </a>
                  <a
                    v-if="req.phone"
                    :href="`tel:${req.phone}`"
                    class="flex items-center gap-2 rounded-md px-2 py-1 text-[12px] text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon
                      name="call-outline"
                      class="text-sm text-gray-500"
                    ></ion-icon>
                    Llamar
                  </a>
                  <a
                    v-if="req.whatsapp"
                    :href="`https://wa.me/${req.whatsapp}`"
                    target="_blank"
                    rel="noopener"
                    class="flex items-center gap-2 rounded-md px-2 py-1 text-[12px] text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon
                      name="logo-whatsapp"
                      class="text-sm text-gray-500"
                    ></ion-icon>
                    WhatsApp
                  </a>
                </div>
              </div>
              <!-- Datos del solicitante -->
              <div class="flex-1 min-w-0">
                <!-- Nombre -->
                <p class="text-sm font-semibold text-gray-900 truncate">
                  {{ req.name }}
                </p>
                <!-- Empresa -->
                <p v-if="req.company" class="text-xs text-gray-600 truncate">
                  {{ req.company }}
                </p>
                <!-- Ubicación -->
                <p class="truncate text-[11px] text-gray-500">
                  {{ req.location || "—" }}
                </p>
              </div>
            </div>
            <!-- Botones de aceptar/rechazar -->
            <div class="flex gap-2 mt-2">
              <button
                type="button"
                class="inline-flex gap-1 items-center px-3 py-1.5 text-xs font-semibold text-white bg-emerald-600 rounded-full shadow-sm hover:bg-emerald-700"
                @click="handleAccept(req)"
              >
                <ion-icon
                  name="checkmark-outline"
                  class="text-[14px]"
                ></ion-icon>
                Aceptar
              </button>
              <button
                type="button"
                class="inline-flex gap-1 items-center px-3 py-1.5 text-xs font-semibold text-rose-700 bg-rose-50 rounded-full ring-1 ring-inset ring-rose-200 hover:bg-rose-100"
                @click="handleReject(req)"
              >
                <ion-icon name="close-outline" class="text-[14px]"></ion-icon>
                Rechazar
              </button>
            </div>
          </li>
        </ul>
      </div>
    </section>

    <!-- KPIs de la Multibolsa -->
    <section class="space-y-3">
      <div class="grid grid-cols-2 gap-3">
        <div class="p-3 bg-white rounded-xl border border-gray-200 shadow-sm">
          <div class="flex justify-between items-center">
            <span class="text-[11px] font-medium text-gray-500"
              >Inmuebles en Bolsa</span
            >
            <ion-icon
              name="home-outline"
              class="text-base text-gray-400"
            ></ion-icon>
          </div>
          <div class="mt-1 text-xl font-semibold text-gray-900">
            {{ kpis.totalProps ?? "—" }}
          </div>
          <div class="mt-0.5 text-[11px] text-gray-500">
            Últimas 24h: <span>{{ kpis.totalPropsDelta ?? "—" }}</span>
          </div>
        </div>
        <div class="p-3 bg-white rounded-xl border border-gray-200 shadow-sm">
          <div class="flex justify-between items-center">
            <span class="text-[11px] font-medium text-gray-500"
              >Mis inmuebles</span
            >
            <ion-icon
              name="business-outline"
              class="text-base text-gray-400"
            ></ion-icon>
          </div>
          <div class="mt-1 text-xl font-semibold text-gray-900">
            {{ kpis.myProps ?? "—" }}
          </div>
          <div class="mt-0.5 text-[11px] text-gray-500">
            En bolsa: <span>{{ kpis.myPropsInBolsa ?? "—" }}</span>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-2 gap-3">
        <div class="p-3 bg-white rounded-xl border border-gray-200 shadow-sm">
          <div class="flex justify-between items-center">
            <span class="text-[11px] font-medium text-gray-500"
              >Solicitudes compradores</span
            >
            <ion-icon
              name="people-outline"
              class="text-base text-gray-400"
            ></ion-icon>
          </div>
          <div class="mt-1 text-xl font-semibold text-gray-900">
            {{ kpis.buyerRequests ?? "—" }}
          </div>
          <div class="mt-0.5 text-[11px] text-gray-500">
            Vigentes: <span>{{ kpis.buyerRequestsActive ?? "—" }}</span>
          </div>
        </div>
        <div class="p-3 bg-white rounded-xl border border-gray-200 shadow-sm">
          <div class="flex justify-between items-center">
            <span class="text-[11px] font-medium text-gray-500"
              >Socios activos</span
            >
            <ion-icon
              name="sparkles-outline"
              class="text-base text-gray-400"
            ></ion-icon>
          </div>
          <div class="mt-1 text-xl font-semibold text-gray-900">
            {{ kpis.activePartners ?? "—" }}
          </div>
        </div>
      </div>
    </section>

    <!-- Últimas novedades (props y solicitudes) -->
    <section class="bg-white rounded-xl border border-gray-200 shadow-sm">
      <header class="flex justify-between items-center px-4 py-3">
        <div class="flex gap-2 items-center">
          <div
            class="flex justify-center items-center w-8 h-8 text-indigo-600 bg-indigo-50 rounded-full"
          >
            <ion-icon name="trending-up-outline" class="text-lg"></ion-icon>
          </div>
          <h3 class="text-sm font-semibold text-gray-800">Últimas novedades</h3>
        </div>
        <a
          href="/"
          class="text-xs font-medium text-mulbin-700 hover:text-mulbin-800"
          >Ver todo</a
        >
      </header>
      <div class="px-4 pb-3">
        <ul class="space-y-3">
          <li
            v-if="loadingFeed"
            class="p-3 bg-gray-50 rounded-lg border border-gray-100 animate-pulse"
          >
            <div class="w-4/5 h-3 bg-gray-200 rounded"></div>
            <div class="mt-2 w-2/5 h-3 bg-gray-200 rounded"></div>
          </li>
          <li
            v-for="item in feed"
            :key="item.id"
            class="p-3 bg-gray-50 rounded-lg border border-gray-100"
          >
            <p class="text-sm font-medium text-gray-800">{{ item.title }}</p>
            <p class="mt-1 text-xs text-gray-500">{{ item.subtitle }}</p>
          </li>
          <li
            v-if="!loadingFeed && feed.length === 0"
            class="p-3 text-xs text-gray-500 bg-gray-50 rounded-lg border border-gray-100"
          >
            Sin novedades recientes.
          </li>
        </ul>
      </div>
    </section>

    <!-- CTA: invitar colega -->
    <section
      class="p-4 bg-gradient-to-br to-white rounded-xl border border-gray-200 shadow-sm from-mulbin-50"
    >
      <div class="flex gap-3 items-start">
        <div
          class="flex justify-center items-center w-9 h-9 rounded-full bg-mulbin-100 text-mulbin-700"
        >
          <ion-icon name="add-circle-outline" class="text-xl"></ion-icon>
        </div>
        <div class="min-w-0">
          <p class="text-sm font-semibold text-mulbin-900">
            Invita a un colega
          </p>
          <p class="mt-0.5 text-xs text-mulbin-700/80">
            Crea sociedad para compartir comisión en la Multibolsa.
          </p>
          <div class="mt-2">
            <button
              type="button"
              class="inline-flex gap-1 items-center px-3 py-1.5 text-xs font-medium text-white rounded-lg bg-mulbin-600 hover:bg-mulbin-700"
              @click="showAddFriendModal = true"
            >
              Invitar ahora
              <ion-icon
                name="arrow-forward-outline"
                class="text-[13px]"
              ></ion-icon>
            </button>
          </div>
        </div>
      </div>
    </section>
    <!-- Modal de confirmación para rechazar solicitud -->
    <ConfirmacionSocioModal
      v-model="showConfirmModal"
      :socio="selectedSocio"
      accion="rechazar"
      @confirmar="confirmReject"
    />
    <!-- Modal para invitar socio -->
    <AgregarSocioModal
      v-model="showAddFriendModal"
      @socio-added="handleSocioAdded"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { PartnerRequest, SidebarKpis, FeedItem } from "./types";
import { SidebarMultibolsaService } from "./services/sidebarMultibolsaService";
import ConfirmacionSocioModal from "@/components/muro-inmobiliario-social/ConfirmacionSocioModal.vue";
import AgregarSocioModal from "@/components/muro-inmobiliario-social/AgregarSocioModal.vue";

const props = withDefaults(
  defineProps<{
    apiSociosEndpoint?: string;
    apiKpisEndpoint?: string;
    apiFeedEndpoint?: string;
  }>(),
  {
    apiSociosEndpoint: "/msi-v5/owner/socios",
    apiKpisEndpoint: "/api/club/kpis",
    apiFeedEndpoint: "/api/club/feed",
  }
);

const pendingRequests = ref<PartnerRequest[]>([]);
const kpis = ref<SidebarKpis>({});
const feed = ref<FeedItem[]>([]);
const loadingFeed = ref<boolean>(true);
const openContactFor = ref<string | null>(null);
const showConfirmModal = ref<boolean>(false);
const selectedSocio = ref<any | null>(null);
const selectedContractId = ref<string>("");
const showAddFriendModal = ref<boolean>(false);

const loadData = async () => {
  try {
    const [reqs, kpiData] = await Promise.all([
      SidebarMultibolsaService.fetchPendingRequests(props.apiSociosEndpoint),
      SidebarMultibolsaService.fetchKpis(props.apiKpisEndpoint),
    ]);
    pendingRequests.value = reqs;
    kpis.value = kpiData;
  } catch (error) {
    console.error("Error cargando datos del sidebar:", error);
  }
};

// Handler para cuando se agrega/solicita un socio desde el modal
const handleSocioAdded = (_persona: any) => {
  showAddFriendModal.value = false;
};

const loadFeed = async () => {
  loadingFeed.value = true;
  try {
    feed.value = await SidebarMultibolsaService.fetchFeed(
      props.apiFeedEndpoint
    );
  } catch (error) {
    console.error("Error cargando feed:", error);
    feed.value = [];
  } finally {
    loadingFeed.value = false;
  }
};

const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement | null;
  if (img) img.src = "https://placehold.co/40x40?text=%20";
};

const toggleContactMenu = (contractId: string) => {
  openContactFor.value =
    openContactFor.value === contractId ? null : contractId;
};

const handleAccept = async (req: PartnerRequest) => {
  try {
    await SidebarMultibolsaService.acceptRequest(req.contractId);
    pendingRequests.value = pendingRequests.value.filter(
      (r) => r.contractId !== req.contractId
    );
  } catch (error) {
    console.error("Error al aceptar solicitud:", error);
  }
};

const handleReject = (req: PartnerRequest) => {
  selectedSocio.value = {
    id: req.contractId,
    nombre: req.name,
    empresa: req.company || "",
    ubicacion: req.location || "",
    avatar: req.avatar || "",
    email: req.email || "",
    telefono: req.phone || "",
    tipo: "porAutorizar",
  };
  selectedContractId.value = req.contractId;
  showConfirmModal.value = true;
};

const confirmReject = async () => {
  if (!selectedContractId.value) return;
  const contratoId = selectedContractId.value;
  try {
    await SidebarMultibolsaService.rejectRequest(contratoId);
    pendingRequests.value = pendingRequests.value.filter(
      (r) => r.contractId !== contratoId
    );
  } catch (error) {
    console.error("Error al rechazar solicitud:", error);
  } finally {
    showConfirmModal.value = false;
    selectedSocio.value = null;
    selectedContractId.value = "";
  }
};

onMounted(async () => {
  await Promise.all([loadData(), loadFeed()]);
  document.addEventListener("click", (e) => {
    const target = e.target as HTMLElement | null;
    if (!target) return;
    const withinMenu = target.closest("[data-contact-menu-container]");
    const isToggleBtn =
      target.closest("button") &&
      (target.closest("button") as HTMLElement).title ===
        "Opciones de contacto";
    if (!withinMenu && !isToggleBtn) {
      openContactFor.value = null;
    }
  });
});
</script>

<style scoped></style>
