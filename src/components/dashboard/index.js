import { createApp } from "vue";
import Alpine from "alpinejs";
import DashboardStats from "./DashboardStats.vue";

// Inicializar Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Inicializar Vue cuando el DOM esté cargado
document.addEventListener("DOMContentLoaded", () => {
  const vueContainer = document.getElementById("dashboard");

  if (vueContainer) {
    const app = createApp(DashboardStats);
    app.mount("#dashboard");

    console.log("Dashboard initialized");
  } else {
    console.warn("Dashboard not found");
  }

  // Aquí puedes mantener cualquier otro código de inicialización que necesites
});
