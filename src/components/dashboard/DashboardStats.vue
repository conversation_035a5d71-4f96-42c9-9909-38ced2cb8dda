<template>
  <div class="p-4 bg-white rounded-lg shadow-md">
    <h2 class="text-xl font-bold mb-4 text-gray-800">
      Estadísticas del sistema
    </h2>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Tarjeta de inmuebles -->
      <div class="p-4 border rounded-lg bg-blue-50 border-blue-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-blue-700">
            <ion-icon name="home-outline" class="mr-1"></ion-icon>
            Total inmuebles
          </h3>
          <span class="px-3 py-1 text-white bg-blue-600 rounded-full">{{
            stats.inmuebles
          }}</span>
        </div>
        <p class="mt-2 text-sm text-blue-600">
          {{
            stats.inmuebles > 0
              ? `${stats.inmuebles_activos} activos y ${
                  stats.inmuebles - stats.inmuebles_activos
                } inactivos`
              : "Registra tu primer inmueble"
          }}
        </p>
      </div>

      <!-- Tarjeta de citas -->
      <div class="p-4 border rounded-lg bg-red-50 border-red-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-red-700">
            <ion-icon name="calendar-outline" class="mr-1"></ion-icon>
            Citas pendientes
          </h3>
          <span class="px-3 py-1 text-white bg-red-600 rounded-full">{{
            stats.citas
          }}</span>
        </div>
        <p class="mt-2 text-sm text-red-600">
          {{
            stats.citas > 0
              ? `Tienes ${stats.citas} citas por atender`
              : "No tienes citas pendientes"
          }}
        </p>
      </div>

      <!-- Tarjeta de consultas -->
      <div class="p-4 border rounded-lg bg-amber-50 border-amber-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-amber-700">
            <ion-icon name="analytics-outline" class="mr-1"></ion-icon>
            Actividad reciente
          </h3>
          <span class="px-3 py-1 text-white bg-amber-600 rounded-full">{{
            stats.visitas
          }}</span>
        </div>
        <p class="mt-2 text-sm text-amber-600">
          Visitas a tus inmuebles en los últimos 7 días
        </p>
      </div>
    </div>

    <div class="mt-6">
      <button
        @click="refreshStats"
        class="px-4 py-2 text-white bg-indigo-600 rounded-md hover:bg-indigo-700 flex items-center"
      >
        <span v-if="loading" class="mr-2">
          <ion-icon
            name="refresh-outline"
            class="animate-spin h-5 w-5"
          ></ion-icon>
        </span>
        <span v-else class="mr-2">
          <ion-icon name="refresh-outline"></ion-icon>
        </span>
        {{ loading ? "Actualizando..." : "Actualizar estadísticas" }}
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from "vue";

export default defineComponent({
  name: "DashboardStats",

  setup() {
    const loading = ref(false);
    const stats = ref({
      inmuebles: 0,
      inmuebles_activos: 0,
      citas: 0,
      visitas: 0,
    });

    // Simulación de carga de datos (en un entorno real, esto sería una API)
    const fetchStats = () => {
      loading.value = true;

      // Simular un retraso para mostrar el spinner
      setTimeout(() => {
        // Usaríamos fetch/axios aquí en un caso real
        stats.value = {
          inmuebles: Math.floor(Math.random() * 50) + 250, // Valor aleatorio entre 250-300
          inmuebles_activos: Math.floor(Math.random() * 30) + 220, // Valor aleatorio menor que inmuebles
          citas: Math.floor(Math.random() * 20),
          visitas: Math.floor(Math.random() * 100) + 50,
        };

        loading.value = false;
      }, 800);
    };

    const refreshStats = () => {
      fetchStats();
    };

    onMounted(() => {
      fetchStats();
    });

    return {
      stats,
      loading,
      refreshStats,
    };
  },
});
</script>
