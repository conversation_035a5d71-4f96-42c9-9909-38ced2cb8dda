# 🔔 Guía de Integración Completa - Sistema de Notificaciones

> **Frontend + Backend + REST API** - Integración completa con código limpio y elegante

## 📋 Tabla de Contenidos

- [🎯 Visión General](#-visión-general)
- [🚀 Setup Completo](#-setup-completo)
- [🔄 Flujo de Integración](#-flujo-de-integración)
- [💡 Ejemplos Prácticos](#-ejemplos-prácticos)
- [🐛 Troubleshooting](#-troubleshooting)
- [📚 Referencias](#-referencias)

---

## 🎯 Visión General

### Arquitectura Completa

```mermaid
graph TB
    subgraph "Frontend (Vue.js)"
        A[Notificaciones.vue]
        B[notificationService.ts]
        C[index.js - Auto-init]
    end
    
    subgraph "Backend (Meteor)"
        D[DDP Publications]
        E[DDP Methods]
        F[REST API]
        G[MongoDB Collection]
    end
    
    subgraph "Integraciones"
        H[Apps Móviles]
        I[Webhooks]
        J[Scripts]
    end
    
    A <--> B
    B <--> D
    B <--> E
    D <--> G
    E <--> G
    F <--> G
    H <--> F
    I <--> F
    J <--> F
    
    style A fill:#42b883
    style D fill:#de4f4f
    style F fill:#4CAF50
    style G fill:#FF9800
```

### Cuándo Usar Cada Tecnología

| Tecnología | Cuándo Usar | Ejemplo |
|------------|-------------|---------|
| **DDP Frontend** | Aplicaciones web reactivas | Aplicación Vue.js principal |
| **DDP Backend** | Sistema principal reactivo | Publications y Methods de Meteor |
| **REST API** | Integraciones externas | Apps móviles, webhooks, scripts |

---

## 🚀 Setup Completo

### 1. **Backend Meteor - DDP + REST**

#### Instalar Dependencias
```bash
cd MulbinComponents/app
meteor npm install
```

#### Configurar Colección
```javascript
// imports/api/notifications/collection.js
import { Mongo } from "meteor/mongo";

export const Notifications = new Mongo.Collection("notifications");

// Índices de performance
if (Meteor.isServer) {
  Meteor.startup(() => {
    Notifications.createIndex({ userId: 1, createdAt: -1 });
    Notifications.createIndex({ userId: 1, read: 1 });
  });
}
```

#### Publications DDP
```javascript
// imports/api/notifications/publications.js
Meteor.publish('userNotifications', function(limit = 20) {
  if (!this.userId) return this.ready();
  
  return Notifications.find(
    { userId: this.userId },
    { sort: { createdAt: -1 }, limit }
  );
});
```

#### Methods DDP
```javascript
// imports/api/notifications/methods.js
Meteor.methods({
  'notifications.markAsRead'(notificationId) {
    check(notificationId, String);
    if (!this.userId) throw new Meteor.Error('not-authorized');
    
    return Notifications.updateAsync(
      { _id: notificationId, userId: this.userId },
      { $set: { read: true, readAt: new Date() } }
    );
  }
});
```

### 2. **Frontend Vue.js - DDP Reactivo**

#### Instalar Dependencias
```bash
cd panel4-templates
npm install
```

#### Servicio DDP Limpio
```typescript
// src/services/notificationService.ts
class NotificationService {
  async connect(): Promise<void> {
    await ddpService.connect();
  }

  async subscribeToNotifications(limit: number = 20): Promise<boolean> {
    const subscription = ddpService.subscribe("userNotifications", limit);
    await subscription.ready();
    return true;
  }

  onNotificationsChange(callback: (notifications: Notification[]) => void): () => void {
    const collection = ddpService.collection("notifications");
    const observer = collection.onChange(() => {
      callback(collection.fetch());
    });
    return () => observer.stop();
  }

  async markAsRead(notificationId: string): Promise<boolean> {
    return await ddpService.call("notifications.markAsRead", notificationId);
  }
}
```

#### Componente Vue Limpio
```vue
<!-- src/components/notifications/Notificaciones.vue -->
<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import notificationService from '../../services/notificationService';

const notifications = ref([]);
const unreadCount = ref(0);
let unsubscribe = null;

onMounted(async () => {
  // 1️⃣ Conectar DDP
  await notificationService.connect();
  
  // 2️⃣ Suscribirse
  await notificationService.subscribeToNotifications(20);
  
  // 3️⃣ Observador único
  unsubscribe = notificationService.onNotificationsChange((newNotifications) => {
    notifications.value = newNotifications;
    unreadCount.value = newNotifications.filter(n => !n.read).length;
  });
  
  // 4️⃣ Estado inicial
  const initial = notificationService.getNotifications();
  notifications.value = initial;
  unreadCount.value = initial.filter(n => !n.read).length;
});

onUnmounted(() => {
  if (unsubscribe) unsubscribe();
});

const markAsRead = async (id) => {
  await notificationService.markAsRead(id);
  // DDP actualiza automáticamente
};
</script>
```

### 3. **Auto-inicialización HTML**

```html
<!-- panel.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Mi Aplicación</title>
</head>
<body>
    <!-- Token de autenticación -->
    <div data-token="user-auth-token" style="display: none;"></div>
    
    <!-- Contenedor auto-inicialización -->
    <div data-notificaciones></div>
    
    <!-- Script del sistema -->
    <script src="/dist/assets/notificaciones.js"></script>
</body>
</html>
```

---

## 🔄 Flujo de Integración

### Flujo Completo de una Notificación

```mermaid
sequenceDiagram
    participant U as Usuario
    participant F as Frontend Vue
    participant D as DDP Server
    participant M as MongoDB
    participant R as REST API
    participant E as App Externa

    Note over U,E: 1. CONFIGURACIÓN INICIAL
    F->>D: connect() + subscribe('userNotifications')
    D->>M: Query inicial
    M->>D: Datos existentes
    D->>F: Sincronización inicial
    F->>F: Configurar observer reactivo

    Note over U,E: 2. CREACIÓN DE NOTIFICACIÓN
    E->>R: POST /api/notifications (nueva notificación)
    R->>M: Insert documento
    M->>D: Trigger change event
    D->>F: DDP delta (nueva notificación)
    F->>F: Observer actualiza UI automáticamente
    F->>U: UI muestra nueva notificación

    Note over U,E: 3. MARCAR COMO LEÍDA
    U->>F: Click en notificación
    F->>D: call('notifications.markAsRead')
    D->>M: Update documento
    M->>D: Trigger change event
    D->>F: DDP delta (notificación actualizada)
    F->>F: Observer actualiza UI automáticamente
    F->>U: UI refleja estado leído
```

### Integración con Aplicación Existente

#### **Paso 1: Configurar Backend**
```javascript
// server/main.js
import '../imports/api/notifications';

Meteor.startup(() => {
  console.log('🔔 Sistema de notificaciones iniciado');
});
```

#### **Paso 2: Integrar Frontend**
```javascript
// Tu aplicación Vue existente
import { createApp } from 'vue';
import App from './App.vue';
import './components/notifications/index.js'; // Auto-inicialización

const app = createApp(App);
app.mount('#app');
```

#### **Paso 3: Configurar Autenticación**
```javascript
// Configurar token globalmente
window.addEventListener('DOMContentLoaded', () => {
  const token = getUserToken(); // Tu función existente
  document.querySelector('[data-token]')?.setAttribute('data-token', token);
});
```

---

## 💡 Ejemplos Prácticos

### Ejemplo 1: Notificación al Crear Post

```javascript
// Backend - En el método de creación de posts
Meteor.methods({
  'posts.create'(postData) {
    const postId = Posts.insert(postData);
    
    // Notificar a seguidores
    const followers = getFollowers(this.userId);
    followers.forEach(follower => {
      // 🚀 DDP automáticamente propaga a clientes suscritos
      Notifications.insert({
        userId: follower.userId,
        title: '🏠 Nueva propiedad',
        message: `${postData.title} - ${postData.location}`,
        type: 'info',
        source: 'post',
        relatedId: postId,
        actionUrl: `/posts/${postId}`,
        read: false,
        createdAt: new Date()
      });
    });
    
    return postId;
  }
});
```

```vue
<!-- Frontend - Se actualiza automáticamente -->
<template>
  <div class="notification" v-for="notification in notifications" :key="notification._id">
    <h4>{{ notification.title }}</h4>
    <p>{{ notification.message }}</p>
    <!-- DDP actualiza esta lista automáticamente cuando se crea el post -->
  </div>
</template>
```

### Ejemplo 2: Webhook Externo + DDP

```javascript
// Webhook externo (Express.js)
app.post('/webhook/payment', async (req, res) => {
  const { userId, amount } = req.body;
  
  // Crear notificación via REST API
  await fetch('https://api.miapp.com/api/notifications', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${API_TOKEN}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      userId: userId,
      title: '💰 Pago recibido',
      message: `Se ha recibido un pago de €${amount}`,
      type: 'success',
      source: 'payment'
    })
  });
  
  res.json({ success: true });
});

// Frontend Vue - Se actualiza automáticamente via DDP
// No necesita cambios, el observer detecta la nueva notificación automáticamente
```

### Ejemplo 3: App Móvil + Web Sincronizadas

```javascript
// App móvil (React Native) usa REST
const markAsReadMobile = async (notificationId) => {
  await fetch(`${API_URL}/api/notifications/${notificationId}/read`, {
    method: 'PUT',
    headers: { 'Authorization': `Bearer ${userToken}` }
  });
};

// Web app (Vue.js) usa DDP
const markAsReadWeb = async (notificationId) => {
  await notificationService.markAsRead(notificationId);
  // DDP actualiza automáticamente
};

// Resultado: Ambas apps se mantienen sincronizadas
// - Móvil actualiza via REST
// - Web recibe actualización via DDP automáticamente
// - Usuario ve cambios en tiempo real en ambas
```

### Ejemplo 4: Sistema de Recordatorios

```javascript
// Backend - Job programado
SyncedCron.add({
  name: 'Recordatorios de citas',
  schedule: parser => parser.text('every 30 minutes'),
  job: function() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const appointments = Appointments.find({
      date: { $gte: tomorrow, $lt: new Date(tomorrow.getTime() + 24*60*60*1000) },
      reminderSent: { $ne: true }
    }).fetch();
    
    appointments.forEach(appointment => {
      // 🚀 DDP propaga automáticamente a clientes
      Notifications.insert({
        userId: appointment.userId,
        title: '⏰ Recordatorio de cita',
        message: `Tienes una cita mañana a las ${appointment.time}`,
        type: 'warning',
        source: 'reminder',
        relatedId: appointment._id,
        actionUrl: `/appointments/${appointment._id}`,
        read: false,
        createdAt: new Date()
      });
      
      Appointments.update(appointment._id, { $set: { reminderSent: true } });
    });
  }
});

// Frontend - Usuario ve recordatorios automáticamente
// Sin código adicional necesario, DDP maneja todo
```

---

## 🐛 Troubleshooting

### Problemas Comunes y Soluciones

#### **🔴 Problema: Notificaciones no aparecen**

```javascript
// ✅ Verificar conexión DDP
console.log('DDP conectado:', window.ddpService?.isConnected());

// ✅ Verificar suscripción
console.log('Suscripciones activas:', window.ddpService?.getSubscriptions());

// ✅ Verificar token
const token = document.querySelector('[data-token]')?.getAttribute('data-token');
console.log('Token encontrado:', !!token);

// ✅ Verificar datos en MongoDB
// En consola del servidor Meteor
Notifications.find({userId: "user123"}).fetch();
```

#### **🔴 Problema: Notificaciones no se actualizan**

```javascript
// ❌ CAUSA COMÚN: Múltiples observadores
// Verificar que solo hay un observador configurado

// ✅ SOLUCIÓN: Un solo observador en onMounted
onMounted(async () => {
  if (!unsubscribe) { // Verificar que no existe ya
    unsubscribe = notificationService.onNotificationsChange(callback);
  }
});
```

#### **🔴 Problema: Error de autenticación REST**

```bash
# ✅ Verificar token válido
curl -H "Authorization: Bearer $TOKEN" \
     "https://api.miapp.com/api/auth/verify"

# ✅ Respuesta esperada:
# {"valid": true, "userId": "user123", "permissions": ["read", "write"]}
```

#### **🔴 Problema: Performance lenta**

```javascript
// ✅ Verificar índices en MongoDB
db.notifications.getIndexes()

// ✅ Verificar límites de suscripción
// No suscribirse a demasiadas notificaciones
await notificationService.subscribeToNotifications(20); // No más de 50-100
```

### Logs de Debug

```javascript
// Habilitar logs detallados en desarrollo
if (import.meta.env.DEV) {
  // Frontend logs
  window.debugNotifications = true;
  
  // Backend logs
  Meteor.settings.public.debug = true;
}
```

---

## 📚 Referencias

### Documentación Técnica

- [📖 Frontend DDP Docs](./FRONTEND-DDP-DOCS.md) - Implementación Vue.js
- [📖 Backend DDP Docs](../../../MulbinComponents/app/imports/api/notifications/BACKEND-DDP-DOCS.md) - Meteor DDP
- [📖 Backend REST Docs](../../../MulbinComponents/app/imports/api/rest/BACKEND-REST-DOCS.md) - API REST
- [📖 Guía Reactividad DDP](../METEOR-DDP-REACTIVIDAD-GUIA.md) - Filosofía Meteor

### Recursos de Aprendizaje

- [Meteor Guide](https://guide.meteor.com/) - Documentación oficial
- [Vue.js 3](https://vuejs.org/) - Framework frontend
- [MongoDB](https://docs.mongodb.com/) - Base de datos

### Herramientas de Desarrollo

- [Meteor DevTools](https://github.com/leonardoventurini/meteor-devtools-evolved) - Debug DDP
- [Vue DevTools](https://devtools.vuejs.org/) - Debug Vue.js
- [MongoDB Compass](https://www.mongodb.com/products/compass) - Explorar datos
- [Postman](https://www.postman.com/) - Testing REST API

---

## 🎯 Checklist de Integración

### ✅ Backend Setup

- [ ] Colección creada con índices
- [ ] Publications DDP configuradas
- [ ] Methods DDP implementados  
- [ ] REST API endpoints activos
- [ ] Autenticación configurada

### ✅ Frontend Setup

- [ ] Servicio DDP conectando correctamente
- [ ] Componente Vue con observador único
- [ ] Auto-inicialización funcionando
- [ ] Token de autenticación configurado
- [ ] UI respondiendo a cambios DDP

### ✅ Integración Completa

- [ ] Frontend recibe notificaciones en tiempo real
- [ ] REST API funciona para apps externas
- [ ] Móvil y web sincronizados
- [ ] Performance optimizada
- [ ] Errores manejados correctamente

### 🚀 **¡Sistema Listo!**

Con esta integración completa tienes:

- ✅ **Frontend reactivo** con Vue.js + DDP
- ✅ **Backend escalable** con Meteor + MongoDB  
- ✅ **REST API completa** para integraciones
- ✅ **Código limpio** sin antipatrones
- ✅ **Documentación completa** para equipos

**¡Tu sistema de notificaciones está listo para producción!** 🎉 