// Este archivo inicializa el componente de notificaciones y lo monta en el DOM
import { createApp } from "vue";
import Notificaciones from "./Notificaciones.vue";
import notificationService from "../../services/notificationService";
import ddpService from "../../services/ddpService";

// Función para montar el componente en un elemento específico
function mountNotificacionesComponent(
  elementSelector = "#notificaciones-container"
) {
  const targetElement = document.querySelector(elementSelector);

  if (!targetElement) {
    console.error(
      `No se encontró el elemento con selector: ${elementSelector}`
    );
    return;
  }

  // Crear un div para contener el componente
  const appContainer = document.createElement("div");
  targetElement.appendChild(appContainer);

  // Montar la aplicación Vue
  const app = createApp(Notificaciones);

  // Proporcionar el servicio de notificaciones
  app.provide("notificationService", notificationService);
  app.provide("ddpService", ddpService);

  app.mount(appContainer);

  console.log("Componente de notificaciones inicializado correctamente");

  return app;
}

// Configurar evento personalizado para notificar cambios en el sidebar
function setupSidebarToggleDetection() {
  // Buscar el botón del sidebar
  const sidebarToggleButton = document.querySelector("#sidebar-toggle");

  if (sidebarToggleButton) {
    // Escuchar el clic en el botón del sidebar
    sidebarToggleButton.addEventListener("click", () => {
      // Emitir evento personalizado
      const event = new CustomEvent("sidebarToggle", {
        detail: { timestamp: Date.now() },
      });
      document.dispatchEvent(event);
    });
  }

  // También observar cambios en clases del sidebar para detectar cuando se oculta o muestra
  const sidebar =
    document.querySelector(".sidebar") || document.querySelector("#sidebar");

  if (sidebar && window.MutationObserver) {
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (
          mutation.attributeName === "class" ||
          mutation.attributeName === "style"
        ) {
          // Emitir evento personalizado
          const event = new CustomEvent("sidebarToggle", {
            detail: { timestamp: Date.now() },
          });
          document.dispatchEvent(event);
          break;
        }
      }
    });

    // Observar cambios en atributos class y style
    observer.observe(sidebar, {
      attributes: true,
      attributeFilter: ["class", "style"],
    });
  }
}

// 🚀 INICIALIZACIÓN REACTIVA - FILOSOFÍA METEOR DDP
async function initializeNotificationService() {
  try {
    // 🔧 OBTENER TOKEN DE AUTENTICACIÓN DESDE EL DOM
    const tokenElement = document.querySelector("[data-token]");
    const token = tokenElement?.getAttribute("data-token") || "";

    if (token) {
      console.log("🔑 Configurando token de autenticación para notificaciones");
      ddpService.setAuthToken(token);
    } else {
      console.warn("⚠️ No se encontró token de autenticación en data-token");
    }

    // 1️⃣ Conectar al sistema DDP una sola vez
    await notificationService.connect();

    // 2️⃣ Suscribirse para reactividad automática
    await notificationService.subscribeToNotifications(20);

    console.log("✅ Servicio DDP inicializado - Reactividad nativa activada");
  } catch (error) {
    console.warn("⚠️ Error al inicializar servicio de notificaciones:", error);
  }
}

// Inicializar cuando el DOM esté cargado
document.addEventListener("DOMContentLoaded", () => {
  // Si no existe el contenedor, vamos a intentar montar en el lugar correcto del header
  const notificacionesElement = document.querySelector("[data-notificaciones]");

  if (notificacionesElement) {
    mountNotificacionesComponent("[data-notificaciones]");
  } else {
    // Buscar el contenedor de notificaciones en el header (según la estructura original)
    const headerButtonsContainer = document.querySelector(
      "header .flex.items-center.space-x-3"
    );

    if (headerButtonsContainer) {
      // Crear contenedor para las notificaciones
      const notificacionesContainer = document.createElement("div");
      notificacionesContainer.setAttribute("data-notificaciones", "");

      // Insertar antes del botón del sidebar
      const sidebarToggleButton =
        headerButtonsContainer.querySelector("#sidebar-toggle");
      if (sidebarToggleButton) {
        headerButtonsContainer.insertBefore(
          notificacionesContainer,
          sidebarToggleButton
        );
        mountNotificacionesComponent("[data-notificaciones]");
      } else {
        // Si no hay botón de sidebar, simplemente añadir al final
        headerButtonsContainer.appendChild(notificacionesContainer);
        mountNotificacionesComponent("[data-notificaciones]");
      }
    } else {
      console.error(
        "No se encontró un lugar adecuado para montar el componente de notificaciones"
      );
    }
  }

  // Configurar detección de cambios en el sidebar
  setupSidebarToggleDetection();

  // También verificar en cambios de tamaño de pantalla
  window.addEventListener("resize", () => {
    const event = new CustomEvent("sidebarToggle", {
      detail: { timestamp: Date.now(), reason: "resize" },
    });
    document.dispatchEvent(event);
  });

  // 🚀 Inicializar servicio DDP reactivo
  initializeNotificationService();
});

// Exponer funciones para uso externo
window.notificacionesMount = mountNotificacionesComponent;
window.notificationService = notificationService;
