<template>
  <div class="notifications-component">
    <button
      @click="toggleNotifications"
      class="relative text-gray-600 hover:text-mulbin-500 focus:outline-none"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="w-6 h-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
        />
      </svg>
      <span
        v-if="unreadCount > 0"
        class="absolute top-[-8px] right-[-8px] inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full"
      >
        {{ unreadCount }}
      </span>
    </button>

    <!-- Panel de notificaciones -->
    <div v-if="showPanel" :class="panelClasses" :style="panelStyles">
      <div
        class="p-3 text-white bg-gradient-to-r to-indigo-800 from-mulbin-600"
      >
        <div class="flex justify-between items-center">
          <h3 class="font-medium">Notificaciones</h3>
          <button
            @click="markAllAsRead"
            class="px-2 py-1 text-xs bg-white bg-opacity-20 rounded hover:bg-opacity-30"
          >
            Marcar todas como leídas
          </button>
        </div>
      </div>

      <div class="overflow-y-auto max-h-96">
        <div v-if="loading" class="p-4 text-center text-gray-500">
          <div
            class="mx-auto mb-2 w-6 h-6 rounded-full border-2 animate-spin border-mulbin-500 border-t-transparent"
          ></div>
          Cargando notificaciones...
        </div>

        <div v-else-if="connectionError" class="p-4 text-center text-red-500">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mx-auto mb-2 w-12 h-12 text-red-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <p class="text-sm">Error de conexión</p>
          <p class="mt-1 text-xs text-red-400">{{ connectionErrorMessage }}</p>
          <button
            @click="toggleNotifications"
            class="px-4 py-1 mt-3 text-sm text-white bg-red-500 rounded hover:bg-red-600"
          >
            Reintentar
          </button>
        </div>

        <div
          v-else-if="notifications.length === 0"
          class="p-8 text-center text-gray-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mx-auto mb-2 w-12 h-12 text-gray-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
            />
          </svg>
          <p class="text-sm">No tienes notificaciones</p>
          <button
            v-if="showAdminButtons"
            @click="generateExampleNotifications"
            class="hidden px-3 py-1 mt-3 text-xs text-white rounded bg-mulbin-500 hover:bg-mulbin-600"
          >
            Generar notificaciones de ejemplo
          </button>
        </div>

        <div v-else>
          <div
            v-for="notification in notifications"
            :key="notification._id"
            :class="[
              'p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition',
              { 'bg-blue-50': !notification.read },
            ]"
            @click="readNotification(notification._id)"
          >
            <div class="flex">
              <div
                :class="[
                  'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3',
                  getIconBackground(notification.type),
                ]"
              >
                <ion-icon
                  v-if="notification.iconName"
                  :name="notification.iconName"
                  class="text-xl text-white"
                ></ion-icon>
                <svg
                  v-else-if="notification.type === 'info'"
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-5 h-5 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <svg
                  v-else-if="
                    notification.type === 'warning' ||
                    notification.type === 'error'
                  "
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-5 h-5 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-5 h-5 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <div class="flex-1">
                <p
                  :class="[
                    'text-sm font-medium',
                    !notification.read ? 'text-gray-900' : 'text-gray-600',
                  ]"
                >
                  {{ notification.title }}
                </p>
                <p
                  :class="[
                    'text-sm',
                    notification.read ? 'text-gray-600' : 'text-gray-900',
                  ]"
                >
                  {{ notification.message }}
                </p>
                <p class="mt-1 text-xs text-gray-500">
                  {{ formatDate(notification.createdAt) }}
                </p>
              </div>
              <div v-if="!notification.read" class="flex-shrink-0 ml-2">
                <div class="w-2 h-2 rounded-full bg-mulbin-500"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="p-2 text-center bg-gray-50 border-t border-gray-100">
        <div class="flex justify-between">
          <button
            v-if="showAdminButtons && notifications.length > 0"
            @click="clearExampleNotifications"
            class="hidden text-xs text-red-500 hover:text-red-700 hover:underline"
          >
            Limpiar ejemplos
          </button>
          <div class="flex-1"></div>
          <a
            href="#"
            class="text-sm text-mulbin-600 hover:text-mulbin-700 hover:underline"
            >Ver todas</a
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onUnmounted,
  reactive,
} from "vue";
import notificationService from "../../services/notificationService";

export default defineComponent({
  name: "Notificaciones",

  setup() {
    const showPanel = ref(false);
    const loading = ref(true);
    const connectionError = ref(false);
    const connectionErrorMessage = ref("");
    const notifications = ref([]);
    const unreadCount = ref(0);
    const showAdminButtons = ref(true); // Para desarrollo, en producción debería ser false o basado en permisos
    let unsubscribeFromChanges = null;

    // Estado de la pantalla
    const viewportWidth = ref(window.innerWidth);
    const hasSidebar = ref(false);
    const sidebarWidth = ref(0);
    const sidebarOpen = ref(false);

    // Clases CSS computadas según tamaño de pantalla
    const panelClasses = computed(() => {
      // Base classes
      const classes = [
        "notifications-panel",
        "bg-white",
        "border",
        "border-gray-200",
        "rounded-lg",
        "shadow-lg",
        "z-50",
        "overflow-hidden",
      ];

      // Ajuste según tamaño de pantalla
      if (viewportWidth.value < 640) {
        // Mobile
        classes.push(
          "fixed",
          "w-full",
          "max-w-[95vw]",
          "left-[2.5vw]",
          "top-16"
        );
      } else if (viewportWidth.value < 1024) {
        // Tablet
        classes.push("absolute", "w-80", "right-0", "mt-2");
      } else {
        // Desktop
        classes.push("absolute", "w-80", "right-0", "mt-2");
      }

      return classes.join(" ");
    });

    // Estilos en línea para ajustes finos
    const panelStyles = computed(() => {
      const styles = {};

      // Ajustes para sidebar en pantallas grandes
      if (
        viewportWidth.value >= 1024 &&
        sidebarOpen.value &&
        sidebarWidth.value > 0
      ) {
        styles.maxHeight = "calc(100vh - 180px)";
      } else if (viewportWidth.value < 640) {
        styles.maxHeight = "calc(80vh - 80px)";
      }

      return styles;
    });

    // 🚀 FUNCIÓN ELIMINADA: loadNotifications()
    // ✅ RAZÓN: Meteor DDP maneja la carga y actualización automáticamente via reactividad
    // ✅ REEMPLAZO: La suscripción inicial + observador es suficiente

    const toggleNotifications = () => {
      showPanel.value = !showPanel.value;

      // 🚀 SIMPLIFICADO: Solo mostrar/ocultar panel
      // ✅ Las notificaciones ya están sincronizadas via DDP reactivamente
      if (showPanel.value) {
        checkSidebarState();
        // 🔄 DDP ya mantiene notifications.value actualizado automáticamente
      }
    };

    const markAllAsRead = async () => {
      try {
        // 🚀 SOLO EJECUTAR LA ACCIÓN - Meteor DDP actualiza automáticamente
        await notificationService.markAllAsRead();
        // ✅ ELIMINADO: Actualizaciones manuales innecesarias
        // ✅ DDP Observer automáticamente actualiza notifications.value y unreadCount.value
      } catch (error) {
        console.error("Error al marcar todas como leídas:", error);
      }
    };

    const readNotification = async (id) => {
      try {
        // 🚀 SOLO EJECUTAR LA ACCIÓN - Meteor DDP actualiza automáticamente
        await notificationService.markAsRead(id);
        // ✅ ELIMINADO: Actualizaciones manuales de UI
        // ✅ DDP Observer automáticamente actualiza el estado cuando el servidor confirma
      } catch (error) {
        console.error("Error al marcar notificación como leída:", error);
      }
    };

    const generateExampleNotifications = async () => {
      try {
        loading.value = true;
        // 🚀 SOLO EJECUTAR LA ACCIÓN - DDP actualiza automáticamente
        await notificationService.generateExampleNotifications(8);
        // ✅ ELIMINADO: loadNotifications() innecesario
        // ✅ DDP Observer detecta los nuevos datos y actualiza la UI automáticamente
      } catch (error) {
        console.error("Error al generar notificaciones de ejemplo:", error);
      } finally {
        loading.value = false;
      }
    };

    const clearExampleNotifications = async () => {
      try {
        loading.value = true;
        // 🚀 SOLO EJECUTAR LA ACCIÓN - DDP actualiza automáticamente
        await notificationService.clearExampleNotifications();
        // ✅ ELIMINADO: loadNotifications() innecesario
        // ✅ DDP Observer detecta la eliminación y actualiza la UI automáticamente
      } catch (error) {
        console.error("Error al limpiar notificaciones de ejemplo:", error);
      } finally {
        loading.value = false;
      }
    };

    const getIconBackground = (type) => {
      switch (type) {
        case "info":
          return "bg-blue-500";
        case "warning":
          return "bg-amber-500";
        case "error":
          return "bg-red-500";
        case "success":
          return "bg-green-500";
        default:
          return "bg-gray-500";
      }
    };

    const formatDate = (date) => {
      if (!date) return "";

      const d = new Date(date);
      const now = new Date();
      const diff = now.getTime() - d.getTime();

      // Menos de 1 hora
      if (diff < 1000 * 60 * 60) {
        const mins = Math.floor(diff / (1000 * 60));
        return `Hace ${mins} ${mins === 1 ? "minuto" : "minutos"}`;
      }

      // Menos de 1 día
      if (diff < 1000 * 60 * 60 * 24) {
        const hours = Math.floor(diff / (1000 * 60 * 60));
        return `Hace ${hours} ${hours === 1 ? "hora" : "horas"}`;
      }

      // Menos de 7 días
      if (diff < 1000 * 60 * 60 * 24 * 7) {
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        return `Hace ${days} ${days === 1 ? "día" : "días"}`;
      }

      // Fecha completa
      return d.toLocaleDateString("es-ES", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    };

    // Mockups para modo fallback
    const getMockNotifications = () => {
      return [
        {
          _id: "mock-1",
          userId: "user123",
          title: "Nueva propiedad disponible",
          message:
            "Se ha agregado una nueva propiedad que coincide con tus criterios de búsqueda.",
          type: "info",
          read: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 min ago
          iconName: "home",
        },
        {
          _id: "mock-2",
          userId: "user123",
          title: "Aviso importante",
          message:
            "Su suscripción vence en 3 días. Renueve para continuar utilizando el servicio.",
          type: "warning",
          read: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          iconName: "alert",
        },
        {
          _id: "mock-3",
          userId: "user123",
          title: "Nuevo mensaje",
          message:
            "Ha recibido una nueva solicitud de contacto para su propiedad.",
          type: "success",
          read: true,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
          iconName: "chatbubble",
        },
      ];
    };

    // Verificar el estado del sidebar
    const checkSidebarState = () => {
      try {
        const sidebar =
          document.querySelector(".sidebar") ||
          document.querySelector("#sidebar");
        if (sidebar) {
          hasSidebar.value = true;
          sidebarWidth.value = sidebar.offsetWidth;
          sidebarOpen.value =
            !sidebar.classList.contains("collapsed") &&
            !sidebar.classList.contains("hidden") &&
            getComputedStyle(sidebar).display !== "none";
        }
      } catch (e) {
        console.error("Error al verificar estado del sidebar:", e);
      }
    };

    // Función para manejar cambios en el tamaño de la ventana
    const handleResize = () => {
      viewportWidth.value = window.innerWidth;
      checkSidebarState();
    };

    // Cerrar el panel al hacer clic fuera
    const handleClickOutside = (event) => {
      const component = event.target.closest(".notifications-component");
      if (!component && showPanel.value) {
        showPanel.value = false;
      }
    };

    // Inicialización cuando el componente se monta
    onMounted(async () => {
      document.addEventListener("click", handleClickOutside);
      window.addEventListener("resize", handleResize);

      // Comprobar inicialmente el sidebar
      setTimeout(checkSidebarState, 100);

      // También verificar cuando pueda cambiar el sidebar
      document.addEventListener("sidebarToggle", checkSidebarState);

      // 🔧 INICIALIZACIÓN REACTIVA COMPLETA - FILOSOFÍA METEOR DDP
      try {
        // 1️⃣ Conectar al servicio DDP una sola vez
        await notificationService.connect();

        // 2️⃣ Suscribirse a las notificaciones una sola vez
        await notificationService.subscribeToNotifications(20);

        // 3️⃣ CONFIGURAR OBSERVADOR REACTIVO ÚNICO - Meteor maneja TODO automáticamente
        unsubscribeFromChanges = notificationService.onNotificationsChange(
          (newNotifications) => {
            // 🔄 REACTIVIDAD NATIVA: Meteor nos notifica automáticamente de CUALQUIER cambio
            notifications.value = newNotifications;
            unreadCount.value = newNotifications.filter((n) => !n.read).length;

            // ✅ SIMPLIFICADO: Un solo punto de actualización para toda la UI
            // ✅ No importa si el panel está abierto o cerrado - siempre mantenemos sincronizado
          }
        );

        // 4️⃣ Estado inicial una sola vez - después DDP maneja todo reactivamente
        const initialNotifications = notificationService.getNotifications();
        notifications.value = initialNotifications;
        unreadCount.value = initialNotifications.filter((n) => !n.read).length;
      } catch (error) {
        console.error(
          "Error al inicializar servicio de notificaciones:",
          error
        );
        connectionError.value = true;
        connectionErrorMessage.value =
          error instanceof Error ? error.message : "Error de conexión DDP";

        // ⚠️ Fallback solo para desarrollo
        if (import.meta.env.DEV) {
          console.warn("🔧 Usando datos mock para desarrollo");
          const mockNotifications = getMockNotifications();
          notifications.value = mockNotifications;
          unreadCount.value = mockNotifications.filter((n) => !n.read).length;
        }
      } finally {
        loading.value = false;
      }
    });

    onUnmounted(() => {
      document.removeEventListener("click", handleClickOutside);
      window.removeEventListener("resize", handleResize);
      document.removeEventListener("sidebarToggle", checkSidebarState);

      // Limpiar suscripción a cambios
      if (unsubscribeFromChanges) {
        unsubscribeFromChanges();
      }
    });

    return {
      showPanel,
      loading,
      notifications,
      unreadCount,
      connectionError,
      connectionErrorMessage,
      panelClasses,
      panelStyles,
      showAdminButtons,
      toggleNotifications,
      markAllAsRead,
      readNotification,
      getIconBackground,
      formatDate,
      generateExampleNotifications,
      clearExampleNotifications,
    };
  },
});
</script>

<style scoped>
.notifications-component {
  position: relative;
  display: inline-block;
}

/* Estilos base necesarios - las clases se aplican dinámicamente */
.notifications-panel {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Ajustes para móvil */
@media (max-width: 640px) {
  .notifications-panel {
    /* Usamos !important solo para asegurar que estos estilos tienen prioridad */
    position: fixed !important;
    top: 4rem !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 95vw !important;
    max-width: 480px !important;
    max-height: 80vh !important;
  }
}

/* Estilo para tablets */
@media (min-width: 641px) and (max-width: 1023px) {
  .notifications-panel {
    position: absolute !important;
    width: 320px !important;
    right: 0 !important;
    max-height: calc(100vh - 10rem) !important;
  }
}

/* Estilo para desktop */
@media (min-width: 1024px) {
  .notifications-panel {
    position: absolute !important;
    width: 400px !important;
    right: 0 !important;
    max-height: calc(100vh - 12rem) !important;
  }
}
</style>
