# 🔔 Notificaciones - Documentación General

> Sistema de notificaciones **100% limpio** siguiendo filosofía Meteor DDP

## 🚨 **CÓDIGO ACTUALIZADO - SIN ANTIPATRONES**

Esta documentación describe un sistema completamente refactorizado que elimina todos los antipatrones y sigue perfectamente la filosofía de reactividad nativa de Meteor DDP.

## 📚 **Documentación Separada por Equipos**

Para facilitar el desarrollo en equipos especializados, la documentación se ha dividido en:

- **[📖 Frontend DDP Docs](./FRONTEND-DDP-DOCS.md)** - Para desarrolladores Vue.js
- **[📖 Backend DDP Docs](../../../MulbinComponents/app/imports/api/notifications/BACKEND-DDP-DOCS.md)** - Para desarrolladores Meteor
- **[📖 Backend REST Docs](../../../MulbinComponents/app/imports/api/rest/BACKEND-REST-DOCS.md)** - Para integraciones externas
- **[📖 Guía de Integración](./INTEGRACION-DOCS.md)** - Setup completo frontend + backend

---

> **⚠️ IMPORTANTE:** El código ha sido completamente limpiado eliminando actualizaciones manuales y funciones innecesarias. Ahora sigue 100% la filosofía DDP reactiva de Meteor.

## 📋 Tabla de Contenidos

- [🎯 Descripción General](#-descripción-general)
- [🏗️ Arquitectura del Componente](#️-arquitectura-del-componente)
- [🧩 Props y Estados](#-props-y-estados)
- [🔧 Funcionalidades Principales](#-funcionalidades-principales)
- [🎨 Interfaz de Usuario](#-interfaz-de-usuario)
- [📡 Integración DDP](#-integración-ddp)
- [🔄 Gestión de Estados](#-gestión-de-estados)
- [💡 Ejemplos de Uso](#-ejemplos-de-uso)

---

## 🎯 Descripción General

### ¿Qué es el Sistema de Notificaciones?

El **Sistema de Notificaciones** es un componente Vue.js que proporciona notificaciones en tiempo real utilizando conexión DDP (Distributed Data Protocol) con el backend Meteor. Maneja diferentes tipos de notificaciones y permite interacciones como marcar como leídas, eliminar y navegar a contenido relacionado.

### Características Principales

- ✅ **Conexión DDP en tiempo real** con backend Meteor
- ✅ **Múltiples tipos de notificaciones** (posts, comentarios, likes, etc.)
- ✅ **Estados de lectura** con persistencia
- ✅ **Navegación contextual** a contenido relacionado
- ✅ **Interfaz responsive** optimizada para móvil y desktop
- ✅ **Gestión de errores** y reconexión automática
- ✅ **Indicadores visuales** de estado y cantidad

### Tipos de Notificaciones Soportadas

| Tipo                 | Descripción                    | Acción                         |
| -------------------- | ------------------------------ | ------------------------------ |
| **nuevo_post**       | Nueva publicación inmobiliaria | Navegar al post                |
| **nuevo_comentario** | Comentario en publicación      | Navegar al post con comentario |
| **nuevo_like**       | Like en publicación            | Navegar al post                |
| **nuevo_socio**      | Nuevo socio agregado           | Navegar a gestión de socios    |
| **sistema**          | Notificaciones del sistema     | Mostrar información            |

---

## 🏗️ Arquitectura del Componente

### Estructura de Archivos

```
src/components/notifications/
├── Notificaciones.vue           # 🎯 Componente principal
├── index.js                     # 📦 Entry point y auto-inicialización
└── NOTIFICACIONES-DOCS.md       # 📖 Esta documentación
```

### Dependencias

```typescript
import { defineComponent, ref, reactive, onMounted, onUnmounted } from "vue";
import inmobiliarioService from "../../services/inmobiliarioService";
import type { Notificacion } from "../../types/inmobiliario";
```

### Flujo de Datos

```mermaid
sequenceDiagram
    participant U as Usuario
    participant N as Notificaciones
    participant DDP as DDP Service
    participant M as Meteor Backend

    U->>N: Abre panel de notificaciones
    N->>DDP: Conectar con token
    DDP->>M: Autenticación
    M->>DDP: Token válido
    N->>DDP: Suscribirse a 'notifications'
    M->>DDP: Nuevas notificaciones
    DDP->>N: Datos reactivos
    N->>U: Actualizar UI

    U->>N: Marcar como leída
    N->>M: Método 'marcarNotificacionLeida'
    M->>DDP: Actualización
    DDP->>N: Estado actualizado
```

---

## 🧩 Props y Estados

### Props del Componente

```typescript
interface Props {
  token: string; // Token de autenticación DDP
  userId: string; // ID del usuario para filtrar notificaciones
  autoConnect: boolean; // Conectar automáticamente al montar
  maxNotifications: number; // Máximo de notificaciones a mostrar
}
```

### Estados Reactivos

```typescript
// Estados principales
const notificaciones = ref<Notificacion[]>([]); // Lista de notificaciones
const loading = ref(true); // Estado de carga inicial
const connectionError = ref(false); // Error de conexión
const isConnected = ref(false); // Estado de conexión DDP

// Estados de UI
const showDropdown = ref(false); // Dropdown visible
const processingIds = ref<Set<string>>(new Set()); // IDs en procesamiento

// Contadores
const unreadCount = computed(
  () => notificaciones.value.filter((n) => !n.leida).length
);
```

### Eventos Emitidos

```typescript
interface Events {
  "notification-click": Notificacion; // Click en notificación
  "mark-as-read": string; // Marcar como leída
  "delete-notification": string; // Eliminar notificación
  "connection-status": boolean; // Estado de conexión
}
```

---

## 🔧 Funcionalidades Principales

### 1. **Conexión DDP Automática**

```typescript
const connectToNotifications = async () => {
  try {
    loading.value = true;
    connectionError.value = false;

    // Conectar con token de autenticación
    await inmobiliarioService.connectWithToken(props.token);
    isConnected.value = true;

    // Suscribirse a notificaciones del usuario
    await inmobiliarioService.subscribe("notifications", props.userId);

    // Configurar listener para cambios en tiempo real
    inmobiliarioService.onNotificationsChange((newNotifications) => {
      notificaciones.value = newNotifications
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(0, props.maxNotifications);
    });
  } catch (error) {
    console.error("Error conectando a notificaciones:", error);
    connectionError.value = true;
    isConnected.value = false;
  } finally {
    loading.value = false;
  }
};
```

### 2. **Gestión de Estados de Lectura**

```typescript
const marcarComoLeida = async (notificacionId: string) => {
  // Optimistic update
  const notificacion = notificaciones.value.find(
    (n) => n._id === notificacionId
  );
  if (notificacion && !notificacion.leida) {
    notificacion.leida = true;
    processingIds.value.add(notificacionId);

    try {
      // Llamada al backend
      await inmobiliarioService.call("marcarNotificacionLeida", notificacionId);
      emit("mark-as-read", notificacionId);
    } catch (error) {
      // Rollback en caso de error
      notificacion.leida = false;
      console.error("Error marcando notificación como leída:", error);
    } finally {
      processingIds.value.delete(notificacionId);
    }
  }
};
```

### 3. **Navegación Contextual**

```typescript
const handleNotificationClick = (notificacion: Notificacion) => {
  // Marcar como leída automáticamente
  if (!notificacion.leida) {
    marcarComoLeida(notificacion._id);
  }

  // Navegar según el tipo de notificación
  switch (notificacion.tipo) {
    case "nuevo_post":
      window.location.href = `/post/${notificacion.postId}`;
      break;

    case "nuevo_comentario":
      window.location.href = `/post/${notificacion.postId}#comentario-${notificacion.comentarioId}`;
      break;

    case "nuevo_like":
      window.location.href = `/post/${notificacion.postId}`;
      break;

    case "nuevo_socio":
      // Emitir evento para cambiar a vista de socios
      emit("notification-click", notificacion);
      break;

    default:
      console.log("Tipo de notificación no manejado:", notificacion.tipo);
  }

  // Cerrar dropdown
  showDropdown.value = false;
};
```

### 4. **Formateo de Tiempo Relativo**

```typescript
const formatTimeAgo = (date: string | Date) => {
  const now = new Date();
  const notificationDate = new Date(date);
  const diffInSeconds = Math.floor(
    (now.getTime() - notificationDate.getTime()) / 1000
  );

  if (diffInSeconds < 60) return "Hace un momento";
  if (diffInSeconds < 3600) return `Hace ${Math.floor(diffInSeconds / 60)} min`;
  if (diffInSeconds < 86400)
    return `Hace ${Math.floor(diffInSeconds / 3600)} h`;
  if (diffInSeconds < 604800)
    return `Hace ${Math.floor(diffInSeconds / 86400)} días`;

  return notificationDate.toLocaleDateString();
};
```

---

## 🎨 Interfaz de Usuario

### Indicador de Notificaciones

```vue
<div class="relative">
  <!-- Botón principal -->
  <button 
    @click="toggleDropdown"
    class="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none"
    :class="{ 'text-blue-600': unreadCount > 0 }"
  >
    <ion-icon name="notifications-outline" class="w-6 h-6"></ion-icon>
    
    <!-- Badge de contador -->
    <span 
      v-if="unreadCount > 0"
      class="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full"
    >
      {{ unreadCount > 99 ? '99+' : unreadCount }}
    </span>
  </button>
</div>
```

### Dropdown de Notificaciones

```vue
<div
  v-if="showDropdown"
  class="absolute right-0 z-50 w-80 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg"
>
  <!-- Header -->
  <div class="flex items-center justify-between p-4 border-b border-gray-100">
    <h3 class="text-lg font-semibold text-gray-900">Notificaciones</h3>
    <button
      v-if="unreadCount > 0"
      @click="marcarTodasComoLeidas"
      class="text-sm text-blue-600 hover:text-blue-800"
    >
      Marcar todas como leídas
    </button>
  </div>

  <!-- Lista de notificaciones -->
  <div class="max-h-96 overflow-y-auto">
    <div
      v-for="notificacion in notificaciones"
      :key="notificacion._id"
      @click="handleNotificationClick(notificacion)"
      class="flex items-start p-4 border-b border-gray-50 cursor-pointer hover:bg-gray-50"
      :class="{ 'bg-blue-50': !notificacion.leida }"
    >
      <!-- Avatar/Icono -->
      <div class="flex-shrink-0 mr-3">
        <div
          class="flex items-center justify-center w-10 h-10 rounded-full"
          :class="getNotificationIconClass(notificacion.tipo)"
        >
          <ion-icon :name="getNotificationIcon(notificacion.tipo)" class="w-5 h-5"></ion-icon>
        </div>
      </div>

      <!-- Contenido -->
      <div class="flex-1 min-w-0">
        <p class="text-sm font-medium text-gray-900">
          {{ notificacion.titulo }}
        </p>
        <p class="text-sm text-gray-600">
          {{ notificacion.mensaje }}
        </p>
        <p class="text-xs text-gray-400 mt-1">
          {{ formatTimeAgo(notificacion.createdAt) }}
        </p>
      </div>

      <!-- Indicador no leída -->
      <div v-if="!notificacion.leida" class="flex-shrink-0 ml-2">
        <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div class="p-4 border-t border-gray-100">
    <button
      @click="verTodasLasNotificaciones"
      class="w-full text-sm text-center text-blue-600 hover:text-blue-800"
    >
      Ver todas las notificaciones
    </button>
  </div>
</div>
```

---

## 📡 Integración DDP

### Publicaciones DDP

```javascript
// Backend Meteor - Publicación de notificaciones
Meteor.publish("notifications", function (userId) {
  if (!this.userId) {
    return this.ready();
  }

  return Notifications.find(
    {
      userId: userId,
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Últimos 30 días
    },
    {
      sort: { createdAt: -1 },
      limit: 50,
    }
  );
});
```

### Métodos DDP

```javascript
// Marcar notificación como leída
Meteor.methods({
  marcarNotificacionLeida(notificacionId) {
    check(notificacionId, String);

    if (!this.userId) {
      throw new Meteor.Error("not-authorized");
    }

    return Notifications.update(
      {
        _id: notificacionId,
        userId: this.userId,
      },
      {
        $set: {
          leida: true,
          fechaLeida: new Date(),
        },
      }
    );
  },
});
```

---

## 💡 Ejemplos de Uso

### Uso Básico

```vue
<template>
  <Notificaciones
    :token="userToken"
    :user-id="userId"
    :auto-connect="true"
    :max-notifications="20"
    @notification-click="handleNotificationClick"
  />
</template>

<script>
import Notificaciones from "./Notificaciones.vue";

export default {
  components: { Notificaciones },
  data() {
    return {
      userToken: "eyJhbGciOiJIUzI1NiIs...",
      userId: "user123",
    };
  },
  methods: {
    handleNotificationClick(notification) {
      console.log("Notificación clickeada:", notification);
    },
  },
};
</script>
```

### Auto-inicialización HTML

```html
<!-- Container para auto-inicialización -->
<div
  data-notificaciones
  data-token="user-auth-token"
  data-user-id="user123"
  data-auto-connect="true"
></div>

<!-- Script de inicialización -->
<script src="dist/assets/notificaciones.js"></script>
```

---

## 📚 Referencias

- [README Principal](../../../README.md)
- [MuroInmobiliarioSocial Docs](../muro-inmobiliario-social/MURO-INMOBILIARIO-SOCIAL-DOCS.md)
- [DDP Service](../../services/ddpService.ts)
- [API DDP Backend](../../../../Meteor/MulbinComponents/app/API-DDP-Frontend-Clients.md)
