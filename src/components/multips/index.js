// Este archivo inicializa el componente MultipsCard y lo monta en el DOM
import { createApp } from "vue";
import MultipsCard from "./MultipsCard.vue";

console.log("🚀 Inicializando MultipsCard...");

// Función para montar el componente en un elemento específico
function mountMultipsCardComponent(elementSelector = "[data-multips-card]") {
  console.log("🔍 Buscando elemento con selector:", elementSelector);

  const targetElement = document.querySelector(elementSelector);

  if (!targetElement) {
    console.error(
      `No se encontró el elemento con selector: ${elementSelector}`
    );
    return;
  }

  console.log("✅ Elemento encontrado:", targetElement);
  console.log("🔍 Atributos del elemento:", {
    "data-ui-style": targetElement.getAttribute("data-ui-style"),
    "data-show-birthday": targetElement.getAttribute("data-show-birthday"),
    "data-show-welcome": targetElement.getAttribute("data-show-welcome"),
    "data-show-motivational-stats": targetElement.getAttribute(
      "data-show-motivational-stats"
    ),
    "data-show-suggestions": targetElement.getAttribute("data-show-suggestions"),
    "data-user-birthday-date": targetElement.getAttribute(
      "data-user-birthday-date"
    ),
  });

  // Obtener props desde data-attributes
  const uiStyle = targetElement.getAttribute("data-ui-style") || "clean";
  const showBirthday =
    targetElement.getAttribute("data-show-birthday") !== "false";
  const showWelcome =
    targetElement.getAttribute("data-show-welcome") !== "false";
  const showMotivationalStats =
    targetElement.getAttribute("data-show-motivational-stats") !== "false";
  const showSuggestions =
    targetElement.getAttribute("data-show-suggestions") !== "false";
  const autoRotate = targetElement.getAttribute("data-auto-rotate") !== "false";
  const rotationInterval = parseInt(
    targetElement.getAttribute("data-rotation-interval") || "30000"
  );
  const userBirthdayDate = targetElement.getAttribute(
    "data-user-birthday-date"
  );
  const userTimezone = targetElement.getAttribute("data-user-timezone");

  console.log("🏗️ Inicializando MultipsCard con props:", {
    uiStyle,
    showBirthday,
    showWelcome,
    showMotivationalStats,
    showSuggestions,
    autoRotate,
    rotationInterval,
    userBirthdayDate,
    userTimezone,
  });

  // Crear un div para contener el componente
  const appContainer = document.createElement("div");
  appContainer.className = "multips-card-app";
  console.log("🏗️ Contenedor creado:", appContainer);

  // Limpiar el contenido del target element y agregar el container
  targetElement.innerHTML = "";
  targetElement.appendChild(appContainer);
  console.log("✅ Contenedor agregado al DOM");

  try {
    // Montar el componente directamente
    const app = createApp(MultipsCard, {
      uiStyle,
      showBirthday,
      showWelcome,
      showMotivationalStats,
      showSuggestions,
      autoRotate,
      rotationInterval,
      userBirthdayDate,
      userTimezone,
    });

    console.log("🏗️ App Vue creada, montando...");
    app.mount(appContainer);
    console.log("✅ Componente MultipsCard montado correctamente");

    return app;
  } catch (error) {
    console.error("❌ Error al montar el componente:", error);
    throw error;
  }
}

// Inicializar cuando el DOM esté cargado
document.addEventListener("DOMContentLoaded", () => {
  // Cargar Ionicons si no están ya cargados
  if (!document.querySelector('script[src*="ionicons"]')) {
    const moduleScript = document.createElement("script");
    moduleScript.type = "module";
    moduleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js";
    document.head.appendChild(moduleScript);

    const noModuleScript = document.createElement("script");
    noModuleScript.noModule = true;
    noModuleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js";
    document.head.appendChild(noModuleScript);
  }

  // Buscar el elemento específico para MultipsCard
  const targetElement = document.querySelector("[data-multips-card]");

  if (targetElement) {
    mountMultipsCardComponent("[data-multips-card]");
  } else {
    console.error("❌ No se encontró el elemento [data-multips-card]");
    console.log(
      "💡 Asegúrate de que el HTML contenga un elemento con el atributo data-multips-card"
    );
    console.log(
      "📝 Ejemplo: <div data-multips-card data-ui-style='clean' data-show-suggestions='true'></div>"
    );
  }
});

// Exportar el componente para uso manual si es necesario
export default MultipsCard;
