// Tipos para el componente MultipsCard

export interface Tip {
  id: string;
  content: string;
  category: "general" | "inmuebles" | "marketing" | "finanzas";
  priority: "low" | "medium" | "high";
  createdAt?: Date;
  expiresAt?: Date;
}

export interface Suggestion {
  id: string;
  content: string;
  category: "profile" | "inmuebles" | "marketing" | "premium" | "tutorial";
  priority: "low" | "medium" | "high";
  action?: {
    text: string;
    url?: string;
    callback?: () => void;
    type: "link" | "button" | "modal";
  };
  conditions?: {
    minInmuebles?: number;
    maxInmuebles?: number;
    profileCompletion?: number;
    userType?: "free" | "premium" | "enterprise";
  };
}

export interface MotivationalStats {
  inmueblesCount: number;
  visitsThisMonth: number;
  inquiriesThisMonth: number;
  profileCompletion: number;
  lastActivity: Date;
}

export interface BirthdayMessage {
  message: string;
  type: "birthday" | "anniversary" | "achievement";
  date: Date;
  isRecurring: boolean;
}

export interface MultipsCardProps {
  uiStyle?: "clean" | "blue" | "mulbin" | "red" | "green" | "default";
  showBirthday?: boolean;
  showWelcome?: boolean;
  showMotivationalStats?: boolean;
  customTips?: Tip[];
  customSuggestions?: Suggestion[];
  maxTipsToShow?: number;
  autoRotate?: boolean;
  rotationInterval?: number; // en milisegundos
}

export interface MultipsCardEmits {
  tipClicked: (tip: Tip) => void;
  suggestionAction: (suggestion: Suggestion) => void;
  welcomeDismissed: () => void;
  tipRotated: (tip: Tip) => void;
  statsViewed: (stats: MotivationalStats) => void;
}

// Estados del componente
export interface ComponentState {
  currentTipIndex: number;
  currentSuggestionIndex: number;
  showWelcomeMessage: boolean;
  isRotating: boolean;
  lastRotation: Date;
}

// Configuración por defecto
export const DEFAULT_CONFIG = {
  maxTipsToShow: 3,
  rotationInterval: 30000, // 30 segundos
  autoRotate: true,
  uiStyle: "clean" as const,
  showBirthday: true,
  showWelcome: true,
  showMotivationalStats: true,
};

// Utilidades para validación
export const isValidTip = (tip: any): tip is Tip => {
  return (
    typeof tip === "object" &&
    tip !== null &&
    typeof tip.id === "string" &&
    typeof tip.content === "string" &&
    ["general", "inmuebles", "marketing", "finanzas"].includes(tip.category) &&
    ["low", "medium", "high"].includes(tip.priority)
  );
};

export const isValidSuggestion = (
  suggestion: any
): suggestion is Suggestion => {
  return (
    typeof suggestion === "object" &&
    suggestion !== null &&
    typeof suggestion.id === "string" &&
    typeof suggestion.content === "string" &&
    ["profile", "inmuebles", "marketing", "premium", "tutorial"].includes(
      suggestion.category
    ) &&
    ["low", "medium", "high"].includes(suggestion.priority)
  );
};

// Constantes
export const TIP_CATEGORIES = [
  "general",
  "inmuebles",
  "marketing",
  "finanzas",
] as const;
export const SUGGESTION_CATEGORIES = [
  "profile",
  "inmuebles",
  "marketing",
  "premium",
  "tutorial",
] as const;
export const PRIORITY_LEVELS = ["low", "medium", "high"] as const;
export const UI_STYLES = [
  "clean",
  "blue",
  "mulbin",
  "red",
  "green",
  "default",
] as const;
