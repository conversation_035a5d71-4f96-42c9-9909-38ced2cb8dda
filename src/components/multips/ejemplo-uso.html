<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ejemplo de Uso - MultipsCard</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script
      type="module"
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"
    ></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      .animate-spin {
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <div id="app">
      <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">
          Ejemplo de Integración - MultipsCard
        </h1>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Columna izquierda: SidebarCardInmuebles -->
          <div class="space-y-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">
              SidebarCardInmuebles (Estilo Clean)
            </h2>

            <div
              class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
            >
              <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                  <h3 class="text-sm font-medium text-gray-700">
                    Inmuebles registrados
                  </h3>
                  <ion-icon
                    name="home-outline"
                    class="text-lg text-gray-600"
                  ></ion-icon>
                </div>
                <div class="text-2xl font-bold text-gray-800">1,247</div>
              </div>

              <div class="mb-2">
                <label class="text-xs font-medium text-gray-600">
                  Registrar / Editar:
                </label>
                <div class="relative">
                  <input
                    type="text"
                    placeholder="Clave del inmueble"
                    class="px-2 py-2 pr-8 w-full text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    class="absolute right-2 top-1/2 p-1 text-blue-600 rounded transition-colors transform -translate-y-1/2 hover:text-blue-700 hover:bg-blue-50"
                  >
                    <ion-icon
                      name="arrow-forward-outline"
                      class="text-lg"
                    ></ion-icon>
                  </button>
                </div>
              </div>

              <button
                class="px-3 py-2 mt-2 w-full text-xs font-medium text-white bg-blue-600 rounded-md transition-colors hover:bg-blue-700"
              >
                REGISTRAR
              </button>

              <button
                class="px-3 py-2 mt-3 w-full text-sm font-medium text-blue-600 bg-white rounded-md border border-blue-200 transition-colors hover:bg-blue-50"
              >
                LISTAR MIS INMUEBLES REGISTRADOS
              </button>
            </div>
          </div>

          <!-- Columna derecha: MultipsCard -->
          <div class="space-y-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">
              MultipsCard (Estilo Clean)
            </h2>

            <div class="space-y-4">
              <!-- Tip del día -->
              <div
                class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-3 border-l-4 border-blue-400"
              >
                <div class="flex items-start space-x-2">
                  <ion-icon
                    name="information-circle"
                    class="text-blue-500 text-lg mt-0.5 flex-shrink-0"
                  ></ion-icon>
                  <div class="flex-1">
                    <h4 class="text-xs font-semibold text-blue-800 mb-1">
                      Tip del día
                    </h4>
                    <p class="text-xs text-blue-700 leading-relaxed">
                      Mantén actualizadas las fotos de tus inmuebles para
                      aumentar el interés de los compradores.
                    </p>
                  </div>
                </div>
              </div>

              <!-- Felicitación de cumpleaños -->
              <div
                class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-3 border-l-4 border-green-400"
              >
                <div class="flex items-start space-x-2">
                  <ion-icon
                    name="gift"
                    class="text-green-500 text-lg mt-0.5 flex-shrink-0"
                  ></ion-icon>
                  <div class="flex-1">
                    <h4 class="text-xs font-semibold text-green-800 mb-1">
                      ¡Feliz Cumpleaños! 🎉
                    </h4>
                    <p class="text-xs text-green-700 leading-relaxed">
                      ¡Que tengas un día maravilloso! Aprovecha para revisar tus
                      inmuebles y hacer planes para el futuro.
                    </p>
                  </div>
                </div>
              </div>

              <!-- Sugerencia -->
              <div
                class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-3 border-l-4 border-purple-400"
              >
                <div class="flex items-start space-x-2">
                  <ion-icon
                    name="trending-up"
                    class="text-purple-500 text-lg mt-0.5 flex-shrink-0"
                  ></ion-icon>
                  <div class="flex-1">
                    <h4 class="text-xs font-semibold text-purple-800 mb-1">
                      Sugerencia
                    </h4>
                    <p class="text-xs text-purple-700 leading-relaxed">
                      ¿Has considerado agregar más fotos a tus inmuebles? Esto
                      puede aumentar el interés en un 40%.
                    </p>
                    <button
                      class="mt-2 text-xs text-purple-600 hover:text-purple-700 font-medium underline"
                    >
                      Ver tutorial de fotos
                    </button>
                  </div>
                </div>
              </div>

              <!-- Estadística motivacional -->
              <div
                class="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-3 border-l-4 border-orange-400"
              >
                <div class="flex items-start space-x-2">
                  <ion-icon
                    name="trophy"
                    class="text-orange-500 text-lg mt-0.5 flex-shrink-0"
                  ></ion-icon>
                  <div class="flex-1">
                    <h4 class="text-xs font-semibold text-orange-800 mb-1">
                      ¡Excelente trabajo!
                    </h4>
                    <p class="text-xs text-orange-700 leading-relaxed">
                      Tienes 1,247 inmuebles registrados. ¡Sigue así!
                    </p>
                    <div class="mt-2 text-lg font-bold text-orange-600">
                      1,247 inmuebles
                    </div>
                  </div>
                </div>
              </div>

              <!-- Footer -->
              <div class="pt-3 border-t border-gray-200">
                <button
                  class="w-full text-xs text-gray-600 hover:text-gray-800 font-medium transition-colors"
                >
                  Ver más tips y sugerencias →
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Código de integración -->
        <div class="mt-12 bg-gray-900 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-white mb-4">
            Código de Integración
          </h3>
          <pre
            class="text-sm text-gray-300 overflow-x-auto"
          ><code>&lt;!-- Reemplazar la sección de desarrollos en dashboard.htm --&gt;
&lt;section class="multips-section"&gt;
  &lt;div
    data-multips-card
    data-ui-style="clean"
    data-show-birthday="true"
    data-show-welcome="true"
    data-show-motivational-stats="true"
    class="multips-container"
  &gt;
    &lt;div class="flex justify-center items-center p-8"&gt;
      &lt;div class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"&gt;&lt;/div&gt;
      &lt;span class="ml-2 text-sm text-gray-600"&gt;Cargando tips...&lt;/span&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/section&gt;</code></pre>
        </div>

        <!-- Características del componente -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div class="flex items-center mb-3">
              <ion-icon
                name="bulb-outline"
                class="text-blue-500 text-xl mr-2"
              ></ion-icon>
              <h3 class="font-semibold text-gray-800">Tips Inteligentes</h3>
            </div>
            <p class="text-sm text-gray-600">
              Consejos útiles para gestión de inmuebles que se adaptan al perfil
              del usuario.
            </p>
          </div>

          <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div class="flex items-center mb-3">
              <ion-icon
                name="trending-up-outline"
                class="text-green-500 text-xl mr-2"
              ></ion-icon>
              <h3 class="font-semibold text-gray-800">Sugerencias</h3>
            </div>
            <p class="text-sm text-gray-600">
              Recomendaciones personalizadas para mejorar el perfil y la gestión
              de inmuebles.
            </p>
          </div>

          <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div class="flex items-center mb-3">
              <ion-icon
                name="gift-outline"
                class="text-purple-500 text-xl mr-2"
              ></ion-icon>
              <h3 class="font-semibold text-gray-800">Motivación</h3>
            </div>
            <p class="text-sm text-gray-600">
              Felicitaciones de cumpleaños y estadísticas motivacionales para
              mantener al usuario activo.
            </p>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Simular comportamiento del componente
      document.addEventListener("DOMContentLoaded", function () {
        // Simular rotación de tips
        let currentTipIndex = 0;
        const tips = [
          "Mantén actualizadas las fotos de tus inmuebles para aumentar el interés de los compradores.",
          "Responde rápidamente a las consultas de los interesados para mejorar la tasa de conversión.",
          "Considera ofrecer tours virtuales para inmuebles de alto valor.",
          "Revisa regularmente los precios del mercado para mantener competitividad.",
          "Documenta todas las mejoras realizadas en tus propiedades para justificar el precio.",
        ];

        const tipElement = document.querySelector(".text-blue-700");
        if (tipElement) {
          setInterval(() => {
            currentTipIndex = (currentTipIndex + 1) % tips.length;
            tipElement.textContent = tips[currentTipIndex];
          }, 5000); // Cambiar cada 5 segundos para demo
        }

        // Simular interacciones
        const buttons = document.querySelectorAll("button");
        buttons.forEach((button) => {
          button.addEventListener("click", function () {
            if (this.textContent.includes("Ver tutorial")) {
              alert("Navegando al tutorial de fotos...");
            } else if (this.textContent.includes("Ver más tips")) {
              alert("Rotando al siguiente tip...");
            }
          });
        });
      });
    </script>
  </body>
</html>
