# MultipsCard

Componente Vue modular para mostrar tips, sugerencias, felicitaciones y estadísticas motivacionales en el sidebar.

## Características

- ✅ **Tips del día** - Consejos útiles para gestión de inmuebles
- ✅ **Sugerencias personalizadas** - Recomendaciones basadas en el perfil del usuario
- ✅ **Felicitaciones de cumpleaños** - Mensajes especiales en fechas importantes
- ✅ **Estadísticas motivacionales** - Métricas para mantener al usuario motivado
- ✅ **Mensaje de bienvenida** - Para nuevos usuarios
- ✅ **Rotación automática** - Los tips cambian automáticamente cada 30 segundos
- ✅ **Estilos configurables** - Múltiples variantes de color compatibles con SidebarCardInmuebles
- ✅ **TypeScript** - Tipado fuerte y mejor experiencia de desarrollo
- ✅ **Composition API** - Vue 3 con mejores prácticas
- ✅ **Responsive** - Diseño adaptativo con Tailwind CSS

## Estructura de archivos

```
src/components/multips/
├── MultipsCard.vue      # Componente principal
├── types.ts             # Interfaces y tipos TypeScript
├── index.js             # Exportación del componente
└── README.md            # Esta documentación
```

## Uso Básico

```vue
<template>
  <div class="sidebar">
    <!-- Componente de inmuebles -->
    <SidebarCardInmuebles
      :total-properties="stats.total"
      data-ui-style="clean"
    />

    <!-- Componente de tips y sugerencias -->
    <MultipsCard
      ui-style="clean"
      :show-birthday="true"
      :show-welcome="true"
      @tip-clicked="handleTipClicked"
      @suggestion-action="handleSuggestionAction"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import MultipsCard from "@/components/multips/MultipsCard.vue";
import type { Tip, Suggestion } from "@/components/multips/types";

const handleTipClicked = (tip: Tip) => {
  console.log("Tip clickeado:", tip);
};

const handleSuggestionAction = (suggestion: Suggestion) => {
  console.log("Acción de sugerencia:", suggestion);
};
</script>
```

## Props

| Prop                    | Tipo           | Predeterminado | Descripción                                     |
| ----------------------- | -------------- | -------------- | ----------------------------------------------- |
| `uiStyle`               | `string`       | `'clean'`      | Estilo visual (`clean`, `blue`, `mulbin`, etc.) |
| `showBirthday`          | `boolean`      | `true`         | Mostrar felicitaciones de cumpleaños            |
| `showWelcome`           | `boolean`      | `true`         | Mostrar mensaje de bienvenida                   |
| `showMotivationalStats` | `boolean`      | `true`         | Mostrar estadísticas motivacionales             |
| `customTips`            | `Tip[]`        | `[]`           | Tips personalizados                             |
| `customSuggestions`     | `Suggestion[]` | `[]`           | Sugerencias personalizadas                      |
| `maxTipsToShow`         | `number`       | `3`            | Máximo número de tips a mostrar                 |
| `autoRotate`            | `boolean`      | `true`         | Rotación automática de tips                     |
| `rotationInterval`      | `number`       | `30000`        | Intervalo de rotación en milisegundos           |

## Eventos

| Evento             | Payload             | Descripción                                          |
| ------------------ | ------------------- | ---------------------------------------------------- |
| `tipClicked`       | `Tip`               | Se emite cuando se hace clic en un tip               |
| `suggestionAction` | `Suggestion`        | Se emite cuando se ejecuta una acción                |
| `welcomeDismissed` | -                   | Se emite cuando se descarta el mensaje de bienvenida |
| `tipRotated`       | `Tip`               | Se emite cuando rota automáticamente un tip          |
| `statsViewed`      | `MotivationalStats` | Se emite cuando se visualizan las estadísticas       |

## Estilos de UI

### 🧹 `clean` (predeterminado)

**Uso**: Para componentes que se renderizan sobre fondos personalizados

- **Background**: Transparente
- **Border**: Ninguno
- **Shadow**: Ninguno
- **Colores**: Grises neutros

### 🔵 `blue`

**Uso**: Estilo estándar azul

- **Background**: Gradiente azul claro
- **Border**: `border-blue-200`

### 🏢 `mulbin`

**Uso**: Colores de marca Mulbin

- **Background**: Gradiente mulbin claro
- **Border**: `border-mulbin-200`

### 🔴 `red`

**Uso**: Variante roja

- **Background**: Gradiente rojo claro
- **Border**: `border-red-200`

### 🟢 `green`

**Uso**: Variante verde

- **Background**: Gradiente verde claro
- **Border**: `border-green-200`

## Integración con SidebarCardInmuebles

El componente está diseñado para integrarse visualmente con `SidebarCardInmuebles`:

```html
<!-- Dashboard template -->
<section
  data-sidebar-card-inmuebles
  data-ui-style="clean"
  class="sidebar-card-inmuebles-container"
>
  <!-- Loading placeholder -->
</section>

<!-- Área de tips (reemplaza temporalmente desarrollos) -->
<section class="multips-container">
  <div data-multips-card data-ui-style="clean"></div>
</section>
```

## Tips Predefinidos

El componente incluye tips útiles para gestión de inmuebles:

- **Marketing**: Consejos para mejorar la visibilidad
- **Inmuebles**: Mejores prácticas de gestión
- **Finanzas**: Optimización de precios y rentabilidad
- **General**: Consejos generales del negocio

## Sugerencias Personalizadas

Las sugerencias incluyen:

- **Perfil**: Completar información del usuario
- **Inmuebles**: Mejorar presentación y fotos
- **Marketing**: Estrategias de promoción
- **Premium**: Beneficios de planes superiores
- **Tutoriales**: Recursos de aprendizaje

## Personalización

### Tips Personalizados

```typescript
const customTips: Tip[] = [
  {
    id: "custom-1",
    content: "Tu tip personalizado aquí",
    category: "inmuebles",
    priority: "high",
  },
];
```

### Sugerencias Personalizadas

```typescript
const customSuggestions: Suggestion[] = [
  {
    id: "custom-1",
    content: "Tu sugerencia personalizada",
    category: "marketing",
    priority: "medium",
    action: {
      text: "Ver más",
      url: "/tu-url",
      type: "link",
    },
  },
];
```

## Métodos Públicos

El componente expone varios métodos útiles:

```typescript
// Referencia al componente
const multipsRef = ref();

// Mostrar mensaje de bienvenida
multipsRef.value.showWelcomeMessage();

// Ocultar mensaje de bienvenida
multipsRef.value.hideWelcomeMessage();

// Siguiente tip
multipsRef.value.nextTip();

// Obtener tip actual
const currentTip = multipsRef.value.getCurrentTip();

// Obtener sugerencia actual
const currentSuggestion = multipsRef.value.getCurrentSuggestion();
```

## Casos de Uso

### ✅ Usar cuando:

- Se necesita llenar espacio vacío en el sidebar
- Se quiere proporcionar valor al usuario con tips útiles
- Se busca mantener al usuario motivado
- Se necesita mostrar sugerencias contextuales

### ✅ Integración ideal:

- Junto a `SidebarCardInmuebles` con estilo `clean`
- En áreas donde antes iban los desarrollos
- Como complemento informativo del dashboard

## Compatibilidad

- ✅ **Vue 3**: Composition API
- ✅ **TypeScript**: Tipado completo
- ✅ **Tailwind CSS**: Clases de utilidad
- ✅ **Ionic Icons**: Íconos consistentes
- ✅ **Responsive**: Adaptativo a diferentes pantallas
- ✅ **Accesibilidad**: Manejo apropiado de eventos y focus
