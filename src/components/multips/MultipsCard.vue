<template>
  <div class="mb-6">
    <div :class="containerClasses">
      <!-- Header con tí<PERSON><PERSON> e <PERSON>cono -->
      <div class="flex justify-between items-center mb-3">
        <h2 class="text-sm font-medium text-gray-700">Tips & Sugerencias</h2>
        <ion-icon name="bulb-outline" :class="iconClasses"></ion-icon>
      </div>

      <!-- Contenido principal -->
      <div class="space-y-4">
        <!-- Tip del día -->
        <div
          v-if="currentTip"
          class="p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border-l-4 border-blue-400"
        >
          <div class="flex items-start space-x-2">
            <ion-icon
              name="information-circle"
              class="flex-shrink-0 mt-0.5 text-lg text-blue-500"
            ></ion-icon>
            <div class="flex-1">
              <h4 class="mb-1 text-xs font-semibold text-blue-800">
                Tip del día
              </h4>
              <p class="text-xs leading-relaxed text-blue-700">
                {{ currentTip.content }}
              </p>
            </div>
          </div>
        </div>

        <!-- Felicitación de cumpleaños -->
        <div
          v-if="birthdayMessage"
          class="p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border-l-4 border-green-400"
        >
          <div class="flex items-start space-x-2">
            <ion-icon
              name="gift"
              class="flex-shrink-0 mt-0.5 text-lg text-green-500"
            ></ion-icon>
            <div class="flex-1">
              <h4 class="mb-1 text-xs font-semibold text-green-800">
                ¡Feliz Cumpleaños! 🎉
              </h4>
              <p class="text-xs leading-relaxed text-green-700">
                {{ birthdayMessage }}
              </p>
            </div>
          </div>
        </div>

        <!-- Sugerencia de mejora -->
        <div
          v-if="currentSuggestion && showSuggestions"
          class="p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border-l-4 border-purple-400"
        >
          <div class="flex items-start space-x-2">
            <ion-icon
              name="trending-up"
              class="flex-shrink-0 mt-0.5 text-lg text-purple-500"
            ></ion-icon>
            <div class="flex-1">
              <h4 class="mb-1 text-xs font-semibold text-purple-800">
                Sugerencia
              </h4>
              <p class="text-xs leading-relaxed text-purple-700">
                {{ currentSuggestion.content }}
              </p>
              <button
                v-if="currentSuggestion.action"
                @click="handleSuggestionAction(currentSuggestion)"
                class="mt-2 text-xs font-medium text-purple-600 underline hover:text-purple-700"
              >
                {{ currentSuggestion.action.text }}
              </button>
            </div>
          </div>
        </div>

        <!-- Estadística motivacional -->
        <div
          v-if="motivationalStats"
          class="p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border-l-4 border-orange-400"
        >
          <div class="flex items-start space-x-2">
            <ion-icon
              name="trophy"
              class="flex-shrink-0 mt-0.5 text-lg text-orange-500"
            ></ion-icon>
            <div class="flex-1">
              <h4 class="mb-1 text-xs font-semibold text-orange-800">
                ¡Excelente trabajo!
              </h4>
              <p class="text-xs leading-relaxed text-orange-700">
                {{ motivationalStats.message }}
              </p>
              <div class="mt-2 text-lg font-bold text-orange-600">
                {{ motivationalStats.value }}
              </div>
            </div>
          </div>
        </div>

        <!-- Mensaje de bienvenida para nuevos usuarios -->
        <div
          v-if="showWelcomeMessage"
          class="p-3 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg border-l-4 border-indigo-400"
        >
          <div class="flex items-start space-x-2">
            <ion-icon
              name="hand-left"
              class="flex-shrink-0 mt-0.5 text-lg text-indigo-500"
            ></ion-icon>
            <div class="flex-1">
              <h4 class="mb-1 text-xs font-semibold text-indigo-800">
                ¡Bienvenido!
              </h4>
              <p class="text-xs leading-relaxed text-indigo-700">
                Estamos aquí para ayudarte a gestionar tus inmuebles de la mejor
                manera.
              </p>
              <button
                @click="dismissWelcome"
                class="mt-2 text-xs font-medium text-indigo-600 underline hover:text-indigo-700"
              >
                Entendido
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer con enlace a más tips -->
      <div class="pt-3 mt-4 border-t border-gray-200">
        <button
          @click="showMoreTips"
          class="w-full text-xs font-medium text-gray-600 transition-colors hover:text-gray-800"
        >
          Ver más tips y sugerencias →
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";

interface Props {
  uiStyle?: string;
  showBirthday?: boolean;
  showWelcome?: boolean;
  showMotivationalStats?: boolean;
  showSuggestions?: boolean;
  autoRotate?: boolean;
  rotationInterval?: number;
  userBirthdayDate?: string; // Formato: "YYYY-MM-DD" o "MM-DD"
  userTimezone?: string; // Zona horaria, ej: "America/Mexico_City", "UTC-6", etc.
  customTips?: Tip[];
  customSuggestions?: Suggestion[];
}

interface Tip {
  id: string;
  content: string;
  category: "general" | "inmuebles" | "marketing" | "finanzas";
  priority: "low" | "medium" | "high";
}

interface Suggestion {
  id: string;
  content: string;
  action?: {
    text: string;
    url?: string;
    callback?: () => void;
  };
}

// Props con valores por defecto
const props = withDefaults(defineProps<Props>(), {
  uiStyle: "clean",
  showBirthday: true,
  showWelcome: true,
  showMotivationalStats: true,
  showSuggestions: true,
  autoRotate: true,
  rotationInterval: 30000, // 30 segundos
  userBirthdayDate: undefined, // No pasar por defecto, permitir que sea undefined
  userTimezone: undefined, // Detectar automáticamente si no se especifica
  customTips: () => [],
  customSuggestions: () => [],
});

// Emits
const emit = defineEmits<{
  tipClicked: [tip: Tip];
  suggestionAction: [suggestion: Suggestion];
  welcomeDismissed: [];
}>();

// Estado reactivo
const showWelcomeMessage = ref<boolean>(props.showWelcome);
const currentTipIndex = ref<number>(0);
const currentSuggestionIndex = ref<number>(0);

// Debug: Log de props para verificar valores
console.log("🔍 MultipsCard props:", {
  showBirthday: props.showBirthday,
  showWelcome: props.showWelcome,
  showMotivationalStats: props.showMotivationalStats,
  showSuggestions: props.showSuggestions,
});

// Tips predefinidos
const defaultTips: Tip[] = [
  {
    id: "tip-1",
    content:
      "Mantén actualizadas las fotos de tus inmuebles para aumentar el interés de los compradores.",
    category: "marketing",
    priority: "high",
  },
  {
    id: "tip-2",
    content:
      "Responde rápidamente a las consultas de los interesados para mejorar la tasa de conversión.",
    category: "inmuebles",
    priority: "medium",
  },
  {
    id: "tip-3",
    content: "Considera ofrecer tours virtuales para inmuebles de alto valor.",
    category: "marketing",
    priority: "medium",
  },
  {
    id: "tip-4",
    content:
      "Revisa regularmente los precios del mercado para mantener competitividad.",
    category: "finanzas",
    priority: "high",
  },
  {
    id: "tip-5",
    content:
      "Documenta todas las mejoras realizadas en tus propiedades para justificar el precio.",
    category: "inmuebles",
    priority: "medium",
  },
];

// Sugerencias predefinidas
const defaultSuggestions: Suggestion[] = [
  {
    id: "suggestion-1",
    content:
      "¿Has considerado agregar más fotos a tus inmuebles? Esto puede aumentar el interés en un 40%.",
    action: {
      text: "Ver tutorial de fotos",
      url: "/tutoriales/fotografia-inmuebles",
    },
  },
  {
    id: "suggestion-2",
    content:
      "Tu perfil está al 75% completo. Completarlo puede mejorar tu visibilidad.",
    action: {
      text: "Completar perfil",
      url: "/perfil/completar",
    },
  },
  {
    id: "suggestion-3",
    content:
      "Basado en tu actividad, podrías beneficiarte de nuestro plan premium.",
    action: {
      text: "Ver planes",
      url: "/planes",
    },
  },
];

// Combinar tips personalizados con los predeterminados
const allTips = computed(() => [...props.customTips, ...defaultTips]);
const allSuggestions = computed(() => [
  ...props.customSuggestions,
  ...defaultSuggestions,
]);

// Computed properties
const currentTip = computed(() => {
  if (allTips.value.length === 0) return null;
  return allTips.value[currentTipIndex.value % allTips.value.length];
});

const currentSuggestion = computed(() => {
  if (allSuggestions.value.length === 0) return null;
  return allSuggestions.value[
    currentSuggestionIndex.value % allSuggestions.value.length
  ];
});

const birthdayMessage = computed(() => {
  if (!props.showBirthday) {
    console.log(
      "🔍 showBirthday es false, no se muestra mensaje de cumpleaños"
    );
    return null;
  }

  const today = new Date();
  const userBirthday = getUserBirthday(); // Función que obtiene el cumpleaños del usuario

  console.log("🔍 Debug cumpleaños:", {
    today: today.toDateString(),
    userBirthday: userBirthday?.toDateString(),
    isBirthday: isUserBirthday(today, userBirthday),
  });

  if (isUserBirthday(today, userBirthday)) {
    return "¡Que tengas un día maravilloso! Aprovecha para revisar tus inmuebles y hacer planes para el futuro.";
  }

  return null;
});

const motivationalStats = computed(() => {
  // Simular estadísticas motivacionales
  const stats = getMotivationalStats();

  if (props.showMotivationalStats && stats.inmueblesCount > 0) {
    return {
      message: `Tienes ${stats.inmueblesCount} inmuebles registrados. ¡Sigue así!`,
      value: `${stats.inmueblesCount} inmuebles`,
    };
  }

  return null;
});

// Computed para las clases del contenedor según el estilo de UI
const containerClasses = computed(() => {
  const baseClasses = "p-4 rounded-lg";

  switch (props.uiStyle) {
    case "clean":
      return `${baseClasses} bg-transparent border-none shadow-none`;
    case "mulbin":
      return `${baseClasses} bg-gradient-to-r from-mulbin-50 to-mulbin-100 border border-mulbin-200`;
    case "blue":
      return `${baseClasses} bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200`;
    case "red":
      return `${baseClasses} bg-gradient-to-r from-red-50 to-red-100 border border-red-200`;
    case "green":
      return `${baseClasses} bg-gradient-to-r from-green-50 to-green-100 border border-green-200`;
    default:
      return `${baseClasses} bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200`;
  }
});

// Computed para las clases del ícono según el estilo de UI
const iconClasses = computed(() => {
  switch (props.uiStyle) {
    case "clean":
      return "text-lg text-gray-600";
    case "mulbin":
      return "text-lg text-mulbin-600";
    case "blue":
      return "text-lg text-blue-600";
    case "red":
      return "text-lg text-red-600";
    case "green":
      return "text-lg text-green-600";
    default:
      return "text-lg text-blue-600";
  }
});

// Métodos
const handleSuggestionAction = (suggestion: Suggestion) => {
  emit("suggestionAction", suggestion);

  if (suggestion.action?.callback) {
    suggestion.action.callback();
  } else if (suggestion.action?.url) {
    window.location.href = suggestion.action.url;
  }
};

const dismissWelcome = () => {
  showWelcomeMessage.value = false;
  emit("welcomeDismissed");

  // Guardar en localStorage para no mostrar de nuevo
  localStorage.setItem("multips-welcome-dismissed", "true");
};

const showMoreTips = () => {
  // Rotar al siguiente tip
  currentTipIndex.value = (currentTipIndex.value + 1) % allTips.value.length;
  currentSuggestionIndex.value =
    (currentSuggestionIndex.value + 1) % allSuggestions.value.length;
};

// Función auxiliar para parsear fechas considerando zona horaria
const parseBirthdayDate = (dateString: string): Date | null => {
  try {
    console.log("🔍 Parseando fecha:", dateString);
    console.log("🌍 Zona horaria especificada:", props.userTimezone);

    // Si el formato es solo MM-DD, agregar el año actual
    let fullDateString = dateString;
    if (dateString.match(/^\d{2}-\d{2}$/)) {
      const currentYear = new Date().getFullYear();
      fullDateString = `${currentYear}-${dateString}`;
      console.log("📅 Fecha expandida con año actual:", fullDateString);
    }

    // Parsear como fecha local para evitar problemas de zona horaria
    // En lugar de new Date("2025-08-22") que es UTC
    // Usamos new Date(2025, 7, 22) que es local (mes 0-indexed)
    const parts = fullDateString.split("-");
    if (parts.length === 3) {
      const year = parseInt(parts[0]);
      const month = parseInt(parts[1]) - 1; // Mes 0-indexed
      const day = parseInt(parts[2]);

      const birthday = new Date(year, month, day);

      console.log("🔍 Debug detallado del parseo de fecha:", {
        original: dateString,
        fullDateString: fullDateString,
        parts: { year, month: month + 1, day }, // Mostrar mes 1-indexed para claridad
        parsed: birthday,
        toDateString: birthday.toDateString(),
        getFullYear: birthday.getFullYear(),
        getMonth: birthday.getMonth() + 1,
        getDate: birthday.getDate(),
        userTimezone: props.userTimezone,
        systemTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: birthday.getTimezoneOffset(),
      });

      return birthday;
    } else {
      // Fallback al método original si no es formato YYYY-MM-DD
      const birthday = new Date(dateString);
      if (!isNaN(birthday.getTime())) {
        console.log("📅 Usando parseo fallback:", birthday.toDateString());
        return birthday;
      }
    }
  } catch (error) {
    console.error("❌ Error al parsear fecha:", error);
  }

  return null;
};

// Funciones auxiliares
const getUserBirthday = (): Date | null => {
  // Si se pasa la fecha por props, usarla
  if (props.userBirthdayDate) {
    const birthday = parseBirthdayDate(props.userBirthdayDate);
    if (birthday) {
      console.log(
        "🎂 Usando fecha de cumpleaños de props:",
        birthday.toDateString()
      );
      return birthday;
    } else {
      console.warn(
        "⚠️ Fecha de cumpleaños inválida en props:",
        props.userBirthdayDate
      );
    }
  }

  // En una implementación real, esto vendría de la API del usuario
  // Por ahora, simulamos un cumpleaños
  const stored = localStorage.getItem("user-birthday");
  if (stored) {
    return new Date(stored);
  }

  // Simular cumpleaños del usuario (ejemplo: 15 de marzo)
  const birthday = new Date();
  birthday.setMonth(2); // Marzo (0-indexed)
  birthday.setDate(15);

  return birthday;
};

const isUserBirthday = (today: Date, birthday: Date | null): boolean => {
  if (!birthday) return false;

  return (
    today.getMonth() === birthday.getMonth() &&
    today.getDate() === birthday.getDate()
  );
};

const getMotivationalStats = () => {
  // En una implementación real, esto vendría de la API
  // Por ahora, simulamos estadísticas
  return {
    inmueblesCount: Math.floor(Math.random() * 50) + 1, // 1-50 inmuebles
    visitsThisMonth: Math.floor(Math.random() * 200) + 10,
    inquiriesThisMonth: Math.floor(Math.random() * 20) + 1,
  };
};

// Rotación automática de tips
let tipRotationInterval: ReturnType<typeof setInterval> | null = null;

const startTipRotation = () => {
  tipRotationInterval = setInterval(() => {
    currentTipIndex.value = (currentTipIndex.value + 1) % allTips.value.length;
  }, props.rotationInterval || 30000); // Usar el intervalo de props o el por defecto
};

const stopTipRotation = () => {
  if (tipRotationInterval) {
    clearInterval(tipRotationInterval);
    tipRotationInterval = null;
  }
};

// Lifecycle hooks
onMounted(() => {
  console.log("🎉 MultipsCard montado - onMounted ejecutado");
  console.log("🔍 Props recibidas:", {
    uiStyle: props.uiStyle,
    showBirthday: props.showBirthday,
    showWelcome: props.showWelcome,
    showMotivationalStats: props.showMotivationalStats,
    showSuggestions: props.showSuggestions,
    userBirthdayDate: props.userBirthdayDate,
  });

  // Verificar si ya se mostró el mensaje de bienvenida
  const welcomeDismissed = localStorage.getItem("multips-welcome-dismissed");
  if (welcomeDismissed === "true") {
    showWelcomeMessage.value = false;
    console.log("🔍 Mensaje de bienvenida descartado anteriormente");
  } else {
    console.log("🔍 Mostrando mensaje de bienvenida");
  }

  // Iniciar rotación automática de tips si está habilitado
  if (props.autoRotate) {
    console.log("🔄 Iniciando rotación automática de tips");
    startTipRotation();
  } else {
    console.log("⏸️ Rotación automática deshabilitada");
  }

  console.log("✅ MultipsCard inicializado completamente");
});

onUnmounted(() => {
  stopTipRotation();
});

// Exponer métodos públicos del componente
defineExpose({
  showWelcomeMessage: () => (showWelcomeMessage.value = true),
  hideWelcomeMessage: () => (showWelcomeMessage.value = false),
  nextTip: () => showMoreTips(),
  getCurrentTip: () => currentTip.value,
  getCurrentSuggestion: () => currentSuggestion.value,
});
</script>

<style scoped>
/* Transiciones suaves */
.space-y-4 > div {
  transition: all 0.3s ease;
}

.space-y-4 > div:hover {
  transform: translateY(-1px);
}

/* Animaciones para los íconos */
ion-icon {
  transition: transform 0.2s ease;
}

.space-y-4 > div:hover ion-icon {
  transform: scale(1.1);
}

/* Efectos de hover en botones */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

/* Gradientes personalizados para mejor contraste */
.bg-gradient-to-r {
  background-size: 200% 100%;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .space-y-4 > div {
    padding: 0.75rem;
  }

  .text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}
</style>
