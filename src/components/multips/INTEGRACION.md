# Integración de MultipsCard

## Descripción

Este documento explica cómo integrar el componente `MultipsCard` en el dashboard para reemplazar temporalmente el área de desarrollos, proporcionando tips, sugerencias y contenido motivacional al usuario.

## Ubicación de Integración

El componente debe integrarse en el archivo:

```
panel4-templates/src/entries/inmueble/home/<USER>/dashboard.htm
```

## Implementación

### 1. Reemplazar la sección de desarrollos

```html
<!-- ANTES: Sección de desarrollos -->
{{#data.desarrollos.exists}}
<section>
  <h2>Edición de desarrollos</h2>
  <div>{{> reg_edit_desarrollos}}</div>
</section>
{{/data.desarrollos.exists}}

<!-- DESPUÉS: Sección de tips y sugerencias -->
<section class="multips-section">
  <div
    data-multips-card
    data-ui-style="clean"
    data-show-birthday="true"
    data-show-welcome="true"
    data-show-motivational-stats="true"
    class="multips-container"
  >
    <div class="flex justify-center items-center p-8">
      <div
        class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
      ></div>
      <span class="ml-2 text-sm text-gray-600">Cargando tips...</span>
    </div>
  </div>
</section>
```

### 2. Estructura completa del dashboard

```html
<main>
  <header>
    <h1>{{ title }}</h1>
  </header>

  <div class="dashboard contenedor min-300">
    <!-- Componente de inmuebles -->
    <section
      data-sidebar-card-inmuebles
      data-total-properties="{{ num_inmuebles }}"
      data-api-endpoint="/msi-v5/owner/inmuebles"
      data-placeholder="Clave del inmueble"
      data-status="all"
      data-ui-style="clean"
      class="sidebar-card-inmuebles-container"
    >
      <div class="flex justify-center items-center p-8">
        <div
          class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
        ></div>
        <span class="ml-2 text-sm text-gray-600">Cargando inmuebles...</span>
      </div>
    </section>

    <!-- Componente de tips y sugerencias (reemplaza desarrollos) -->
    <section class="multips-section">
      <div
        data-multips-card
        data-ui-style="clean"
        data-show-birthday="true"
        data-show-welcome="true"
        data-show-motivational-stats="true"
        class="multips-container"
      >
        <div class="flex justify-center items-center p-8">
          <div
            class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
          ></div>
          <span class="ml-2 text-sm text-gray-600">Cargando tips...</span>
        </div>
      </div>
    </section>
  </div>

  <!-- Resto de secciones del dashboard -->
  <div class="dashboard">
    <section>
      <h2>Listado e impresión de inmuebles</h2>
      <div>{{> listado_impresion}}</div>
    </section>
  </div>

  <div class="dashboard">
    <section>
      <h2>Personalizar ficha de llenado de inmuebles</h2>
      <div>{{> personaliza_ficha}}</div>
    </section>
  </div>

  <div class="dashboard">
    <section>
      <h2>Publicación externa</h2>
      <div>{{> publicacion_externa}}</div>
    </section>
  </div>
</main>
```

## Configuración del Componente

### Atributos de datos disponibles

```html
<div data-multips-card data-ui-style="clean" <!-- Estilo visual -->
  data-show-birthday="true"
  <!-- Mostrar felicitaciones -->
  data-show-welcome="true"
  <!-- Mostrar bienvenida -->
  data-show-motivational-stats="true"
  <!-- Mostrar estadísticas -->
  data-auto-rotate="true"
  <!-- Rotación automática -->
  data-rotation-interval="30000"
  <!-- Intervalo en ms -->
  class="multips-container" >
</div>
```

### Estilos de UI disponibles

- `clean` - Transparente, sin bordes (recomendado para fondos personalizados)
- `blue` - Estilo azul estándar
- `mulbin` - Colores de marca Mulbin
- `red` - Variante roja
- `green` - Variante verde

## Estilos CSS Adicionales

### Para el contenedor principal

```css
.multips-section {
  margin-bottom: 1.5rem;
}

.multips-container {
  /* El componente se auto-estiliza */
}

/* Ajustes responsive */
@media (max-width: 768px) {
  .multips-section {
    margin-bottom: 1rem;
  }
}
```

### Integración con Tailwind

Si usas Tailwind CSS, puedes agregar clases adicionales:

```html
<section class="multips-section mb-6">
  <div
    data-multips-card
    data-ui-style="clean"
    class="multips-container rounded-lg"
  >
    <!-- Loading placeholder -->
  </div>
</section>
```

## Comportamiento del Componente

### 1. Carga inicial

- Muestra un spinner de carga
- Carga tips y sugerencias predefinidos
- Verifica si es cumpleaños del usuario
- Muestra mensaje de bienvenida para nuevos usuarios

### 2. Funcionamiento normal

- Rotación automática de tips cada 30 segundos
- Sugerencias contextuales basadas en el perfil
- Estadísticas motivacionales
- Interacciones del usuario (clic en sugerencias, descartar bienvenida)

### 3. Estados del componente

- **Loading**: Mientras se inicializa
- **Active**: Mostrando contenido
- **Empty**: Sin tips disponibles
- **Error**: Si hay problemas de carga

## Personalización Avanzada

### Tips personalizados

```javascript
// En tu archivo de configuración
window.multipsConfig = {
  customTips: [
    {
      id: "custom-1",
      content: "Tu tip personalizado aquí",
      category: "inmuebles",
      priority: "high",
    },
  ],
  customSuggestions: [
    {
      id: "custom-1",
      content: "Tu sugerencia personalizada",
      category: "marketing",
      priority: "medium",
      action: {
        text: "Ver más",
        url: "/tu-url",
        type: "link",
      },
    },
  ],
};
```

### Integración con API

```javascript
// Obtener tips desde tu API
fetch("/api/tips")
  .then((response) => response.json())
  .then((tips) => {
    // Los tips se mostrarán automáticamente
    console.log("Tips cargados:", tips);
  });
```

## Eventos y Callbacks

### Escuchar eventos del componente

```javascript
// En tu archivo principal
document.addEventListener("tipClicked", (event) => {
  const tip = event.detail;
  console.log("Tip clickeado:", tip);

  // Analytics
  trackEvent("tip_clicked", {
    tip_id: tip.id,
    category: tip.category,
  });
});

document.addEventListener("suggestionAction", (event) => {
  const suggestion = event.detail;
  console.log("Acción de sugerencia:", suggestion);

  // Navegación o acción personalizada
  if (suggestion.action?.url) {
    window.location.href = suggestion.action.url;
  }
});
```

## Compatibilidad

### Navegadores soportados

- ✅ Chrome 88+
- ✅ Firefox 85+
- ✅ Safari 14+
- ✅ Edge 88+

### Frameworks compatibles

- ✅ Vue 3
- ✅ Vanilla JavaScript
- ✅ Cualquier framework que soporte Web Components

## Troubleshooting

### Problemas comunes

1. **El componente no se renderiza**

   - Verificar que el archivo `MultipsCard.vue` esté disponible
   - Comprobar que no hay errores en la consola del navegador

2. **Los tips no rotan**

   - Verificar que `data-auto-rotate="true"`
   - Comprobar que no hay conflictos de JavaScript

3. **Estilos no se aplican**
   - Verificar que Tailwind CSS esté cargado
   - Comprobar que el atributo `data-ui-style` sea válido

### Debug

```javascript
// Habilitar logs de debug
localStorage.setItem("multips-debug", "true");

// Ver estado del componente
console.log("Multips config:", window.multipsConfig);
```

## Próximos Pasos

1. **Implementar** el componente en el dashboard
2. **Personalizar** tips y sugerencias según tu negocio
3. **Integrar** con tu sistema de analytics
4. **Monitorear** el engagement de los usuarios
5. **Iterar** basado en feedback y métricas
