import type {
  Property,
  PropertySearchParams,
  PropertySearchResponse,
} from "../types";

// Tipos para respuesta de msi-v5 (simplificados para los campos disponibles)
interface MSIInmueble {
  key: string; // "aa2" - clave interna
  titulo: string; // "Inmueble en Altavista"
  imagenPrincipal: string | null; // URL de la imagen (puede ser null)
}

interface MSIInmueblesResponse {
  statusCode: number;
  data: {
    inmuebles: MSIInmueble[];
    limit: number;
    page: number;
    total: number;
    total_pages: number;
  };
}

class PropertyService {
  // Convertir inmueble de msi-v5 a formato del componente (simplificado)
  private mapMSIInmuebleToProperty(inmueble: MSIInmueble): Property {
    // Manejar imagenPrincipal que puede ser null
    let image = "/images/placeholder-property.jpg"; // Default fallback

    if (inmueble.imagenPrincipal && inmueble.imagenPrincipal.trim() !== "") {
      // Si existe la imagenPrincipal debo sustituir `/alta/` por `/peque/` para mostrar una resolución adecuada de la imagen
      image = inmueble.imagenPrincipal.replace("/alta/", "/peque/");
    }

    return {
      key: inmueble.key,
      name: inmueble.titulo,
      image: image,
    };
  }

  // Construir headers con autenticación
  private getAuthHeaders(): HeadersInit {
    return {
      "Content-Type": "application/json",
      Accept: "application/json",
      // La autenticación se maneja via cookies/session por el nginx proxy
      "X-Requested-With": "XMLHttpRequest",
    };
  }

  // Buscar propiedades usando el endpoint real de msi-v5
  async searchProperties(
    params: PropertySearchParams,
    endpoint: string = "/msi-v5/owner/inmuebles"
  ): Promise<PropertySearchResponse> {
    try {
      const queryParams = new URLSearchParams();

      // Mapear parámetros del componente a msi-v5 (basado en respuesta real)
      if (params.query) {
        queryParams.append("busqueda", params.query);
      }
      if (params.limit) {
        queryParams.append("limite", params.limit.toString());
      }
      // Defino los campos a recuperar, el parámetro es `fields` y es un array de strings
      queryParams.append("fields[]", "key");
      queryParams.append("fields[]", "titulo");
      queryParams.append("fields[]", "imagenPrincipal");
      // Solo estos campos están disponibles en el backend
      if (params.status) {
        queryParams.append("status", params.status);
      }

      const url = `${endpoint}?${queryParams.toString()}`;

      const response = await fetch(url, {
        method: "GET",
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const msiResponse: MSIInmueblesResponse = await response.json();

      // Convertir respuesta de msi-v5 a formato del componente
      const properties = msiResponse.data.inmuebles.map((inmueble) =>
        this.mapMSIInmuebleToProperty(inmueble)
      );

      return {
        properties,
        total: msiResponse.data.total,
        page: msiResponse.data.page,
        limit: msiResponse.data.limit,
        hasMore: msiResponse.data.page < msiResponse.data.total_pages,
      };
    } catch (error) {
      console.error("Error fetching properties from msi-v5:", error);

      // Fallback a datos dummy en caso de error
      console.warn("Falling back to dummy data due to API error");
      return this.searchPropertiesDummy(params);
    }
  }

  // Buscar propiedad por clave (ya no tenemos ID numerico)
  async getPropertyById(id: number): Promise<Property | null> {
    try {
      // Convertir ID a string y buscar como clave
      const searchResult = await this.searchProperties({
        query: id.toString(),
        limit: 1,
      });

      if (searchResult.properties.length > 0) {
        return searchResult.properties[0];
      }

      return null;
    } catch (error) {
      console.error("Error fetching property by ID from msi-v5:", error);
      return null;
    }
  }

  // Obtener propiedad por clave usando búsqueda
  async getPropertyByKey(key: string): Promise<Property | null> {
    try {
      const searchResult = await this.searchProperties({
        query: key,
        limit: 1,
      });

      const exactMatch = searchResult.properties.find(
        (p) => p.key.toLowerCase() === key.toLowerCase()
      );

      return exactMatch || null;
    } catch (error) {
      console.error("Error fetching property by key from msi-v5:", error);
      return null;
    }
  }

  // Obtener estadísticas usando endpoint de msi-v5
  async getPropertyStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    sold: number;
  }> {
    try {
      // Usar el endpoint principal sin filtros para obtener totales
      const allProperties = await this.searchProperties({ limit: 1 });
      const total = allProperties.total;

      // Ya no tenemos estados, todos los inmuebles son "activos"
      return {
        total,
        active: total,
        inactive: 0,
        sold: 0,
      };
    } catch (error) {
      console.error("Error fetching property stats from msi-v5:", error);

      // Fallback a stats dummy
      return {
        total: 0,
        active: 0,
        inactive: 0,
        sold: 0,
      };
    }
  }

  // Mantener método dummy como fallback
  private async searchPropertiesDummy(
    params: PropertySearchParams
  ): Promise<PropertySearchResponse> {
    // Datos dummy originales como fallback
    const DUMMY_PROPERTIES: Property[] = [];

    // Simular latencia
    await new Promise((resolve) => setTimeout(resolve, 300));

    let filtered = [...DUMMY_PROPERTIES];

    // Aplicar filtros básicos
    if (params.query) {
      const query = params.query.toLowerCase();
      filtered = filtered.filter(
        (property) =>
          property.key.toLowerCase().includes(query) ||
          property.name.toLowerCase().includes(query)
      );
    }

    // Paginación
    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedResults = filtered.slice(startIndex, endIndex);

    return {
      properties: paginatedResults,
      total: filtered.length,
      page,
      limit,
      hasMore: endIndex < filtered.length,
    };
  }

  // Método mantenido por compatibilidad pero sin funcionalidad
  setBaseUrl(_url: string): void {
    // No longer needed since we don't store baseUrl
  }

  // Método legacy para compatibilidad
  getAllPropertiesDummy(): Property[] {
    return [];
  }
}

// Instancia singleton del servicio
export const propertyService = new PropertyService();

// Función helper para formatear respuestas de error
export const handlePropertyServiceError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === "string") {
    return error;
  }
  return "Error desconocido en el servicio de propiedades";
};
