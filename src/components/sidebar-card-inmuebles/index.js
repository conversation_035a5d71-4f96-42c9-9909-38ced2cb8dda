// Este archivo inicializa el componente de sidebar card inmuebles y lo monta en el DOM
import { createApp } from "vue";
import SidebarCardInmuebles from "./SidebarCardInmuebles.vue";

console.log("🚀 Inicializando SidebarCardInmuebles...");

// Función para montar el componente en un elemento específico
function mountSidebarCardInmueblesComponent(
  elementSelector = "[data-sidebar-card-inmuebles]"
) {
  const targetElement = document.querySelector(elementSelector);

  if (!targetElement) {
    console.error(
      `No se encontró el elemento con selector: ${elementSelector}`
    );
    return;
  }

  // Obtener props desde data-attributes
  const totalProperties = parseInt(
    targetElement.getAttribute("data-total-properties") || "0"
  );
  const apiEndpoint =
    targetElement.getAttribute("data-api-endpoint") ||
    "/msi-v5/owner/inmuebles";
  const placeholder =
    targetElement.getAttribute("data-placeholder") || "Clave del inmueble";
  const disabled = targetElement.getAttribute("data-disabled") === "true";
  const status = targetElement.getAttribute("data-status") || "publicado";
  const uiStyle = targetElement.getAttribute("data-ui-style") || "default";

  console.log("🏗️ Inicializando SidebarCardInmuebles con props:", {
    totalProperties,
    apiEndpoint,
    placeholder,
    disabled,
    status,
    uiStyle,
  });

  // Crear un div para contener el componente
  const appContainer = document.createElement("div");
  appContainer.className = "sidebar-card-inmuebles-app";

  // Limpiar el contenido del target element y agregar el container
  targetElement.innerHTML = "";
  targetElement.appendChild(appContainer);

  // Montar el componente directamente
  const app = createApp(SidebarCardInmuebles, {
    totalProperties,
    apiEndpoint,
    placeholder,
    disabled,
    status,
    uiStyle,
  });

  app.mount(appContainer);

  console.log("✅ Componente SidebarCardInmuebles montado correctamente");

  return app;
}

// Inicializar cuando el DOM esté cargado
document.addEventListener("DOMContentLoaded", () => {
  // Cargar Ionicons si no están ya cargados
  if (!document.querySelector('script[src*="ionicons"]')) {
    const moduleScript = document.createElement("script");
    moduleScript.type = "module";
    moduleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js";
    document.head.appendChild(moduleScript);

    const noModuleScript = document.createElement("script");
    noModuleScript.noModule = true;
    noModuleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js";
    document.head.appendChild(noModuleScript);
  }

  // Buscar el elemento específico para SidebarCardInmuebles
  const targetElement = document.querySelector("[data-sidebar-card-inmuebles]");

  if (targetElement) {
    mountSidebarCardInmueblesComponent("[data-sidebar-card-inmuebles]");
  } else {
    console.error(
      "❌ No se encontró el elemento [data-sidebar-card-inmuebles]"
    );
    console.log(
      "💡 Asegúrate de que el HTML contenga un elemento con el atributo data-sidebar-card-inmuebles"
    );
    console.log(
      "📝 Ejemplo: <div data-sidebar-card-inmuebles data-total-properties='0'></div>"
    );
  }
});

// Exponer funciones para uso externo
window.sidebarCardInmueblesMount = mountSidebarCardInmueblesComponent;

export { SidebarCardInmuebles };
