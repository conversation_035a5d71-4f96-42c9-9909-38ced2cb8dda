// Tipos para el componente SidebarCardInmuebles

export interface Property {
  id?: number; // Opcional, se puede derivar del key
  key: string; // Campo principal del backend
  name: string; // titulo del backend -> name del componente
  image: string; // imagenPrincipal del backend
  // Campos simplificados - solo los disponibles del backend
}

export interface PropertySearchResponse {
  properties: Property[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface PropertySearchParams {
  query?: string;
  page?: number;
  limit?: number;
  status?: string;
  // Campos simplificados - solo búsqueda básica
}

export interface SidebarCardInmueblesProps {
  apiEndpoint?: string;
  totalProperties?: number;
  placeholder?: string;
  disabled?: boolean;
  initialProperties?: Property[];
  enableVirtualScrolling?: boolean;
  maxResults?: number;
  status?: string;
}

export interface SidebarCardInmueblesEmits {
  propertySelected: (property: Property) => void;
  propertySubmitted: (property: Property) => void;
  propertyEdit: (property: Property) => void;
  propertyRegister: (query: string) => void;
  searchChanged: (query: string) => void;
  error: (error: Error) => void;
  loading: (isLoading: boolean) => void;
}

// Estados del componente
export interface ComponentState {
  searchQuery: string;
  selectedProperty: Property | null;
  highlightedIndex: number;
  showDropdown: boolean;
  loading: boolean;
  error: string | null;
}

// Utilidades para validación
export const isValidProperty = (property: any): property is Property => {
  return (
    typeof property === "object" &&
    property !== null &&
    typeof property.key === "string" &&
    typeof property.name === "string" &&
    typeof property.image === "string"
  );
};

// Utilidades para formateo y validación de claves
export const formatPropertyKey = (key: string): string => {
  return key.toUpperCase().trim();
};

export const isValidPropertyKey = (key: string): boolean => {
  if (!key || key.trim() === "") return false;

  // Reglas de validación:
  // - No espacios en blanco
  // - Solo letras, números y algunos caracteres especiales permitidos (guiones, guiones bajos)
  // - Longitud mínima de 2 caracteres
  // - Longitud máxima de 20 caracteres
  const validPattern = /^[a-zA-Z0-9_-]{2,20}$/;

  return validPattern.test(key.trim());
};

// Constantes
export const DEFAULT_PLACEHOLDER_IMAGE = "/images/placeholder-property.jpg";
export const MAX_DROPDOWN_ITEMS = 10;
export const DEBOUNCE_DELAY = 300;
