# UI Styles - SidebarCardInmuebles

## Descripción

El componente `SidebarCardInmuebles` ahora soporta diferentes estilos visuales a través del atributo `data-ui-style`. Esto permite adaptarlo a diferentes contextos y diseños.

## Estilos Disponibles

### 🧹 `clean`
**Uso**: Para componentes que se renderizan sobre fondos personalizados
- **Background**: Transparente
- **Border**: Ninguno  
- **Shadow**: Ninguno
- **Colores**: Grises neutros

```html
<div data-sidebar-card-inmuebles data-ui-style="clean"></div>
```

### 🔵 `blue` (default)
**Uso**: Estilo estándar azul
- **Background**: Gradiente azul claro (`blue-50` a `blue-100`)
- **Border**: `border-blue-200`
- **Colores**: Azules (`blue-600`)

```html
<div data-sidebar-card-inmuebles data-ui-style="blue"></div>
```

### 🏢 `mulbin`
**Uso**: Colores de marca Mulbin
- **Background**: Gradiente mulbin claro (`mulbin-50` a `mulbin-100`)
- **Border**: `border-mulbin-200`
- **Colores**: Mulbin (`mulbin-600`)

```html
<div data-sidebar-card-inmuebles data-ui-style="mulbin"></div>
```

### 🔴 `red`
**Uso**: Variante roja
- **Background**: Gradiente rojo claro (`red-50` a `red-100`)
- **Border**: `border-red-200`
- **Colores**: Rojos (`red-600`)

```html
<div data-sidebar-card-inmuebles data-ui-style="red"></div>
```

### 🟢 `green`
**Uso**: Variante verde
- **Background**: Gradiente verde claro (`green-50` a `green-100`)
- **Border**: `border-green-200`
- **Colores**: Verdes (`green-600`)

```html
<div data-sidebar-card-inmuebles data-ui-style="green"></div>
```

## Implementación

### Props TypeScript
```typescript
interface Props {
  uiStyle?: string; // 'clean' | 'blue' | 'mulbin' | 'red' | 'green' | 'default'
}
```

### HTML de Integración
```html
<section 
  data-sidebar-card-inmuebles
  data-total-properties="1250"
  data-api-endpoint="/msi-v5/owner/inmuebles"
  data-placeholder="Clave del inmueble"
  data-status="all"
  data-ui-style="clean"
  class="sidebar-card-inmuebles-container"
>
  <!-- Loading placeholder -->
</section>
```

## Casos de Uso

### ✅ Usar `clean` cuando:
- El componente se renderiza sobre un fondo con imagen
- Se integra en dashboards con estilos personalizados
- Se necesita un look minimalista
- El contenedor padre ya tiene background/border

### ✅ Usar colores específicos cuando:
- Se quiere mantener consistencia con la paleta de la aplicación
- Se necesita diferenciación visual entre secciones
- Se implementa theming por roles o categorías

## Compatibilidad

- ✅ Retrocompatible: componentes sin `data-ui-style` usan el estilo `blue` por defecto
- ✅ Fallback seguro: estilos no reconocidos usan el estilo `blue` por defecto
- ✅ Responsive: todos los estilos mantienen la responsividad del componente

## Testing

Para probar los estilos, usar el archivo `test-ui-styles.html`:

```bash
npm run build
npm run preview
# Visitar: http://localhost:4173/test-ui-styles.html
```