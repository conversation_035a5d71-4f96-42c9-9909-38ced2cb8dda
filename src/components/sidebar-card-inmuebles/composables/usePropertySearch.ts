import { ref, computed, watch } from "vue";
import type {
  Property,
  PropertySearchParams,
  PropertySearchResponse,
} from "../types";
import { propertyService } from "../services/propertyService";

// Función debounce simple (sin dependencias externas)
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T & { cancel: () => void } {
  let timeoutId: NodeJS.Timeout | null = null;

  const debounced = ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => func(...args), delay);
  }) as T & { cancel: () => void };

  debounced.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  return debounced;
}

export function usePropertySearch(
  options: {
    apiEndpoint?: string;
    debounceMs?: number;
    maxResults?: number;
  } = {}
) {
  // Estado reactivo
  const searchQuery = ref<string>("");
  const properties = ref<Property[]>([]);
  const loading = ref<boolean>(false);
  const error = ref<string | null>(null);
  const hasSearched = ref<boolean>(false);

  // Opciones con valores por defecto
  const {
    apiEndpoint = "/api/properties",
    debounceMs = 300,
    maxResults = 10,
  } = options;

  // Propiedades filtradas basadas en la búsqueda local
  const filteredProperties = computed(() => {
    if (!searchQuery.value.trim()) return [];

    const query = searchQuery.value.toLowerCase().trim();
    return properties.value
      .filter(
        (property) =>
          property.key.toLowerCase().includes(query) ||
          property.name.toLowerCase().includes(query)
      )
      .slice(0, maxResults);
  });

  // Función de búsqueda en la API
  const searchPropertiesAPI = async (params: PropertySearchParams) => {
    if (loading.value) return;

    loading.value = true;
    error.value = null;

    try {
      const response = await propertyService.searchProperties(
        {
          ...params,
          limit: maxResults,
        },
        apiEndpoint
      );

      properties.value = response.properties;
      hasSearched.value = true;
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : "Error al buscar propiedades";
      console.error("Error en búsqueda de propiedades:", err);
    } finally {
      loading.value = false;
    }
  };

  // Búsqueda con debounce
  const debouncedSearch = debounce(async (query: string) => {
    if (!query.trim()) {
      properties.value = [];
      hasSearched.value = false;
      return;
    }

    await searchPropertiesAPI({ query: query.trim() });
  }, debounceMs);

  // Watcher para búsqueda automática
  watch(searchQuery, (newQuery) => {
    debouncedSearch(newQuery);
  });

  // Función para cargar propiedades iniciales
  const loadInitialProperties = async () => {
    await searchPropertiesAPI({ limit: maxResults });
  };

  // Función para buscar por clave exacta
  const findPropertyByKey = (key: string): Property | null => {
    return (
      properties.value.find((p) => p.key.toLowerCase() === key.toLowerCase()) ||
      null
    );
  };

  // Función para limpiar búsqueda
  const clearSearch = () => {
    searchQuery.value = "";
    properties.value = [];
    error.value = null;
    hasSearched.value = false;
    debouncedSearch.cancel();
  };

  // Función para refrescar datos
  const refresh = async () => {
    if (searchQuery.value.trim()) {
      await searchPropertiesAPI({ query: searchQuery.value.trim() });
    } else {
      await loadInitialProperties();
    }
  };

  return {
    // Estado reactivo
    searchQuery,
    properties,
    filteredProperties,
    loading,
    error,
    hasSearched,

    // Métodos
    searchPropertiesAPI,
    loadInitialProperties,
    findPropertyByKey,
    clearSearch,
    refresh,

    // Computed properties
    hasResults: computed(() => filteredProperties.value.length > 0),
    isEmpty: computed(
      () => hasSearched.value && filteredProperties.value.length === 0
    ),

    // Utilidades
    cancelPendingSearch: () => debouncedSearch.cancel(),
  };
}
