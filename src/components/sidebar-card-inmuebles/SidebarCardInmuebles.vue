<template>
  <div class="mb-6">
    <div :class="containerClasses">
      <!-- Header motivacional para primer inmueble -->
      <div class="mb-4 text-center" v-if="totalProperties === 0">
        <div class="mb-3">
          <ion-icon name="home" :class="[iconClasses, 'text-3xl']"></ion-icon>
        </div>
        <h2 class="mb-1 text-lg font-bold text-gray-800">
          ¡Registra tu primer inmueble!
        </h2>
      </div>

      <!-- Header normal para usuarios con inmuebles -->
      <div class="flex justify-between items-center mb-2" v-else>
        <h2 class="text-sm font-medium text-gray-700">Inmuebles registrados</h2>
        <ion-icon name="home-outline" :class="iconClasses"></ion-icon>
      </div>
      <div :class="counterClasses" v-if="totalProperties > 0">
        {{ formattedTotalProperties }}
      </div>

      <!-- Formulario de búsqueda -->
      <div class="mb-2">
        <label
          class="text-xs font-medium text-gray-600"
          v-if="totalProperties > 0"
        >
          Registrar clave / Editar clave:
        </label>
        <label class="mb-1 text-sm font-semibold text-blue-700" v-else>
          ✨ Define la clave de tu primer inmueble:
        </label>
        <div class="relative" ref="dropdownContainer">
          <div class="flex space-x-1">
            <!-- Input de búsqueda -->
            <div class="relative flex-1">
              <input
                ref="propertyInput"
                v-model="searchQuery"
                type="text"
                :placeholder="placeholder"
                class="px-2 py-2 pr-8 w-full text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                autocomplete="off"
                @input="handleInput"
                @keydown="handleKeydown"
                @focus="handleFocus"
              />

              <!-- Indicador de carga -->
              <div
                v-if="searchLoading"
                class="absolute right-2 top-1/2 transform -translate-y-1/2"
              >
                <div
                  class="w-4 h-4 rounded-full border-2 border-blue-500 animate-spin border-t-transparent"
                ></div>
              </div>

              <!-- Botón de envío inline (cuando hay dropdown visible y clave válida) -->
              <button
                v-else-if="shouldShowSubmitArrow"
                type="button"
                class="absolute right-2 top-1/2 p-1 text-blue-600 rounded transition-colors transform -translate-y-1/2 hover:text-blue-700 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                :title="buttonText + ' inmueble'"
                @click="handleSubmit"
              >
                <ion-icon
                  name="arrow-forward-outline"
                  class="text-lg"
                ></ion-icon>
              </button>

              <!-- Botón para limpiar (cuando hay texto pero no dropdown) -->
              <button
                v-else-if="
                  searchQuery.trim() && !showDropdown && !searchLoading
                "
                type="button"
                class="absolute right-2 top-1/2 p-1 text-gray-400 rounded transition-colors transform -translate-y-1/2 hover:text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
                title="Limpiar búsqueda"
                @click="clearSearch"
              >
                <ion-icon name="close-outline" class="text-lg"></ion-icon>
              </button>
            </div>

            <!-- Contenedor para la imagen de preview -->
            <Transition name="fade">
              <div
                v-if="selectedProperty"
                class="overflow-hidden w-12 h-9 bg-gray-100 rounded border border-gray-300"
              >
                <img
                  :src="selectedProperty.image"
                  :alt="selectedProperty.name"
                  class="object-cover w-full h-full"
                  @error="handleImageError"
                />
              </div>
            </Transition>
          </div>

          <!-- Dropdown de autocomplete -->
          <Transition name="dropdown">
            <div
              v-if="showDropdown && filteredProperties.length > 0"
              class="overflow-y-auto absolute z-10 mt-1 w-full max-h-32 bg-white rounded-md border border-gray-300 shadow-lg"
            >
              <div
                v-for="(property, index) in filteredProperties"
                :key="property.key"
                :class="[
                  'px-3 py-2 cursor-pointer border-b border-gray-100 last:border-b-0',
                  index === highlightedIndex
                    ? 'bg-blue-100'
                    : 'hover:bg-blue-50',
                ]"
                @click="selectProperty(property)"
                @mouseenter="highlightedIndex = index"
              >
                <div class="flex items-center space-x-2">
                  <img
                    :src="property.image"
                    :alt="property.name"
                    class="object-cover w-8 h-6 rounded border"
                    @error="handleImageError"
                  />
                  <div>
                    <div class="text-sm font-medium text-gray-900">
                      {{ property.key }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ property.name }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Transition>

          <!-- Botón VAMOS -->
          <button
            :disabled="
              !searchQuery.trim() ||
              loading ||
              (!selectedProperty && !isCurrentKeyValid)
            "
            class="px-3 py-2 mt-2 w-full text-xs font-medium text-white bg-blue-600 rounded-md transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed"
            @click="handleSubmit"
          >
            <span v-if="loading">Cargando...</span>
            <span v-else-if="totalProperties === 0"
              >🚀 COMENZAR MI HISTORIA</span
            >
            <span v-else>{{ buttonText }}</span>
          </button>
        </div>
      </div>

      <!-- Enlace a lista de inmuebles (solo si ya tiene inmuebles) -->
      <button
        v-if="totalProperties > 0"
        class="px-3 py-2 mt-3 w-full text-sm font-medium text-blue-600 bg-white rounded-md border border-blue-200 transition-colors hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        @click="handleViewAll"
      >
        LISTAR MIS INMUEBLES REGISTRADOS
      </button>

      <!-- Mensaje para primer inmueble -->
      <div
        v-else
        class="p-3 mt-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200"
      >
        <div class="flex items-center space-x-2 text-sm text-amber-700">
          <ion-icon name="bulb" class="text-lg"></ion-icon>
          <span class="font-medium">¡Publica tu primer inmueble!</span>
        </div>
        <p class="mt-1 text-xs text-amber-600">
          Aprovecha todas las herramientas de colaboración que te ofrece nuestra
          plataforma.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";
import { propertyService } from "./services/propertyService";
import type { Property } from "./types";
import { isValidPropertyKey } from "./types";

interface Props {
  apiEndpoint?: string;
  totalProperties?: number;
  placeholder?: string;
  disabled?: boolean;
  status?: string;
  uiStyle?: string;
}

// Props con valores por defecto
const props = withDefaults(defineProps<Props>(), {
  apiEndpoint: "/msi-v5/owner/inmuebles",
  totalProperties: 0, // Se obtendrá dinámicamente
  placeholder: "Ej: CASA123, DEPTO001",
  disabled: false,
  status: "publicado",
  uiStyle: "default",
});

// Emits
const emit = defineEmits<{
  propertySelected: [property: Property];
  propertySubmitted: [property: Property];
  propertyEdit: [property: Property];
  propertyRegister: [query: string];
  searchChanged: [query: string];
}>();

// Refs para elementos del DOM
const propertyInput = ref<HTMLInputElement>();
const dropdownContainer = ref<HTMLElement>();

// Estado reactivo
const searchQuery = ref<string>("");
const selectedProperty = ref<Property | null>(null);
const highlightedIndex = ref<number>(-1);
const showDropdown = ref<boolean>(false);
const loading = ref<boolean>(false);
const searchLoading = ref<boolean>(false);
const totalPropertiesComputed = ref<number>(props.totalProperties);

// Datos de propiedades obtenidos de la API
const propertiesData = ref<Property[]>([]);

// Debounce para búsqueda
let searchTimeout: number | null = null;

// Función de validación importada de types.ts

// Función para construir URL de navegación
const buildInmuebleUrl = (claveprop: string): string => {
  const baseUrl = "/inmueble.php";
  const params = new URLSearchParams({
    ppinmueble: "", // vacío
    paso: "1",
    padesarrollo: "", // vacío
    claveprop: claveprop,
  });

  return `${baseUrl}?${params.toString()}`;
};

// Computed properties
const filteredProperties = computed(() => {
  return propertiesData.value;
});

const totalProperties = computed(() => {
  return totalPropertiesComputed.value || props.totalProperties;
});

// Número de inmuebles con separador de miles por comas
const formattedTotalProperties = computed(() => {
  const value = totalProperties.value ?? 0;
  return Number.isFinite(value) ? value.toLocaleString("es-MX") : "0";
});

// Computed para las clases del contenedor según el estilo de UI
const containerClasses = computed(() => {
  const baseClasses = "p-4 rounded-lg";

  switch (props.uiStyle) {
    case "clean":
      return `${baseClasses} bg-transparent border-none shadow-none`;
    case "mulbin":
      return `${baseClasses} bg-gradient-to-r from-mulbin-50 to-mulbin-100 border border-mulbin-200`;
    case "blue":
      return `${baseClasses} bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200`;
    case "red":
      return `${baseClasses} bg-gradient-to-r from-red-50 to-red-100 border border-red-200`;
    case "green":
      return `${baseClasses} bg-gradient-to-r from-green-50 to-green-100 border border-green-200`;
    default:
      return `${baseClasses} bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200`;
  }
});

// Computed para las clases del ícono según el estilo de UI
const iconClasses = computed(() => {
  switch (props.uiStyle) {
    case "clean":
      return "text-lg text-gray-600";
    case "mulbin":
      return "text-lg text-mulbin-600";
    case "blue":
      return "text-lg text-blue-600";
    case "red":
      return "text-lg text-red-600";
    case "green":
      return "text-lg text-green-600";
    default:
      return "text-lg text-blue-600";
  }
});

// Computed para las clases del contador según el estilo de UI
const counterClasses = computed(() => {
  switch (props.uiStyle) {
    case "clean":
      return "text-2xl font-bold text-gray-800";
    case "mulbin":
      return "text-2xl font-bold text-mulbin-600";
    case "blue":
      return "text-2xl font-bold text-blue-600";
    case "red":
      return "text-2xl font-bold text-red-600";
    case "green":
      return "text-2xl font-bold text-green-600";
    default:
      return "text-2xl font-bold text-blue-600";
  }
});

// Computed para verificar si la clave actual es válida
const isCurrentKeyValid = computed(() => {
  return isValidPropertyKey(searchQuery.value);
});

// Computed para verificar si se debe mostrar la flecha/botón de envío
const shouldShowSubmitArrow = computed(() => {
  // Mostrar flecha si:
  // 1. Hay una propiedad seleccionada (para editar) O
  // 2. Hay texto válido para nueva clave
  return (
    searchQuery.value.trim() &&
    showDropdown.value &&
    (selectedProperty.value || // Hay propiedad seleccionada (editar)
      isCurrentKeyValid.value) // O es una clave válida (registrar)
  );
});

// Computed property for button text
const buttonText = computed(() => {
  if (selectedProperty.value) {
    return "EDITAR";
  } else {
    return "REGISTRAR";
  }
});

// Métodos para búsqueda con API
const searchProperties = async (query: string) => {
  if (!query.trim()) {
    propertiesData.value = [];
    showDropdown.value = false;
    return;
  }

  searchLoading.value = true;

  try {
    const response = await propertyService.searchProperties(
      {
        query: query.trim(),
        limit: 10,
        status: props.status,
      },
      props.apiEndpoint
    );

    propertiesData.value = response.properties;
    showDropdown.value = response.properties.length > 0;

    // Verificar coincidencia exacta
    const exactMatch = response.properties.find(
      (p) => p.key.toLowerCase() === query.toLowerCase()
    );

    if (exactMatch) {
      selectProperty(exactMatch);
    } else {
      clearSelection();
    }
  } catch (error) {
    console.error("Error searching properties:", error);
    propertiesData.value = [];
    showDropdown.value = false;
  } finally {
    searchLoading.value = false;
  }
};

// Métodos
const handleInput = () => {
  highlightedIndex.value = -1;
  emit("searchChanged", searchQuery.value);

  // Cancelar búsqueda anterior
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  if (!searchQuery.value.trim()) {
    clearSelection();
    propertiesData.value = [];
    showDropdown.value = false;
    return;
  }

  // Debounce la búsqueda
  searchTimeout = setTimeout(() => {
    searchProperties(searchQuery.value);
  }, 300);
};

const handleFocus = () => {
  if (searchQuery.value.trim() && filteredProperties.value.length > 0) {
    showDropdown.value = true;
  }
};

const handleKeydown = (event: KeyboardEvent) => {
  if (!showDropdown.value) {
    if (event.key === "Enter" && selectedProperty.value) {
      event.preventDefault();
      handleSubmit();
    }
    return;
  }

  switch (event.key) {
    case "ArrowDown":
      event.preventDefault();
      navigateDropdown("down");
      break;
    case "ArrowUp":
      event.preventDefault();
      navigateDropdown("up");
      break;
    case "Enter":
      event.preventDefault();
      if (highlightedIndex.value >= 0) {
        selectHighlighted();
      } else if (selectedProperty.value) {
        handleSubmit();
      }
      break;
    case "Escape":
      event.preventDefault();
      showDropdown.value = false;
      highlightedIndex.value = -1;
      break;
  }
};

const navigateDropdown = (direction: "up" | "down") => {
  if (filteredProperties.value.length === 0) return;

  if (direction === "down") {
    highlightedIndex.value =
      highlightedIndex.value < filteredProperties.value.length - 1
        ? highlightedIndex.value + 1
        : 0;
  } else {
    highlightedIndex.value =
      highlightedIndex.value > 0
        ? highlightedIndex.value - 1
        : filteredProperties.value.length - 1;
  }

  // Scroll automático para mantener elemento visible
  nextTick(() => {
    const dropdown = dropdownContainer.value?.querySelector(
      '[role="option"].bg-blue-100'
    );
    dropdown?.scrollIntoView({ block: "nearest" });
  });
};

const selectHighlighted = () => {
  if (
    highlightedIndex.value >= 0 &&
    filteredProperties.value[highlightedIndex.value]
  ) {
    selectProperty(filteredProperties.value[highlightedIndex.value]);
  }
};

const selectProperty = (property: Property) => {
  selectedProperty.value = property;
  searchQuery.value = property.key;
  showDropdown.value = false;
  highlightedIndex.value = -1;

  emit("propertySelected", property);
};

const clearSelection = () => {
  selectedProperty.value = null;
  highlightedIndex.value = -1;
};

const handleSubmit = async () => {
  if (!searchQuery.value.trim() || loading.value) return;

  loading.value = true;

  try {
    let claveprop: string;

    if (selectedProperty.value) {
      // Hay una propiedad seleccionada - EDITAR
      claveprop = selectedProperty.value.key;

      // Emitir eventos para compatibilidad con implementaciones existentes
      emit("propertyEdit", selectedProperty.value);
      emit("propertySubmitted", selectedProperty.value);
    } else {
      // No hay propiedad seleccionada pero sí hay texto válido - REGISTRAR
      if (!isCurrentKeyValid.value) {
        console.warn(
          "Intento de registrar con clave inválida:",
          searchQuery.value
        );
        return;
      }

      claveprop = searchQuery.value.trim();

      // Emitir evento para compatibilidad
      emit("propertyRegister", claveprop);
    }

    // Construir URL y navegar
    const targetUrl = buildInmuebleUrl(claveprop);
    console.log("Navegando a:", targetUrl);

    // Navegar al endpoint
    window.location.href = targetUrl;
  } catch (error) {
    console.error("Error al procesar envío:", error);
  } finally {
    loading.value = false;
  }
};

const handleViewAll = () => {
  // Navegar directamente a la página de listado de inmuebles
  window.location.href = "/verinmuebles.php";
};

const clearForm = () => {
  searchQuery.value = "";
  selectedProperty.value = null;
  showDropdown.value = false;
  highlightedIndex.value = -1;
};

const clearSearch = () => {
  searchQuery.value = "";
  selectedProperty.value = null;
  showDropdown.value = false;
  highlightedIndex.value = -1;
  propertiesData.value = [];

  // Enfocar el input después de limpiar
  nextTick(() => {
    propertyInput.value?.focus();
  });
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = "/images/placeholder-property.jpg"; // Imagen de fallback
};

// Click outside handler
const handleClickOutside = (event: Event) => {
  if (!dropdownContainer.value?.contains(event.target as Node)) {
    showDropdown.value = false;
  }
};

// Cargar estadísticas de propiedades
const loadPropertyStats = async () => {
  try {
    const stats = await propertyService.getPropertyStats();
    totalPropertiesComputed.value = stats.total;
  } catch (error) {
    console.error("Error loading property stats:", error);
  }
};

// Lifecycle hooks
onMounted(() => {
  document.addEventListener("click", handleClickOutside);

  // Ya no necesitamos configurar URL base

  // Cargar estadísticas si no se proporcionaron
  if (props.totalProperties === 0) {
    loadPropertyStats();
  }
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);

  // Limpiar timeout si existe
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
});

// Exponer métodos públicos del componente
defineExpose({
  clearForm,
  selectProperty,
  focus: () => propertyInput.value?.focus(),
});
</script>

<style scoped>
/* Transiciones */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
  transform-origin: top;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scaleY(0.8) translateY(-10px);
}

/* Mejoras de accesibilidad */
.dropdown-option {
  transition: background-color 0.15s ease;
}

/* Loading spinner opcional */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}
</style>
