# SidebarCardInmuebles

Componente Vue modular para la gestión y búsqueda de inmuebles en el sidebar.

## Características

- ✅ **Búsqueda con autocompletado** - Búsqueda inteligente por clave o nombre
- ✅ **Navegación con teclado** - Soporte completo para Arrow keys, Enter, Escape
- ✅ **Vista previa de imágenes** - Muestra imagen del inmueble seleccionado
- ✅ **Botón inline inteligente** - Ícono de flecha en el input para enviar cuando hay dropdown
- ✅ **Limpiar búsqueda** - Ícono X para limpiar el contenido del input
- ✅ **Texto dinámico** - Botón cambia entre "EDITAR" y "REGISTRAR" automáticamente
- ✅ **TypeScript** - Tipado fuerte y mejor experiencia de desarrollo
- ✅ **Composition API** - Vue 3 con mejores prácticas
- ✅ **Preparado para API** - Estructura lista para integraciones reales
- ✅ **Responsive** - Diseño adaptativo con Tailwind CSS
- ✅ **Accesibilidad** - Manejo apropiado de focus y eventos de teclado
- ✅ **Estilos configurables** - Múltiples variantes de color y estilo clean para fondos personalizados

## Estructura de archivos

```
src/components/sidebar-card-inmuebles/
├── SidebarCardInmuebles.vue      # Componente principal
├── types.ts                      # Interfaces y tipos TypeScript
├── services/
│   └── propertyService.ts        # Servicio para API calls
├── composables/
│   └── usePropertySearch.ts      # Lógica de búsqueda reutilizable
└── README.md                     # Esta documentación
```

## Uso Básico

```vue
<template>
  <div class="sidebar">
    <!-- Otros elementos del sidebar -->

    <SidebarCardInmuebles
      :total-properties="stats.total"
      api-endpoint="/api/properties"
      @property-selected="handlePropertySelected"
      @property-edit="handlePropertyEdit"
      @property-register="handlePropertyRegister"
      @view-all-clicked="navigateToPropertyList"
    />

    <!-- Más contenido del sidebar -->
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import SidebarCardInmuebles from "@/components/sidebar-card-inmuebles/SidebarCardInmuebles.vue";
import type { Property } from "@/components/sidebar-card-inmuebles/types";

const stats = ref({ total: 291 });

const handlePropertySelected = (property: Property) => {
  console.log("Propiedad seleccionada:", property);
};

const handlePropertyEdit = (property: Property) => {
  // Navegar a la página de edición del inmueble existente
  router.push(`/properties/${property.id}/edit`);
};

const handlePropertyRegister = (query: string) => {
  // Navegar a la página de registro de nuevo inmueble con la clave prellenada
  router.push(`/properties/new?key=${encodeURIComponent(query)}`);
};

const navigateToPropertyList = () => {
  router.push("/properties");
};
</script>
```

## Props

| Prop              | Tipo      | Predeterminado         | Descripción                           |
| ----------------- | --------- | ---------------------- | ------------------------------------- |
| `apiEndpoint`     | `string`  | `'/api/properties'`    | Endpoint de la API para búsqueda      |
| `totalProperties` | `number`  | `291`                  | Número total de inmuebles registrados |
| `placeholder`     | `string`  | `'Clave del inmueble'` | Texto placeholder del input           |
| `disabled`        | `boolean` | `false`                | Deshabilitar el componente            |
| `uiStyle`         | `string`  | `'default'`            | Estilo visual (`clean`, `blue`, `mulbin`, `red`, `green`) |

## Eventos

| Evento              | Payload    | Descripción                                                |
| ------------------- | ---------- | ---------------------------------------------------------- |
| `propertySelected`  | `Property` | Se emite cuando se selecciona una propiedad del dropdown   |
| `propertySubmitted` | `Property` | Se emite cuando se hace clic en "EDITAR" (compatibilidad)  |
| `propertyEdit`      | `Property` | Se emite cuando hay match y se hace clic en "EDITAR"       |
| `propertyRegister`  | `string`   | Se emite cuando no hay match y se hace clic en "REGISTRAR" |
| `searchChanged`     | `string`   | Se emite cuando cambia el texto de búsqueda                |

## Tipos TypeScript

```typescript
interface Property {
  id: number;
  key: string;
  name: string;
  price: number;
  image: string;
  status: "active" | "inactive" | "sold";
  description?: string;
  location?: string;
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  type?: "house" | "apartment" | "office" | "commercial";
}
```

## Estilos de UI

El componente soporta múltiples estilos visuales configurables:

### Estilo Clean (para fondos personalizados)
```html
<!-- Para usar sobre fondos con imagen o colores personalizados -->
<div data-sidebar-card-inmuebles data-ui-style="clean"></div>
```

### Estilos de Color
```html
<!-- Estilo de marca Mulbin -->
<div data-sidebar-card-inmuebles data-ui-style="mulbin"></div>

<!-- Estilo azul (default) -->
<div data-sidebar-card-inmuebles data-ui-style="blue"></div>

<!-- Estilo rojo -->
<div data-sidebar-card-inmuebles data-ui-style="red"></div>

<!-- Estilo verde -->
<div data-sidebar-card-inmuebles data-ui-style="green"></div>
```

Ver [`UI_STYLES.md`](./UI_STYLES.md) para documentación completa de estilos.

## Uso Avanzado

### Con estado global (Pinia/Vuex)

```vue
<script setup lang="ts">
import { computed } from "vue";
import { usePropertyStore } from "@/stores/propertyStore";
import SidebarCardInmuebles from "@/components/sidebar-card-inmuebles/SidebarCardInmuebles.vue";

const propertyStore = usePropertyStore();

const totalProperties = computed(() => propertyStore.totalCount);

const handlePropertySubmitted = async (property: Property) => {
  await propertyStore.setCurrentProperty(property);
  router.push(`/properties/${property.id}/edit`);
};
</script>
```

### Con validación personalizada

```vue
<script setup lang="ts">
import { ref } from "vue";
import SidebarCardInmuebles from "@/components/sidebar-card-inmuebles/SidebarCardInmuebles.vue";

const cardRef = ref();

const validateAndSubmit = (property: Property) => {
  if (!hasPermissionToEdit(property)) {
    showErrorMessage("No tienes permisos para editar esta propiedad");
    return;
  }

  // Continuar con la lógica normal
  router.push(`/properties/${property.id}/edit`);
};

// Método público del componente para limpiar el formulario
const clearForm = () => {
  cardRef.value?.clearForm();
};
</script>

<template>
  <SidebarCardInmuebles ref="cardRef" @property-submitted="validateAndSubmit" />
</template>
```

## Integración con API Real

Para conectar con una API real, modifica el `propertyService.ts`:

```typescript
// En propertyService.ts, reemplaza searchProperties con:
async searchProperties(params: PropertySearchParams): Promise<PropertySearchResponse> {
  return this.fetchFromAPI<PropertySearchResponse>(
    `/properties/search?${new URLSearchParams(params).toString()}`
  )
}
```

## Personalización de Estilos

El componente usa Tailwind CSS. Para personalizar:

```vue
<style scoped>
/* Cambiar colores del tema */
.bg-blue-50 {
  @apply bg-purple-50;
}

.text-blue-600 {
  @apply text-purple-600;
}

/* Personalizar transiciones */
.dropdown-enter-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
```

## Testing

Ejemplo de prueba unitaria:

```typescript
import { mount } from "@vue/test-utils";
import SidebarCardInmuebles from "@/components/sidebar-card-inmuebles/SidebarCardInmuebles.vue";

describe("SidebarCardInmuebles", () => {
  it("emite evento cuando se selecciona una propiedad", async () => {
    const wrapper = mount(SidebarCardInmuebles);

    // Simular búsqueda
    await wrapper.find("input").setValue("aa1");

    // Simular selección
    await wrapper.find('[data-testid="property-option"]').trigger("click");

    expect(wrapper.emitted("propertySelected")).toBeTruthy();
  });
});
```

## Migración desde HTML Vanilla

Si estás migrando del HTML original, estos son los cambios principales:

1. **Estado reactivo**: Variables como `selectedProperty` ahora son `ref()`
2. **Event listeners**: Eventos DOM se manejan con `@click`, `@input`, etc.
3. **Manipulación DOM**: No más `getElementById`, se usan template refs
4. **Datos**: JSON estático se mueve a un servicio reutilizable

## Troubleshooting

### El dropdown no aparece

- Verifica que `ion-icon` esté disponible globalmente
- Asegúrate de que Tailwind CSS esté configurado correctamente

### Las imágenes no cargan

- Configura la imagen de fallback en `handleImageError`
- Verifica las rutas de las imágenes dummy

### TypeScript errors

- Instala los tipos necesarios: `npm install --save-dev @types/node`
- Configura tsconfig.json apropiadamente
