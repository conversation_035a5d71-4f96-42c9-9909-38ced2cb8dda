# 🔧 Troubleshooting: SidebarCardInmuebles

## 🚨 Problema: El componente no se renderiza

### ✅ Lista de Verificación

#### 1. **Verificar que el build se completó**

```bash
npm run build
```

**Buscar en la salida:**

```
✓ dist/assets/sidebarCardInmuebles-[hash].js
✓ dist/assets/sidebarCardInmuebles-[hash].css
```

#### 2. **Verificar que el elemento HTML existe**

En `src/partials/sidebar_home.html` debe existir:

```html
<div
  data-sidebar-card-inmuebles
  data-total-properties="291"
  data-api-endpoint="/msi-v5/owner/inmuebles"
  data-placeholder="Clave del inmueble"
  class="sidebar-card-inmuebles-container"
></div>
```

#### 3. **Verificar que el script se carga**

En `panel.html` debe estar:

```html
{{#is_home}}
<script
  type="module"
  src="./src/components/sidebar-card-inmuebles/index.js"
></script>
{{/is_home}}
```

#### 4. **Verificar en la consola del navegador**

Abrir DevTools (F12) y buscar:

```
🚀 Inicializando SidebarCardInmuebles...
🏗️ Inicializando SidebarCardInmuebles con props: {...}
✅ Componente SidebarCardInmuebles montado correctamente
```

### 🔍 Pasos de Debug

#### Paso 1: Verificar carga del script

```javascript
// En la consola del navegador:
console.log("Script cargado:", typeof window.sidebarCardInmueblesMount);
// Debería mostrar: "function"
```

#### Paso 2: Verificar elemento DOM

```javascript
// En la consola del navegador:
console.log(
  "Elemento encontrado:",
  document.querySelector("[data-sidebar-card-inmuebles]")
);
// Debería mostrar el elemento HTML
```

#### Paso 3: Verificar que estás en la página correcta

El componente solo se carga en páginas donde `{{#is_home}}` es `true`.

#### Paso 4: Probar manualmente

```javascript
// En la consola del navegador:
window.sidebarCardInmueblesMount("[data-sidebar-card-inmuebles]");
```

### 🛠️ Soluciones Comunes

#### Error: "No se encontró el elemento"

**Causa:** El elemento HTML no existe o el selector es incorrecto.
**Solución:**

1. Verificar que `sidebar_home.html` tenga el elemento con `data-sidebar-card-inmuebles`
2. Verificar que estás en una página donde se incluye `sidebar_home.html`

#### Error: "Cannot read properties of undefined"

**Causa:** El componente Vue tiene errores de sintaxis o dependencias faltantes.
**Solución:**

1. Verificar que no hay errores de TypeScript
2. Ejecutar `npm run build` y revisar errores

#### Error: "Script not loading"

**Causa:** Ruta incorrecta del script o problema de módulos ES.
**Solución:**

1. Verificar que la ruta en `panel.html` sea correcta
2. Verificar que el servidor sirve archivos estáticos correctamente

#### El componente se monta pero no funciona

**Causa:** Problemas con los eventos o datos dummy.
**Solución:**

1. Verificar en Network tab que no hay errores 404
2. Verificar que los datos dummy están cargándose

### 🧪 Página de Prueba

He creado `test-sidebar-component.html` para pruebas aisladas:

1. **Abrir:** `http://localhost:8020/test-sidebar-component.html`
2. **Verificar:** Que el componente se carga correctamente
3. **Probar:** Funcionalidad de búsqueda y eventos

### 📊 Comparación con Componente Funcional

El componente de **notificaciones** funciona correctamente. Comparar:

#### ✅ Notificaciones (funciona):

- Archivo: `src/components/notifications/index.js`
- Selector: `[data-notificaciones]`
- Patrón: Función `mountNotificacionesComponent()`

#### 🔄 SidebarCardInmuebles (nuestro):

- Archivo: `src/components/sidebar-card-inmuebles/index.js`
- Selector: `[data-sidebar-card-inmuebles]`
- Patrón: Función `mountSidebarCardInmueblesComponent()`

### 🚀 Test Rápido

```bash
# 1. Build
npm run build

# 2. Verificar archivos generados
ls -la dist/assets/sidebarCard*

# 3. Iniciar servidor (si no está corriendo)
npm run dev

# 4. Abrir página de prueba
# http://localhost:8020/test-sidebar-component.html
```

### 📝 Logs Esperados

Si todo funciona correctamente, deberías ver:

```
🚀 Inicializando SidebarCardInmuebles...
🏗️ Inicializando SidebarCardInmuebles con props: {
  totalProperties: 291,
  apiEndpoint: "/msi-v5/owner/inmuebles",
  placeholder: "Clave del inmueble",
  disabled: false
}
✅ Componente SidebarCardInmuebles montado correctamente
```

### 🆘 Si Nada Funciona

1. **Comparar con notificaciones:**

   - Copiar exactamente el patrón de `notifications/index.js`
   - Usar el mismo selector pattern

2. **Verificar dependencias:**

   - Vue 3 disponible
   - Ionicons cargados
   - Tailwind CSS disponible

3. **Rollback temporal:**
   - Restaurar el HTML/JS original
   - Verificar que el sidebar funciona sin Vue
   - Integrar paso a paso

### 📞 Información para Soporte

Si necesitas ayuda, proporciona:

1. **Logs de consola completos**
2. **Network tab de DevTools**
3. **Resultado de:** `npm run build`
4. **URL donde estás probando**
5. **Navegador y versión**

---

**¡El componente debería funcionar siguiendo estos pasos! 🎯**
