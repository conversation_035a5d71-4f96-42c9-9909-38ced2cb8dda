<template>
  <div ref="rootEl" class="bg-white rounded-lg shadow-md">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
      <h2 class="text-lg font-semibold text-gray-900">Mis inmuebles</h2>
      <div class="text-sm text-gray-500" v-if="total">
        <span v-if="searchQuery"> {{ filteredInmuebles.length }} de </span>
        {{ total }} inmueble{{ total !== 1 ? "s" : "" }}
      </div>
    </div>

    <!-- Buscador (solo input) -->
    <div class="p-4 border-b">
      <BuscadorInmuebles
        v-model="searchQuery"
        placeholder="¿Qué buscas? Casa, departamento, terreno..."
      />
    </div>

    <!-- Indicador de carga -->
    <div v-if="loading" class="p-8 text-center text-gray-500">
      <div class="flex justify-center">
        <div
          class="w-8 h-8 rounded-full border-4 border-gray-200 animate-spin border-t-mulbin-600"
        ></div>
      </div>
      <p class="mt-2">Cargando inmuebles...</p>
    </div>

    <!-- Error de carga -->
    <div v-else-if="error" class="p-8 text-center text-red-500">
      <ion-icon name="alert-circle-outline" class="mb-2 text-5xl"></ion-icon>
      <p>{{ error }}</p>
      <button
        @click="fetchInmuebles"
        class="px-4 py-2 mt-4 text-white rounded-lg bg-mulbin-600 hover:bg-mulbin-700"
      >
        Reintentar
      </button>
    </div>

    <!-- Lista -->
    <div v-else>
      <div v-if="filteredInmuebles.length" class="divide-y divide-gray-100">
        <div
          v-for="inmueble in filteredInmuebles"
          :key="inmueble.id"
          class="p-4"
        >
          <div
            class="flex flex-col space-y-3 lg:flex-row lg:space-y-0 lg:space-x-4"
          >
            <!-- Imagen principal -->
            <div class="flex-shrink-0">
              <img
                :src="`/static/img/sin_imagen.jpg`"
                :data-src="
                  inmueble.imagenPrincipal ||
                  `${mulbinUrl}/images/sin_imagen.jpg`
                "
                :alt="inmueble.titulo"
                class="object-cover w-full h-48 rounded-lg lazy lg:w-64 lg:h-40"
                loading="lazy"
              />
              <!-- Badges de operaciones múltiples (solo operaciones disponibles) -->
              <div class="relative -mt-8 ml-2">
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="operacion in getOperacionesDisponibles(inmueble)"
                    :key="operacion"
                    :class="[
                      'inline-block px-2 py-1 text-xs font-bold text-white rounded',
                      getBadgeColor(operacion),
                      operacion === getOperacionPrincipal(inmueble)
                        ? 'ring-2 ring-white ring-opacity-50'
                        : '',
                    ]"
                  >
                    {{ operacion?.toUpperCase() }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Información principal -->
            <div class="flex-1 min-w-0">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <!-- Key del inmueble -->
                  <div v-if="inmueble.key" class="mb-1">
                    <span
                      class="inline-block px-2 py-1 font-mono text-xs font-bold text-gray-700 bg-gray-100 rounded-md"
                      :title="`Clave del inmueble: ${inmueble.key}`"
                    >
                      {{ inmueble.key }}
                    </span>
                  </div>
                  <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">
                    {{ decodeHtmlEntities(stripTags(inmueble.titulo)) }}
                  </h3>
                  <p class="mt-1 text-sm text-gray-600 line-clamp-2">
                    {{ decodeHtmlEntities(stripTags(inmueble.descripcion)) }}
                  </p>
                </div>

                <!-- Precio de la operación principal -->
                <div class="flex-shrink-0 ml-4 text-right">
                  <p class="text-xl font-bold text-green-600">
                    ${{
                      formatPrice(
                        getPrecioOperacion(
                          inmueble,
                          getOperacionPrincipal(inmueble)
                        )
                      )
                    }}
                  </p>
                  <p class="text-xs text-gray-600 capitalize">
                    {{ getOperacionPrincipal(inmueble) }}
                    <span v-if="getOperacionPrincipal(inmueble) === 'renta'">
                      / mes
                    </span>
                    <!-- Información de comisión compartida para operación principal (solo si > 0%) -->
                    <span
                      v-if="
                        getComisionCompartida(
                          inmueble,
                          getOperacionPrincipal(inmueble)
                        )
                      "
                      class="ml-1 font-medium text-blue-600"
                    >
                      ({{
                        formatComisionCompartida(
                          inmueble,
                          getOperacionPrincipal(inmueble)
                        )
                      }})
                    </span>
                  </p>
                  <!-- Precios adicionales para todas las operaciones (disponibles y no disponibles) -->
                  <div
                    v-if="getTodasOperaciones(inmueble).length > 1"
                    class="mt-1 space-y-1"
                  >
                    <p
                      v-for="operacion in getTodasOperaciones(inmueble).filter(
                        (op) => op !== getOperacionPrincipal(inmueble)
                      )"
                      :key="operacion"
                      :class="[
                        'text-sm',
                        isOperacionDisponible(inmueble, operacion)
                          ? 'text-gray-500'
                          : 'text-gray-400 line-through',
                      ]"
                    >
                      <span class="capitalize">{{ operacion }}:</span>
                      ${{
                        formatPrice(getPrecioOperacion(inmueble, operacion))
                      }}
                      <span v-if="operacion === 'renta'" class="text-xs"
                        >/ mes</span
                      >
                      <!-- Información de comisión compartida (solo si > 0%) -->
                      <span
                        v-if="getComisionCompartida(inmueble, operacion)"
                        class="ml-1 text-xs font-medium text-blue-600"
                      >
                        ({{ formatComisionCompartida(inmueble, operacion) }})
                      </span>
                      <span
                        v-if="!isOperacionDisponible(inmueble, operacion)"
                        class="ml-1 text-xs text-gray-500"
                      >
                        (no disponible)
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              <!-- Características -->
              <div class="flex flex-wrap gap-4 mt-3 text-sm text-gray-600">
                <span v-if="inmueble.recamaras" class="flex items-center">
                  <ion-icon name="bed-outline" class="mr-1"></ion-icon>
                  {{ inmueble.recamaras }} recámaras
                </span>
                <span v-if="inmueble.banos" class="flex items-center">
                  <ion-icon name="water-outline" class="mr-1"></ion-icon>
                  {{ inmueble.banos }} baños
                </span>
                <span v-if="inmueble.area" class="flex items-center">
                  <ion-icon name="resize-outline" class="mr-1"></ion-icon>
                  {{ inmueble.area }} m²
                </span>
                <span class="flex items-center">
                  <ion-icon name="location-outline" class="mr-1"></ion-icon>
                  {{ inmueble.ubicacion }}
                </span>
              </div>

              <!-- Acciones -->
              <div
                class="flex justify-between items-center pt-3 mt-3 border-t border-gray-100"
              >
                <!-- Botón de edición -->
                <a
                  :href="`/inmueble.php?ppinmueble=&paso=6&claveprop=${
                    inmueble.key || inmueble.id
                  }`"
                  class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg transition-colors hover:bg-gray-200"
                  title="Editar inmueble"
                >
                  <ion-icon name="create-outline" class="sm:mr-1"></ion-icon>
                  <span class="hidden text-sm sm:block">Editar</span>
                </a>

                <!-- Contenedor de botones de socios -->
                <div class="flex gap-3 items-center">
                  <!-- Botón de hilos de interés (visual, sin integración aún) -->
                  <button
                    v-if="false"
                    @click="abrirHilosInmueble(inmueble)"
                    :class="[
                      'inline-flex justify-center items-center w-12 h-12 rounded-lg transition-all duration-200 socios-button',
                      'bg-gray-100 text-gray-400 hover:bg-gray-200 hover:text-mulbin-600',
                    ]"
                    title="Hilos de interés"
                  >
                    <ion-icon name="bulb-outline" class="text-xl"></ion-icon>
                  </button>

                  <!-- Icono de socios con badge de notificaciones -->
                  <div class="relative">
                    <button
                      @click="verSolicitudesSocios(inmueble)"
                      :class="[
                        'inline-flex justify-center items-center w-12 h-12 rounded-lg transition-all duration-200 socios-button',
                        estaPublicadoConSocios(inmueble)
                          ? 'bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 border-2 border-blue-300'
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200 hover:text-mulbin-600',
                      ]"
                      title="Este inmueble en mi red de socios"
                    >
                      <ion-icon name="globe-outline" class="text-xl"></ion-icon>
                    </button>

                    <!-- Badge de socios publicados (abajo, mulbin) -->
                    <div
                      v-if="getSociosPublicados(inmueble).length > 0"
                      class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold text-white bg-mulbin-500 rounded-full border-2 border-blue-300 shadow-lg"
                      :title="`Publicado con ${
                        getSociosPublicados(inmueble).length
                      } socio${
                        getSociosPublicados(inmueble).length !== 1 ? 's' : ''
                      }`"
                    >
                      {{
                        getSociosPublicados(inmueble).length > 99
                          ? "99+"
                          : getSociosPublicados(inmueble).length
                      }}
                    </div>

                    <!-- Badge de solicitudes nuestras pendientes (izquierda, gris) -->
                    <div
                      v-if="getSolicitudesPendientesOwner(inmueble).length > 0"
                      class="absolute -top-2 -left-2 flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold text-white bg-gray-500 rounded-full border-2 border-gray-100 shadow-lg"
                      :title="`${
                        getSolicitudesPendientesOwner(inmueble).length
                      } solicitud${
                        getSolicitudesPendientesOwner(inmueble).length !== 1
                          ? 'es'
                          : ''
                      } nuestra${
                        getSolicitudesPendientesOwner(inmueble).length !== 1
                          ? 's'
                          : ''
                      } pendiente${
                        getSolicitudesPendientesOwner(inmueble).length !== 1
                          ? 's'
                          : ''
                      } de autorización`"
                    >
                      {{
                        getSolicitudesPendientesOwner(inmueble).length > 99
                          ? "99+"
                          : getSolicitudesPendientesOwner(inmueble).length
                      }}
                    </div>

                    <!-- Badge de notificaciones pendientes (derecha, rojo) -->
                    <div
                      v-if="getSolicitudesPendientes(inmueble) > 0"
                      class="absolute -top-2 -right-2 flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold text-white bg-red-600 rounded-full border-2 border-red-100 shadow-lg badge-notification"
                      :title="`${getSolicitudesPendientes(inmueble)} solicitud${
                        getSolicitudesPendientes(inmueble) !== 1 ? 'es' : ''
                      } pendiente${
                        getSolicitudesPendientes(inmueble) !== 1 ? 's' : ''
                      } de socios esperando tu respuesta`"
                    >
                      {{
                        getSolicitudesPendientes(inmueble) > 99
                          ? "99+"
                          : getSolicitudesPendientes(inmueble)
                      }}
                    </div>
                  </div>

                  <!-- Botón de solicitar publicación -->
                  <button
                    @click="solicitarPublicacion(inmueble)"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-mulbin-600 hover:bg-mulbin-700"
                    title="Solicitar publicación a socios"
                  >
                    <ion-icon name="send-outline" class="mr-2"></ion-icon>
                    Solicitar publicación a socios
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Estado vacío -->
      <div v-else class="p-8 text-center text-gray-500">
        <ion-icon name="home-outline" class="mb-2 text-5xl"></ion-icon>
        <p class="text-lg font-medium">No se encontraron inmuebles</p>
        <p class="mt-2 text-sm">
          No tienes inmuebles publicados o no cumplen con los criterios
          actuales.
        </p>
      </div>
    </div>

    <!-- Modal de selector de socios -->
    <div
      v-if="showSociosSelector"
      class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
      @click.self="cerrarSelectorSocios"
    >
      <div
        class="bg-white rounded-xl p-6 max-w-lg w-full mx-4 max-h-[80vh] overflow-y-auto"
      >
        <div class="flex justify-between items-center mb-4">
          <div>
            <h3 class="text-lg font-semibold">
              Solicitar publicación a socios
            </h3>
            <p class="mt-1 text-sm text-gray-600" v-if="inmuebleParaSolicitud">
              {{ inmuebleParaSolicitud.titulo }}
            </p>
          </div>
          <button
            @click="cerrarSelectorSocios"
            class="flex justify-center items-center w-8 h-8 bg-gray-100 rounded-full hover:bg-gray-200"
          >
            <ion-icon name="close"></ion-icon>
          </button>
        </div>

        <!-- Descripción de la acción -->
        <div class="p-3 mb-4 bg-blue-50 rounded-lg border border-blue-200">
          <p class="text-sm text-blue-700">
            Enviarás una solicitud a tus socios para que publiquen este inmueble
            en la web de sus agencias.
          </p>
        </div>

        <!-- Selector de socios -->
        <SelectorSociosWrapper
          :is-public="isPublicPost"
          :selected-socios="selectedFriends"
          :available-friends="availableFriends"
          :textos-audiencia-privada="textosAudienciaPrivada"
          :textos-audiencia-publica="textosAudienciaPublica"
          @update:isPublic="handleAudiencePublicChange"
          @update:selectedSocios="handleSelectedSociosChange"
        />

        <!-- Acciones del modal -->
        <div class="flex gap-3 justify-end mt-6">
          <button
            type="button"
            @click="cerrarSelectorSocios"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg border border-gray-200 hover:bg-gray-200"
          >
            Cancelar
          </button>
          <button
            @click="enviarSolicitudPublicacion"
            :disabled="
              enviandoSolicitud ||
              (!isPublicPost && selectedFriends.length === 0)
            "
            :class="[
              'px-4 py-2 text-sm font-medium rounded-lg transition-all',
              enviandoSolicitud ||
              (!isPublicPost && selectedFriends.length === 0)
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-mulbin-600 hover:bg-mulbin-700 text-white shadow-sm hover:shadow-md',
            ]"
          >
            <span v-if="enviandoSolicitud" class="flex items-center">
              <ion-icon
                name="sync-outline"
                class="mr-2 animate-spin"
              ></ion-icon>
              Enviando...
            </span>
            <span v-else>
              Enviar solicitud
              <span v-if="!isPublicPost && selectedFriends.length > 0">
                ({{ selectedFriends.length }}
                {{ selectedFriends.length === 1 ? "socio" : "socios" }})
              </span>
              <span v-else-if="isPublicPost && availableFriends.length > 0">
                ({{ availableFriends.length }}
                {{ availableFriends.length === 1 ? "socio" : "socios" }})
              </span>
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Modal de solicitudes de socios -->
    <div
      v-if="showSolicitudesModal"
      class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
      @click.self="cerrarSolicitudesModal"
    >
      <div
        class="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
      >
        <div class="flex justify-between items-center mb-4">
          <div>
            <h3 class="text-lg font-semibold">Solicitudes de Socios</h3>
            <p class="mt-1 text-sm text-gray-600" v-if="inmuebleConSolicitudes">
              Clave: <strong>{{ inmuebleConSolicitudes.key }}</strong>
              <br />
              <span class="text-sm !text-gray-500 !font-normal">
                {{ inmuebleConSolicitudes.titulo }}
              </span>
            </p>
          </div>
          <button
            @click="cerrarSolicitudesModal"
            class="flex justify-center items-center w-8 h-8 bg-gray-100 rounded-full hover:bg-gray-200"
          >
            <ion-icon name="close"></ion-icon>
          </button>
        </div>

        <div v-if="inmuebleConSolicitudes?.inmuebleConSocios?.length">
          <!-- Caso 1: Socios esperando respuesta del propietario -->
          <div
            v-if="
              getSociosEsperandoRespuesta(inmuebleConSolicitudes).length > 0
            "
            class="mb-6"
          >
            <h4 class="mb-3 font-semibold text-gray-900 text-md">
              🤔 Esperando tu respuesta ({{
                getSociosEsperandoRespuesta(inmuebleConSolicitudes).length
              }})
            </h4>
            <p class="mb-3 text-sm text-gray-600">
              Estos socios han solicitado publicar tu inmueble en su web y están
              esperando que aceptes o rechaces.
            </p>

            <div class="space-y-3">
              <div
                v-for="solicitud in getSociosEsperandoRespuesta(
                  inmuebleConSolicitudes
                )"
                :key="`esperando-${solicitud.id}`"
                class="flex justify-between items-center p-4 bg-yellow-50 rounded-lg border border-yellow-200"
              >
                <!-- Información del socio -->
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      class="flex justify-center items-center w-12 h-12 bg-yellow-600 rounded-full"
                    >
                      <span class="text-sm font-semibold text-white">
                        {{
                          (solicitud.datos_socio.nombre?.charAt(0) || "") +
                          (solicitud.datos_socio.apellidos?.charAt(0) || "")
                        }}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">
                      {{
                        solicitud.datos_socio.nombre_completo || "Sin nombre"
                      }}
                    </h4>
                    <p class="text-sm text-gray-600">
                      {{ solicitud.datos_socio.empresa || "Sin empresa" }}
                    </p>
                    <div class="flex items-center mt-1 space-x-2">
                      <span class="text-xs text-gray-500">
                        Solicitó:
                        {{
                          new Date(
                            solicitud.fecha_solicitud
                          ).toLocaleDateString("es-MX", {
                            weekday: "short",
                            year: "numeric",
                            month: "short",
                            day: "numeric",
                          })
                        }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Acciones -->
                <div class="flex items-center space-x-2">
                  <button
                    @click="
                      confirmarAccion(
                        () => aceptarSolicitudSocio(solicitud),
                        'aceptar'
                      )
                    "
                    :disabled="procesandoSolicitud"
                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-green-600 rounded-lg transition-colors hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Aceptar solicitud"
                  >
                    <ion-icon
                      name="checkmark-outline"
                      class="mr-1"
                      v-if="!procesandoSolicitud"
                    ></ion-icon>
                    <ion-icon
                      name="sync-outline"
                      class="mr-1 animate-spin"
                      v-if="procesandoSolicitud"
                    ></ion-icon>
                    Aceptar
                  </button>
                  <button
                    @click="
                      confirmarAccion(
                        () => rechazarSolicitudSocio(solicitud),
                        'rechazar'
                      )
                    "
                    :disabled="procesandoSolicitud"
                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg transition-colors hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Rechazar solicitud"
                  >
                    <ion-icon
                      name="close-outline"
                      class="mr-1"
                      v-if="!procesandoSolicitud"
                    ></ion-icon>
                    <ion-icon
                      name="sync-outline"
                      class="mr-1 animate-spin"
                      v-if="procesandoSolicitud"
                    ></ion-icon>
                    Rechazar
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Caso 2: Inmueble ya publicado con socios -->
          <div
            v-if="getSociosPublicados(inmuebleConSolicitudes).length > 0"
            class="mb-6"
          >
            <h4 class="mb-3 font-semibold text-gray-900 text-md">
              ✅ Publicado con socios ({{
                getSociosPublicados(inmuebleConSolicitudes).length
              }})
            </h4>
            <p class="mb-3 text-sm text-gray-600">
              Tu inmueble está actualmente publicado en las webs de estos
              socios.
            </p>

            <div class="space-y-3">
              <div
                v-for="socio in getSociosPublicados(inmuebleConSolicitudes)"
                :key="`publicado-${socio.id}`"
                class="flex justify-between items-center p-4 bg-green-50 rounded-lg border border-green-200"
              >
                <!-- Información del socio -->
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      class="flex justify-center items-center w-12 h-12 bg-green-600 rounded-full"
                    >
                      <span class="text-sm font-semibold text-white">
                        {{
                          (socio.datos_socio.nombre?.charAt(0) || "") +
                          (socio.datos_socio.apellidos?.charAt(0) || "")
                        }}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">
                      {{ socio.datos_socio.nombre_completo || "Sin nombre" }}
                    </h4>
                    <p class="text-sm text-gray-600">
                      {{ socio.datos_socio.empresa || "Sin empresa" }}
                    </p>
                    <div class="flex items-center mt-1 space-x-2">
                      <span class="text-xs text-green-700">
                        ✓ Publicado desde:
                        {{
                          new Date(socio.fecha_solicitud).toLocaleDateString(
                            "es-MX",
                            { year: "numeric", month: "short", day: "numeric" }
                          )
                        }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Acciones -->
                <div class="flex items-center space-x-2">
                  <button
                    @click="
                      confirmarAccion(() => quitarDeSuWeb(socio), 'quitar')
                    "
                    :disabled="procesandoSolicitud"
                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 rounded-lg transition-colors hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Quitar de su web"
                  >
                    <ion-icon
                      name="remove-circle-outline"
                      class="mr-1"
                      v-if="!procesandoSolicitud"
                    ></ion-icon>
                    <ion-icon
                      name="sync-outline"
                      class="mr-1 animate-spin"
                      v-if="procesandoSolicitud"
                    ></ion-icon>
                    Quitar de su web
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Caso 3: Propietario esperando respuesta de socios -->
          <div
            v-if="
              getSolicitudesPendientesOwner(inmuebleConSolicitudes).length > 0
            "
            class="mb-6"
          >
            <h4 class="mb-3 font-semibold text-gray-900 text-md">
              ⏳ Esperando respuesta de socios ({{
                getSolicitudesPendientesOwner(inmuebleConSolicitudes).length
              }})
            </h4>
            <p class="mb-3 text-sm text-gray-600">
              Has enviado solicitudes a estos socios y están pendientes de
              respuesta.
            </p>

            <div class="space-y-3">
              <div
                v-for="solicitud in getSolicitudesPendientesOwner(
                  inmuebleConSolicitudes
                )"
                :key="`pendiente-owner-${solicitud.id}`"
                class="flex justify-between items-center p-4 bg-blue-50 rounded-lg border border-blue-200"
              >
                <!-- Información del socio -->
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div
                      class="flex justify-center items-center w-12 h-12 bg-blue-600 rounded-full"
                    >
                      <span class="text-sm font-semibold text-white">
                        {{
                          (solicitud.datos_socio.nombre?.charAt(0) || "") +
                          (solicitud.datos_socio.apellidos?.charAt(0) || "")
                        }}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">
                      {{
                        solicitud.datos_socio.nombre_completo || "Sin nombre"
                      }}
                    </h4>
                    <p class="text-sm text-gray-600">
                      {{ solicitud.datos_socio.empresa || "Sin empresa" }}
                    </p>
                    <div class="flex items-center mt-1 space-x-2">
                      <span class="text-xs text-blue-700">
                        📤 Enviado:
                        {{
                          new Date(
                            solicitud.fecha_solicitud
                          ).toLocaleDateString("es-MX", {
                            weekday: "short",
                            year: "numeric",
                            month: "short",
                            day: "numeric",
                          })
                        }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Acciones -->
                <div class="flex items-center space-x-2">
                  <button
                    @click="
                      confirmarAccion(
                        () => cancelarMiSolicitud(solicitud),
                        'cancelar'
                      )
                    "
                    :disabled="procesandoSolicitud"
                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg transition-colors hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Cancelar mi solicitud"
                  >
                    <ion-icon
                      name="close-circle-outline"
                      class="mr-1"
                      v-if="!procesandoSolicitud"
                    ></ion-icon>
                    <ion-icon
                      name="sync-outline"
                      class="mr-1 animate-spin"
                      v-if="procesandoSolicitud"
                    ></ion-icon>
                    Cancelar mi solicitud
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Estado vacío -->
        <div v-else class="py-8 text-center">
          <ion-icon
            name="globe-outline"
            class="mb-2 text-5xl text-gray-400"
          ></ion-icon>
          <p class="text-gray-600">
            No hay registros de casas compartidas para este inmueble
          </p>
        </div>

        <!-- Acciones del modal -->
        <div class="flex justify-end mt-6">
          <button
            type="button"
            @click="cerrarSolicitudesModal"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg border border-gray-200 hover:bg-gray-200"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  onUnmounted,
  ref,
  nextTick,
  computed,
  watch,
} from "vue";
import axios from "axios";
import BuscadorInmuebles from "../ui/BuscadorInmuebles.vue";
import SelectorSociosWrapper from "../selector-socios-wrapper/SelectorSociosWrapper.vue";
import { useModal } from "../../composables/useModal";

interface PrecioOperacion {
  precio: number;
  disponible: boolean;
  periodo?: string;
  comision_compartida?: {
    porcentaje: number;
    tipo: string;
    descripcion: string;
    cantidad_fija?: number | null;
  };
}

interface InmuebleConSocio {
  id: number;
  contrato_socio: number;
  contrato: number; // Agregado para identificar el contrato
  quien_solicita: number; // Agregado para identificar quién solicita
  token: string | null; // Agregado para validar solicitudes
  autorizado: string;
  metodo: string;
  fecha_solicitud: string;
  esta_autorizado: boolean;
  es_metodo_tradicional: boolean;
  es_metodo_ampi_plus: boolean;
  datos_socio: {
    nombre: string | null;
    apellidos: string | null;
    empresa: string | null;
    nombre_completo: string | null;
  };
}

interface InmuebleOwner {
  id: string;
  key?: string;
  titulo: string;
  descripcion: string;
  precio: number;
  moneda?: string;
  precios?: {
    venta?: PrecioOperacion;
    renta?: PrecioOperacion;
    traspaso?: PrecioOperacion;
  };
  operacion: "venta" | "renta" | "traspaso";
  tipo: string;
  colonia?: string;
  ciudad?: string;
  provincia?: string;
  ubicacion: string;
  recamaras?: number;
  banos?: number;
  area?: number;
  imagenPrincipal?: string | null;
  imagenes?: string[];
  fechaCreacion?: string | null;
  inmuebleConSocios?: InmuebleConSocio[]; // Historial completo de casas compartidas del inmueble
}

interface Socio {
  _id: string;
  id: string;
  name: string;
  company: string;
  location: string;
  avatar: string;
  email: string;
  phone: string;
  wa: string;
  telegram: string;
  verified: boolean;
  etiquetas: any[];
}

export default defineComponent({
  name: "InmueblesOwner",
  components: { BuscadorInmuebles, SelectorSociosWrapper },
  setup() {
    const mulbinUrl = import.meta.env.VITE_MULBIN_URL;
    const rootEl = ref<HTMLElement | null>(null);
    const contratoOwner = ref<number | null>(null);

    // Sistema de modales
    const { success, error: showError, confirm, warning } = useModal();

    const inmuebles = ref<InmuebleOwner[]>([]);
    const searchQuery = ref<string>("");
    const total = ref<number>(0);
    const loading = ref<boolean>(false);
    const error = ref<string | null>(null);

    // Estados del selector de socios
    const showSociosSelector = ref<boolean>(false);
    const inmuebleParaSolicitud = ref<InmuebleOwner | null>(null);
    const isPublicPost = ref<boolean>(true);
    const selectedFriends = ref<string[]>([]);
    const availableFriends = ref<Socio[]>([]);
    const loadingFriends = ref<boolean>(false);
    const enviandoSolicitud = ref<boolean>(false);

    // Estados del modal de solicitudes de socios
    const showSolicitudesModal = ref<boolean>(false);
    const inmuebleConSolicitudes = ref<InmuebleOwner | null>(null);
    const procesandoSolicitud = ref<boolean>(false);

    // Textos para el selector de socios
    const textosAudienciaPublica = {
      titulo: "Todos mis socios",
      descripcion:
        "Todos tus socios directos autorizados recibirán la solicitud de publicación de este inmueble",
    };

    const textosAudienciaPrivada = {
      titulo: "Solo a socios determinados",
      descripcion:
        "Solo tus socios seleccionados recibirán la solicitud para publicar este inmueble",
    };

    // Lazyload
    let imageObserver: IntersectionObserver | null = null;
    const initLazyObserver = () => {
      if (typeof window === "undefined") return;
      if ("IntersectionObserver" in window) {
        imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement & {
                dataset: { src?: string };
              };
              const dataSrc = img?.dataset?.src;
              if (dataSrc) {
                img.src = dataSrc;
              }
              img.classList.remove("lazy");
              observer.unobserve(img);
            }
          });
        });
      }
    };

    const observeLazyImages = () => {
      const scope: Document | Element =
        (rootEl.value as Element | null) ?? document;
      const images = scope.querySelectorAll<HTMLImageElement>("img.lazy");
      images.forEach((img) => {
        if (imageObserver) {
          imageObserver.unobserve(img); // evitar observers duplicados
          imageObserver.observe(img);
        } else {
          const dataSrc = (img as any)?.dataset?.src as string | undefined;
          if (dataSrc) {
            img.src = dataSrc;
          }
          img.classList.remove("lazy");
        }
      });
    };

    const fetchInmuebles = async () => {
      loading.value = true;
      error.value = null;
      try {
        const response = await axios.get(
          `/msi-v5/owner/inmuebles?limit=0&view=shared`
        );
        if (response.data && response.data.statusCode === 200) {
          const data = response.data.data || {};
          const inmueblesData = data.inmuebles || [];

          // Obtener el contrato del owner desde el primer inmueble
          if (
            inmueblesData.length > 0 &&
            inmueblesData[0].contrato_propietario
          ) {
            contratoOwner.value = inmueblesData[0].contrato_propietario;
          }

          // Sustituir '/alta/' por '/peque/' en todas las imágenes para optimizar ancho de banda
          inmuebles.value = inmueblesData.map((inmueble: any) => ({
            ...inmueble,
            imagenPrincipal: inmueble.imagenPrincipal
              ? inmueble.imagenPrincipal.replace("/alta/", "/peque/")
              : null,
            imagenes: inmueble.imagenes
              ? inmueble.imagenes.map((img: string) =>
                  img.replace("/alta/", "/peque/")
                )
              : [],
            // Los inmuebleConSocios ya vienen del backend
            inmuebleConSocios: inmueble.inmuebleConSocios || [],
          }));

          total.value = data.total || 0;
          nextTick(() => observeLazyImages());
        } else {
          error.value = "Error al cargar tus inmuebles";
        }
      } catch (err: any) {
        console.error("Error fetching owner inmuebles:", err);
        if (err.response?.status === 403) {
          window.location.href = "/";
          return;
        }
        error.value =
          err.response?.data?.data?.error ||
          "No se pudieron cargar tus inmuebles. Intenta nuevamente.";
      } finally {
        loading.value = false;
      }
    };

    // Normaliza texto para búsquedas sin acentos y en minúsculas
    const normalizeText = (text: string): string => {
      return (text || "")
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .toLowerCase();
    };

    // Lista filtrada por búsqueda local
    const filteredInmuebles = computed(() => {
      const q = normalizeText(searchQuery.value);
      if (!q) return inmuebles.value;
      return inmuebles.value.filter((inmueble) => {
        // Obtener todas las operaciones para búsqueda (disponibles o no)
        const todasOperaciones = getTodasOperaciones(inmueble);
        const operacionesTexto = todasOperaciones.join(" ");

        return (
          normalizeText(inmueble.titulo).includes(q) ||
          normalizeText(inmueble.descripcion).includes(q) ||
          normalizeText(inmueble.ubicacion).includes(q) ||
          normalizeText(inmueble.tipo).includes(q) ||
          normalizeText(inmueble.colonia || "").includes(q) ||
          normalizeText(inmueble.ciudad || "").includes(q) ||
          normalizeText(inmueble.key || "").includes(q) || // ✅ Buscar por clave
          normalizeText(inmueble.operacion).includes(q) || // ✅ Buscar por operación principal
          normalizeText(operacionesTexto).includes(q) // ✅ Buscar por todas las operaciones
        );
      });
    });

    const solicitarPublicacion = (inmueble: InmuebleOwner) => {
      inmuebleParaSolicitud.value = inmueble;
      showSociosSelector.value = true;

      // Cargar socios si no están disponibles
      if (availableFriends.value.length === 0) {
        loadUserFriends();
      }

      console.log("📝 Abriendo selector de socios para inmueble", inmueble.id);
    };

    // Cargar lista de socios (copiado del componente NuevaPublicacionModal)
    const loadUserFriends = async () => {
      try {
        loadingFriends.value = true;
        const response = await axios.get("/msi-v5/owner/socios");

        if (response.data && response.data.statusCode === 200) {
          const tagsBackend = response.data.data.tags || [];
          const etiquetasDisponibles = tagsBackend.map((tag: any) => {
            let color = "#7C3AED";
            let backgroundColor = "#EDE9FE";
            if (tag.style) {
              const bgMatch = tag.style.match(/background-color:\s*([^;]+);?/);
              const borderMatch = tag.style.match(
                /border:\s*1px solid ([^;]+);?/
              );
              if (bgMatch) backgroundColor = bgMatch[1].trim();
              if (borderMatch) color = borderMatch[1].trim();
            }
            return {
              id: tag.id.toString(),
              nombre: tag.tag,
              color,
              backgroundColor,
              descripcion: tag.description || undefined,
            };
          });

          availableFriends.value = (response.data.data.socios || [])
            .filter((socio: any) => socio.tipo === "directo")
            .map((socio: any) => {
              const etiquetas = (socio.tags || [])
                .map((tagId: any) =>
                  etiquetasDisponibles.find(
                    (e: any) => e.id == tagId.toString()
                  )
                )
                .filter(Boolean);

              return {
                _id: socio.id,
                id: socio.id,
                name: socio.nombre,
                company: socio.empresa,
                location: socio.ubicacion,
                avatar: socio.avatar,
                email: socio.email,
                phone: socio.telefono,
                wa: socio.wa,
                telegram: socio.telegram,
                verified: socio.verified || false,
                etiquetas: etiquetas,
              };
            });
        } else {
          availableFriends.value = [];
        }
      } catch (error) {
        console.error("Error al cargar socios:", error);
        availableFriends.value = [];
      } finally {
        loadingFriends.value = false;
      }
    };

    // Manejar cambios en la audiencia pública
    const handleAudiencePublicChange = (isPublic: boolean) => {
      isPublicPost.value = isPublic;
    };

    // Manejar cambios en socios seleccionados
    const handleSelectedSociosChange = (socios: string[]) => {
      selectedFriends.value = socios;
    };

    // Enviar solicitud de publicación
    const enviarSolicitudPublicacion = async () => {
      if (!inmuebleParaSolicitud.value) return;

      try {
        enviandoSolicitud.value = true;

        const targetUserIds = isPublicPost.value
          ? availableFriends.value.map((socio) => socio._id)
          : selectedFriends.value;

        // Llamada al backend para enviar la solicitud
        const response = await axios.post(
          `/msi-v5/owner/inmuebles-socios/${inmuebleParaSolicitud.value.id}/publicar-inmueble`,
          {
            socios: targetUserIds,
            metodo: "tradicional", // Valor por defecto, se puede hacer configurable más adelante
          }
        );

        if (response.data && response.data.statusCode === 200) {
          const data = response.data.data;

          // Cerrar modal y resetear
          cerrarSelectorSocios();

          // Mostrar confirmación de éxito con detalles
          const mensaje =
            data.solicitudes_enviadas > 0
              ? `Solicitud enviada exitosamente a ${
                  data.solicitudes_enviadas
                } socio${
                  data.solicitudes_enviadas !== 1 ? "s" : ""
                } para el inmueble "${data.inmueble_titulo}"`
              : "No se pudo enviar ninguna solicitud";

          const titulo =
            data.solicitudes_enviadas > 0
              ? "¡Solicitud enviada!"
              : "Sin solicitudes enviadas";

          if (data.solicitudes_enviadas > 0) {
            success(mensaje, titulo);
          } else {
            showError(mensaje, titulo);
          }

          // Log para debug
          console.log("📤 Respuesta del servidor:", {
            total_socios: data.total_socios,
            solicitudes_enviadas: data.solicitudes_enviadas,
            solicitudes_fallidas: data.solicitudes_fallidas,
            detalles: data.detalles,
          });

          // Actualizar la lista de inmuebles para reflejar las nuevas solicitudes enviadas
          await fetchInmuebles();
        } else {
          throw new Error(
            response.data?.data?.error || "Error en la respuesta del servidor"
          );
        }
      } catch (err) {
        console.error("Error al enviar solicitud:", err);

        // Determinar el mensaje de error más específico
        let errorMessage = "Error al enviar la solicitud. Intenta nuevamente.";

        if (err.response?.status === 404) {
          errorMessage =
            "Inmueble no encontrado o no tienes permisos para acceder a él.";
        } else if (err.response?.status === 400) {
          errorMessage =
            err.response.data?.data?.error || "Datos de solicitud inválidos.";
        } else if (err.response?.status === 403) {
          errorMessage = "No tienes permisos para realizar esta acción.";
        } else if (err.response?.data?.data?.error) {
          errorMessage = err.response.data.data.error;
        }

        showError(errorMessage, "Error");
      } finally {
        enviandoSolicitud.value = false;
      }
    };

    // Cerrar selector de socios
    const cerrarSelectorSocios = () => {
      showSociosSelector.value = false;
      inmuebleParaSolicitud.value = null;
      isPublicPost.value = true;
      selectedFriends.value = [];
    };

    // Funciones para manejar operaciones múltiples
    const getOperacionesDisponibles = (inmueble: InmuebleOwner): string[] => {
      const operaciones: string[] = [];
      if (inmueble.precios) {
        // Solo incluir operaciones que estén realmente disponibles
        if (inmueble.precios.venta?.disponible === true)
          operaciones.push("venta");
        if (inmueble.precios.renta?.disponible === true)
          operaciones.push("renta");
        if (inmueble.precios.traspaso?.disponible === true)
          operaciones.push("traspaso");
      } else {
        // Fallback para compatibilidad con formato anterior
        operaciones.push(inmueble.operacion);
      }
      return operaciones;
    };

    // Nueva función: obtener todas las operaciones del inmueble (disponibles o no)
    const getTodasOperaciones = (inmueble: InmuebleOwner): string[] => {
      const operaciones: string[] = [];
      if (inmueble.precios) {
        // Incluir todas las operaciones que tengan precio, independientemente de disponibilidad
        if (inmueble.precios.venta?.precio !== undefined)
          operaciones.push("venta");
        if (inmueble.precios.renta?.precio !== undefined)
          operaciones.push("renta");
        if (inmueble.precios.traspaso?.precio !== undefined)
          operaciones.push("traspaso");
      } else {
        // Fallback para compatibilidad con formato anterior
        operaciones.push(inmueble.operacion);
      }
      return operaciones;
    };

    // Función para verificar si una operación específica está disponible
    const isOperacionDisponible = (
      inmueble: InmuebleOwner,
      operacion: string
    ): boolean => {
      if (
        inmueble.precios &&
        inmueble.precios[operacion as keyof typeof inmueble.precios]
      ) {
        return (
          inmueble.precios[operacion as keyof typeof inmueble.precios]
            ?.disponible === true
        );
      }
      // Fallback: si no hay estructura de precios, asumir que la operación principal está disponible
      return inmueble.operacion === operacion;
    };

    // Función para obtener la información de comisión compartida de una operación
    const getComisionCompartida = (
      inmueble: InmuebleOwner,
      operacion: string
    ) => {
      if (
        inmueble.precios &&
        inmueble.precios[operacion as keyof typeof inmueble.precios]
      ) {
        const comision =
          inmueble.precios[operacion as keyof typeof inmueble.precios]
            ?.comision_compartida;

        // No mostrar comisiones del 0% ya que no tiene sentido
        if (comision && comision.porcentaje > 0) {
          return comision;
        }
      }
      return null;
    };

    // Función para formatear la información de comisión compartida
    // Solo se muestran comisiones con porcentaje > 0
    const formatComisionCompartida = (
      inmueble: InmuebleOwner,
      operacion: string
    ): string => {
      const comision = getComisionCompartida(inmueble, operacion);
      if (!comision) return "";

      const { porcentaje, tipo } = comision;

      switch (tipo) {
        case "sobre comision":
          return `${porcentaje}% sobre comisión`;
        case "sobre precio":
          return `${porcentaje}% sobre precio`;
        case "1 mes":
          return `${porcentaje}% sobre 1 mes`;
        case "cantidad fija":
          return `${porcentaje}% cantidad fija`;
        default:
          return `${porcentaje}%`;
      }
    };

    const getOperacionPrincipal = (inmueble: InmuebleOwner): string => {
      const operaciones = getTodasOperaciones(inmueble);
      // Prioridad: venta > renta > traspaso (según documentación del API)
      if (operaciones.includes("venta")) return "venta";
      if (operaciones.includes("renta")) return "renta";
      if (operaciones.includes("traspaso")) return "traspaso";
      return inmueble.operacion;
    };

    const getPrecioOperacion = (
      inmueble: InmuebleOwner,
      operacion: string
    ): number => {
      if (
        inmueble.precios &&
        inmueble.precios[operacion as keyof typeof inmueble.precios]
      ) {
        // Retornar precio independientemente de disponibilidad
        return (
          inmueble.precios[operacion as keyof typeof inmueble.precios]
            ?.precio || 0
        );
      }
      // Fallback para compatibilidad
      return inmueble.precio;
    };

    const getBadgeColor = (operacion: string): string => {
      switch (operacion) {
        case "venta":
          return "bg-green-600";
        case "renta":
          return "bg-blue-600";
        case "traspaso":
          return "bg-orange-600";
        default:
          return "bg-gray-600";
      }
    };

    // Funciones para clasificar los diferentes casos de casas compartidas

    // Caso 1: contratoSocio espera respuesta de contratoOwner
    const getSociosEsperandoRespuesta = (
      inmueble: InmuebleOwner
    ): InmuebleConSocio[] => {
      if (!contratoOwner.value) return [];
      return (
        inmueble.inmuebleConSocios?.filter(
          (socio) =>
            socio.autorizado === "No" &&
            socio.token != null &&
            socio.contrato !== contratoOwner.value &&
            socio.quien_solicita !== contratoOwner.value
        ) || []
      );
    };

    // Caso 2: El inmueble ya se encuentra publicado con contratoSocio
    const getSociosPublicados = (
      inmueble: InmuebleOwner
    ): InmuebleConSocio[] => {
      return (
        inmueble.inmuebleConSocios?.filter(
          (socio) => socio.autorizado === "Si"
        ) || []
      );
    };

    // Caso 3: contratoOwner espera una respuesta de contratoSocio
    const getSolicitudesPendientesOwner = (
      inmueble: InmuebleOwner
    ): InmuebleConSocio[] => {
      if (!contratoOwner.value) return [];
      return (
        inmueble.inmuebleConSocios?.filter(
          (socio) =>
            socio.autorizado === "No" &&
            socio.contrato !== contratoOwner.value &&
            socio.quien_solicita === contratoOwner.value
        ) || []
      );
    };

    // Función para obtener el número total de notificaciones (casos que requieren atención)
    const getSolicitudesPendientes = (inmueble: InmuebleOwner): number => {
      const esperandoRespuesta = getSociosEsperandoRespuesta(inmueble);
      return esperandoRespuesta.length;
    };

    // Función para verificar si el inmueble está publicado con socios
    const estaPublicadoConSocios = (inmueble: InmuebleOwner): boolean => {
      const sociosPublicados = getSociosPublicados(inmueble);
      return sociosPublicados.length > 0;
    };

    // Función para ver las solicitudes de socios
    const verSolicitudesSocios = (inmueble: InmuebleOwner) => {
      console.log("🔍 Ver solicitudes de socios para inmueble:", inmueble.id);

      const esperandoRespuesta = getSociosEsperandoRespuesta(inmueble);
      const sociosPublicados = getSociosPublicados(inmueble);
      const solicitudesPendientesOwner =
        getSolicitudesPendientesOwner(inmueble);

      const totalCasosCompartidos =
        esperandoRespuesta.length +
        sociosPublicados.length +
        solicitudesPendientesOwner.length;

      if (totalCasosCompartidos === 0) {
        showError(
          "Este inmueble no tiene registros de casas compartidas.",
          "Sin Registros"
        );
        return;
      }

      inmuebleConSolicitudes.value = inmueble;
      showSolicitudesModal.value = true;
    };

    // Abrir hilos de interés (placeholder visual)
    const abrirHilosInmueble = (inmueble: InmuebleOwner) => {
      console.log("💬 Hilos de interés para inmueble:", inmueble.id);
      warning(
        "Hilos de interés para inmuebles se encuentra en proceso de desarrollo",
        "Hilos de interés"
      );
    };

    // Cerrar modal de solicitudes
    const cerrarSolicitudesModal = () => {
      showSolicitudesModal.value = false;
      inmuebleConSolicitudes.value = null;
    };

    // Aceptar solicitud de socio
    const aceptarSolicitudSocio = async (solicitud: InmuebleConSocio) => {
      if (!inmuebleConSolicitudes.value) return;

      try {
        procesandoSolicitud.value = true;

        // Llamada al backend para aceptar solicitud
        const response = await axios.post(
          `/msi-v5/owner/inmuebles-socios/${inmuebleConSolicitudes.value.id}/aceptar-publicar-con-socio`,
          {
            solicitud_id: solicitud.id,
          }
        );

        if (response.data && response.status === 200) {
          console.log("✅ Solicitud aceptada:", response.data);
        } else {
          throw new Error("Error en la respuesta del servidor");
        }

        success(
          `Solicitud de ${
            solicitud.datos_socio.nombre_completo || "socio"
          } ha sido aceptada`,
          "Solicitud Aceptada"
        );

        // Remover la solicitud aceptada de la lista local inmediatamente
        if (inmuebleConSolicitudes.value?.inmuebleConSocios) {
          inmuebleConSolicitudes.value.inmuebleConSocios =
            inmuebleConSolicitudes.value.inmuebleConSocios.filter(
              (s: InmuebleConSocio) => s.id !== solicitud.id
            );

          // También actualizar la lista principal de inmuebles para reflejar el cambio en el badge
          const inmuebleIndex = inmuebles.value.findIndex(
            (inm) => inm.id === inmuebleConSolicitudes.value?.id
          );
          if (
            inmuebleIndex !== -1 &&
            inmuebles.value[inmuebleIndex].inmuebleConSocios
          ) {
            inmuebles.value[inmuebleIndex].inmuebleConSocios = inmuebles.value[
              inmuebleIndex
            ].inmuebleConSocios!.filter(
              (s: InmuebleConSocio) => s.id !== solicitud.id
            );
          }

          // Si ya no quedan solicitudes, cerrar la modal
          if (inmuebleConSolicitudes.value.inmuebleConSocios.length === 0) {
            cerrarSolicitudesModal();
          }
        }

        // Actualizar la lista de inmuebles en segundo plano
        await fetchInmuebles();
      } catch (error) {
        console.error("Error al aceptar solicitud:", error);
        showError(
          "Error al aceptar la solicitud. Intenta nuevamente.",
          "Error"
        );
      } finally {
        procesandoSolicitud.value = false;
      }
    };

    // Rechazar solicitud de socio
    const rechazarSolicitudSocio = async (solicitud: InmuebleConSocio) => {
      if (!inmuebleConSolicitudes.value) return;

      try {
        procesandoSolicitud.value = true;

        // Llamada al backend para rechazar solicitud
        const response = await axios.post(
          `/msi-v5/owner/inmuebles-socios/${inmuebleConSolicitudes.value.id}/rechazar-publicar-con-socio`,
          {
            solicitud_id: solicitud.id,
          }
        );

        if (response.data && response.status === 200) {
          console.log("❌ Solicitud rechazada:", response.data);
        } else {
          throw new Error("Error en la respuesta del servidor");
        }

        success(
          `Solicitud de ${
            solicitud.datos_socio.nombre_completo || "socio"
          } ha sido rechazada`,
          "Solicitud Rechazada"
        );

        // Remover la solicitud rechazada de la lista local inmediatamente
        if (inmuebleConSolicitudes.value?.inmuebleConSocios) {
          inmuebleConSolicitudes.value.inmuebleConSocios =
            inmuebleConSolicitudes.value.inmuebleConSocios.filter(
              (s: InmuebleConSocio) => s.id !== solicitud.id
            );

          // También actualizar la lista principal de inmuebles para reflejar el cambio en el badge
          const inmuebleIndex = inmuebles.value.findIndex(
            (inm) => inm.id === inmuebleConSolicitudes.value?.id
          );
          if (
            inmuebleIndex !== -1 &&
            inmuebles.value[inmuebleIndex].inmuebleConSocios
          ) {
            inmuebles.value[inmuebleIndex].inmuebleConSocios = inmuebles.value[
              inmuebleIndex
            ].inmuebleConSocios!.filter(
              (s: InmuebleConSocio) => s.id !== solicitud.id
            );
          }

          // Si ya no quedan solicitudes, cerrar la modal
          if (inmuebleConSolicitudes.value.inmuebleConSocios.length === 0) {
            cerrarSolicitudesModal();
          }
        }

        // Actualizar la lista de inmuebles en segundo plano
        await fetchInmuebles();
      } catch (error) {
        console.error("Error al rechazar solicitud:", error);
        showError(
          "Error al rechazar la solicitud. Intenta nuevamente.",
          "Error"
        );
      } finally {
        procesandoSolicitud.value = false;
      }
    };

    // Función de confirmación para acciones
    const confirmarAccion = (
      accion: () => void,
      tipo: "aceptar" | "rechazar" | "quitar" | "cancelar"
    ) => {
      const configuracion = {
        aceptar: {
          titulo: "Aceptar Solicitud",
          mensaje:
            "¿Estás seguro de que quieres aceptar esta solicitud? El socio podrá publicar tu inmueble en su web.",
        },
        rechazar: {
          titulo: "Rechazar Solicitud",
          mensaje:
            "¿Estás seguro de que quieres rechazar esta solicitud? El socio no podrá publicar tu inmueble.",
        },
        quitar: {
          titulo: "Quitar de Web",
          mensaje:
            "¿Estás seguro de que quieres quitar el inmueble de la web del socio? Se dejará de mostrar inmediatamente.",
        },
        cancelar: {
          titulo: "Cancelar Solicitud",
          mensaje:
            "¿Estás seguro de que quieres cancelar tu solicitud? El socio ya no recibirá tu petición.",
        },
      };

      const config = configuracion[tipo];
      confirm(config.mensaje, accion, config.titulo);
    };

    // Función para quitar inmueble de la web del socio
    const quitarDeSuWeb = async (socio: InmuebleConSocio) => {
      if (!inmuebleConSolicitudes.value) return;

      try {
        procesandoSolicitud.value = true;

        // Llamada al backend para quitar el inmueble
        const response = await axios.post(
          `/msi-v5/owner/inmuebles-socios/${inmuebleConSolicitudes.value.id}/quitar-de-web-socio`,
          {
            solicitud_id: socio.id,
          }
        );

        if (response.data && response.status === 200) {
          console.log("🚫 Inmueble quitado de web del socio:", response.data);
        } else {
          throw new Error("Error en la respuesta del servidor");
        }

        success(
          `Inmueble quitado de la web de ${
            socio.datos_socio.nombre_completo || "socio"
          }`,
          "Inmueble Retirado"
        );

        // Actualizar la lista local inmediatamente
        if (inmuebleConSolicitudes.value?.inmuebleConSocios) {
          inmuebleConSolicitudes.value.inmuebleConSocios =
            inmuebleConSolicitudes.value.inmuebleConSocios.filter(
              (s: InmuebleConSocio) => s.id !== socio.id
            );

          // También actualizar la lista principal de inmuebles
          const inmuebleIndex = inmuebles.value.findIndex(
            (inm) => inm.id === inmuebleConSolicitudes.value?.id
          );
          if (
            inmuebleIndex !== -1 &&
            inmuebles.value[inmuebleIndex].inmuebleConSocios
          ) {
            inmuebles.value[inmuebleIndex].inmuebleConSocios = inmuebles.value[
              inmuebleIndex
            ].inmuebleConSocios!.filter(
              (s: InmuebleConSocio) => s.id !== socio.id
            );
          }

          // Si ya no quedan registros, cerrar la modal
          if (inmuebleConSolicitudes.value.inmuebleConSocios.length === 0) {
            cerrarSolicitudesModal();
          }
        }

        // Actualizar la lista de inmuebles en segundo plano
        await fetchInmuebles();
      } catch (error) {
        console.error("Error al quitar inmueble de web del socio:", error);
        showError(
          "Error al quitar el inmueble de la web del socio. Intenta nuevamente.",
          "Error"
        );
      } finally {
        procesandoSolicitud.value = false;
      }
    };

    // Función para cancelar mi solicitud
    const cancelarMiSolicitud = async (solicitud: InmuebleConSocio) => {
      if (!inmuebleConSolicitudes.value) return;

      try {
        procesandoSolicitud.value = true;

        // Llamada al backend para cancelar solicitud
        const response = await axios.post(
          `/msi-v5/owner/inmuebles-socios/${inmuebleConSolicitudes.value.id}/cancelar-mi-solicitud`,
          {
            solicitud_id: solicitud.id,
          }
        );

        if (response.data && response.status === 200) {
          console.log("❌ Solicitud cancelada:", response.data);
        } else {
          throw new Error("Error en la respuesta del servidor");
        }

        success(
          `Tu solicitud a ${
            solicitud.datos_socio.nombre_completo || "socio"
          } ha sido cancelada`,
          "Solicitud Cancelada"
        );

        // Actualizar la lista local inmediatamente
        if (inmuebleConSolicitudes.value?.inmuebleConSocios) {
          inmuebleConSolicitudes.value.inmuebleConSocios =
            inmuebleConSolicitudes.value.inmuebleConSocios.filter(
              (s: InmuebleConSocio) => s.id !== solicitud.id
            );

          // También actualizar la lista principal de inmuebles
          const inmuebleIndex = inmuebles.value.findIndex(
            (inm) => inm.id === inmuebleConSolicitudes.value?.id
          );
          if (
            inmuebleIndex !== -1 &&
            inmuebles.value[inmuebleIndex].inmuebleConSocios
          ) {
            inmuebles.value[inmuebleIndex].inmuebleConSocios = inmuebles.value[
              inmuebleIndex
            ].inmuebleConSocios!.filter(
              (s: InmuebleConSocio) => s.id !== solicitud.id
            );
          }

          // Si ya no quedan registros, cerrar la modal
          if (inmuebleConSolicitudes.value.inmuebleConSocios.length === 0) {
            cerrarSolicitudesModal();
          }
        }

        // Actualizar la lista de inmuebles en segundo plano
        await fetchInmuebles();
      } catch (error) {
        console.error("Error al cancelar solicitud:", error);
        showError(
          "Error al cancelar la solicitud. Intenta nuevamente.",
          "Error"
        );
      } finally {
        procesandoSolicitud.value = false;
      }
    };

    const formatPrice = (price: number) => {
      return price.toLocaleString("es-MX");
    };

    function stripTags(input: string) {
      return input ? input.replace(/<[^>]*>/g, "") : "";
    }

    function decodeHtmlEntities(input: string) {
      if (!input) return "";
      const txt = document.createElement("textarea");
      txt.innerHTML = input;
      return txt.value;
    }

    onMounted(() => {
      initLazyObserver();
      nextTick(() => observeLazyImages());
      fetchInmuebles();
    });

    // Re-observar imágenes cuando cambia la lista renderizada
    watch(
      () => inmuebles.value,
      () => nextTick(() => observeLazyImages())
    );
    watch(
      () => filteredInmuebles.value,
      () => nextTick(() => observeLazyImages())
    );

    onUnmounted(() => {
      if (imageObserver) {
        imageObserver.disconnect();
        imageObserver = null;
      }
    });

    return {
      rootEl,
      mulbinUrl,
      inmuebles,
      filteredInmuebles,
      total,
      loading,
      error,
      fetchInmuebles,
      solicitarPublicacion,
      formatPrice,
      stripTags,
      decodeHtmlEntities,
      searchQuery,
      // Funciones para operaciones múltiples
      getOperacionesDisponibles,
      getTodasOperaciones,
      isOperacionDisponible,
      getComisionCompartida,
      formatComisionCompartida,
      getOperacionPrincipal,
      getPrecioOperacion,
      getBadgeColor,
      // Funciones para solicitudes de socios
      getSolicitudesPendientes,
      getSociosEsperandoRespuesta,
      getSociosPublicados,
      getSolicitudesPendientesOwner,
      estaPublicadoConSocios,
      verSolicitudesSocios,
      confirmarAccion,
      quitarDeSuWeb,
      cancelarMiSolicitud,
      // Estados del modal de solicitudes
      showSolicitudesModal,
      inmuebleConSolicitudes,
      procesandoSolicitud,
      cerrarSolicitudesModal,
      aceptarSolicitudSocio,
      rechazarSolicitudSocio,
      // Placeholder hilos de interés
      abrirHilosInmueble,
      // Estados del selector de socios
      showSociosSelector,
      inmuebleParaSolicitud,
      isPublicPost,
      selectedFriends,
      availableFriends,
      loadingFriends,
      enviandoSolicitud,
      textosAudienciaPublica,
      textosAudienciaPrivada,
      // Métodos del selector de socios
      loadUserFriends,
      handleAudiencePublicChange,
      handleSelectedSociosChange,
      enviarSolicitudPublicacion,
      cerrarSelectorSocios,
    };
  },
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

/* Estilos para el badge de notificaciones */
.badge-notification {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Hover effect para el botón de socios */
.socios-button:hover .badge-notification {
  animation: none;
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}

/* Efecto especial cuando hay notificaciones */
.socios-button:has(.badge-notification) {
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.socios-button:has(.badge-notification):hover {
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
  transform: translateY(-1px);
}
</style>
