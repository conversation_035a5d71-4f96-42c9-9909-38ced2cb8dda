# 🏠 InmueblesSocios.vue — Integración con msi-v5 (documentación final)

## Descripción

`InmueblesSocios.vue` lista los inmuebles de los socios directos del usuario autenticado consumiendo el endpoint `GET /msi-v5/owner/inmuebles/socios`. Ofrece:

- **🆕 NUEVO: Búsqueda simplificada** solo con campo de texto en tiempo real (local) incluyendo clave del inmueble
- **🆕 NUEVO: Indicador de coincidencias** muestra "X/Y coincidencias" mientras el usuario escribe
- **🆕 NUEVO: Botón favoritos rediseñado** con mejor ubicación y animaciones
- **🆕 NUEVO: Operaciones múltiples** con priorización visual según filtros inteligentes
- **🆕 NUEVO: Precios formateados** directamente desde el backend con información de comisiones
- **🆕 NUEVO: Visualización de clave** badge elegante con la clave única del inmueble (ej: "PROP-001")
- **🆕 NUEVO: Indicador de publicación** badge sobre la foto que muestra si el inmueble está publicado en mi web
- **🆕 NUEVO: Gestión de publicación** botón para solicitar publicar/quitar inmuebles de mi web
- **Favoritos** persistidos localmente mediante `localStorage`
- **Detalle y contacto** con el socio (WhatsApp, teléfono, email)
- **Carga perezosa de imágenes** con `IntersectionObserver` y fallback
- **Botón "Ir arriba"** cuando el buscador queda fuera de vista

---

## API consumida

Endpoint:

```
GET /msi-v5/owner/inmuebles/socios
```

Parámetros de consulta soportados:

- `operacion`: `venta` | `renta` | `traspaso`
- `tipo`: `casa` | `departamento` | `terreno` | `local` | `oficina` | `bodega`
- `ubicacion`: `norte` | `sur` | `este` | `oeste` | `centro`
- `precio_max`: número entero

Ejemplo de request:

```bash
GET /msi-v5/owner/inmuebles/socios?operacion=venta&tipo=casa&ubicacion=norte&precio_max=3000000
```

Respuesta esperada:

```json
{
  "statusCode": 200,
  "data": {
    "inmuebles": [
      {
        "id": "...",
        "titulo": "...",
        "descripcion": "...",
        "precio": 1234567,
        "operacion": "venta",
        "tipo": "casa",
        "ubicacion": "Zona Norte",
        "recamaras": 3,
        "banos": 2,
        "area": 120,
        "imagenes": [],
        "imagenPrincipal": "...",
        "publicadoEnMiWeb": true,
        "socio": { "id": "...", "nombre": "...", "empresa": "..." },
        "esFavorito": false,
        "fechaCreacion": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 25,
    "total_socios": 5,
    "filtros_aplicados": { "operacion": "venta" }
  }
}
```

Notas:

- La búsqueda de texto no se envía al servidor; se aplica localmente.
- El componente sincroniza `esFavorito` consultando el servicio de favoritos local.

---

## Comportamiento del buscador

- **Búsqueda por texto (local, en tiempo real)**: filtra sobre `titulo`, `descripcion`, `ubicacion`, `tipo`, `socio.nombre`, `socio.empresa`, `key` usando coincidencias parciales y normalización sin acentos.
- **Filtros avanzados (servidor)**: `operacion`, `tipo`, `ubicacion`, `precio_max`. Se aplican al hacer clic en el botón “Buscar inmuebles”, realizando una nueva consulta al endpoint.
- **Limpiar filtros**: restablece filtros y búsqueda y vuelve a consultar al servidor sin filtros.

---

## Favoritos

- Persistencia local mediante `favoritosService` (`localStorage`).
- `totalFavoritos` se mantiene reactivo suscribiéndose a cambios del servicio.
- `marcarFavorito(inmueble)` alterna el estado local y registra en consola el resultado.
- La vista de favoritos se activa con “Mis Favoritos” y muestra `InmueblesFavoritos`. El botón de cabecera emite `ir-a-filtros` al componente padre.

Limitación actual: no hay sincronización con backend para favoritos.

---

## Props y eventos

Props:

- `filtrosIniciales?: FiltrosIniciales` — filtros por defecto que se aplican antes de la primera carga.
  - `ubicacion?: string` — ciudad inicial
  - `operacion?: string` — operación inicial
  - `tipos?: number[]` — array de IDs de tipos de inmuebles
  - `🆕 operacionPrioritaria?: string` — **NUEVA**: operación a destacar visualmente en los cards
- `activarFavoritosInmediatamente?: boolean` — si es `true`, activa la vista de favoritos al montar.

Eventos emitidos:

- `total-updated: number` — total de inmuebles devuelto por el servidor.
- `ir-a-filtros` — solicitud al padre para navegar a filtros inteligentes.
- `favoritos-changed: boolean` — indica entrada/salida de la vista de favoritos.
- `favoritos-activados` — confirma que se activó la vista de favoritos tras `activarFavoritosInmediatamente`.

---

## Estados de UI y errores

- **Cargando**: indicador visible durante la consulta.
- **Error**: muestra mensaje; 403 redirige a `/` (sesión inválida).
- **Vacío**: mensajes contextuales según si hay búsqueda de texto o filtros aplicados.

---

## 🌐 Gestión de Publicación en Mi Web

### **🆕 NUEVO: Campo `publicadoEnMiWeb`**

La API ahora incluye el campo `publicadoEnMiWeb: boolean` que indica si el inmueble del socio ya está publicado en el sitio web del usuario autenticado.

#### **Indicadores Visuales**

1. **Badge sobre la foto**:

   - **Verde con checkmark**: "En mi web" (cuando `publicadoEnMiWeb: true`)
   - **Blanco con globo**: "Disponible" (cuando `publicadoEnMiWeb: false`)

2. **Botón de acción**:
   - **Icono `add-circle-outline` (azul)**: "Solicitar publicar en mi web"
   - **Icono `remove-circle-outline` (rojo)**: "Quitar este inmueble de mi web"

#### **Funcionalidad**

```typescript
// Función para alternar estado de publicación usando sistema de modales
const togglePublicacionEnMiWeb = (inmueble: Inmueble) => {
  const { confirm, success, error: showError } = useModal();

  // 1. Mostrar confirmación personalizada con modal
  confirm(
    mensaje,
    async () => {
      // 2. Llamar al endpoint correspondiente (pendiente implementación)
      // 3. Actualizar estado local
      // 4. Mostrar mensaje de éxito con modal personalizado
      success(mensajeExito, tituloExito);
    },
    titulo
  );
};
```

#### **Estados de UI**

```css
/* Badge sobre la foto */
.publicado-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  backdrop-filter: blur(4px);
}

/* Botón de acción */
.publicacion-btn {
  transition: colors 200ms;
}
```

#### **Sistema de Modales Integrado**

El componente utiliza el sistema de modales personalizado (`useModal`) en lugar de `window.confirm()` y `window.alert()`:

```typescript
// Importación del sistema de modales
import { useModal } from "../../composables/useModal";

// En el setup
const { confirm, success, error: showError } = useModal();

// Ejemplos de uso:
// 1. Confirmación de acción
confirm(
  "¿Quieres publicar este inmueble?",
  async () => {
    // Lógica de publicación
  },
  "Publicar en mi web"
);

// 2. Mensaje de éxito
success("Inmueble publicado exitosamente", "¡Publicado!");

// 3. Mensaje de error
showError("No se pudo completar la operación", "Error");
```

#### **Casos de Uso**

- **Publicar**: Usuario ve un inmueble interesante y quiere agregarlo a su web
- **Quitar**: Usuario ya no quiere mostrar cierto inmueble en su web
- **Visual**: Identificar rápidamente qué inmuebles ya están en mi web
- **Confirmaciones**: Modales personalizados con mejor UX que alertas nativas

---

## Interacciones adicionales

- **Contacto**: WhatsApp, teléfono y email desde los datos del socio.
- **🆕 NUEVO: Precios múltiples**: Muestra operación prioritaria destacada y operaciones secundarias en tamaño menor.
- **🆕 NUEVO: Formateo automático**: Usa `precio_formateado` del backend con moneda y separadores apropiados.
- **🆕 NUEVO: Información de comisiones**: Muestra porcentaje de comisión cuando está disponible.
- **Imágenes**: `IntersectionObserver` para lazy loading con fallback inmediato si no está disponible.
- **Scroll inteligente**: detección de visibilidad del buscador y botón "Ir arriba" para volver suavemente al buscador, soportando contenedor con scroll o `window`.

---

## Ejemplo de uso

```vue
<InmueblesSocios
  :filtros-iniciales="{
    ubicacion: 'norte',
    operacion: 'venta',
    operacionPrioritaria: 'renta', // 🆕 NUEVO: Destacar renta en la UI
  }"
  :activar-favoritos-inmediatamente="false"
  @total-updated="onTotal"
  @ir-a-filtros="abrirFiltros"
  @favoritos-changed="onFavoritosChanged"
  @favoritos-activados="onFavoritosActivados"
/>
```

### **🆕 NUEVO: Características del botón favoritos rediseñado**

```css
/* Mejoras visuales implementadas */
- Diseño con bordes redondeados (rounded-xl)
- Efecto hover con escalado de icono
- Contador con animación pulse
- Gradiente sutil en el fondo
- Sombras dinámicas
- Transiciones suaves
- Responsive design
```

### **🆕 NUEVO: Estados del indicador de filtros**

```typescript
// El componente recibe filtros desde FiltroInmueblesInteligente
// pero no los muestra visualmente en la interfaz
// Los filtros se aplican internamente para priorización de operaciones
```

### **🆕 NUEVO: Testing del buscador simplificado**

Para verificar que el buscador funciona correctamente:

```javascript
// Logs de debugging esperados:
console.log("🔍 Filtrado de texto:", {
  query: searchQuery.value,
  resultados: filteredInmuebles.value.length,
  total: inmuebles.value.length,
});

// Casos de prueba:
// 1. Escribir "casa" → filtra por tipo
// 2. Escribir "centro" → filtra por ubicación
// 3. Escribir "María" → filtra por nombre de socio
// 4. Escribir "amplio" → filtra por descripción
// 5. Escribir "PROP-001" → filtra por clave del inmueble
// 6. Limpiar campo → muestra todos los inmuebles
```

### **🆕 NUEVO: Debugging de operaciones múltiples**

```javascript
// Verificar priorización de operaciones:
console.log("Operación prioritaria:", getOperacionPrioritaria(inmueble));
console.log("Operaciones secundarias:", getOperacionesSecundarias(inmueble));
console.log("Filtro prioritario activo:", operacionPrioritaria.value);
```

### **🆕 NUEVO: Indicador de Coincidencias Inteligente**

#### **Comportamiento Visual Dinámico**

```css
/* Estados de color del indicador */
- Sin resultados (0 coincidencias): Rojo con ícono de alerta
- Coincidencias parciales (1-N): Verde con ícono de búsqueda
- Todos los resultados (N/N): Azul con ícono de búsqueda
```

#### **Textos Contextuales**

```javascript
// Ejemplos de textos según porcentaje:
"Sin resultados"; // 0% (0 coincidencias)
"Pocas coincidencias"; // 1-24%
"Algunas coincidencias"; // 25-49%
"Buena coincidencia"; // 50-74%
"Muy buena coincidencia"; // 75-99%
"Todos los inmuebles"; // 100% (todas las coincidencias)
```

#### **Animaciones y Efectos**

```css
/* Mejoras implementadas */
- Transición suave entre estados: duration-300
- Escalado sutil cuando no hay resultados: scale-105
- Colores dinámicos según cantidad de coincidencias
- Layout responsive con justify-between
- Badge redondeado para el texto contextual
```

#### **Ejemplo de Uso en Tiempo Real**

```
// Usuario escribe "casa" en buscador:
🔍 15/53 coincidencias [Buena coincidencia]

// Usuario escribe "casa centro":
🔍 3/53 coincidencias [Pocas coincidencias]

// Usuario escribe "xyz123":
⚠️ 0/53 coincidencias [Sin resultados]

// Usuario borra todo:
(Indicador se oculta, muestra todos los inmuebles)
```

````

---

## Características del diseño

### **🆕 NUEVO: Badge de clave del inmueble**

```css
/* Características del diseño implementado */
- Posición: Esquina superior derecha del título
- Tipografía: Font mono para mejor legibilidad de códigos
- Colores: Tema mulbin (text-mulbin-600, bg-mulbin-50, border-mulbin-200)
- Interactividad: Tooltip con información completa
- Responsive: Se mantiene visible en todas las resoluciones
- Accesibilidad: Alto contraste y texto descriptivo
````

### **Integración en búsqueda**

```typescript
// La búsqueda ahora incluye la clave del inmueble
const normalizedKey = normalizeText(inmueble.key || "");

// Ejemplos de búsqueda por clave:
// "PROP" → encuentra "PROP-001", "PROP-025", etc.
// "001" → encuentra "PROP-001", "HOUSE-001", etc.
// "prop-001" → encuentra "PROP-001" (sin distinción de mayúsculas)
```

## Compatibilidad y dependencias

- Vue 3.x, TypeScript, Tailwind CSS
- Axios
- Navegadores modernos (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)

---

## Consideraciones

- No hay paginación ni carga incremental en el componente actual.
- `ubicacion` en el request usa valores cortos (`norte`, `sur`, `este`, `oeste`, `centro`); la UI muestra etiquetas legibles (p. ej., "Zona Norte").
- **🆕 NUEVO: Compatibilidad**: Mantiene soporte para API antigua mediante campos `precio`, `operacion`, `precio_principal` y `operacion_principal`.
- **🆕 NUEVO: Priorización inteligente**: Si no se especifica operación prioritaria, usa el orden del backend (venta > renta > traspaso).
- **🆕 NUEVO: Clave del inmueble**: Campo `key` integrado en interfaz TypeScript, visualización y búsqueda local.
- **🆕 NUEVO: Publicación en mi web**: Campo `publicadoEnMiWeb` con indicadores visuales y funcionalidad de gestión.
- **🆕 NUEVO: Sistema de modales**: Integración completa con `useModal` para confirmaciones y alertas personalizadas.
- **⚠️ Pendiente**: Los endpoints para publicar/quitar inmuebles aún no están implementados en el backend.
- **✅ Simplificado**: Sin indicador visual de filtros aplicados (eliminado por el usuario).
