<template>
  <div class="filtro-inmuebles-inteligente">
    <!-- Estad<PERSON> de Carga -->
    <div v-if="loading" class="loading-state">
      <div
        class="mx-auto w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
      ></div>
      <p class="mt-2 text-center text-gray-600">
        Cargando opciones disponibles...
      </p>
    </div>

    <!-- Error State -->
    <div
      v-else-if="error"
      class="p-4 bg-red-50 rounded-lg border border-red-200 error-state"
    >
      <div class="flex items-center">
        <svg
          class="mr-2 w-5 h-5 text-red-600"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
            clip-rule="evenodd"
          />
        </svg>
        <span class="text-red-700">{{ error }}</span>
      </div>
      <button
        @click="cargarMetadatos"
        class="mt-2 text-sm text-red-600 underline hover:text-red-800"
      >
        Intentar de nuevo
      </button>
    </div>

    <!-- Sin Inmuebles Disponibles -->
    <div
      v-else-if="!tieneInmuebles"
      class="p-6 text-center bg-gray-50 rounded-lg border border-gray-200 sin-inmuebles"
    >
      <svg
        class="mx-auto mb-4 w-12 h-12 text-gray-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m4 0V9a1 1 0 011-1h4a1 1 0 011 1v12M13 7h.01M9 7h.01"
        />
      </svg>
      <h3 class="mb-2 text-lg font-medium text-gray-900">
        No hay inmuebles disponibles
      </h3>
      <p class="mb-4 text-gray-600">{{ mensajeSinInmuebles }}</p>
      <div class="flex gap-3 justify-center">
        <button
          @click="verMisInmuebles"
          class="px-4 py-2 text-white rounded-lg transition-colors bg-mulbin-600 hover:bg-mulbin-700"
        >
          Mis Inmuebles
        </button>
        <button
          @click="$emit('irASocios')"
          class="px-4 py-2 text-white bg-blue-600 rounded-lg transition-colors hover:bg-blue-700"
        >
          Ver Mis Socios
        </button>
      </div>
    </div>

    <!-- Selector de Ciudad (Paso 1) -->
    <div v-else-if="!ciudadSeleccionada" class="selector-ciudad">
      <!-- Header con Recomendación -->
      <div
        class="p-4 mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg header-recomendacion"
      >
        <div class="flex justify-between items-start mb-2">
          <h3 class="text-lg font-semibold text-gray-900">
            <ion-icon
              name="search-outline"
              class="mr-2 text-mulbin-600"
            ></ion-icon>
            ¿En qué ciudad buscas inmuebles?
          </h3>

          <div class="flex items-center">
            <!-- 🆕 Favoritos -->
            <button
              v-if="totalFavoritos > 0"
              @click="irAFavoritos"
              class="flex items-center px-3 py-2 text-sm font-medium text-yellow-700 bg-yellow-100 rounded-lg border border-yellow-300 transition-colors hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
              :title="`Tienes ${totalFavoritos} inmuebles marcados como favoritos`"
            >
              <ion-icon name="star" class="mr-2"></ion-icon>
              <span class="hidden sm:inline">Mis Favoritos</span>
              <span class="sm:hidden">★</span>
              <span
                class="px-2 py-0.5 ml-1 text-xs font-bold text-yellow-800 bg-yellow-200 rounded-full"
              >
                {{ totalFavoritos }}
              </span>
            </button>

            <!-- 🆕 Ver solo mis inmuebles (owner) -->
            <button
              @click.stop="verMisInmuebles"
              class="flex relative items-center px-3 py-2 ml-2 text-sm font-medium text-white rounded-lg border shadow-md transition-all duration-300 shadow-mulbin-700 bg-mulbin-700 border-mulbin-700 hover:bg-mulbin-600 hover:scale-105 hover:outline-none hover:ring-2 hover:ring-mulbin-500 hover:ring-offset-2"
              title="Mis inmuebles en la Multibolsa"
            >
              <!-- Efecto de respiración con anillo -->
              <div
                class="absolute inset-0 rounded-lg opacity-30 animate-pulse bg-mulbin-400"
              ></div>
              <div class="flex relative items-center">
                <ion-icon name="home-outline" class="mr-2"></ion-icon>
                Mis Inmuebles
              </div>
            </button>
          </div>
        </div>
        <div v-if="recomendacion?.mensaje" class="recomendacion-inteligente">
          <div class="flex items-start">
            <svg
              class="flex-shrink-0 mt-0.5 mr-2 w-5 h-5 text-blue-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
              />
            </svg>
            <div>
              <p class="text-sm text-blue-700">{{ recomendacion.mensaje }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Grid de Ciudades -->
      <div class="grid grid-cols-1 gap-4 px-3 md:grid-cols-2 lg:grid-cols-3">
        <button
          v-for="ciudad in ciudades"
          :key="ciudad.nombre"
          @click="seleccionarCiudad(ciudad)"
          class="relative p-4 bg-white rounded-lg border border-gray-200 transition-all duration-200 ciudad-card group hover:border-blue-300 hover:shadow-lg"
          :class="{
            'border-blue-500 shadow-md':
              ciudad.nombre === recomendacion?.ciudad_destacada,
          }"
        >
          <!-- Badge de Recomendado -->
          <div
            v-if="ciudad.nombre === recomendacion?.ciudad_destacada"
            class="absolute -top-2 -right-2 z-10 px-2 py-1 text-xs text-white bg-blue-600 rounded-full"
          >
            Recomendado
          </div>

          <div class="text-left">
            <h3 class="mb-1 font-semibold text-gray-900">
              {{ ciudad.nombre }}
            </h3>
            <p class="mb-3 text-sm text-gray-600">
              {{ ciudad.total_inmuebles }} inmuebles disponibles
            </p>

            <!-- Operaciones disponibles en esta ciudad -->
            <div class="flex flex-wrap gap-1 operaciones-preview">
              <span
                v-for="(count, operacion) in ciudad.operaciones"
                :key="operacion"
                class="inline-block px-2 py-1 text-xs text-gray-700 bg-gray-100 rounded"
              >
                {{ getOperacionLabel(String(operacion)) }}: {{ count }}
              </span>
            </div>
          </div>

          <!-- Flecha de navegación -->
          <div
            class="absolute right-3 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 group-hover:text-blue-600"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </button>
      </div>

      <!-- Estadísticas Globales -->
      <div class="p-4 mt-6 bg-gray-50 rounded-lg estadisticas-globales">
        <div class="grid grid-cols-2 gap-4 text-center md:grid-cols-5">
          <div>
            <p class="text-2xl font-bold text-blue-600">{{ totalInmuebles }}</p>
            <p class="text-sm text-gray-600">Total Inmuebles</p>
          </div>
          <div>
            <p class="text-2xl font-bold text-green-600">{{ totalSocios }}</p>
            <p class="text-sm text-gray-600">Socios Activos</p>
          </div>
          <div>
            <p class="text-2xl font-bold text-purple-600">
              {{ ciudades.length }}
            </p>
            <p class="text-sm text-gray-600">Ciudades</p>
          </div>
          <div>
            <p class="text-2xl font-bold text-orange-600">
              {{ operaciones.length }}
            </p>
            <p class="text-sm text-gray-600">Tipos de Operación</p>
          </div>
          <div>
            <p class="text-2xl font-bold text-teal-600">
              {{ tipos.length }}
            </p>
            <p class="text-sm text-gray-600">Tipos de Inmuebles</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Selector de Operación (Paso 2) -->
    <div v-else-if="!operacionSeleccionada" class="selector-operacion">
      <!-- Breadcrumb -->
      <div class="flex justify-between items-center mb-4 breadcrumb">
        <div class="flex items-center">
          <button
            @click="volverACiudades"
            class="flex items-center text-blue-600 transition-colors hover:text-blue-800"
            title="Cambiar ciudad"
          >
            <svg
              class="mr-1 w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
            {{ ciudadSeleccionada.nombre }}
          </button>
        </div>

        <!-- Botón de favoritos -->
        <button
          v-if="totalFavoritos > 0"
          @click="irAFavoritos"
          class="flex items-center px-3 py-2 text-sm font-medium text-yellow-700 bg-yellow-100 rounded-lg border border-yellow-300 transition-colors hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
          :title="`Tienes ${totalFavoritos} inmuebles marcados como favoritos`"
        >
          <ion-icon name="star" class="mr-2"></ion-icon>
          <span class="hidden sm:inline">Mis Favoritos</span>
          <span class="sm:hidden">★</span>
          <span
            class="px-2 py-0.5 ml-1 text-xs font-bold text-yellow-800 bg-yellow-200 rounded-full"
          >
            {{ totalFavoritos }}
          </span>
        </button>
      </div>

      <!-- Header -->
      <div
        class="p-3 mb-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg"
      >
        <div class="text-center">
          <h2 class="mb-3 text-2xl font-bold text-gray-900">
            ¿Qué tipo de operación buscas en
            <span class="text-green-600">{{ ciudadSeleccionada.nombre }}</span
            >?
          </h2>
          <div
            class="inline-flex items-center px-4 py-2 rounded-full shadow-sm bg-white/80"
          >
            <svg
              class="mr-2 w-4 h-4 text-green-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                clip-rule="evenodd"
              />
            </svg>
            <span class="text-sm font-medium text-green-700">
              {{ ciudadSeleccionada.total_inmuebles }} inmuebles disponibles en
              esta ciudad
            </span>
          </div>
        </div>
      </div>

      <!-- Grid de Operaciones -->
      <div class="grid grid-cols-1 gap-4 px-3 pb-3 md:grid-cols-3">
        <button
          v-for="operacion in operacionesDeCiudad"
          :key="operacion.tipo"
          @click="seleccionarOperacion(operacion)"
          class="relative p-6 bg-white rounded-lg border border-gray-200 transition-all duration-200 operacion-card group hover:border-green-300 hover:shadow-lg"
          :class="{
            'border-green-500 shadow-md':
              operacion.tipo === recomendacion?.operacion_destacada,
          }"
        >
          <!-- Badge de Recomendado -->
          <div
            v-if="operacion.tipo === recomendacion?.operacion_destacada"
            class="absolute -top-2 -right-2 z-10 px-2 py-1 text-xs text-white bg-green-600 rounded-full"
          >
            Más Inmuebles
          </div>

          <div class="text-center">
            <!-- Icono de Operación -->
            <div class="mb-3 icon-operacion">
              <svg
                v-if="operacion.tipo === 'venta'"
                class="mx-auto w-8 h-8 text-blue-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                  clip-rule="evenodd"
                />
              </svg>
              <svg
                v-else-if="operacion.tipo === 'renta'"
                class="mx-auto w-8 h-8 text-green-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  clip-rule="evenodd"
                />
              </svg>
              <svg
                v-else
                class="mx-auto w-8 h-8 text-purple-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l3.707 3.707A1 1 0 0018 17V7a1 1 0 00-.293-.707z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>

            <h3 class="mb-1 font-semibold text-gray-900">
              {{ operacion.label }}
            </h3>
            <p class="mb-1 text-2xl font-bold text-gray-800">
              {{ operacion.count }}
            </p>
            <p class="text-sm text-gray-600">inmuebles disponibles</p>
          </div>

          <!-- Flecha de navegación -->
          <div class="flex justify-center mt-4">
            <div
              class="text-gray-400 transition-colors group-hover:text-green-600"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Loading Tipos (Paso 3a) -->
    <div v-else-if="loadingTipos" class="loading-tipos">
      <!-- Breadcrumb -->
      <div class="flex justify-between items-center mb-4 breadcrumb">
        <div class="flex items-center">
          <button
            @click="volverACiudades"
            class="flex items-center text-blue-600 transition-colors hover:text-blue-800"
            title="Cambiar ciudad"
          >
            <svg
              class="mr-1 w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
            {{ ciudadSeleccionada.nombre }}
          </button>
          <span class="mx-2 text-gray-400">/</span>
          <button
            @click="volverAOperaciones"
            class="text-green-600 transition-colors hover:text-green-800"
            title="Cambiar operación"
          >
            {{ operacionSeleccionada.label }}
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div class="flex flex-col justify-center items-center py-12">
        <div
          class="mx-auto w-8 h-8 rounded-full border-b-2 border-purple-600 animate-spin"
        ></div>
        <p class="mt-4 text-center text-gray-600">
          Cargando tipos de inmuebles disponibles...
        </p>
        <p class="mt-2 text-sm text-center text-gray-500">
          {{ operacionSeleccionada.label }} en {{ ciudadSeleccionada.nombre }}
        </p>
      </div>
    </div>

    <!-- Selector de Tipos (Paso 3b) -->
    <div v-else class="selector-tipos">
      <!-- Breadcrumb -->
      <div class="flex justify-between items-center mb-4 breadcrumb">
        <div class="flex items-center">
          <button
            @click="volverACiudades"
            class="flex items-center text-blue-600 transition-colors hover:text-blue-800"
            title="Cambiar ciudad"
          >
            <svg
              class="mr-1 w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
            {{ ciudadSeleccionada.nombre }}
          </button>
          <span class="mx-2 text-gray-400">/</span>
          <button
            @click="volverAOperaciones"
            class="text-green-600 transition-colors hover:text-green-800"
            title="Cambiar operación"
          >
            {{ operacionSeleccionada.label }}
          </button>
        </div>

        <!-- Botón de favoritos en paso 3 -->
        <button
          v-if="totalFavoritos > 0"
          @click="irAFavoritos"
          class="flex items-center px-3 py-2 text-sm font-medium text-yellow-700 bg-yellow-100 rounded-lg border border-yellow-300 transition-colors hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
          :title="`Tienes ${totalFavoritos} inmuebles marcados como favoritos`"
        >
          <ion-icon name="star" class="mr-2"></ion-icon>
          <span class="hidden sm:inline">Mis Favoritos</span>
          <span class="sm:hidden">★</span>
          <span
            class="px-2 py-0.5 ml-1 text-xs font-bold text-yellow-800 bg-yellow-200 rounded-full"
          >
            {{ totalFavoritos }}
          </span>
        </button>
      </div>

      <!-- Header -->
      <div
        class="p-3 mb-6 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg"
      >
        <div class="text-center">
          <h2 class="mb-3 text-2xl font-bold text-gray-900">
            ¿Qué tipos de inmuebles buscas?
          </h2>
          <div class="mb-2">
            <span class="text-sm text-gray-600">en </span>
            <span class="font-medium text-blue-600">{{
              ciudadSeleccionada.nombre
            }}</span>
            <span class="text-sm text-gray-600"> para </span>
            <span class="font-medium text-green-600">
              {{ operacionSeleccionada.label }}
            </span>
          </div>
        </div>

        <!-- Sin Tipos Disponibles -->
        <div
          v-if="tiposFiltrados.length === 0"
          class="p-6 mb-6 text-center bg-orange-50 rounded-lg border border-orange-200"
        >
          <svg
            class="mx-auto mb-4 w-12 h-12 text-orange-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.864-.833-2.633 0L4.18 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <h3 class="mb-2 text-lg font-medium text-orange-900">
            No hay tipos de inmuebles disponibles
          </h3>
          <p class="mb-4 text-orange-700">
            Para <strong>{{ operacionSeleccionada.label }}</strong> en
            <strong>{{ ciudadSeleccionada.nombre }}</strong> no hay inmuebles
            disponibles en este momento.
          </p>
          <div class="flex gap-3 justify-center">
            <button
              @click="volverAOperaciones"
              class="px-4 py-2 text-orange-600 bg-white rounded-lg border border-orange-300 transition-colors hover:bg-orange-50"
            >
              Cambiar Operación
            </button>
            <button
              @click="volverACiudades"
              class="px-4 py-2 text-white bg-orange-600 rounded-lg transition-colors hover:bg-orange-700"
            >
              Cambiar Ciudad
            </button>
          </div>
        </div>

        <!-- Barra de tipos seleccionados (solo si hay tipos disponibles) -->
        <div
          v-else
          class="p-3 bg-purple-50 rounded-lg border border-purple-200"
        >
          <div class="flex justify-between items-center">
            <div>
              <p class="font-medium text-purple-900">
                <span v-if="tiposSeleccionados.length === 0">
                  Selecciona uno o más tipos de inmuebles
                </span>
                <span v-else>
                  {{ tiposSeleccionados.length }} tipo{{
                    tiposSeleccionados.length > 1 ? "s" : ""
                  }}
                  seleccionado{{ tiposSeleccionados.length > 1 ? "s" : "" }}
                </span>
              </p>
              <p class="text-sm text-purple-700">
                <span v-if="tiposSeleccionados.length === 0">
                  Haz clic en los tipos que desees incluir en tu búsqueda
                </span>
                <span v-else>
                  {{ tiposSeleccionados.map((t) => t.nombre).join(", ") }}
                </span>
              </p>
            </div>
            <button
              @click="confirmarTipos"
              :disabled="tiposSeleccionados.length === 0"
              class="px-4 py-2 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
              :class="{
                'bg-purple-600 hover:bg-purple-700':
                  tiposSeleccionados.length > 0,
                'bg-gray-400 cursor-not-allowed':
                  tiposSeleccionados.length === 0,
              }"
            >
              <span v-if="tiposSeleccionados.length === 0">
                Selecciona tipos
              </span>
              <span v-else>
                Buscar Inmuebles
                <svg
                  class="inline-block ml-1 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- Grid de Tipos (con selección múltiple) -->
      <div
        v-if="tiposFiltrados.length > 0"
        class="grid grid-cols-1 gap-4 px-3 pb-3 md:grid-cols-2 lg:grid-cols-3"
      >
        <div
          v-for="tipo in tiposFiltrados"
          :key="tipo.id"
          @click="toggleTipo(tipo)"
          class="relative p-6 bg-white rounded-lg border-2 transition-all duration-200 cursor-pointer tipo-card group"
          :class="{
            'border-purple-500 bg-purple-50 shadow-lg':
              isTipoSeleccionado(tipo),
            'border-gray-200 hover:border-purple-300 hover:shadow-md':
              !isTipoSeleccionado(tipo),
            'border-purple-600 shadow-xl': esElTipoMasPopular(tipo),
          }"
        >
          <!-- Badge de Más Popular -->
          <div
            v-if="esElTipoMasPopular(tipo)"
            class="absolute -top-2 -right-2 z-10 px-2 py-1 text-xs text-white bg-purple-600 rounded-full"
          >
            Más Inmuebles
          </div>

          <!-- Checkbox visual -->
          <div class="absolute top-3 right-3">
            <div
              class="flex justify-center items-center w-6 h-6 rounded-full border-2 transition-all duration-200"
              :class="{
                'border-purple-500 bg-purple-500': isTipoSeleccionado(tipo),
                'border-gray-300 bg-white': !isTipoSeleccionado(tipo),
              }"
            >
              <svg
                v-if="isTipoSeleccionado(tipo)"
                class="w-4 h-4 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>

          <div class="pr-8 text-center">
            <!-- Icono del tipo de inmueble -->
            <div class="mb-3 icon-tipo">
              <svg
                v-if="tipo.nombre.toLowerCase().includes('casa')"
                class="mx-auto w-8 h-8 text-purple-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                />
              </svg>
              <svg
                v-else-if="tipo.nombre.toLowerCase().includes('departamento')"
                class="mx-auto w-8 h-8 text-purple-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 2h8v8H6V6zm2 2v4h4V8H8z"
                  clip-rule="evenodd"
                />
              </svg>
              <svg
                v-else-if="tipo.nombre.toLowerCase().includes('terreno')"
                class="mx-auto w-8 h-8 text-purple-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 6.707 6.293a1 1 0 00-1.414 1.414l4 4a1 1 0 001.414 0l4-4z"
                  clip-rule="evenodd"
                />
              </svg>
              <svg
                v-else
                class="mx-auto w-8 h-8 text-purple-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>

            <h3 class="mb-1 font-semibold text-gray-900">
              {{ tipo.plural }}
            </h3>
            <p class="mb-1 text-xl font-bold text-gray-800">
              {{ tipo.total_inmuebles }}
            </p>
            <p class="text-sm text-gray-600">disponibles</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, computed, onMounted, onUnmounted, defineComponent } from "vue";
import axios from "axios";
import favoritosService from "../../services/favoritosService";

export default defineComponent({
  name: "FiltroInmueblesInteligente",

  props: {
    autoCargar: {
      type: Boolean,
      default: true,
    },
  },

  emits: ["irASocios", "cargarInmuebles", "verFavoritos"],

  setup(props, { emit }) {
    // Estado reactivo
    const loading = ref(false);
    const error = ref("");

    // Datos del endpoint - primera llamada (modo completo)
    const ciudades = ref<any[]>([]);
    const operaciones = ref<any[]>([]);
    const tipos = ref<any[]>([]);
    const totalInmuebles = ref(0);
    const totalSocios = ref(0);
    const recomendacion = ref<any>(null);

    // Datos del endpoint - segunda llamada (modo filtrado)
    const tiposFiltrados = ref<any[]>([]);
    const totalInmueblesFiltrados = ref(0);
    const loadingTipos = ref(false);
    const errorTipos = ref("");

    // Estado de selección
    const ciudadSeleccionada = ref<any>(null);
    const tiposSeleccionados = ref<any[]>([]);
    const operacionSeleccionada = ref<any>(null);

    // 🆕 NUEVO: Estado de favoritos
    const totalFavoritos = ref(favoritosService.getFavoritos().length);
    let unsubscribeFavoritos: (() => void) | null = null;

    // Computed
    const tieneInmuebles = computed(() => totalInmuebles.value > 0);

    const mensajeSinInmuebles = computed(() => {
      if (totalSocios.value === 0) {
        return "No tienes socios directos autorizados. Invita socios para ver sus inmuebles.";
      }
      return "Tus socios no tienen inmuebles publicados en este momento.";
    });

    const operacionesDeCiudad = computed(() => {
      if (!ciudadSeleccionada.value) return [];

      const operacionesCiudad = ciudadSeleccionada.value.operaciones || {};

      return Object.entries(operacionesCiudad)
        .map(([tipo, count]) => ({
          tipo,
          label: getOperacionLabel(tipo),
          count,
        }))
        .sort((a, b) => (b.count as number) - (a.count as number));
    });

    // Función para identificar el tipo más popular (ya no computed, uso directo en template)
    const esElTipoMasPopular = (tipo: any) => {
      if (tiposFiltrados.value.length === 0) return false;
      const tipoTopInmuebles = Math.max(
        ...tiposFiltrados.value.map((t) => t.total_inmuebles)
      );
      return tipo.total_inmuebles === tipoTopInmuebles;
    };

    // Métodos
    const cargarMetadatos = async () => {
      loading.value = true;
      error.value = "";

      try {
        console.log("🔍 Cargando metadatos de inmuebles...");

        const response = await axios.get(
          "/msi-v5/owner/inmuebles/socios/metadata"
        );

        if (response.data && response.data.statusCode === 200) {
          const data = response.data.data;

          ciudades.value = data.ciudades || [];
          operaciones.value = data.operaciones || [];
          tipos.value = data.tipos || [];
          totalInmuebles.value = data.total_inmuebles || 0;
          totalSocios.value = data.total_socios || 0;
          recomendacion.value = data.recomendacion || null;

          console.log("📊 Metadatos cargados exitosamente:", {
            ciudades: ciudades.value.length,
            operaciones: operaciones.value.length,
            tipos: tipos.value.length,
            total_inmuebles: totalInmuebles.value,
            recomendacion: recomendacion.value?.mensaje,
          });
        }
      } catch (err: any) {
        console.error("❌ Error al cargar metadatos:", err);

        if (err.response?.status === 403) {
          window.location.href = "/";
          return;
        }

        error.value = "No se pudieron cargar las opciones disponibles";
      } finally {
        loading.value = false;
      }
    };

    const seleccionarCiudad = (ciudad: any) => {
      ciudadSeleccionada.value = ciudad;
      console.log("🏙️ Ciudad seleccionada:", ciudad.nombre);
    };

    const toggleTipo = (tipo: any) => {
      const index = tiposSeleccionados.value.findIndex((t) => t.id === tipo.id);

      if (index === -1) {
        // Agregar tipo
        tiposSeleccionados.value.push(tipo);
        console.log("✅ Tipo agregado:", tipo.nombre);
      } else {
        // Remover tipo
        tiposSeleccionados.value.splice(index, 1);
        console.log("❌ Tipo removido:", tipo.nombre);
      }

      console.log(
        "🏗️ Tipos seleccionados:",
        tiposSeleccionados.value.map((t) => t.nombre)
      );
    };

    const isTipoSeleccionado = (tipo: any) => {
      return tiposSeleccionados.value.some((t) => t.id === tipo.id);
    };

    const seleccionarTipos = () => {
      console.log(
        "✅ Tipos confirmados:",
        tiposSeleccionados.value.map((t) => t.nombre)
      );
      // No necesitamos hacer nada más, ya que tiposSeleccionados ya tiene los tipos
      // El template se actualizará automáticamente para mostrar el paso 3
    };

    const seleccionarOperacion = async (operacion: any) => {
      operacionSeleccionada.value = operacion;
      console.log("🏠 Operación seleccionada:", operacion.tipo);

      // Segunda llamada al API con filtros específicos
      await cargarTiposFiltrados(
        ciudadSeleccionada.value.nombre,
        operacion.tipo
      );
    };

    const cargarTiposFiltrados = async (ciudad: string, operacion: string) => {
      loadingTipos.value = true;
      errorTipos.value = "";
      tiposFiltrados.value = [];
      totalInmueblesFiltrados.value = 0;

      try {
        console.log("🔍 Cargando tipos filtrados para:", { ciudad, operacion });

        const params = new URLSearchParams({
          ciudad: ciudad,
          operacion: operacion,
        });

        const response = await axios.get(
          `/msi-v5/owner/inmuebles/socios/metadata?${params}`
        );

        if (response.data && response.data.statusCode === 200) {
          const data = response.data.data;

          tiposFiltrados.value = data.tipos || [];
          totalInmueblesFiltrados.value = data.total_inmuebles || 0;

          console.log("🏠 Tipos filtrados cargados exitosamente:", {
            ciudad: data.filtros_aplicados?.ciudad,
            operacion: data.filtros_aplicados?.operacion,
            tipos: tiposFiltrados.value.length,
            total_inmuebles: totalInmueblesFiltrados.value,
          });

          // Verificar si hay tipos disponibles
          if (tiposFiltrados.value.length === 0) {
            console.warn(
              `⚠️ No hay tipos disponibles para ${operacion} en ${ciudad}`
            );
          }
        }
      } catch (err: any) {
        console.error("❌ Error al cargar tipos filtrados:", err);

        if (err.response?.status === 403) {
          window.location.href = "/";
          return;
        }

        errorTipos.value = "No se pudieron cargar los tipos disponibles";
      } finally {
        loadingTipos.value = false;
      }
    };

    const volverACiudades = () => {
      ciudadSeleccionada.value = null;
      tiposSeleccionados.value = [];
      operacionSeleccionada.value = null;
      tiposFiltrados.value = [];
      totalInmueblesFiltrados.value = 0;
      loadingTipos.value = false;
      errorTipos.value = "";
    };

    const volverAOperaciones = () => {
      operacionSeleccionada.value = null; // Resetear operación para volver al paso 2
      tiposSeleccionados.value = [];
      tiposFiltrados.value = [];
      totalInmueblesFiltrados.value = 0;
      loadingTipos.value = false;
      errorTipos.value = "";
    };

    const reiniciarFiltros = () => {
      ciudadSeleccionada.value = null;
      tiposSeleccionados.value = [];
      operacionSeleccionada.value = null;
      tiposFiltrados.value = [];
      totalInmueblesFiltrados.value = 0;
      loadingTipos.value = false;
      errorTipos.value = "";
    };

    const confirmarTipos = () => {
      console.log(
        "✅ Confirmando tipos y cargando inmuebles:",
        tiposSeleccionados.value.map((t) => t.nombre)
      );
      cargarInmuebles();
    };

    // 🔥 FUNCIÓN ACTUALIZADA: Validación basada en datos reales de la segunda llamada
    const validarDisponibilidad = () => {
      if (
        !ciudadSeleccionada.value ||
        tiposSeleccionados.value.length === 0 ||
        !operacionSeleccionada.value
      ) {
        return {
          valido: false,
          inmuebles: 0,
          mensaje: "Faltan filtros por seleccionar",
        };
      }

      // Calcular inmuebles reales basado en los tipos seleccionados de la segunda llamada
      let inmueblesReales = 0;

      tiposSeleccionados.value.forEach((tipoSeleccionado) => {
        const tipoFiltrado = tiposFiltrados.value.find(
          (t) => t.id === tipoSeleccionado.id
        );
        if (tipoFiltrado) {
          inmueblesReales += tipoFiltrado.total_inmuebles || 0;
        }
      });

      const mensaje =
        inmueblesReales > 0
          ? `Se encontraron ${inmueblesReales} inmuebles disponibles`
          : "No se encontraron inmuebles con esta combinación de filtros";

      return {
        valido: inmueblesReales > 0,
        inmuebles: inmueblesReales,
        mensaje,
      };
    };

    const cargarInmuebles = () => {
      const validacion = validarDisponibilidad();

      if (!validacion.valido) {
        console.warn("⚠️ Validación falló:", validacion.mensaje);
        alert(`⚠️ ${validacion.mensaje}\nPor favor, ajusta tus filtros.`);
        return;
      }

      const filtros = {
        ubicacion: ciudadSeleccionada.value.nombre,
        tipos: tiposSeleccionados.value.map((t) => t.id), // Array de IDs de tipos
        operacion: operacionSeleccionada.value.tipo,
      };

      console.log("🚀 Validación exitosa! Cargando inmuebles:", {
        filtros,
        estimacion: validacion.inmuebles,
        mensaje: validacion.mensaje,
      });

      emit("cargarInmuebles", filtros);
    };

    const getOperacionLabel = (operacion: string): string => {
      const labels: Record<string, string> = {
        venta: "En Venta",
        renta: "En Renta",
        traspaso: "En Traspaso",
      };
      return labels[operacion] || operacion;
    };

    // 🆕 NUEVO: Función para ir a favoritos
    const irAFavoritos = () => {
      emit("verFavoritos");
    };

    // 🆕 NUEVO: Ir directamente al listado del owner
    const verMisInmuebles = () => {
      emit("cargarInmuebles", { ownerOnly: true });
    };

    // Lifecycle
    onMounted(() => {
      if (props.autoCargar) {
        cargarMetadatos();
      }

      // 🆕 NUEVO: Suscribirse a cambios en favoritos
      unsubscribeFavoritos = favoritosService.onChange(() => {
        totalFavoritos.value = favoritosService.getFavoritos().length;
      });
    });

    // 🆕 NUEVO: Limpiar suscripción al desmontar
    onUnmounted(() => {
      if (unsubscribeFavoritos) {
        unsubscribeFavoritos();
        unsubscribeFavoritos = null;
      }
    });

    return {
      // Estado reactivo - primera llamada
      loading,
      error,
      ciudades,
      operaciones,
      tipos,
      totalInmuebles,
      totalSocios,
      recomendacion,
      ciudadSeleccionada,
      tiposSeleccionados,
      operacionSeleccionada,

      // Estado reactivo - segunda llamada
      tiposFiltrados,
      totalInmueblesFiltrados,
      loadingTipos,
      errorTipos,

      // Computed
      tieneInmuebles,
      mensajeSinInmuebles,
      operacionesDeCiudad,

      // Métodos
      cargarMetadatos,
      cargarTiposFiltrados,
      seleccionarCiudad,
      toggleTipo,
      isTipoSeleccionado,
      seleccionarTipos,
      seleccionarOperacion,
      volverACiudades,
      volverAOperaciones,
      confirmarTipos,
      reiniciarFiltros,
      validarDisponibilidad,
      cargarInmuebles,
      getOperacionLabel,
      esElTipoMasPopular,
      irAFavoritos,
      verMisInmuebles,

      // Estado de favoritos
      totalFavoritos,
    };
  },
});
</script>

<style scoped>
.filtro-inmuebles-inteligente {
  @apply max-w-6xl mx-auto p-4;
}

.ciudad-card {
  position: relative;
  min-height: 120px;
}

.operacion-card {
  position: relative;
  min-height: 160px;
}

.tipo-card {
  position: relative;
  min-height: 140px;
}

.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive improvements */
@media (max-width: 768px) {
  .ciudad-card,
  .operacion-card,
  .tipo-card {
    min-height: auto;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .breadcrumb .text-sm {
    font-size: 0.75rem;
  }
}
</style>
