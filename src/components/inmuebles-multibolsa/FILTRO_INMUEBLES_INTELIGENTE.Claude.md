# 🧠 FiltroInmueblesInteligente.vue - Documentación Técnica Completa para Claude

## 📋 Contexto y Propósito

### ¿Qué es este componente?
`FiltroInmueblesInteligente.vue` es un componente Vue 3 que implementa un **filtro progresivo de 3 pasos** para la búsqueda de inmuebles de socios directos. Funciona como un "bypass inteligente" que permite al usuario filtrar de manera progresiva antes de cargar el listado completo de inmuebles.

### ¿Por qué existe?
- **Problema**: Los corredores necesitaban navegar por miles de inmuebles sin saber qué opciones reales tenían disponibles
- **Solución**: Un filtro progresivo que muestra solo las opciones con inventario real
- **Beneficio**: UX mejorada, menos clics inútiles, decisiones informadas

### Arquitectura de 3 Pasos (Actualizada con 2 Llamadas al Backend)
1. **Paso 1**: Selección de Ciudad (basada en primera llamada completa)
2. **Paso 2**: Selección de Operación (basada en primera llamada)  
3. **Paso 3a**: Loading de tipos específicos (segunda llamada con filtros)
4. **Paso 3b**: Selección de Tipos de Inmuebles (basada en datos reales filtrados)

## 🏗️ Arquitectura Técnica

### Backend Dependency
- **Endpoint Principal**: `/msi-v5/owner/inmuebles/socios/metadata`
- **Implementación**: `MetadataInmueblesSociosOwnerAction.php`
- **Documentación Backend**: `msi-v5/docs/METADATA_INMUEBLES_SOCIOS_API.md`

### Flujo de Datos (Actualizado con 2 Llamadas)
```
[FiltroInmueblesInteligente] 
    ↓ cargarMetadatos() - Primera llamada
[GET /msi-v5/owner/inmuebles/socios/metadata]
    ↓ respuesta (ciudades, operaciones, recomendaciones)
[Selección de ciudad y operación]
    ↓ cargarTiposFiltrados() - Segunda llamada
[GET /msi-v5/owner/inmuebles/socios/metadata?ciudad=X&operacion=Y]
    ↓ respuesta (tipos específicos para esos filtros)
[Selección de tipos múltiples]
    ↓ emit('cargarInmuebles', filtros)
[Componente Padre (Inmuebles.vue)]
    ↓ 
[GET /msi-v5/owner/inmuebles/socios/list con filtros aplicados]
```

### Estructura del Objeto de Metadatos del Backend

#### **Primera Llamada (Modo Completo)**
```typescript
interface MetadatosResponseCompleto {
  statusCode: 200,
  data: {
    ciudades: Array<{
      nombre: string,
      total_inmuebles: number,
      operaciones: {
        venta?: number,
        renta?: number,
        traspaso?: number
      },
      tipos: {
        [nombreTipo: string]: number
      }
    }>,
    operaciones: Array<{
      nombre: 'venta' | 'renta' | 'traspaso',
      label: 'En Venta' | 'En Renta' | 'En Traspaso',
      total_inmuebles: number,
      ciudades: Record<string, number>,
      tipos: Record<string, number>
    }>,
    tipos: Array<{  // ✅ AHORA IMPLEMENTADO
      id: number,
      nombre: string,
      plural: string,
      total_inmuebles: number,
      ciudades: Record<string, number>,
      operaciones: Record<string, number>
    }>,
    total_inmuebles: number,
    total_socios: number,
    recomendacion: {
      mensaje: string,
      ciudad_destacada: string,
      operacion_destacada: string,
      tipo_destacado: string,
      estadisticas: {
        ciudad_top_porcentaje: number,
        operacion_top_porcentaje: number,
        tipo_top_porcentaje: number
      }
    }
  }
}
```

#### **Segunda Llamada (Modo Filtrado)**
```typescript
interface MetadatosResponseFiltrado {
  statusCode: 200,
  data: {
    tipos: Array<{
      id: number,
      nombre: string,
      plural: string,
      total_inmuebles: number
    }>,
    total_inmuebles: number,
    total_socios: number,
    filtros_aplicados: {
      ciudad: string,
      operacion: string
    }
  }
}
```

## 🔌 Props y Emits

### Props
```typescript
interface Props {
  autoCargar: boolean = true  // Si debe cargar metadatos al montar el componente
}
```

### Emits
```typescript
interface Emits {
  'irASocios': () => void              // Navegar a gestión de socios
  'cargarInmuebles': (filtros: any) => void  // Cargar inmuebles con filtros
  'verFavoritos': () => void           // Ver inmuebles favoritos
}
```

### Estructura del Objeto de Filtros Emitido
```typescript
interface FiltrosInmuebles {
  ubicacion: string,           // Nombre de la ciudad seleccionada
  tipos: number[],            // Array de IDs de tipos de inmuebles
  operacion: 'venta' | 'renta' | 'traspaso',
  ownerOnly?: boolean         // Para ver solo inmuebles propios
}
```

## 📊 Estado Reactivo Completo

### Estado Principal (Actualizado)
```typescript
// Carga y errores - primera llamada
const loading = ref(false)
const error = ref("")

// Datos del backend - primera llamada (modo completo)
const ciudades = ref<any[]>([])
const operaciones = ref<any[]>([])
const tipos = ref<any[]>([])           // ✅ AHORA SE LLENA (pero no se usa en paso 3)
const totalInmuebles = ref(0)
const totalSocios = ref(0)
const recomendacion = ref<any>(null)

// Datos del backend - segunda llamada (modo filtrado)
const tiposFiltrados = ref<any[]>([])  // ✅ NUEVO: Tipos específicos filtrados
const totalInmueblesFiltrados = ref(0) // ✅ NUEVO: Total para filtros específicos
const loadingTipos = ref(false)        // ✅ NUEVO: Loading de segunda llamada
const errorTipos = ref("")             // ✅ NUEVO: Errores de segunda llamada

// Estado de selección del usuario
const ciudadSeleccionada = ref<any>(null)
const operacionSeleccionada = ref<any>(null)
const tiposSeleccionados = ref<any[]>([])

// Integración con favoritos
const totalFavoritos = ref(favoritosService.getFavoritos().length)
```

### Computed Properties Críticos

#### `operacionesDeCiudad`
```typescript
// Extrae las operaciones disponibles en la ciudad seleccionada
// Ordena por cantidad de inmuebles (descendente)
const operacionesDeCiudad = computed(() => {
  if (!ciudadSeleccionada.value) return []
  
  const operacionesCiudad = ciudadSeleccionada.value.operaciones || {}
  
  return Object.entries(operacionesCiudad)
    .map(([tipo, count]) => ({
      tipo,
      label: getOperacionLabel(tipo),
      count,
    }))
    .sort((a, b) => (b.count as number) - (a.count as number))
})
```

#### `esElTipoMasPopular` ✅ NUEVO
```typescript
// ✅ REEMPLAZA tiposDeOperacionYCiudad: Función directa para identificar tipos populares
// Usa datos reales de la segunda llamada en lugar de estimaciones
const esElTipoMasPopular = (tipo: any) => {
  if (tiposFiltrados.value.length === 0) return false
  const tipoTopInmuebles = Math.max(...tiposFiltrados.value.map(t => t.total_inmuebles))
  return tipo.total_inmuebles === tipoTopInmuebles
}
```

#### `tiposDeOperacionYCiudad` ❌ ELIMINADO
```typescript
// ❌ YA NO SE USA: Era una estimación compleja e inexacta
// ✅ REEMPLAZADO POR: tiposFiltrados (datos reales de la segunda llamada)
// const tiposDeOperacionYCiudad = computed(() => { ... })
```

## 🔧 Métodos Clave

### `cargarMetadatos()` ✅ ACTUALIZADO
```typescript
// ✅ PRIMERA LLAMADA: Carga metadatos completos (modo sin filtros)
const cargarMetadatos = async () => {
  loading.value = true
  error.value = ""
  
  try {
    const response = await axios.get("/msi-v5/owner/inmuebles/socios/metadata")
    
    if (response.data && response.data.statusCode === 200) {
      const data = response.data.data
      
      ciudades.value = data.ciudades || []
      operaciones.value = data.operaciones || []
      tipos.value = data.tipos || []  // ✅ AHORA SE LLENA con tipos completos
      totalInmuebles.value = data.total_inmuebles || 0
      totalSocios.value = data.total_socios || 0
      recomendacion.value = data.recomendacion || null
      
      console.log("📊 Metadatos completos cargados:", {
        ciudades: ciudades.value.length,
        operaciones: operaciones.value.length,
        tipos: tipos.value.length,
        total_inmuebles: totalInmuebles.value,
      })
    }
  } catch (err: any) {
    if (err.response?.status === 403) {
      window.location.href = "/"
      return
    }
    error.value = "No se pudieron cargar las opciones disponibles"
  } finally {
    loading.value = false
  }
}
```

### `cargarTiposFiltrados()` ✅ NUEVO
```typescript
// ✅ SEGUNDA LLAMADA: Carga tipos específicos para ciudad y operación
const cargarTiposFiltrados = async (ciudad: string, operacion: string) => {
  loadingTipos.value = true
  errorTipos.value = ""
  tiposFiltrados.value = []
  totalInmueblesFiltrados.value = 0

  try {
    const params = new URLSearchParams({
      ciudad: ciudad,
      operacion: operacion,
    })

    const response = await axios.get(
      `/msi-v5/owner/inmuebles/socios/metadata?${params}`
    )

    if (response.data && response.data.statusCode === 200) {
      const data = response.data.data

      tiposFiltrados.value = data.tipos || []
      totalInmueblesFiltrados.value = data.total_inmuebles || 0

      console.log("🏠 Tipos filtrados cargados:", {
        ciudad: data.filtros_aplicados?.ciudad,
        operacion: data.filtros_aplicados?.operacion,
        tipos: tiposFiltrados.value.length,
        total_inmuebles: totalInmueblesFiltrados.value,
      })
    }
  } catch (err: any) {
    if (err.response?.status === 403) {
      window.location.href = "/"
      return
    }
    errorTipos.value = "No se pudieron cargar los tipos disponibles"
  } finally {
    loadingTipos.value = false
  }
}
```

### `validarDisponibilidad()` ✅ ACTUALIZADO
```typescript
// ✅ FUNCIÓN MEJORADA: Validación basada en datos reales de la segunda llamada
// Ya no usa estimaciones inexactas, sino datos precisos filtrados
const validarDisponibilidad = () => {
  if (!ciudadSeleccionada.value || tiposSeleccionados.value.length === 0 || !operacionSeleccionada.value) {
    return { valido: false, inmuebles: 0, mensaje: "Faltan filtros por seleccionar" }
  }
  
  // ✅ CÁLCULO REAL: Suma inmuebles de tipos seleccionados de la segunda llamada
  let inmueblesReales = 0
  
  tiposSeleccionados.value.forEach((tipoSeleccionado) => {
    const tipoFiltrado = tiposFiltrados.value.find(t => t.id === tipoSeleccionado.id)
    if (tipoFiltrado) {
      inmueblesReales += tipoFiltrado.total_inmuebles || 0
    }
  })
  
  return {
    valido: inmueblesReales > 0,
    inmuebles: inmueblesReales,
    mensaje: inmueblesReales > 0 
      ? `Se encontraron ${inmueblesReales} inmuebles disponibles`  // ✅ Ya no "aproximadamente"
      : "No se encontraron inmuebles con esta combinación de filtros"
  }
}
```

### `cargarInmuebles()`
```typescript
// ⚠️ FUNCIÓN FINAL: Emite evento con filtros para cargar inmuebles
// Incluye validación previa y construcción del objeto de filtros
const cargarInmuebles = () => {
  const validacion = validarDisponibilidad()
  
  if (!validacion.valido) {
    alert(`⚠️ ${validacion.mensaje}\nPor favor, ajusta tus filtros.`)
    return
  }
  
  const filtros = {
    ubicacion: ciudadSeleccionada.value.nombre,
    tipos: tiposSeleccionados.value.map(t => t.id), // Array de IDs
    operacion: operacionSeleccionada.value.tipo,
  }
  
  emit("cargarInmuebles", filtros)
}
```

## 🎨 Estados UI y Flujo Visual

### Estados Posibles (Actualizados)
1. **Loading**: Spinner mientras carga metadatos iniciales
2. **Error**: Mensaje de error con botón "Intentar de nuevo"
3. **Sin Inmuebles**: Mensaje + botón "Ver Mis Socios"
4. **Paso 1**: Selector de ciudades con recomendaciones
5. **Paso 2**: Selector de operaciones (después de seleccionar ciudad)
6. **Paso 3a**: Loading de tipos específicos (segunda llamada en progreso)
7. **Paso 3b**: Selector múltiple de tipos (con datos reales filtrados)
8. **Sin Tipos**: Mensaje cuando no hay tipos para la combinación elegida

### Indicadores Visuales
- **Badges "Recomendado"**: En ciudad/operación destacadas
- **Estadísticas globales**: Totales de inmuebles, socios, ciudades, operaciones, tipos
- **Breadcrumbs**: Navegación entre pasos con botones de "volver"
- **Checkboxes visuales**: En selección múltiple de tipos
- **Botón favoritos**: Persistente en todos los pasos

### Estados de Validación (Actualizados)
```typescript
// ✅ LÓGICA DE NAVEGACIÓN ACTUALIZADA CON 2 LLAMADAS
v-if="loading"                          // Estado de carga inicial
v-else-if="error"                       // Estado de error
v-else-if="!tieneInmuebles"            // Sin inmuebles disponibles
v-else-if="!ciudadSeleccionada"        // Paso 1: Seleccionar ciudad
v-else-if="!operacionSeleccionada"     // Paso 2: Seleccionar operación
v-else-if="loadingTipos"               // Paso 3a: Loading tipos específicos
v-else                                  // Paso 3b: Seleccionar tipos filtrados
```

## 🔗 Integración con Otros Componentes

### Componente Padre: `Inmuebles.vue`
```vue
<FiltroInmueblesInteligente 
  @cargarInmuebles="aplicarFiltrosInteligentes"
  @verFavoritos="mostrarFavoritos" 
  @irASocios="navegarASocios"
/>
```

### Servicio de Favoritos
```typescript
import favoritosService from "../../services/favoritosService"

// Reactivo a cambios en favoritos
unsubscribeFavoritos = favoritosService.onChange(() => {
  totalFavoritos.value = favoritosService.getFavoritos().length
})
```

### Backend de Inmuebles
- **Endpoint de listado**: `/msi-v5/owner/inmuebles/socios/list`
- **Filtros aplicados**: Los filtros del componente se pasan al endpoint de listado
- **Implementación**: `ListInmueblesSociosOwnerAction.php`

## ✅ Limitaciones Resueltas y TODOs Pendientes

### 1. ✅ RESUELTO: Tipos de Inmuebles Implementados
```typescript
tiposFiltrados.value = data.tipos || []  // ✅ AHORA se llenan con datos reales
```
- ✅ El backend **SÍ** devuelve información de tipos con la segunda llamada
- ✅ El paso 3 funciona completamente con datos reales
- ✅ `tiposFiltrados` contiene datos precisos de la segunda llamada
- ✅ `validarDisponibilidad()` hace cálculos exactos, no estimaciones

### 2. Hardcoded Redirects
```typescript
if (err.response?.status === 403) {
  window.location.href = "/"  // ⚠️ Hardcodeado, debería usar router
}
```

### 3. Alerts en Lugar de UI Reactiva
```typescript
alert(`⚠️ ${validacion.mensaje}\nPor favor, ajusta tus filtros.`)
```

### 4. Magic Numbers y Strings
```typescript
response.data.statusCode === 200  // ⚠️ Magic number
```

## 🚀 Flujo Completo de Uso

### Escenario Típico (Actualizado)
```
1. Usuario entra → cargarMetadatos() automático (primera llamada)
2. Ve ciudades disponibles con recomendación inteligente
3. Selecciona ciudad → seleccionarCiudad(ciudad)
4. Ve operaciones de esa ciudad → seleccionarOperacion(operacion)
5. Loading de tipos → cargarTiposFiltrados() automático (segunda llamada)
6. Ve tipos reales disponibles → toggleTipo() múltiple
7. Confirma tipos → confirmarTipos() → emit('cargarInmuebles', filtros)
8. Componente padre recibe filtros y carga inmuebles reales
```

### Funciones de Escape
- **"Ver Mis Favoritos"**: Disponible en todos los pasos
- **"Mis Inmuebles"**: Bypass directo al listado del owner
- **"Ver Mis Socios"**: Cuando no hay inmuebles disponibles
- **Breadcrumbs**: Navegación hacia atrás en cualquier momento

## 🔍 Debugging y Logging

### Console Logs Importantes (Actualizados)
```typescript
// Primera llamada
console.log("🔍 Cargando metadatos de inmuebles...")
console.log("📊 Metadatos completos cargados:", { ciudades, operaciones, tipos, total_inmuebles })

// Segunda llamada  
console.log("🔍 Cargando tipos filtrados para:", { ciudad, operacion })
console.log("🏠 Tipos filtrados cargados:", { ciudad, operacion, tipos, total_inmuebles })

// Selecciones del usuario
console.log("🏙️ Ciudad seleccionada:", ciudad.nombre)
console.log("🏠 Operación seleccionada:", operacion.tipo)
console.log("✅ Tipos confirmados:", tiposSeleccionados.value.map(t => t.nombre))

// Validación final
console.log("🚀 Validación exitosa! Cargando inmuebles:", { filtros, inmuebles_exactos, mensaje })
```

### Puntos de Debugging Críticos (Actualizados)
1. **Primera llamada**: `response.data.data` → ciudades, operaciones, tipos globales
2. **Segunda llamada**: `response.data.data` → tipos filtrados específicos
3. **Cálculos de `operacionesDeCiudad`**: Verificar extracción desde primera llamada
4. **Tipos filtrados**: Verificar `tiposFiltrados.value` tras segunda llamada
5. **Validación real**: Verificar sumas exactas con `tiposFiltrados`
6. **Emisión de filtros**: Verificar estructura del objeto emitido

## 📦 Dependencias

### Vue 3 Composition API
```typescript
import { ref, computed, onMounted, onUnmounted, defineComponent } from "vue"
```

### HTTP Client
```typescript
import axios from "axios"
```

### Servicios Internos
```typescript
import favoritosService from "../../services/favoritosService"
```

### Iconos
- **Ion Icons**: `<ion-icon name="...">` usado en toda la UI

## 💾 Persistencia y Estado

### No Persistent State
- El componente **NO** guarda estado en localStorage
- Cada carga es fresh desde el backend
- Los filtros se pasan al padre pero no se guardan

### Estado Reactivo de Favoritos
- **Sí persistente**: Via `favoritosService`
- **Reactivo**: Se actualiza automáticamente cuando cambian favoritos
- **Cleanup**: Se desuscribe en `onUnmounted`

## 🎯 Casos de Uso Específicos

### Corredor con Muchos Socios
- Ve recomendaciones basadas en estadísticas reales
- Filtra progresivamente para encontrar oportunidades específicas

### Corredor Nuevo Sin Socios
- Ve mensaje específico "No tienes socios directos autorizados"
- Botón directo para ir a gestión de socios

### Corredor con Socios Sin Inventario
- Ve mensaje "Tus socios no tienen inmuebles publicados"
- Mantiene acceso a sus propios inmuebles

### Búsqueda Directa de Favoritos
- Bypass completo del filtro progresivo
- Acceso directo desde cualquier paso

---

## 🔮 Consideraciones Futuras

### ✅ COMPLETADO: Backend de Tipos Implementado
1. ✅ `MetadataInmueblesSociosOwnerAction.php` actualizado con tipos
2. ✅ El paso 3 funciona completamente con datos reales
3. ✅ `validarDisponibilidad()` da cálculos exactos
4. ✅ UX completada según el diseño con 2 llamadas

### Optimizaciones Posibles
1. **Cache de metadatos**: Para evitar recarga en cada visita
2. **Router integration**: Para navegación más elegante
3. **Error boundaries**: Para manejo de errores más robusto
4. **Loading states**: Más granulares por sección

### ✅ NUEVA ARQUITECTURA IMPLEMENTADA (2025-01-24)
1. **Flujo de 2 llamadas**: Primera para metadatos completos, segunda para tipos filtrados
2. **Datos reales**: Ya no hay estimaciones, sino cálculos exactos
3. **UX mejorada**: Loading states específicos y mensajes informativos
4. **Performance**: Solo carga tipos necesarios en el momento adecuado

---

*Esta documentación cubre el estado ACTUALIZADO del componente al 2025-01-24 con la nueva arquitectura de 2 llamadas al backend implementada.*
