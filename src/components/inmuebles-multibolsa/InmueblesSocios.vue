<template>
  <div ref="rootEl" class="bg-white rounded-lg shadow-md">
    <!-- Contenido principal de inmuebles (oculto cuando se muestran favoritos) -->
    <div v-if="!showFavoritos">
      <!-- 🎯 NUEVO: Buscador simplificado solo con búsqueda de texto -->
      <div
        ref="buscadorRef"
        class="p-4 bg-gradient-to-r from-gray-50 to-white border-b"
      >
        <div class="flex gap-4 items-center">
          <!-- Buscador principal (expandido) -->
          <div class="flex-1">
            <BuscadorInmuebles
              v-model="searchQuery"
              placeholder="¿Qué buscas? Casa, departamento, terreno, clave (PROP-001)..."
            />
          </div>

          <!-- Botón favoritos reubicado con mejor diseño -->
          <div v-if="false" class="flex-shrink-0">
            <button
              @click="verFavoritos"
              class="flex relative items-center px-4 py-3 font-medium text-yellow-700 bg-white rounded-xl border-2 border-yellow-200 shadow-sm transition-all duration-200 hover:border-yellow-300 hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 hover:shadow-md group"
              :title="`Tienes ${totalFavoritos} inmuebles marcados como favoritos`"
            >
              <!-- Ícono de estrella con animación -->
              <ion-icon
                name="star"
                class="text-xl text-yellow-500 transition-transform duration-200 group-hover:scale-110"
              ></ion-icon>

              <!-- Texto responsive -->
              <span class="hidden ml-2 font-semibold sm:inline"
                >Mis Favoritos</span
              >

              <!-- Contador de favoritos (siempre visible) -->
              <span
                v-if="totalFavoritos > 0"
                class="flex absolute -top-2 -right-2 justify-center items-center w-6 h-6 text-xs font-bold text-white bg-red-500 rounded-full shadow-md animate-pulse"
              >
                {{ totalFavoritos > 99 ? "99+" : totalFavoritos }}
              </span>

              <!-- Indicador cuando no hay favoritos -->
              <span
                v-else
                class="hidden ml-1 text-xs text-gray-400 opacity-75 sm:inline"
              >
                (0)
              </span>
            </button>
          </div>
        </div>

        <!-- 🆕 NUEVO: Indicador de coincidencias en tiempo real -->
        <div
          v-if="searchQuery"
          :class="[
            'mt-2 px-3 py-1.5 rounded-lg border transition-all duration-300 transform',
            filteredInmuebles.length === 0
              ? 'bg-red-50 border-red-200 scale-105'
              : filteredInmuebles.length === inmuebles.length
              ? 'bg-blue-50 border-blue-200'
              : 'bg-green-50 border-green-200',
          ]"
        >
          <div class="flex justify-between items-center text-sm">
            <div class="flex items-center">
              <ion-icon
                :name="
                  filteredInmuebles.length === 0 ? 'alert-circle' : 'search'
                "
                :class="[
                  'mr-2',
                  filteredInmuebles.length === 0
                    ? 'text-red-600'
                    : filteredInmuebles.length === inmuebles.length
                    ? 'text-blue-600'
                    : 'text-green-600',
                ]"
              ></ion-icon>
              <span
                class="font-medium"
                :class="[
                  filteredInmuebles.length === 0
                    ? 'text-red-800'
                    : filteredInmuebles.length === inmuebles.length
                    ? 'text-blue-800'
                    : 'text-green-800',
                ]"
              >
                {{ filteredInmuebles.length }}/{{ inmuebles.length }}
                coincidencias
              </span>
            </div>
            <span
              class="px-2 py-0.5 text-xs font-medium rounded-full"
              :class="[
                filteredInmuebles.length === 0
                  ? 'bg-red-100 text-red-700'
                  : filteredInmuebles.length === inmuebles.length
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-green-100 text-green-700',
              ]"
            >
              {{ getCoincidenciasTexto() }}
            </span>
          </div>
        </div>
      </div>

      <!-- Botón "ir arriba" cuando el buscador está fuera de vista -->
      <div v-if="buscadorOutOfView" class="fixed right-4 bottom-4 z-50">
        <button
          @click="irArriba"
          class="flex items-center px-4 py-2 text-white rounded-full shadow-lg transition-all duration-300 bg-mulbin-600 hover:bg-mulbin-700 hover:scale-105"
        >
          <ion-icon name="chevron-up-outline" class="mr-1"></ion-icon>
          <span class="text-sm font-medium">Ir arriba</span>
        </button>
      </div>

      <!-- Indicador de carga -->
      <div v-if="loading" class="p-8 text-center text-gray-500">
        <div class="flex justify-center">
          <div
            class="w-8 h-8 rounded-full border-4 border-gray-200 animate-spin border-t-mulbin-600"
          ></div>
        </div>
        <p class="mt-2">Cargando inmuebles...</p>
      </div>

      <!-- Error de carga -->
      <div v-else-if="error" class="p-8 text-center text-red-500">
        <ion-icon name="alert-circle-outline" class="mb-2 text-5xl"></ion-icon>
        <p>{{ error }}</p>
        <button
          @click="fetchInmuebles"
          class="px-4 py-2 mt-4 text-white rounded-lg bg-mulbin-600 hover:bg-mulbin-700"
        >
          Reintentar
        </button>
      </div>

      <div v-else>
        <!-- Lista de inmuebles -->
        <div v-if="filteredInmuebles.length" class="divide-y divide-gray-100">
          <div
            v-for="inmueble in filteredInmuebles"
            :key="inmueble.id"
            class="p-4 transition-colors cursor-pointer hover:bg-gray-50"
            @click="verDetalleInmueble(inmueble)"
          >
            <div
              class="flex flex-col space-y-3 lg:flex-row lg:space-y-0 lg:space-x-4"
            >
              <!-- Imagen principal -->
              <div class="relative flex-shrink-0">
                <img
                  :src="`/static/img/sin_imagen.jpg`"
                  :data-src="
                    inmueble.imagenPrincipal ||
                    `${mulbinUrl}/images/sin_imagen.jpg`
                  "
                  :alt="inmueble.titulo"
                  class="object-cover w-full h-48 rounded-lg lazy lg:w-64 lg:h-40"
                  loading="lazy"
                />

                <!-- 🆕 NUEVO: Indicadores de estado de publicación -->
                <div
                  v-if="inmueble.publicacionEnMiWeb?.estado === 'publicado'"
                  class="flex absolute top-2 left-2 items-center px-2 py-1 text-xs font-semibold text-white bg-green-600 bg-opacity-90 rounded-full shadow-lg backdrop-blur-sm"
                  title="Este inmueble ya está publicado en tu sitio web"
                >
                  <ion-icon
                    name="checkmark-circle"
                    class="mr-1 text-sm"
                  ></ion-icon>
                  <span>En mi web</span>
                </div>
                <div
                  v-else-if="
                    inmueble.publicacionEnMiWeb?.estado ===
                    'solicitud_pendiente'
                  "
                  class="flex absolute top-2 left-2 items-center px-2 py-1 text-xs font-semibold text-white rounded-full shadow-lg backdrop-blur-sm"
                  :class="
                    inmueble.publicacionEnMiWeb?.accion_pendiente_de === 'socio'
                      ? 'bg-indigo-600 bg-opacity-90'
                      : 'bg-orange-500 bg-opacity-90'
                  "
                  :title="
                    inmueble.publicacionEnMiWeb?.accion_pendiente_de === 'socio'
                      ? 'Solicitud pendiente de autorización de tu socio'
                      : 'Solicitud pendiente de tu autorización'
                  "
                >
                  <ion-icon name="time-outline" class="mr-1 text-sm"></ion-icon>
                  <span>Pendiente</span>
                </div>
                <div
                  v-else-if="
                    inmueble.publicacionEnMiWeb?.estado === 'rechazado'
                  "
                  class="flex absolute top-2 left-2 items-center px-2 py-1 text-xs font-semibold text-white bg-red-500 bg-opacity-90 rounded-full shadow-lg backdrop-blur-sm"
                  title="Solicitud de publicación rechazada"
                >
                  <ion-icon name="close-circle" class="mr-1 text-sm"></ion-icon>
                  <span>Rechazado</span>
                </div>
              </div>

              <!-- Información del inmueble -->
              <div class="flex-1 min-w-0">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <!-- 🆕 NUEVO: Header con título y clave del inmueble -->
                    <div class="flex gap-2 justify-between items-start mb-1">
                      <h3
                        class="flex-1 text-lg font-semibold text-gray-900 line-clamp-2"
                      >
                        {{ decodeHtmlEntities(stripTags(inmueble.titulo)) }}
                      </h3>
                      <!-- Badge con la clave del inmueble -->
                      <span
                        class="flex-shrink-0 px-2 py-1 font-mono text-xs font-semibold rounded-md border text-mulbin-600 bg-mulbin-50 border-mulbin-200"
                        :title="`Clave del inmueble: ${inmueble.key}`"
                      >
                        {{ inmueble.key }}
                      </span>
                    </div>
                    <p class="mt-1 text-sm text-gray-600 line-clamp-2">
                      {{ decodeHtmlEntities(stripTags(inmueble.descripcion)) }}
                    </p>
                  </div>

                  <!-- 🆕 NUEVO: Precio de operación prioritaria con info adicional -->
                  <div class="flex-shrink-0 ml-4 text-right">
                    <!-- Precio prioritario (más grande) -->
                    <div class="mb-1">
                      <p class="text-2xl font-bold text-green-600">
                        {{
                          formatPriceWithCurrency(
                            getOperacionPrioritaria(inmueble)
                          )
                        }}
                      </p>
                      <p
                        v-if="
                          getOperacionPrioritaria(inmueble).tipo === 'renta'
                        "
                        class="-mt-1 text-xs text-gray-500"
                      >
                        {{
                          getOperacionPrioritaria(inmueble).periodo || "mensual"
                        }}
                      </p>
                      <p
                        v-if="getOperacionPrioritaria(inmueble).comision > 0"
                        class="-mt-0.5 text-xs text-gray-400"
                      >
                        {{ getOperacionPrioritaria(inmueble).comision }}%
                        comisión
                      </p>
                    </div>

                    <!-- Precios secundarios (más pequeños) -->
                    <div
                      v-if="getOperacionesSecundarias(inmueble).length > 0"
                      class="space-y-0.5"
                    >
                      <div
                        v-for="operacion in getOperacionesSecundarias(inmueble)"
                        :key="operacion.tipo"
                        class="text-sm text-gray-500"
                      >
                        <span class="font-medium">{{ operacion.tipo }}:</span>
                        <span class="ml-1">{{
                          formatPriceWithCurrency(operacion)
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Características del inmueble -->
                <div class="flex flex-wrap gap-4 mt-3 text-sm text-gray-600">
                  <span v-if="inmueble.recamaras" class="flex items-center">
                    <ion-icon name="bed-outline" class="mr-1"></ion-icon>
                    {{ inmueble.recamaras }} recámaras
                  </span>
                  <span v-if="inmueble.banos" class="flex items-center">
                    <ion-icon name="water-outline" class="mr-1"></ion-icon>
                    {{ inmueble.banos }} baños
                  </span>
                  <span v-if="inmueble.area" class="flex items-center">
                    <ion-icon name="resize-outline" class="mr-1"></ion-icon>
                    {{ inmueble.area }} m²
                  </span>
                  <span class="flex items-center">
                    <ion-icon name="location-outline" class="mr-1"></ion-icon>
                    {{ getLocationLabel(inmueble.ubicacion) }}
                  </span>
                </div>

                <!-- Información del socio propietario -->
                <div
                  class="flex justify-between items-center pt-3 mt-3 border-t border-gray-100"
                >
                  <div class="flex items-center">
                    <img
                      :src="`${mulbinUrl}/images/avatar.png`"
                      :data-src="
                        inmueble.socio.avatar ||
                        `${mulbinUrl}/images/avatar.png`
                      "
                      :alt="inmueble.socio.nombre"
                      class="mr-2 w-8 h-8 rounded-full lazy"
                      loading="lazy"
                    />
                    <div>
                      <p class="text-sm font-medium text-gray-900">
                        {{ inmueble.socio.nombre }}
                      </p>
                      <p class="text-xs text-gray-500">
                        {{ inmueble.socio.empresa }}
                      </p>
                    </div>
                  </div>

                  <!-- Acciones rápidas -->
                  <div class="flex items-center space-x-2">
                    <button
                      @click.stop="contactarSocio(inmueble.socio, 'whatsapp')"
                      class="p-2 text-green-500 rounded-full hover:bg-green-50"
                      title="WhatsApp"
                    >
                      <ion-icon name="logo-whatsapp" class="text-lg"></ion-icon>
                    </button>
                    <button
                      @click.stop="contactarSocio(inmueble.socio, 'telefono')"
                      class="p-2 text-blue-500 rounded-full hover:bg-blue-50"
                      title="Teléfono"
                    >
                      <ion-icon name="call-outline" class="text-lg"></ion-icon>
                    </button>
                    <button
                      @click.stop="abrirCrearHilo(inmueble)"
                      class="p-2 text-purple-500 rounded-full hover:bg-purple-50"
                      title="Crear hilo de interés"
                    >
                      <ion-icon name="chatbubble-ellipses-outline" class="text-lg"></ion-icon>
                    </button>

                    <!-- 🆕 NUEVO: Botón de publicación en mi web con menú contextual -->
                    <div class="relative">
                      <!-- Botón principal -->
                      <button
                        v-if="!isAccionPendienteOwner(inmueble)"
                        @click.stop="
                          inmueble.publicacionEnMiWeb?.estado ===
                            'solicitud_pendiente' &&
                          inmueble.publicacionEnMiWeb?.accion_pendiente_de ===
                            'socio'
                            ? confirmarCancelarSolicitud(inmueble)
                            : togglePublicacionEnMiWeb(inmueble)
                        "
                        class="p-2 rounded-full transition-colors duration-200"
                        :disabled="
                          inmueble.publicacionEnMiWeb?.estado ===
                            'solicitud_pendiente' &&
                          inmueble.publicacionEnMiWeb?.accion_pendiente_de ===
                            'owner'
                        "
                        :class="
                          inmueble.publicacionEnMiWeb?.estado === 'publicado'
                            ? 'text-red-500 hover:bg-red-50'
                            : inmueble.publicacionEnMiWeb?.estado ===
                                'solicitud_pendiente' &&
                              inmueble.publicacionEnMiWeb
                                ?.accion_pendiente_de === 'socio'
                            ? 'text-red-500 hover:bg-red-50'
                            : inmueble.publicacionEnMiWeb?.estado ===
                              'solicitud_pendiente'
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-mulbin-600 hover:bg-mulbin-50'
                        "
                        :title="
                          inmueble.publicacionEnMiWeb?.estado === 'publicado'
                            ? 'Quitar este inmueble de mi web'
                            : inmueble.publicacionEnMiWeb?.estado ===
                                'solicitud_pendiente' &&
                              inmueble.publicacionEnMiWeb
                                ?.accion_pendiente_de === 'socio'
                            ? 'Cancelar solicitud de publicación'
                            : inmueble.publicacionEnMiWeb?.estado ===
                              'solicitud_pendiente'
                            ? 'Solicitud pendiente de autorización'
                            : 'Solicitar publicar en mi web'
                        "
                      >
                        <ion-icon
                          :name="
                            inmueble.publicacionEnMiWeb?.estado === 'publicado'
                              ? 'remove-circle-outline'
                              : inmueble.publicacionEnMiWeb?.estado ===
                                  'solicitud_pendiente' &&
                                inmueble.publicacionEnMiWeb
                                  ?.accion_pendiente_de === 'socio'
                              ? 'close-circle-outline'
                              : inmueble.publicacionEnMiWeb?.estado ===
                                'solicitud_pendiente'
                              ? 'time-outline'
                              : 'add-circle-outline'
                          "
                          class="text-lg"
                        ></ion-icon>
                      </button>

                      <!-- Botón con menú contextual para owner -->
                      <button
                        v-else
                        @click.stop="toggleMenuContextual(inmueble.id)"
                        class="p-2 text-orange-500 rounded-full transition-colors duration-200 hover:bg-orange-50"
                        title="Acción pendiente de tu parte - Haz clic para ver opciones"
                      >
                        <ion-icon
                          name="ellipsis-vertical"
                          class="text-lg"
                        ></ion-icon>
                      </button>

                      <!-- Menú contextual para owner -->
                      <div
                        v-if="menuContextualAbierto === inmueble.id"
                        class="absolute right-0 bottom-full z-10 py-2 mb-2 bg-white rounded-lg border border-gray-200 shadow-lg min-w-32"
                      >
                        <button
                          @click.stop="confirmarAceptarPublicacion(inmueble)"
                          class="flex items-center px-3 py-2 w-full text-sm text-left text-green-700 hover:bg-green-50"
                        >
                          <ion-icon
                            name="checkmark-circle"
                            class="mr-2 text-green-600"
                          ></ion-icon>
                          Aceptar
                        </button>
                        <button
                          @click.stop="confirmarRechazarPublicacion(inmueble)"
                          class="flex items-center px-3 py-2 w-full text-sm text-left text-red-700 hover:bg-red-50"
                        >
                          <ion-icon
                            name="close-circle"
                            class="mr-2 text-red-600"
                          ></ion-icon>
                          Rechazar
                        </button>
                        <div class="my-1 border-t border-gray-200"></div>
                        <button
                          @click.stop="cerrarMenuContextual"
                          class="flex items-center px-3 py-2 w-full text-sm text-left text-gray-600 hover:bg-gray-50"
                        >
                          <ion-icon name="close" class="mr-2"></ion-icon>
                          Cerrar
                        </button>
                      </div>
                    </div>

                    <button
                      v-if="false"
                      @click.stop="marcarFavorito(inmueble)"
                      class="p-2 rounded-full hover:bg-yellow-50"
                      :class="
                        inmueble.esFavorito
                          ? 'text-yellow-500'
                          : 'text-gray-400'
                      "
                      title="Marcar como favorito"
                    >
                      <ion-icon
                        :name="inmueble.esFavorito ? 'star' : 'star-outline'"
                        class="text-lg"
                      ></ion-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Estado vacío -->
        <div
          v-if="!filteredInmuebles.length"
          class="p-8 text-center text-gray-500"
        >
          <ion-icon name="home-outline" class="mb-2 text-5xl"></ion-icon>
          <div v-if="!loading && !error">
            <p class="text-lg font-medium">No se encontraron inmuebles</p>
            <p class="mt-2 text-sm">
              {{
                searchQuery
                  ? "No hay inmuebles que coincidan con tu búsqueda (título, descripción, ubicación, tipo, socio o clave)"
                  : "Tus socios aún no han publicado inmuebles o no tienes socios directos autorizados"
              }}
            </p>
            <button
              v-if="searchQuery"
              @click="limpiarFiltros"
              class="px-4 py-2 mt-4 text-white rounded-lg bg-mulbin-600 hover:bg-mulbin-700"
            >
              <ion-icon name="close-circle-outline" class="mr-2"></ion-icon>
              Limpiar búsqueda
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Vista de favoritos -->
    <div v-if="showFavoritos" class="bg-white rounded-lg shadow-md">
      <!-- Header de favoritos con botón de regreso -->
      <div class="flex justify-between items-center p-4 border-b">
        <div class="flex items-center">
          <button
            @click="irAFiltros"
            class="flex items-center px-3 py-1 mr-4 text-sm text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200"
          >
            <ion-icon name="arrow-back" class="mr-1"></ion-icon>
            Volver a Inmuebles
          </button>
        </div>
        <div class="text-sm text-gray-500">
          {{ totalFavoritos }} inmueble{{
            totalFavoritos !== 1 ? "s" : ""
          }}
          guardado{{ totalFavoritos !== 1 ? "s" : "" }}
        </div>
      </div>

      <!-- Componente de favoritos -->
      <InmueblesFavoritos @volver="irAFiltros" @ir-a-filtros="irAFiltros" />
    </div>

    <!-- Modal de detalle de inmueble -->
    <DetalleInmuebleModal
      v-model="showDetalleModal"
      :inmueble="inmuebleSeleccionado"
      @contactar="contactarSocio"
    />

    <!-- Modal para crear hilos de interés -->
    <NuevoHiloForm
      :show="showCrearHiloModal"
      :token="props.token"
      :post="inmuebleSeleccionadoParaHilo"
      @close="handleCloseCrearHilo"
      @hilo-created="handleHiloCreated"
      @abrir-chat="handleAbrirChatFromHilo"
      @error="handleHiloError"
    />

    <!-- Modal de chat para hilos de interés -->
    <ChatHiloModal
      :show="showChatModal"
      :hilo-seleccionado="hiloSeleccionado"
      :token="props.token"
      @close="cerrarChatModal"
      @error="handleChatError"
      @success="handleChatSuccess"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onUnmounted,
  reactive,
  watch,
  nextTick,
} from "vue";
import axios from "axios";
import DetalleInmuebleModal from "../muro-inmobiliario-social/DetalleInmuebleModal.vue";
import InmueblesFavoritos from "../muro-inmobiliario-social/InmueblesFavoritos.vue";
import BuscadorInmuebles from "../ui/BuscadorInmuebles.vue";
import NuevoHiloForm from "../muro-inmobiliario-social/NuevoHiloForm.vue";
import ChatHiloModal from "../muro-inmobiliario-social/ChatHiloModal.vue";
import favoritosService from "../../services/favoritosService";
import inmobiliarioService from "../../services/inmobiliarioService";
import ddpService from "../../services/ddpService";
import { useModal } from "../../composables/useModal";

interface Socio {
  id: string;
  meteor_id?: string; // ✅ NUEVO: ID en Meteor para crear hilos de interés
  nombre: string;
  empresa: string;
  avatar?: string;
  telefono?: string;
  whatsapp?: string;
  email?: string;
}

interface Operacion {
  tipo: "venta" | "renta" | "traspaso";
  precio: number;
  precio_formateado: string;
  comision: number;
  moneda: string;
  disponible: boolean;
  periodo?: string;
}

interface PublicacionEnMiWeb {
  publicado: boolean;
  estado: "sin_solicitud" | "solicitud_pendiente" | "publicado" | "rechazado";
  descripcion: string;
  fecha_solicitud: string | null;
  token: string | null;
  accion_pendiente_de: "owner" | "socio" | null; // 🆕 NUEVO: Indica de quién depende la acción
}

interface Inmueble {
  id: string;
  key: string; // 🆕 NUEVO: Clave única del inmueble (ej: "PROP-001")
  titulo: string;
  descripcion: string;
  operaciones: Operacion[]; // 🆕 NUEVO: Array de operaciones
  operacion_principal: "venta" | "renta" | "traspaso"; // 🆕 COMPATIBILIDAD
  precio_principal: number; // 🆕 COMPATIBILIDAD
  precio: number; // 🔄 DEPRECATED: Mantenido para compatibilidad
  operacion: "venta" | "renta" | "traspaso"; // 🔄 DEPRECATED: Mantenido para compatibilidad
  tipo: string;
  ubicacion: string;
  recamaras?: number;
  banos?: number;
  area?: number;
  imagenPrincipal?: string;
  imagenes: string[];
  publicacionEnMiWeb: PublicacionEnMiWeb; // 🆕 NUEVO: Estado completo de publicación
  socio: Socio;
  esFavorito: boolean;
  fechaCreacion: string;
}

interface FiltrosInmuebles {
  operacion: string;
  tipo: string;
  tipos?: number[]; // Array de IDs de tipos para selección múltiple
  ubicacion: string;
  precioMax?: number;
}

// 🆕 NUEVO: Props para la operación prioritaria desde filtros inteligentes
interface FiltrosIniciales {
  ubicacion?: string;
  operacion?: string;
  tipos?: number[];
  operacionPrioritaria?: string; // 🆕 Para priorización visual
}

export default defineComponent({
  name: "Inmuebles",

  components: {
    DetalleInmuebleModal,
    InmueblesFavoritos,
    BuscadorInmuebles,
    NuevoHiloForm,
    ChatHiloModal,
  },

  props: {
    // 🆕 NUEVO: Token de autenticación
    token: {
      type: String,
      default: "",
    },
    // 🆕 NUEVO: Props para filtros iniciales desde FiltroInmueblesInteligente
    filtrosIniciales: {
      type: Object as () => FiltrosIniciales,
      default: () => ({}),
    },
    // 🆕 NUEVO: Prop para activar favoritos inmediatamente
    activarFavoritosInmediatamente: {
      type: Boolean,
      default: false,
    },
  },

  emits: [
    "total-updated",
    "ir-a-filtros",
    "favoritos-changed",
    "favoritos-activados",
  ],

  setup(props, { emit }) {
    const mulbinUrl = import.meta.env.VITE_MULBIN_URL;
    const rootEl = ref<HTMLElement | null>(null);

    // Sistema de modales personalizado
    const { confirm, success, error: showError } = useModal();

    const searchQuery = ref("");
    const inmuebles = ref<Inmueble[]>([]);
    const loading = ref(false);
    const error = ref<string | null>(null);

    // Modal de detalle
    const showDetalleModal = ref(false);
    const inmuebleSeleccionado = ref<Inmueble | null>(null);
    
    // Modal de crear hilo
    const showCrearHiloModal = ref(false);
    const inmuebleSeleccionadoParaHilo = ref<any | null>(null);
    
    // Modal de chat hilo
    const showChatModal = ref(false);
    const hiloSeleccionado = ref<any>(null);

    // Vista de favoritos
    const showFavoritos = ref(false);

    // 🆕 NUEVO: Control del menú contextual para owner
    const menuContextualAbierto = ref<string | null>(null);

    // Control de visibilidad del buscador
    const buscadorRef = ref<HTMLElement | null>(null);
    const scrollContainer = ref<HTMLElement | null>(null);
    const buscadorOutOfView = ref(false);

    // Throttle para el scroll
    let scrollTimeout: ReturnType<typeof setTimeout> | null = null;

    // Lazyload: IntersectionObserver para imágenes
    let imageObserver: IntersectionObserver | null = null;

    const initLazyObserver = () => {
      if (typeof window === "undefined") return;
      if ("IntersectionObserver" in window) {
        imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement & {
                dataset: { src?: string };
              };
              const dataSrc = img?.dataset?.src;
              if (dataSrc) {
                img.src = dataSrc;
              }
              img.classList.remove("lazy");
              observer.unobserve(img);
            }
          });
        });
      }
    };

    const observeLazyImages = () => {
      const scope: Document | Element =
        (rootEl.value as Element | null) ?? document;
      const images = scope.querySelectorAll<HTMLImageElement>(".lazy");

      images.forEach((img) => {
        if (imageObserver) {
          imageObserver.observe(img);
        } else {
          // Fallback sin IntersectionObserver: carga inmediata
          const dataSrc = (img as any)?.dataset?.src as string | undefined;
          if (dataSrc) {
            img.src = dataSrc;
          }
          img.classList.remove("lazy");
        }
      });
    };

    // Filtros
    const filters = reactive<FiltrosInmuebles>({
      operacion: "",
      tipo: "",
      ubicacion: "",
      precioMax: undefined,
    });

    // 🆕 NUEVO: Operación prioritaria desde filtros inteligentes
    const operacionPrioritaria = ref<string>("");

    // Función para normalizar texto (eliminar acentos)
    const normalizeText = (text: string): string => {
      return text
        .normalize("NFD") // Normalizar con descomposición canónica
        .replace(/[\u0300-\u036f]/g, "") // Eliminar diacríticos
        .toLowerCase(); // Convertir a minúsculas
    };

    // Función para obtener los inmuebles desde la API real
    const fetchInmuebles = async () => {
      loading.value = true;
      error.value = null;

      try {
        // Construir parámetros para la API (solo filtros avanzados, NO searchQuery)
        const params = new URLSearchParams();

        // Agregar limit=0 para obtener todos los inmuebles
        params.append("limit", "0");

        if (filters.operacion) params.append("operacion", filters.operacion);
        if (filters.tipo) params.append("tipo", filters.tipo);
        if (filters.tipos && Array.isArray(filters.tipos)) {
          // Agregar múltiples tipos como parámetros separados
          filters.tipos.forEach((tipoId: number) => {
            params.append("tipos[]", tipoId.toString());
          });
        }
        if (filters.ubicacion) params.append("ubicacion", filters.ubicacion);
        if (filters.precioMax)
          params.append("precio_max", filters.precioMax.toString());
        // NO incluimos searchQuery aquí - se maneja localmente

        // Llamada real al endpoint de msi-v5
        const response = await axios.get(
          `/msi-v5/owner/inmuebles/socios?${params}`
        );

        if (response.data && response.data.statusCode === 200) {
          const inmueblesData = response.data.data.inmuebles || [];

          // 🆕 NUEVO: Sincronizar estado de favoritos desde localStorage
          inmuebles.value = inmueblesData.map((inmueble: Inmueble) => ({
            ...inmueble,
            esFavorito: favoritosService.esFavorito(inmueble.id),
          }));

          // 🆕 NUEVO: Emitir el total de inmuebles encontrados
          const total = response.data.data.total || 0;
          emit("total-updated", total);

          // Log para debugging
          console.log("✅ Inmuebles cargados exitosamente:", {
            total: response.data.data.total,
            total_socios: response.data.data.total_socios,
            filtros: response.data.data.filtros_aplicados,
          });
        } else {
          error.value = "Error al cargar los inmuebles de socios";
        }
      } catch (err: any) {
        console.error("Error fetching inmuebles:", err);

        // Manejo específico de errores
        if (err.response?.status === 403) {
          // Sesión perdida, redirigir al login
          window.location.href = "/";
          return;
        }

        // Mostrar mensaje de error específico si está disponible
        if (err.response?.data?.data?.error) {
          error.value = err.response.data.data.error;
        } else {
          error.value =
            "No se pudieron cargar los inmuebles. Por favor intenta nuevamente.";
        }
      } finally {
        loading.value = false;
      }
    };

    // Inmuebles filtrados por búsqueda local en tiempo real
    const filteredInmuebles = computed(() => {
      // Normalizar el texto de búsqueda
      const normalizedQuery = normalizeText(searchQuery.value);

      // Si no hay búsqueda, mostrar todos los inmuebles
      if (!normalizedQuery) {
        return inmuebles.value;
      }

      // Filtrar inmuebles por búsqueda de texto en tiempo real
      return inmuebles.value.filter((inmueble) => {
        // Normalizar los textos del inmueble
        const normalizedTitulo = normalizeText(inmueble.titulo);
        const normalizedDescripcion = normalizeText(inmueble.descripcion);
        const normalizedUbicacion = normalizeText(inmueble.ubicacion);
        const normalizedTipo = normalizeText(inmueble.tipo);
        const normalizedSocioNombre = normalizeText(inmueble.socio.nombre);
        const normalizedSocioEmpresa = normalizeText(inmueble.socio.empresa);
        // 🆕 NUEVO: Incluir la clave del inmueble en la búsqueda
        const normalizedKey = normalizeText(inmueble.key || "");

        // Realizar la búsqueda en los textos normalizados
        return (
          normalizedTitulo.includes(normalizedQuery) ||
          normalizedDescripcion.includes(normalizedQuery) ||
          normalizedUbicacion.includes(normalizedQuery) ||
          normalizedTipo.includes(normalizedQuery) ||
          normalizedSocioNombre.includes(normalizedQuery) ||
          normalizedSocioEmpresa.includes(normalizedQuery) ||
          normalizedKey.includes(normalizedQuery) // 🆕 NUEVO: Búsqueda por clave
        );
      });
    });

    // 🔄 TOTAL DE FAVORITOS (REACTIVO)
    // Usar ref() en lugar de computed() porque los datos vienen de localStorage (externo)
    const totalFavoritos = ref(favoritosService.getFavoritos().length);

    // 📡 SUSCRIPCIÓN A CAMBIOS EN FAVORITOS
    // Variable para almacenar la función de desuscripción
    let unsubscribeFavoritos: (() => void) | null = null;

    // Manejo del scroll para detectar visibilidad del buscador
    const handleScroll = () => {
      // Limpiar timeout anterior si existe
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      // Usar throttle para evitar demasiadas ejecuciones
      scrollTimeout = setTimeout(() => {
        if (!buscadorRef.value) {
          return;
        }

        updateBuscadorVisibility();
      }, 16); // ~60fps
    };

    // Función para ir arriba (hasta donde el buscador sea visible)
    const irArriba = async () => {
      if (!buscadorRef.value) return;

      const container = scrollContainer.value;
      const buscadorRect = buscadorRef.value.getBoundingClientRect();

      // Calcular la posición donde el buscador estará completamente visible
      // Agregamos un pequeño margen (20px) para que se vea bien
      const targetScrollPosition = container
        ? container.scrollTop + buscadorRect.top - 20
        : window.scrollY + buscadorRect.top - 20;

      if (container) {
        // Scroll en el contenedor específico
        container.scrollTo({
          top: Math.max(0, targetScrollPosition), // No ir a posición negativa
          behavior: "smooth",
        });
      } else {
        // Scroll en window como fallback
        window.scrollTo({
          top: Math.max(0, targetScrollPosition), // No ir a posición negativa
          behavior: "smooth",
        });
      }
    };

    // Función para encontrar el contenedor con scroll
    const findScrollContainer = (): HTMLElement | null => {
      // Primero intentar buscar por ID específico
      const mainContent = document.getElementById("main-content");
      if (mainContent) {
        console.log("🎯 Contenedor main-content encontrado:", mainContent);
        return mainContent;
      }

      // Buscar cualquier contenedor con overflow-auto
      const containers = document.querySelectorAll(
        '[class*="overflow-auto"], [class*="overflow-scroll"]'
      );
      if (containers.length > 0) {
        const container = containers[0] as HTMLElement;
        console.log("🎯 Contenedor con overflow encontrado:", container);
        return container;
      }

      console.log("⚠️ No se encontró contenedor con scroll, usando window");
      return null;
    };

    // Detectar si el buscador está fuera de vista
    const updateBuscadorVisibility = () => {
      if (buscadorRef.value) {
        const buscadorRect = buscadorRef.value.getBoundingClientRect();

        // El buscador está fuera de vista si su parte inferior está por arriba del viewport
        buscadorOutOfView.value = buscadorRect.bottom < 0;
      }
    };

    // 🆕 NUEVO: Aplicar filtros iniciales si se proporcionan
    const aplicarFiltrosIniciales = () => {
      if (props.filtrosIniciales) {
        if (props.filtrosIniciales.ubicacion) {
          filters.ubicacion = props.filtrosIniciales.ubicacion;
        }
        if (props.filtrosIniciales.operacion) {
          filters.operacion = props.filtrosIniciales.operacion;
          // 🆕 NUEVO: Guardar operación prioritaria para UI
          operacionPrioritaria.value = props.filtrosIniciales.operacion;
        }
        if (
          props.filtrosIniciales.tipos &&
          props.filtrosIniciales.tipos.length > 0
        ) {
          filters.tipos = props.filtrosIniciales.tipos;
        }
        // 🆕 NUEVO: Operación prioritaria explícita
        if (props.filtrosIniciales.operacionPrioritaria) {
          operacionPrioritaria.value =
            props.filtrosIniciales.operacionPrioritaria;
        }
        console.log("🎯 Filtros iniciales aplicados:", {
          ...props.filtrosIniciales,
          operacionPrioritaria: operacionPrioritaria.value,
        });
      }
    };

    // Cargar inmuebles cuando el componente se monta
    onMounted(async () => {
      // Inicializar lazyload y observar imágenes iniciales
      initLazyObserver();
      nextTick(() => observeLazyImages());
      
      // 🆕 NUEVO: Inicializar token para hilos y conexión DDP
      await initializeToken();

      // Aplicar filtros iniciales antes de cargar
      aplicarFiltrosIniciales();
      fetchInmuebles();

      // 🆕 NUEVO: Suscribirse a cambios en favoritos para reactividad
      unsubscribeFavoritos = favoritosService.onChange(() => {
        totalFavoritos.value = favoritosService.getFavoritos().length;
      });

      // 🔍 Buscar contenedor con scroll
      scrollContainer.value = findScrollContainer();

      // Agregar listener de scroll al contenedor correcto
      if (scrollContainer.value) {
        scrollContainer.value.addEventListener("scroll", handleScroll, {
          passive: true,
        });
        scrollContainer.value.addEventListener(
          "scroll",
          updateBuscadorVisibility,
          {
            passive: true,
          }
        );
      } else {
        window.addEventListener("scroll", handleScroll, { passive: true });
        window.addEventListener("scroll", updateBuscadorVisibility, {
          passive: true,
        });
      }

      // Inicializar valores
      updateBuscadorVisibility();
    });

    // Limpiar listeners al desmontar
    onUnmounted(() => {
      // Limpiar observer de lazyload
      if (imageObserver) {
        imageObserver.disconnect();
        imageObserver = null;
      }

      // 🆕 NUEVO: Desuscribirse de cambios en favoritos
      if (unsubscribeFavoritos) {
        unsubscribeFavoritos();
        unsubscribeFavoritos = null;
      }

      if (scrollContainer.value) {
        scrollContainer.value.removeEventListener("scroll", handleScroll);
        scrollContainer.value.removeEventListener(
          "scroll",
          updateBuscadorVisibility
        );
      } else {
        window.removeEventListener("scroll", handleScroll);
        window.removeEventListener("scroll", updateBuscadorVisibility);
      }
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
        scrollTimeout = null;
      }
    });

    // 🗑️ ELIMINADO: aplicarFiltros ya no es necesario con búsqueda local

    // Ver detalle de inmueble
    const verDetalleInmueble = (inmueble: Inmueble) => {
      inmuebleSeleccionado.value = inmueble;
      showDetalleModal.value = true;
    };

    // Contactar socio
    const contactarSocio = (socio: Socio, metodo: string) => {
      if (metodo === "whatsapp" && socio.whatsapp) {
        const mensaje = encodeURIComponent(
          `Hola ${socio.nombre}, me interesa uno de tus inmuebles.`
        );
        const url = `https://wa.me/${socio.whatsapp}?text=${mensaje}`;
        window.open(url, "_blank");
      } else if (metodo === "telefono" && socio.telefono) {
        window.open(`tel:${socio.telefono}`, "_self");
      } else if (metodo === "email" && socio.email) {
        const asunto = encodeURIComponent("Consulta sobre inmueble");
        const cuerpo = encodeURIComponent(
          `Hola ${socio.nombre},\n\nMe interesa obtener más información sobre uno de tus inmuebles.\n\nSaludos.`
        );
        window.open(
          `mailto:${socio.email}?subject=${asunto}&body=${cuerpo}`,
          "_self"
        );
      }
    };

    // 🆕 NUEVO: Marcar como favorito con persistencia en localStorage (ACTUALIZADO)
    const marcarFavorito = (inmueble: Inmueble) => {
      const operacionPrioritaria = getOperacionPrioritaria(inmueble);

      const exito = favoritosService.toggleFavorito({
        id: inmueble.id,
        titulo: inmueble.titulo,
        precio: operacionPrioritaria.precio, // 🆕 Usar precio de operación prioritaria
        operacion: operacionPrioritaria.tipo, // 🆕 Usar operación prioritaria
        ubicacion: inmueble.ubicacion,
      });

      if (exito) {
        // Actualizar estado local solo si el toggle fue exitoso
        inmueble.esFavorito = !inmueble.esFavorito;

        console.log(
          `⭐ Inmueble ${inmueble.id} ${
            inmueble.esFavorito ? "agregado a" : "removido de"
          } favoritos (${operacionPrioritaria.tipo}: ${formatPriceWithCurrency(
            operacionPrioritaria
          )})`
        );
      } else {
        console.warn("⚠️ No se pudo actualizar el estado de favorito");
      }
    };

    // Ver favoritos
    const verFavoritos = () => {
      showFavoritos.value = true;
      emit("favoritos-changed", true); // Notificar que se cambió a favoritos
    };

    // 🆕 NUEVO: Watcher para activar favoritos automáticamente (después de definir verFavoritos)
    watch(
      () => props.activarFavoritosInmediatamente,
      (nuevoValor) => {
        if (nuevoValor) {
          console.log(
            "⭐ Activando favoritos automáticamente desde filtro inteligente"
          );
          verFavoritos();
          // Notificar al padre que se activaron los favoritos
          emit("favoritos-activados"); // Señal para resetear la prop
        }
      },
      { immediate: true }
    );

    // Observar imágenes cada vez que cambia la lista renderizada
    watch(
      () => inmuebles.value,
      () => nextTick(() => observeLazyImages())
    );
    watch(
      () => filteredInmuebles.value,
      () => nextTick(() => observeLazyImages())
    );

    // Volver a la lista de inmuebles desde favoritos
    const volverAInmuebles = () => {
      showFavoritos.value = false;
      emit("favoritos-changed", false); // Notificar que se salió de favoritos
    };

    // Ir a filtros inteligentes desde favoritos
    const irAFiltros = () => {
      emit("ir-a-filtros");
    };

    // Formatear precio
    const formatPrice = (price: number) => {
      return price.toLocaleString("es-MX");
    };

    // Obtener label de ubicación (ya no necesario porque el servidor devuelve la ubicación formateada)
    const getLocationLabel = (location: string) => {
      // El servidor ya devuelve la ubicación formateada, pero mantenemos esta función
      // por compatibilidad con el template
      return location;
    };

    // 🎯 NUEVO: Limpiar solo búsqueda de texto (filtros vienen de filtro inteligente)
    const limpiarFiltros = () => {
      searchQuery.value = "";
    };

    // Limpiar etiquetas HTML de un string
    function stripTags(input: string) {
      return input ? input.replace(/<[^>]*>/g, "") : "";
    }

    // Decodificar entidades HTML
    function decodeHtmlEntities(input: string) {
      if (!input) return "";
      const txt = document.createElement("textarea");
      txt.innerHTML = input;
      return txt.value;
    }

    // 🆕 NUEVO: Obtener operación prioritaria para mostrar en el card
    const getOperacionPrioritaria = (inmueble: Inmueble): Operacion => {
      if (!inmueble.operaciones || inmueble.operaciones.length === 0) {
        // Fallback a datos de compatibilidad
        return {
          tipo: inmueble.operacion || inmueble.operacion_principal,
          precio: inmueble.precio || inmueble.precio_principal,
          precio_formateado: `$${formatPrice(
            inmueble.precio || inmueble.precio_principal
          )}`,
          comision: 0,
          moneda: "MXN",
          disponible: true,
        };
      }

      // Si hay una operación prioritaria desde filtros, buscarla primero
      if (operacionPrioritaria.value) {
        const operacionEspecifica = inmueble.operaciones.find(
          (op) => op.tipo === operacionPrioritaria.value
        );
        if (operacionEspecifica) {
          return operacionEspecifica;
        }
      }

      // Si no, usar la primera operación (ya ordenada por prioridad en el backend)
      return inmueble.operaciones[0];
    };

    // 🆕 NUEVO: Obtener operaciones secundarias (no prioritarias)
    const getOperacionesSecundarias = (inmueble: Inmueble): Operacion[] => {
      if (!inmueble.operaciones || inmueble.operaciones.length <= 1) {
        return [];
      }

      const prioritaria = getOperacionPrioritaria(inmueble);
      return inmueble.operaciones.filter((op) => op.tipo !== prioritaria.tipo);
    };

    // 🆕 NUEVO: Formatear precio con moneda
    const formatPriceWithCurrency = (operacion: Operacion): string => {
      if (operacion.precio_formateado) {
        return operacion.precio_formateado;
      }
      return `$${formatPrice(operacion.precio)} ${operacion.moneda || "MXN"}`;
    };

    // 🆕 NUEVO: Obtener color del badge según tipo de operación
    const getOperacionBadgeColor = (
      tipo: string,
      esPrioritaria: boolean = false
    ): string => {
      const baseColors = {
        venta: esPrioritaria ? "bg-green-600" : "bg-green-500",
        renta: esPrioritaria ? "bg-blue-600" : "bg-blue-500",
        traspaso: esPrioritaria ? "bg-orange-600" : "bg-orange-500",
      };
      return baseColors[tipo as keyof typeof baseColors] || "bg-gray-500";
    };

    // 🆕 NUEVO: Obtener texto contextual de coincidencias
    const getCoincidenciasTexto = (): string => {
      const total = inmuebles.value.length;
      const encontradas = filteredInmuebles.value.length;
      const porcentaje =
        total > 0 ? Math.round((encontradas / total) * 100) : 0;

      if (encontradas === 0) {
        return "Sin resultados";
      } else if (encontradas === total) {
        return "Todos los inmuebles";
      } else if (porcentaje >= 75) {
        return "Muy buena coincidencia";
      } else if (porcentaje >= 50) {
        return "Buena coincidencia";
      } else if (porcentaje >= 25) {
        return "Algunas coincidencias";
      } else {
        return "Pocas coincidencias";
      }
    };

    // 🆕 NUEVO: Manejar publicación/remoción del inmueble en mi web (estados)
    const togglePublicacionEnMiWeb = (inmueble: Inmueble) => {
      const estadoActual =
        inmueble.publicacionEnMiWeb?.estado || "sin_solicitud";
      const tituloInmueble = stripTags(inmueble.titulo);
      const socioNombre = stripTags(
        (inmueble as any)?.socio?.nombre || "tu socio"
      );

      // Determinar acción y mensajes según estado
      const esPublicado = estadoActual === "publicado";
      const esPendiente = estadoActual === "solicitud_pendiente";
      const esSolicitable =
        estadoActual === "sin_solicitud" || estadoActual === "rechazado";

      if (esPendiente) {
        showError(
          "Ya existe una solicitud pendiente de autorización por parte del socio.",
          "Solicitud pendiente"
        );
        return;
      }

      const mensaje = esPublicado
        ? `¿Estás seguro de que quieres quitar "${tituloInmueble}" de tu sitio web?`
        : `Se enviará una solicitud a tu socio ${socioNombre} para la publicación de su inmueble en tu web. Una vez autorizado se publicará. Recuerda que tú siempre tendrás el control de los clientes y contactos que se originen desde tu web relacionados a este inmueble.`;

      const titulo = esPublicado ? "Quitar de mi web" : "Solicitar publicación";

      confirm(
        mensaje,
        async () => {
          try {
            console.log(
              `🌐 ${
                esPublicado ? "Quitando" : "Solicitando"
              } publicación del inmueble ${inmueble.id} (${inmueble.key})...`
            );

            // Llamada real al endpoint correspondiente
            if (esPublicado) {
              // Llamar endpoint para quitar publicación
              const response = await axios.post(
                `/msi-v5/owner/inmuebles-socios/${inmueble.id}/quitar-publicacion`
              );

              if (response.data && response.data.statusCode === 200) {
                // Actualizar estado local con datos del servidor
                inmueble.publicacionEnMiWeb = {
                  publicado: false,
                  estado: "sin_solicitud",
                  descripcion: "No hay solicitud de publicación",
                  fecha_solicitud: null,
                  token: null,
                  accion_pendiente_de: null,
                };
                success(
                  `"${tituloInmueble}" ha sido removido exitosamente de tu sitio web`,
                  "¡Removido!"
                );
              } else {
                throw new Error(
                  response.data?.data?.error ||
                    "Error en la respuesta del servidor"
                );
              }
            } else if (esSolicitable) {
              // Llamar endpoint para solicitar publicación
              const response = await axios.post(
                `/msi-v5/owner/inmuebles-socios/${inmueble.id}/solicitar-publicacion`
              );
              if (response.data && response.data.statusCode === 200) {
                const data = response.data.data;
                // Actualizar estado local con datos del servidor
                inmueble.publicacionEnMiWeb = {
                  publicado: false,
                  estado: data.estado || "solicitud_pendiente",
                  descripcion: "Solicitud pendiente de autorización",
                  fecha_solicitud: new Date().toISOString(),
                  token: data.token,
                  accion_pendiente_de: data.accion_pendiente_de || "socio",
                };
                success(
                  `Se envió la solicitud a ${socioNombre}. Quedará pendiente hasta su autorización.`,
                  "Solicitud enviada"
                );
              } else {
                throw new Error(
                  response.data?.data?.error ||
                    "Error en la respuesta del servidor"
                );
              }
            }
          } catch (error: any) {
            console.error("❌ Error al cambiar estado de publicación:", error);
            let mensajeError =
              "No se pudo completar la operación. Intenta nuevamente.";
            if (error.response?.status === 403) {
              mensajeError = "No tienes permisos para realizar esta acción.";
              showError(mensajeError, "Sin permisos");
              setTimeout(() => (window.location.href = "/"), 2000);
              return;
            } else if (error.response?.status === 404) {
              mensajeError =
                "El inmueble no fue encontrado o ya no está disponible.";
            } else if (error.response?.status === 400) {
              mensajeError =
                error.response.data?.data?.error ||
                "Datos inválidos para la operación.";
            } else if (error.response?.data?.data?.error) {
              mensajeError = error.response.data.data.error;
            }
            showError(mensajeError, "Error");
          }
        },
        titulo
      );
    };

    // 🆕 NUEVO: Funciones helper para el menú contextual del owner
    const isAccionPendienteOwner = (inmueble: Inmueble): boolean => {
      return (
        inmueble.publicacionEnMiWeb?.estado === "solicitud_pendiente" &&
        inmueble.publicacionEnMiWeb?.accion_pendiente_de === "owner"
      );
    };

    const toggleMenuContextual = (inmuebleId: string) => {
      menuContextualAbierto.value =
        menuContextualAbierto.value === inmuebleId ? null : inmuebleId;
    };

    const cerrarMenuContextual = () => {
      menuContextualAbierto.value = null;
    };

    // 🆕 NUEVO: Funciones de confirmación para el menú contextual
    const confirmarAceptarPublicacion = (inmueble: Inmueble) => {
      const tituloInmueble = stripTags(inmueble.titulo);
      const socioNombre = stripTags(
        (inmueble as any)?.socio?.nombre || "tu socio"
      );

      confirm(
        `Se publicará en tu sitio web el inmueble "<b>${tituloInmueble}</b>" de <b>${socioNombre}</b><br>Una vez publicado, aparecerá en tu web y podrás recibir consultas de clientes.`,
        async () => {
          await aceptarPublicacion(inmueble);
        },
        "Confirmar publicación"
      );
    };

    const confirmarRechazarPublicacion = (inmueble: Inmueble) => {
      const tituloInmueble = stripTags(inmueble.titulo);
      const socioNombre = stripTags(
        (inmueble as any)?.socio?.nombre || "tu socio"
      );

      confirm(
        `¿Estás seguro de que quieres rechazar la publicación de "<b>${tituloInmueble}</b>" de <b>${socioNombre}</b>?`,
        async () => {
          await rechazarPublicacion(inmueble);
        },
        "Confirmar rechazo"
      );
    };

    // 🆕 NUEVO: Función para cancelar solicitud de publicación
    const confirmarCancelarSolicitud = (inmueble: Inmueble) => {
      const tituloInmueble = stripTags(inmueble.titulo);
      const socioNombre = stripTags(
        (inmueble as any)?.socio?.nombre || "tu socio"
      );

      confirm(
        `¿Estás seguro de que quieres cancelar la solicitud de publicación de "${tituloInmueble}" de ${socioNombre}?\n\nEsta acción cancelará la solicitud pendiente y el inmueble volverá a estar disponible para solicitar publicación en el futuro.`,
        async () => {
          await cancelarSolicitud(inmueble);
        },
        "Confirmar cancelación"
      );
    };

    const aceptarPublicacion = async (inmueble: Inmueble) => {
      try {
        const tituloInmueble = stripTags(inmueble.titulo);

        console.log(
          `✅ Aceptando publicación del inmueble ${inmueble.id} (${inmueble.key})...`
        );

        // Llamada real al endpoint para aceptar solicitud de publicación
        const response = await axios.post(
          `/msi-v5/owner/inmuebles-socios/${inmueble.id}/aceptar-publicacion`
        );

        if (response.data && response.data.statusCode === 200) {
          // Actualizar estado local con datos del servidor
          inmueble.publicacionEnMiWeb = {
            publicado: true,
            estado: "publicado",
            descripcion: "Publicado en mi web",
            fecha_solicitud:
              inmueble.publicacionEnMiWeb?.fecha_solicitud ||
              new Date().toISOString(),
            token: response.data.data?.token || null,
            accion_pendiente_de: null,
          };

          cerrarMenuContextual();
          success(
            `"${tituloInmueble}" ha sido publicado exitosamente en tu sitio web`,
            "¡Publicado!"
          );
        } else {
          throw new Error(
            response.data?.data?.error || "Error en la respuesta del servidor"
          );
        }
      } catch (error: any) {
        console.error("❌ Error al aceptar publicación:", error);

        let errorMessage =
          "No se pudo aceptar la publicación. Intenta nuevamente.";

        if (error.response?.status === 404) {
          errorMessage = "Inmueble no encontrado o solicitud ya procesada.";
        } else if (error.response?.status === 400) {
          errorMessage =
            error.response.data?.data?.error || "Solicitud inválida.";
        } else if (error.response?.status === 403) {
          errorMessage = "No tienes permisos para realizar esta acción.";
        } else if (error.response?.data?.data?.error) {
          errorMessage = error.response.data.data.error;
        }

        showError(errorMessage, "Error");
      }
    };

    const rechazarPublicacion = async (inmueble: Inmueble) => {
      try {
        const tituloInmueble = stripTags(inmueble.titulo);

        console.log(
          `❌ Rechazando publicación del inmueble ${inmueble.id} (${inmueble.key})...`
        );

        // Llamada real al endpoint para rechazar solicitud de publicación
        const response = await axios.post(
          `/msi-v5/owner/inmuebles-socios/${inmueble.id}/rechazar-publicacion`
        );

        if (response.data && response.data.statusCode === 200) {
          // Actualizar estado local con datos del servidor
          inmueble.publicacionEnMiWeb = {
            publicado: false,
            estado: "rechazado",
            descripcion: "Solicitud no autorizada",
            fecha_solicitud:
              inmueble.publicacionEnMiWeb?.fecha_solicitud ||
              new Date().toISOString(),
            token: null,
            accion_pendiente_de: null,
          };

          cerrarMenuContextual();
          success(
            `Se rechazó la publicación de "${tituloInmueble}"`,
            "Solicitud rechazada"
          );
        } else {
          throw new Error(
            response.data?.data?.error || "Error en la respuesta del servidor"
          );
        }
      } catch (error: any) {
        console.error("❌ Error al rechazar publicación:", error);

        let errorMessage =
          "No se pudo rechazar la publicación. Intenta nuevamente.";

        if (error.response?.status === 404) {
          errorMessage = "Inmueble no encontrado o solicitud ya procesada.";
        } else if (error.response?.status === 400) {
          errorMessage =
            error.response.data?.data?.error || "Solicitud inválida.";
        } else if (error.response?.status === 403) {
          errorMessage = "No tienes permisos para realizar esta acción.";
        } else if (error.response?.data?.data?.error) {
          errorMessage = error.response.data.data.error;
        }

        showError(errorMessage, "Error");
      }
    };

    // 🆕 NUEVO: Función para cancelar solicitud de publicación
    const cancelarSolicitud = async (inmueble: Inmueble) => {
      try {
        const tituloInmueble = stripTags(inmueble.titulo);

        console.log(
          `❌ Cancelando solicitud de publicación del inmueble ${inmueble.id} (${inmueble.key})...`
        );

        // Llamada real al endpoint para cancelar solicitud de publicación
        const response = await axios.post(
          `/msi-v5/owner/inmuebles-socios/${inmueble.id}/cancelar-solicitud`
        );

        if (response.data && response.data.statusCode === 200) {
          // Actualizar estado local con datos del servidor
          inmueble.publicacionEnMiWeb = {
            publicado: false,
            estado: "sin_solicitud",
            descripcion: "No hay solicitud de publicación",
            fecha_solicitud: null,
            token: null,
            accion_pendiente_de: null,
          };

          success(
            `Se canceló la solicitud de publicación de "${tituloInmueble}"`,
            "Solicitud cancelada"
          );
        } else {
          throw new Error(
            response.data?.data?.error || "Error en la respuesta del servidor"
          );
        }
      } catch (error: any) {
        console.error("❌ Error al cancelar solicitud:", error);

        let errorMessage =
          "No se pudo cancelar la solicitud. Intenta nuevamente.";

        if (error.response?.status === 404) {
          errorMessage = "Inmueble no encontrado o solicitud ya procesada.";
        } else if (error.response?.status === 400) {
          errorMessage =
            error.response.data?.data?.error || "Solicitud inválida.";
        } else if (error.response?.status === 403) {
          errorMessage = "No tienes permisos para realizar esta acción.";
        } else if (error.response?.data?.data?.error) {
          errorMessage = error.response.data.data.error;
        }

        showError(errorMessage, "Error");
      }
    };

    // 🆕 NUEVO: Funciones para manejo de hilos de interés
    const abrirCrearHilo = (inmueble: Inmueble) => {
      // 🏠 CORREGIDO: Preparar datos específicos para hilos de inmuebles
      const inmuebleData = {
        // Datos básicos del inmueble
        inmuebleId: inmueble.id,
        titulo: `Interés en ${inmueble.titulo}`,
        referenciaPrivada: `Inmueble ${inmueble.key} - ${inmueble.socio.nombre}`,
        
        // Información completa del inmueble y socio
        inmuebleInfo: {
          titulo: inmueble.titulo,
          descripcion: inmueble.descripcion,
          socio: {
            id: inmueble.socio.id,
            meteor_id: inmueble.socio.meteor_id, // ✅ CAMPO CLAVE para Meteor
            nombre: inmueble.socio.nombre,
            empresa: inmueble.socio.empresa,
          }
        }
      };
      
      // Para compatibilidad con NuevoHiloForm, también agregar formato de post
      const postData = {
        _id: inmueble.id,
        id: inmueble.id,
        title: inmueble.titulo,
        authorCache: {
          firstName: inmueble.socio.nombre.split(' ')[0] || '',
          lastName: inmueble.socio.nombre.split(' ').slice(1).join(' ') || '',
          name: inmueble.socio.nombre,
        },
        // 🏠 NUEVO: Agregar datos específicos del inmueble
        inmuebleData: inmuebleData,
        esInmueble: true, // Flag para identificar que es un inmueble
      };
      
      inmuebleSeleccionadoParaHilo.value = postData;
      showCrearHiloModal.value = true;
    };

    const handleCloseCrearHilo = () => {
      showCrearHiloModal.value = false;
      inmuebleSeleccionadoParaHilo.value = null;
    };

    const handleHiloCreated = async (hiloRecienCreado: any) => {
      console.log('✅ Hilo creado exitosamente desde inmueble:', hiloRecienCreado);
      console.log('🔍 DEBUG: props.token =', props.token);
      
      // Cerrar modal de crear hilo
      handleCloseCrearHilo();
      
      // 🔧 VERIFICAR que tenemos token antes de abrir chat
      if (!props.token) {
        console.warn('⚠️ Token no disponible en props, reintentando obtenerlo...');
        await initializeToken();
        console.log('🔍 DEBUG: props.token después de reintentar =', props.token);
      }
      
      // Abrir chat modal inmediatamente
      hiloSeleccionado.value = hiloRecienCreado;
      showChatModal.value = true;
    };

    // Funciones del modal de chat
    const cerrarChatModal = async () => {
      showChatModal.value = false;
      hiloSeleccionado.value = null;
    };

    const handleChatError = (message: string) => {
      showError(message, "Error en el chat");
    };

    const handleChatSuccess = (message: string) => {
      success(message, "Éxito");
    };

    const handleAbrirChatFromHilo = (hiloRecienCreado: any) => {
      console.log('🔥 Intentando abrir chat desde hilo:', hiloRecienCreado);
      // Esta función se puede implementar cuando tengamos el sistema de chat
      // window.open(`/chat/${hiloRecienCreado._id}`, '_blank');
    };

    const handleHiloError = (error: any) => {
      console.error('❌ Error en el hilo:', error);
      showError('Ocurrió un error al crear el hilo de interés', 'Error');
    };

    // 🆕 NUEVO: Inicializar token y conexión DDP
    const initializeToken = async () => {
      try {
        console.log('🔍 DEBUG: Iniciando initializeToken...');
        console.log('🔍 DEBUG: props.token =', props.token);
        
        // Usar token desde props (viene desde BaseMultibolsaInmobiliaria)
        if (props.token) {
          console.log('✅ Token obtenido desde props');
          
          // 🆕 NUEVO: Guardar token en localStorage para futuras sesiones
          localStorage.setItem('mulbin_token', props.token);
          console.log('💾 Token guardado en localStorage');
        } else {
          console.warn('⚠️ No se recibió token desde props');
          console.log('🔍 DEBUG: Verificar que BaseMultibolsaInmobiliaria.vue esté pasando :token');
        }
        
        // 🚀 INICIALIZAR CONEXIÓN DDP si tenemos token (siguiendo METEOR-DDP-REACTIVIDAD-GUIA.md)
        if (props.token) {
          console.log('🔌 Inicializando conexión DDP para InmueblesSocios...');
          await inmobiliarioService.connectWithToken(props.token);
          console.log('✅ Conexión DDP establecida correctamente');
        } else {
          console.warn('⚠️ No se puede inicializar DDP sin token');
        }
        
      } catch (error) {
        console.error('❌ Error inicializando token y conexión DDP:', error);
      }
    };

    return {
      props, // ✅ NUEVO: Exponer props para acceso en template
      rootEl,
      searchQuery,
      inmuebles,
      loading,
      error,
      mulbinUrl,
      filteredInmuebles,
      filters,
      showDetalleModal,
      inmuebleSeleccionado,
      // Modal de hilos
      showCrearHiloModal,
      inmuebleSeleccionadoParaHilo,
      showChatModal,
      hiloSeleccionado,
      // Control de visibilidad
      buscadorRef,
      scrollContainer,
      buscadorOutOfView,
      // Favoritos
      showFavoritos,
      totalFavoritos,
      // Métodos
      fetchInmuebles,
      verDetalleInmueble,
      contactarSocio,
      marcarFavorito,
      verFavoritos,
      volverAInmuebles,
      irAFiltros,
      formatPrice,
      getLocationLabel,
      irArriba,
      limpiarFiltros,
      stripTags,
      decodeHtmlEntities,
      // 🆕 NUEVO: Funciones para manejo de operaciones múltiples
      getOperacionPrioritaria,
      getOperacionesSecundarias,
      formatPriceWithCurrency,
      getOperacionBadgeColor,
      getCoincidenciasTexto,
      operacionPrioritaria,
      // 🆕 NUEVO: Función para manejar publicación en mi web
      togglePublicacionEnMiWeb,
      // 🆕 NUEVO: Funciones para el menú contextual del owner
      isAccionPendienteOwner,
      toggleMenuContextual,
      cerrarMenuContextual,
      confirmarAceptarPublicacion,
      confirmarRechazarPublicacion,
      confirmarCancelarSolicitud,
      aceptarPublicacion,
      rechazarPublicacion,
      cancelarSolicitud,
      menuContextualAbierto,
      // 🆕 NUEVO: Funciones para hilos de interés
      abrirCrearHilo,
      handleCloseCrearHilo,
      handleHiloCreated,
      handleAbrirChatFromHilo,
      handleHiloError,
      cerrarChatModal,
      handleChatError,
      handleChatSuccess,
      initializeToken,
    };
  },
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

/* Transiciones suaves para el buscador */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Efectos hover para la barra comprimida */
.cursor-pointer:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Animación del ícono de pulso */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Posición sticky mejorada */
.sticky-search {
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(8px);
}
</style>
