<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MicroDash - Sistema Unificado de Apariencias</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      type="module"
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"
    ></script>
    <style>
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      /* Estilos para separadores elegantes */
      .border-l-3 {
        border-left-width: 3px;
      }

      /* Transiciones suaves para bordes */
      .group {
        transition: all 0.2s ease, border-left-color 0.2s ease;
      }

      /* Efectos de hover mejorados */
      .group:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* Separadores entre elementos */
      .border-t.border-gray-100 {
        border-top-color: #f3f4f6;
        border-top-width: 1px;
      }

      /* Separadores entre secciones */
      .border-t-2.border-t-gray-100 {
        border-top-color: #f9fafb;
        border-top-width: 2px;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-7xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
        🎨 MicroDash - Sistema Unificado de Apariencias
      </h1>

      <div class="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 class="text-lg font-semibold text-blue-800 mb-3">
          ✨ Nuevo Sistema Unificado Implementado
        </h2>
        <ul class="text-blue-700 space-y-1">
          <li>
            ✅ <strong>Sistema unificado</strong> - Las apariencias usan los
            mismos estilos que uiStyle
          </li>
          <li>
            ✅ <strong>Consistencia total</strong> - No hay duplicación de
            conceptos
          </li>
          <li>
            ✅ <strong>Flexibilidad completa</strong> - Todos los estilos
            disponibles para apariencias
          </li>
          <li>
            ✅ <strong>Mantenibilidad</strong> - Un solo sistema de estilos para
            todo
          </li>
          <li>
            ✅ <strong>Lógica coherente</strong> - clean = transparente, blue =
            azul sólido, etc.
          </li>
        </ul>
      </div>

      <!-- Grid de Apariencias -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <!-- Apariencia Clean -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            📋 Apariencia Clean (Default)
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">Navegación</h4>
              <ion-icon
                name="menu-outline"
                class="text-lg text-gray-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia clean -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-gray-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-gray-400"
              >
                <ion-icon
                  name="globe-outline"
                  class="text-lg text-gray-500 group-hover:text-gray-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-gray-600 group-hover:text-gray-700"
                  >
                    Plataformas disponibles
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-gray-50 cursor-pointer hover:shadow-sm border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-gray-400"
              >
                <ion-icon
                  name="link-outline"
                  class="text-lg text-gray-500 group-hover:text-gray-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-gray-600 group-hover:text-gray-700"
                  >
                    Configurar enlaces
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo transparente con hover gris claro</li>
              <li>Texto gris con hover más oscuro</li>
              <li>Separadores elegantes sutiles</li>
              <li>Ideal para navegación</li>
            </ul>
          </div>
        </div>

        <!-- Apariencia Soft-Blue -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            🔵 Apariencia Soft-Blue
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">Enlaces suaves</h4>
              <ion-icon
                name="link-outline"
                class="text-lg text-blue-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia soft-blue -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-blue-300 bg-blue-25"
              >
                <ion-icon
                  name="document-outline"
                  class="text-lg text-blue-500 group-hover:text-blue-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-blue-600 group-hover:text-blue-700"
                  >
                    Ver documentación
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-300 bg-blue-25"
              >
                <ion-icon
                  name="help-circle-outline"
                  class="text-lg text-blue-500 group-hover:text-blue-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-blue-600 group-hover:text-blue-700"
                  >
                    Ayuda y soporte
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo azul muy claro (bg-blue-25)</li>
              <li>Texto azul con hover más oscuro</li>
              <li>Íconos azules con hover más oscuro</li>
              <li>Ideal para enlaces discretos</li>
            </ul>
          </div>
        </div>

        <!-- Apariencia Blue -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            🔵 Apariencia Blue
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Acciones principales
              </h4>
              <ion-icon
                name="flash-outline"
                class="text-lg text-blue-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia blue -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon
                  name="add-circle-outline"
                  class="text-lg text-white group-hover:text-blue-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-blue-100"
                  >
                    Crear nuevo proyecto
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon
                  name="rocket-outline"
                  class="text-lg text-white group-hover:text-blue-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-blue-100"
                  >
                    Lanzar proyecto
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo azul sólido con hover más oscuro</li>
              <li>Texto e íconos blancos</li>
              <li>Efecto de escala en hover</li>
              <li>Ideal para acciones principales</li>
            </ul>
          </div>
        </div>

        <!-- Apariencia Mulbin -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            🟣 Apariencia Mulbin
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Funciones premium
              </h4>
              <ion-icon
                name="star-outline"
                class="text-lg text-purple-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia mulbin -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-purple-600 text-white hover:bg-purple-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-purple-300"
              >
                <ion-icon
                  name="diamond-outline"
                  class="text-lg text-white group-hover:text-purple-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-purple-100"
                  >
                    Funciones premium
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-purple-600 text-white hover:bg-purple-700 cursor-pointer hover:shadow-lg hover:scale-105 border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-purple-300"
              >
                <ion-icon
                  name="trophy-outline"
                  class="text-lg text-white group-hover:text-purple-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-purple-100"
                  >
                    Características avanzadas
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo púrpura sólido con hover más oscuro</li>
              <li>Texto e íconos blancos</li>
              <li>Efecto de escala en hover</li>
              <li>Ideal para funciones premium</li>
            </ul>
          </div>
        </div>

        <!-- Apariencia Red -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">🔴 Apariencia Red</h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Alertas y crítico
              </h4>
              <ion-icon
                name="warning-outline"
                class="text-lg text-red-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia red -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-red-600 text-white hover:bg-red-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-red-300"
              >
                <ion-icon
                  name="alert-circle-outline"
                  class="text-lg text-white group-hover:text-red-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-red-100"
                  >
                    Ver alertas críticas
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-red-600 text-white hover:bg-red-700 cursor-pointer hover:shadow-lg hover:scale-105 border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-red-300"
              >
                <ion-icon
                  name="close-circle-outline"
                  class="text-lg text-white group-hover:text-red-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-red-100"
                  >
                    Resolver problemas
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo rojo sólido con hover más oscuro</li>
              <li>Texto e íconos blancos</li>
              <li>Efecto de escala en hover</li>
              <li>Ideal para alertas y crítico</li>
            </ul>
          </div>
        </div>

        <!-- Apariencia Green -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            🟢 Apariencia Green
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Éxito y confirmación
              </h4>
              <ion-icon
                name="checkmark-circle-outline"
                class="text-lg text-green-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia green -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-green-600 text-white hover:bg-green-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-green-300"
              >
                <ion-icon
                  name="checkmark-outline"
                  class="text-lg text-white group-hover:text-green-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-green-100"
                  >
                    Confirmar acción
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-green-600 text-white hover:bg-green-700 cursor-pointer hover:shadow-lg hover:scale-105 border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-green-300"
              >
                <ion-icon
                  name="thumbs-up-outline"
                  class="text-lg text-white group-hover:text-green-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-green-100"
                  >
                    Aprobar solicitud
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo verde sólido con hover más oscuro</li>
              <li>Texto e íconos blancos</li>
              <li>Efecto de escala en hover</li>
              <li>Ideal para éxito y confirmación</li>
            </ul>
          </div>
        </div>

        <!-- Apariencia Default -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            🔘 Apariencia Default
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Acciones estándar
              </h4>
              <ion-icon
                name="settings-outline"
                class="text-lg text-blue-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia default -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon
                  name="settings-outline"
                  class="text-lg text-white group-hover:text-blue-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-blue-100"
                  >
                    Configuración
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon
                  name="help-circle-outline"
                  class="text-lg text-white group-hover:text-blue-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-blue-100"
                  >
                    Ayuda y soporte
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo azul sólido (mismo que blue)</li>
              <li>Texto e íconos blancos</li>
              <li>Efecto de escala en hover</li>
              <li>Apariencia estándar del sistema</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Ejemplo de Código -->
      <div class="mt-12 bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-4">
          💻 Código del Sistema Unificado
        </h3>

        <div class="space-y-4">
          <div>
            <h4 class="text-sm font-medium text-gray-300 mb-2">
              HTML con Apariencias Unificadas:
            </h4>
            <pre
              class="bg-gray-900 p-4 rounded text-sm text-gray-200 overflow-x-auto"
            ><code>&lt;div
  data-micro-dash
  data-title="Mi Sitio Web"
  data-icon="globe-outline"
  data-ui-style="clean"
  data-default-appearance="clean"
  data-links='[
    {
      "id": "view-external-publication",
      "title": "Plataformas disponibles",
      "url": "./importExportFromTo.php",
      "icon": "globe-outline",
      "appearance": "clean"
    },
    {
      "id": "documentation",
      "title": "Ver documentación",
      "url": "./docs.php",
      "icon": "document-outline",
      "appearance": "soft-blue"
    },
    {
      "id": "configure-links",
      "title": "Configurar enlaces",
      "url": "./configure-links.php",
      "icon": "link-outline",
      "appearance": "blue"
    },
    {
      "id": "premium-features",
      "title": "Funciones premium",
      "url": "./premium.php",
      "icon": "star-outline",
      "appearance": "mulbin"
    },
    {
      "id": "critical-alerts",
      "title": "Alertas críticas",
      "url": "./alerts.php",
      "icon": "warning-outline",
      "appearance": "red"
    },
    {
      "id": "confirm-action",
      "title": "Confirmar acción",
      "url": "./confirm.php",
      "icon": "checkmark-outline",
      "appearance": "green"
    }
  ]'
&gt;
  &lt;!-- Loading placeholder --&gt;
&lt;/div&gt;</code></pre>
          </div>

          <div>
            <h4 class="text-sm font-medium text-gray-300 mb-2">
              Configuración Global:
            </h4>
            <pre
              class="bg-gray-900 p-4 rounded text-sm text-gray-200 overflow-x-auto"
            ><code>&lt;div
  data-micro-dash
  data-title="Dashboard de Acciones"
  data-icon="flash-outline"
  data-ui-style="blue"
  data-default-appearance="blue"
  data-links='[...]'
&gt;
  &lt;!-- Todos los enlaces serán azules por defecto --&gt;
&lt;/div&gt;</code></pre>
          </div>
        </div>
      </div>

      <!-- Beneficios del Sistema Unificado -->
      <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">
          ✨ Beneficios del Sistema Unificado
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-700 mb-2">🎯 Consistencia</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Mismo sistema de colores</strong> - uiStyle y
                appearance usan los mismos valores
              </li>
              <li>
                ✅ <strong>No hay duplicación</strong> - Un solo conjunto de
                estilos
              </li>
              <li>
                ✅ <strong>Lógica coherente</strong> - clean = transparente,
                blue = azul sólido
              </li>
              <li>
                ✅ <strong>Mantenimiento simple</strong> - Cambios en un lugar
                afectan todo
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-2">🚀 Flexibilidad</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Todos los estilos disponibles</strong> - clean, blue,
                mulbin, red, green
              </li>
              <li>
                ✅ <strong>Configuración por enlace</strong> - Cada enlace puede
                tener su estilo
              </li>
              <li>
                ✅ <strong>Configuración global</strong> - Estilo por defecto
                para toda la instancia
              </li>
              <li>
                ✅ <strong>Combinaciones ilimitadas</strong> - uiStyle para el
                contenedor, appearance para enlaces
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Casos de Uso del Sistema Unificado -->
      <div
        class="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6"
      >
        <h3 class="text-lg font-semibold text-gray-700 mb-4">
          🎯 Casos de Uso del Sistema Unificado
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-700 mb-2">📋 Apariencia Clean</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Navegación</strong> - Enlaces de menú y opciones
              </li>
              <li>✅ <strong>Referencias</strong> - Enlaces informativos</li>
              <li>✅ <strong>Sidebars</strong> - Navegación lateral</li>
              <li>✅ <strong>Dashboards</strong> - Enlaces de consulta</li>
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-2">🔵 Apariencia Blue</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Acciones principales</strong> - Botones de llamada a
                la acción
              </li>
              <li>
                ✅ <strong>Funciones críticas</strong> - Operaciones importantes
              </li>
              <li>✅ <strong>Formularios</strong> - Envío y confirmación</li>
              <li>
                ✅ <strong>Navegación principal</strong> - Enlaces destacados
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-2">🟣 Apariencia Mulbin</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Funciones premium</strong> - Características
                avanzadas
              </li>
              <li>✅ <strong>Marca corporativa</strong> - Identidad visual</li>
              <li>✅ <strong>Funciones especiales</strong> - Acceso VIP</li>
              <li>
                ✅ <strong>Características únicas</strong> - Diferenciación
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-2">🔴 Apariencia Red</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>✅ <strong>Alertas críticas</strong> - Problemas urgentes</li>
              <li>
                ✅ <strong>Acciones destructivas</strong> - Eliminar, cancelar
              </li>
              <li>
                ✅ <strong>Notificaciones importantes</strong> - Requieren
                atención
              </li>
              <li>
                ✅ <strong>Errores del sistema</strong> - Reportes de problemas
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-2">🟢 Apariencia Green</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>✅ <strong>Confirmaciones</strong> - Aprobar, aceptar</li>
              <li>✅ <strong>Éxito</strong> - Operaciones completadas</li>
              <li>✅ <strong>Acciones positivas</strong> - Crear, agregar</li>
              <li>
                ✅ <strong>Estados exitosos</strong> - Indicadores de progreso
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Simulación de funcionalidades
      console.log("🎨 MicroDash - Sistema unificado de apariencias cargado");

      // Simular hover effects
      document.querySelectorAll(".group").forEach((group) => {
        group.addEventListener("mouseenter", () => {
          console.log("🖱️ Hover en:", group.textContent.trim());
        });
      });
    </script>
  </body>
</html>
