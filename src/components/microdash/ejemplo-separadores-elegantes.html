<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MicroDash - Separadores Elegantes</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      type="module"
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"
    ></script>
    <style>
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      /* Estilos para separadores elegantes */
      .border-l-3 {
        border-left-width: 3px;
      }

      /* Transiciones suaves para bordes */
      .group {
        transition: all 0.2s ease, border-left-color 0.2s ease;
      }

      /* Efectos de hover mejorados */
      .group:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* Separadores entre elementos */
      .border-t.border-gray-100 {
        border-top-color: #f3f4f6;
        border-top-width: 1px;
      }

      /* Separadores entre secciones */
      .border-t-2.border-t-gray-100 {
        border-top-color: #f9fafb;
        border-top-width: 2px;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
        🎨 MicroDash - Separadores Elegantes
      </h1>

      <div class="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 class="text-lg font-semibold text-blue-800 mb-3">
          ✨ Nuevos Separadores Implementados
        </h2>
        <ul class="text-blue-700 space-y-1">
          <li>
            ✅ <strong>Bordes izquierdos de color</strong> - Se activan en hover
          </li>
          <li>
            ✅ <strong>Separadores horizontales</strong> - Líneas sutiles entre
            opciones
          </li>
          <li>
            ✅ <strong>Espaciado mejorado</strong> - Padding y márgenes
            optimizados
          </li>
          <li>
            ✅ <strong>Sombras en hover</strong> - Efectos visuales elegantes
          </li>
          <li>✅ <strong>Transiciones suaves</strong> - Animaciones fluidas</li>
        </ul>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Ejemplo 1: Menú Simple (como tu integración) -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            📋 Menú Simple con Separadores
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Publicación externa
              </h4>
              <ion-icon
                name="globe-outline"
                class="text-lg text-gray-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con separadores -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-blue-400"
              >
                <ion-icon
                  name="globe-outline"
                  class="text-lg text-blue-500 group-hover:text-blue-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-blue-600 group-hover:text-blue-700"
                  >
                    Plataformas disponibles
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-400"
              >
                <ion-icon
                  name="link-outline"
                  class="text-lg text-blue-500 group-hover:text-blue-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-blue-600 group-hover:text-blue-700"
                  >
                    Configurar enlaces
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-400"
              >
                <ion-icon
                  name="settings-outline"
                  class="text-lg text-blue-500 group-hover:text-blue-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-blue-600 group-hover:text-blue-700"
                  >
                    Ajustes de publicación
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Bordes izquierdos que se activan en hover</li>
              <li>Separadores horizontales sutiles</li>
              <li>Espaciado consistente entre opciones</li>
              <li>Efectos de hover con sombras</li>
            </ul>
          </div>
        </div>

        <!-- Ejemplo 2: Con Secciones -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            📁 Con Secciones y Separadores
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Gestión de Inmuebles
              </h4>
              <ion-icon
                name="home-outline"
                class="text-lg text-blue-600"
              ></ion-icon>
            </div>

            <!-- Sección 1 -->
            <div class="overflow-hidden rounded-lg border border-gray-200 mb-4">
              <div class="px-3 py-2 bg-gray-50 border-b border-gray-200">
                <h5 class="text-xs font-medium text-gray-700">Propiedades</h5>
              </div>
              <div class="p-3 space-y-0">
                <div
                  class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-blue-400"
                >
                  <ion-icon
                    name="add-circle-outline"
                    class="text-lg text-blue-500 group-hover:text-blue-600"
                  ></ion-icon>
                  <div class="flex-1">
                    <span
                      class="text-sm font-medium text-blue-600 group-hover:text-blue-700"
                    >
                      Agregar nueva propiedad
                    </span>
                  </div>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200"
                  >
                    Nuevo
                  </span>
                </div>

                <div
                  class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-400"
                >
                  <ion-icon
                    name="list-outline"
                    class="text-lg text-blue-500 group-hover:text-blue-600"
                  ></ion-icon>
                  <div class="flex-1">
                    <span
                      class="text-sm font-medium text-blue-600 group-hover:text-blue-700"
                    >
                      Ver todas las propiedades
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sección 2 con separador -->
            <div
              class="overflow-hidden rounded-lg border border-gray-200 mt-4 pt-4 border-t-2 border-t-gray-100"
            >
              <div class="px-3 py-2 bg-gray-50 border-b border-gray-200">
                <h5 class="text-xs font-medium text-gray-700">Reportes</h5>
              </div>
              <div class="p-3 space-y-0">
                <div
                  class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-blue-400"
                >
                  <ion-icon
                    name="trending-up-outline"
                    class="text-lg text-blue-500 group-hover:text-blue-600"
                  ></ion-icon>
                  <div class="flex-1">
                    <span
                      class="text-sm font-medium text-blue-600 group-hover:text-blue-700"
                    >
                      Ventas del mes
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Separadores entre secciones (bordes más gruesos)</li>
              <li>Separadores entre enlaces (bordes sutiles)</li>
              <li>Bordes izquierdos de color en hover</li>
              <li>Espaciado visual mejorado</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Código de Implementación -->
      <div class="mt-12 bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-4">
          💻 Código de Separadores Elegantes
        </h3>

        <div class="space-y-4">
          <div>
            <h4 class="text-sm font-medium text-gray-300 mb-2">
              HTML con Separadores:
            </h4>
            <pre
              class="bg-gray-900 p-4 rounded text-sm text-gray-200 overflow-x-auto"
            ><code>&lt;div
  data-micro-dash
  data-title="Publicación externa"
  data-icon="globe-outline"
  data-ui-style="clean"
  data-links='[
    {
      "id": "view-external-publication",
      "title": "Plataformas disponibles",
      "url": "./importExportFromTo.php",
      "icon": "globe-outline"
    },
    {
      "id": "configure-links",
      "title": "Configurar enlaces",
      "url": "./configure-links.php",
      "icon": "link-outline"
    },
    {
      "id": "publication-settings",
      "title": "Ajustes de publicación",
      "url": "./publication-settings.php",
      "icon": "settings-outline"
    }
  ]'
&gt;
  &lt;!-- Loading placeholder --&gt;
&lt;/div&gt;</code></pre>
          </div>

          <div>
            <h4 class="text-sm font-medium text-gray-300 mb-2">
              CSS de Separadores:
            </h4>
            <pre
              class="bg-gray-900 p-4 rounded text-sm text-gray-200 overflow-x-auto"
            ><code>/* Estilos para separadores elegantes */
.border-l-3 {
  border-left-width: 3px;
}

/* Transiciones suaves para bordes */
.group {
  transition: all 0.2s ease, border-left-color 0.2s ease;
}

/* Efectos de hover mejorados */
.group:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Separadores entre elementos */
.border-t.border-gray-100 {
  border-top-color: #f3f4f6;
  border-top-width: 1px;
}

/* Separadores entre secciones */
.border-t-2.border-t-gray-100 {
  border-top-color: #f9fafb;
  border-top-width: 2px;
}</code></pre>
          </div>
        </div>
      </div>

      <!-- Beneficios de los Separadores -->
      <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">
          ✨ Beneficios de los Separadores Elegantes
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-700 mb-2">🎯 Mejoras Visuales</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Claridad visual</strong> - Cada opción está
                claramente separada
              </li>
              <li>
                ✅ <strong>Jerarquía visual</strong> - Fácil distinguir entre
                elementos
              </li>
              <li>
                ✅ <strong>Profesionalismo</strong> - Apariencia más pulida y
                elegante
              </li>
              <li>
                ✅ <strong>Consistencia</strong> - Separadores uniformes en todo
                el menú
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-2">🚀 Mejoras de UX</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Navegación clara</strong> - Fácil identificar dónde
                empieza/termina cada opción
              </li>
              <li>
                ✅ <strong>Feedback visual</strong> - Bordes izquierdos indican
                la opción activa
              </li>
              <li>
                ✅ <strong>Reducción de errores</strong> - Menos confusión al
                hacer clic
              </li>
              <li>
                ✅ <strong>Accesibilidad</strong> - Mejor contraste y separación
                visual
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Simulación de funcionalidades
      console.log("🎨 MicroDash - Separadores elegantes cargado");

      // Simular hover effects
      document.querySelectorAll(".group").forEach((group) => {
        group.addEventListener("mouseenter", () => {
          console.log("🖱️ Hover en:", group.textContent.trim());
        });
      });
    </script>
  </body>
</html>
