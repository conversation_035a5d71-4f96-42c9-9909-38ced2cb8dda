<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MicroDash - Ejemplo de Uso</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      type="module"
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"
    ></script>
    <style>
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
        🚀 MicroDash - Componente de Micro Dashboards
      </h1>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Ejemplo 1: Dashboard de Citas (como en la imagen) -->
        <div class="space-y-6">
          <h2 class="text-xl font-semibold text-gray-700">
            📅 Dashboard de Citas
          </h2>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-sm font-medium text-gray-700">Citas</h3>
            </div>

            <div class="space-y-3">
              <a
                href="#"
                class="block text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
              >
                Registrar una nueva cita
              </a>
              <a
                href="#"
                class="block text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
              >
                Ver citas solicitadas que no he confirmado
              </a>
              <a
                href="#"
                class="block text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
              >
                Ver mis citas hechas de hoy en adelante
              </a>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Enlaces simples sin secciones</li>
              <li>Estilo limpio y minimalista</li>
              <li>Hover effects en los enlaces</li>
              <li>Diseño responsive</li>
            </ul>
          </div>
        </div>

        <!-- Ejemplo 2: MicroDash con secciones -->
        <div class="space-y-6">
          <h2 class="text-xl font-semibold text-gray-700">
            🔧 MicroDash con Secciones
          </h2>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <div>
                <h3 class="text-sm font-medium text-gray-700">
                  Gestión de Inmuebles
                </h3>
                <p class="text-xs text-gray-500 mt-1">
                  Acceso rápido a funciones principales
                </p>
              </div>
              <ion-icon
                name="home-outline"
                class="text-lg text-blue-600"
              ></ion-icon>
            </div>

            <!-- Sección: Propiedades -->
            <div class="border border-gray-200 rounded-lg overflow-hidden mb-4">
              <div
                class="px-3 py-2 bg-gray-50 border-b border-gray-200 cursor-pointer hover:bg-gray-100"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <ion-icon
                      name="business-outline"
                      class="text-gray-500 text-sm"
                    ></ion-icon>
                    <h4 class="text-xs font-medium text-gray-700">
                      Propiedades
                    </h4>
                  </div>
                  <ion-icon
                    name="chevron-up"
                    class="text-gray-400 text-xs"
                  ></ion-icon>
                </div>
              </div>

              <div class="p-3 space-y-2">
                <div
                  class="group flex items-center space-x-3 p-2 rounded-md hover:bg-blue-50 cursor-pointer transition-all duration-200"
                >
                  <ion-icon
                    name="add-circle-outline"
                    class="text-lg text-blue-500 group-hover:text-blue-600"
                  ></ion-icon>
                  <div class="flex-1">
                    <span
                      class="text-sm font-medium text-blue-600 group-hover:text-blue-700"
                    >
                      Agregar nueva propiedad
                    </span>
                  </div>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200"
                  >
                    Nuevo
                  </span>
                </div>

                <div
                  class="group flex items-center space-x-3 p-2 rounded-md hover:bg-blue-50 cursor-pointer transition-all duration-200"
                >
                  <ion-icon
                    name="list-outline"
                    class="text-lg text-blue-500 group-hover:text-blue-600"
                  ></ion-icon>
                  <div class="flex-1">
                    <span
                      class="text-sm font-medium text-blue-600 group-hover:text-blue-700"
                    >
                      Ver todas las propiedades
                    </span>
                    <p class="text-xs text-gray-500 mt-1">
                      Gestiona tu inventario completo
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sección: Reportes -->
            <div class="border border-gray-200 rounded-lg overflow-hidden">
              <div
                class="px-3 py-2 bg-gray-50 border-b border-gray-200 cursor-pointer hover:bg-gray-100"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <ion-icon
                      name="bar-chart-outline"
                      class="text-gray-500 text-sm"
                    ></ion-icon>
                    <h4 class="text-xs font-medium text-gray-700">Reportes</h4>
                  </div>
                  <ion-icon
                    name="chevron-down"
                    class="text-gray-400 text-xs"
                  ></ion-icon>
                </div>
              </div>

              <div class="p-3 space-y-2">
                <div
                  class="group flex items-center space-x-3 p-2 rounded-md hover:bg-blue-50 cursor-pointer transition-all duration-200"
                >
                  <ion-icon
                    name="trending-up-outline"
                    class="text-lg text-blue-500 group-hover:text-blue-600"
                  ></ion-icon>
                  <div class="flex-1">
                    <span
                      class="text-sm font-medium text-blue-600 group-hover:text-blue-700"
                    >
                      Ventas del mes
                    </span>
                  </div>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200"
                  >
                    Hot
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Secciones colapsables</li>
              <li>Íconos y badges</li>
              <li>Descripciones en enlaces</li>
              <li>Hover effects avanzados</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Código de implementación -->
      <div class="mt-12 bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-4">
          💻 Código de Implementación
        </h3>

        <div class="space-y-4">
          <div>
            <h4 class="text-sm font-medium text-gray-300 mb-2">HTML Básico:</h4>
            <pre
              class="bg-gray-900 p-4 rounded text-sm text-gray-200 overflow-x-auto"
            ><code>&lt;div
  data-micro-dash
  data-title="Gestión de Inmuebles"
  data-icon="home-outline"
  data-description="Acceso rápido a funciones principales"
  data-ui-style="clean"
  data-show-icons="true"
  data-show-descriptions="true"
  data-show-badges="true"
  data-links='[
    {
      "id": "add-property",
      "title": "Agregar nueva propiedad",
      "url": "/propiedades/nueva",
      "icon": "add-circle-outline",
      "badge": "Nuevo",
      "badgeColor": "green"
    },
    {
      "id": "list-properties",
      "title": "Ver todas las propiedades",
      "url": "/propiedades",
      "icon": "list-outline",
      "description": "Gestiona tu inventario completo"
    }
  ]'
&gt;
  &lt;div class="flex justify-center items-center p-8"&gt;
    &lt;div class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"&gt;&lt;/div&gt;
    &lt;span class="ml-2 text-sm text-gray-600"&gt;Cargando dashboard...&lt;/span&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
          </div>

          <div>
            <h4 class="text-sm font-medium text-gray-300 mb-2">
              Con Secciones:
            </h4>
            <pre
              class="bg-gray-900 p-4 rounded text-sm text-gray-200 overflow-x-auto"
            ><code>&lt;div
  data-micro-dash
  data-title="Dashboard Completo"
  data-ui-style="blue"
  data-sections='[
    {
      "id": "properties",
      "title": "Propiedades",
      "icon": "business-outline",
      "collapsible": true,
      "links": [
        {
          "id": "add-property",
          "title": "Agregar nueva propiedad",
          "url": "/propiedades/nueva",
          "icon": "add-circle-outline",
          "badge": "Nuevo",
          "badgeColor": "green"
        }
      ]
    }
  ]'
&gt;
  &lt;!-- Loading placeholder --&gt;
&lt;/div&gt;</code></pre>
          </div>
        </div>
      </div>

      <!-- Características del componente -->
      <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">
          ✨ Características del Componente
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-700 mb-2">
              🎨 Estilos Disponibles
            </h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                <code class="bg-gray-100 px-1 rounded">clean</code> -
                Transparente, sin bordes
              </li>
              <li>
                <code class="bg-gray-100 px-1 rounded">blue</code> - Gradiente
                azul
              </li>
              <li>
                <code class="bg-gray-100 px-1 rounded">mulbin</code> - Gradiente
                marca
              </li>
              <li>
                <code class="bg-gray-100 px-1 rounded">red</code> - Gradiente
                rojo
              </li>
              <li>
                <code class="bg-gray-100 px-1 rounded">green</code> - Gradiente
                verde
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-2">🔧 Funcionalidades</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>✅ Enlaces simples o con secciones</li>
              <li>✅ Secciones colapsables</li>
              <li>✅ Íconos personalizables</li>
              <li>✅ Badges con colores</li>
              <li>✅ Descripciones en enlaces</li>
              <li>✅ Responsive design</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Simulación de funcionalidades
      console.log("🚀 MicroDash - Ejemplo de uso cargado");

      // Simular clicks en enlaces
      document.querySelectorAll("a").forEach((link) => {
        link.addEventListener("click", (e) => {
          e.preventDefault();
          console.log("🔗 Enlace clickeado:", link.textContent);
        });
      });

      // Simular toggle de secciones
      document
        .querySelectorAll(".border.border-gray-200")
        .forEach((section) => {
          const header = section.querySelector(".px-3.py-2");
          const content = section.querySelector(".p-3");
          const chevron = header.querySelector('ion-icon[name*="chevron"]');

          if (header && content && chevron) {
            header.addEventListener("click", () => {
              const isCollapsed = content.style.display === "none";
              content.style.display = isCollapsed ? "block" : "none";
              chevron.name = isCollapsed ? "chevron-up" : "chevron-down";
            });
          }
        });
    </script>
  </body>
</html>
