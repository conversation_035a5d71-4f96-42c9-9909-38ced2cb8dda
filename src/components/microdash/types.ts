// Tipos para el componente MicroDash

export interface MicroDashLink {
  id: string; // Identificador único
  title: string; // Texto del enlace
  url?: string; // URL de destino
  callback?: () => void; // Función callback
  icon?: string; // <PERSON><PERSON>o (Ionic Icons)
  description?: string; // Descripción opcional
  badge?: string; // Texto del badge
  badgeColor?: "blue" | "green" | "red" | "yellow" | "purple" | "gray";
  external?: boolean; // Enlace externo
  disabled?: boolean; // Enlace deshabilitado
  appearance?:
    | "clean"
    | "soft-blue"
    | "blue"
    | "mulbin"
    | "red"
    | "green"
    | "default"; // Estilo visual del enlace (usa los mismos estilos que uiStyle)
}

export interface MicroDashSection {
  id: string;
  title: string;
  links: MicroDashLink[];
  collapsible?: boolean;
  collapsed?: boolean;
  icon?: string;
  description?: string;
}

export interface MicroDashProps {
  uiStyle?: "clean" | "blue" | "mulbin" | "red" | "green" | "default";
  title?: string;
  icon?: string;
  description?: string;
  sections?: MicroDashSection[];
  links?: MicroDashLink[];
  showIcons?: boolean;
  showDescriptions?: boolean;
  showBadges?: boolean;
  compact?: boolean;
  maxLinksPerSection?: number;
  allowCollapse?: boolean;
  defaultCollapsed?: boolean;
  instanceId?: string; // ID único para identificar la instancia
  defaultAppearance?:
    | "clean"
    | "soft-blue"
    | "blue"
    | "mulbin"
    | "red"
    | "green"
    | "default"; // Apariencia por defecto para enlaces (usa los mismos estilos que uiStyle)
}

export interface MicroDashEmits {
  linkClicked: [link: MicroDashLink];
  sectionToggled: [section: MicroDashSection, collapsed: boolean];
  linkHovered: [link: MicroDashLink];
}

// Estados del componente
export interface ComponentState {
  collapsedSections: Set<string>;
  hoveredLink: string | null;
  activeSection: string | null;
}

// Configuración por defecto
export const DEFAULT_CONFIG = {
  uiStyle: "clean" as const,
  showIcons: true,
  showDescriptions: true,
  showBadges: true,
  compact: false,
  maxLinksPerSection: 10,
  allowCollapse: true,
  defaultCollapsed: false,
  defaultAppearance: "clean" as const,
};

// Utilidades para validación
export const isValidMicroDashLink = (link: any): link is MicroDashLink => {
  return (
    typeof link === "object" &&
    link !== null &&
    typeof link.id === "string" &&
    typeof link.title === "string" &&
    (typeof link.url === "string" || typeof link.callback === "function")
  );
};

export const isValidMicroDashSection = (
  section: any
): section is MicroDashSection => {
  return (
    typeof section === "object" &&
    section !== null &&
    typeof section.id === "string" &&
    typeof section.title === "string" &&
    Array.isArray(section.links) &&
    section.links.every(isValidMicroDashLink)
  );
};

// Constantes
export const BADGE_COLORS = [
  "blue",
  "green",
  "red",
  "yellow",
  "purple",
  "gray",
] as const;
export const UI_STYLES = [
  "clean",
  "blue",
  "mulbin",
  "red",
  "green",
  "default",
] as const;

// Colores de badges predefinidos
export const BADGE_COLOR_CLASSES = {
  blue: "bg-blue-100 text-blue-800 border-blue-200",
  green: "bg-green-100 text-green-800 border-green-200",
  red: "bg-red-100 text-red-800 border-red-200",
  yellow: "bg-yellow-100 text-yellow-800 border-yellow-200",
  purple: "bg-purple-100 text-purple-800 border-purple-200",
  gray: "bg-gray-100 text-gray-800 border-gray-200",
};
