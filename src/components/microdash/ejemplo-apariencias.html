<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MicroDash - Apariencias Classic vs Button</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      type="module"
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"
    ></script>
    <style>
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      /* Estilos para separadores elegantes */
      .border-l-3 {
        border-left-width: 3px;
      }

      /* Transiciones suaves para bordes */
      .group {
        transition: all 0.2s ease, border-left-color 0.2s ease;
      }

      /* Efectos de hover mejorados */
      .group:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* Separadores entre elementos */
      .border-t.border-gray-100 {
        border-top-color: #f3f4f6;
        border-top-width: 1px;
      }

      /* Separadores entre secciones */
      .border-t-2.border-t-gray-100 {
        border-top-color: #f9fafb;
        border-top-width: 2px;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
        🎨 MicroDash - Apariencias Classic vs Button
      </h1>

      <div class="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 class="text-lg font-semibold text-blue-800 mb-3">
          ✨ Nuevas Apariencias Implementadas
        </h2>
        <ul class="text-blue-700 space-y-1">
          <li>
            ✅ <strong>Classic (default)</strong> - Estilo clásico con
            separadores elegantes
          </li>
          <li>
            ✅ <strong>Button</strong> - Estilo de botón prominente y llamativo
          </li>
          <li>
            ✅ <strong>Configuración por enlace</strong> - Cada enlace puede
            tener su propia apariencia
          </li>
          <li>
            ✅ <strong>Configuración global</strong> - Apariencia por defecto
            para toda la instancia
          </li>
          <li>
            ✅ <strong>Transiciones suaves</strong> - Animaciones fluidas entre
            estados
          </li>
        </ul>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Apariencia Classic -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            📋 Apariencia Classic (Default)
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Publicación externa
              </h4>
              <ion-icon
                name="globe-outline"
                class="text-lg text-gray-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia classic -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-blue-400"
              >
                <ion-icon
                  name="globe-outline"
                  class="text-lg text-blue-500 group-hover:text-blue-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-blue-600 group-hover:text-blue-700"
                  >
                    Plataformas disponibles
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-400"
              >
                <ion-icon
                  name="link-outline"
                  class="text-lg text-blue-500 group-hover:text-blue-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-blue-600 group-hover:text-blue-700"
                  >
                    Configurar enlaces
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-400"
              >
                <ion-icon
                  name="settings-outline"
                  class="text-lg text-blue-500 group-hover:text-blue-600"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-blue-600 group-hover:text-blue-700"
                  >
                    Ajustes de publicación
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo transparente con hover azul claro</li>
              <li>Bordes izquierdos de color en hover</li>
              <li>Separadores horizontales sutiles</li>
              <li>Texto azul con hover más oscuro</li>
              <li>Íconos azules con hover más oscuro</li>
            </ul>
          </div>
        </div>

        <!-- Apariencia Button -->
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-gray-700">
            🔘 Apariencia Button
          </h3>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-sm font-medium text-gray-700">
                Acciones principales
              </h4>
              <ion-icon
                name="flash-outline"
                class="text-lg text-blue-600"
              ></ion-icon>
            </div>

            <!-- Simulación de enlaces con apariencia button -->
            <div class="space-y-0">
              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon
                  name="add-circle-outline"
                  class="text-lg text-white group-hover:text-blue-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-blue-100"
                  >
                    Crear nuevo proyecto
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon
                  name="rocket-outline"
                  class="text-lg text-white group-hover:text-blue-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-blue-100"
                  >
                    Lanzar proyecto
                  </span>
                </div>
              </div>

              <div
                class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-t border-gray-100 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon
                  name="analytics-outline"
                  class="text-lg text-white group-hover:text-blue-100"
                ></ion-icon>
                <div class="flex-1">
                  <span
                    class="text-base font-medium text-white group-hover:text-blue-100"
                  >
                    Ver estadísticas
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <p><strong>Características:</strong></p>
            <ul class="list-disc list-inside mt-2 space-y-1">
              <li>Fondo azul sólido con hover más oscuro</li>
              <li>Texto e íconos blancos</li>
              <li>Efecto de escala en hover (scale-105)</li>
              <li>Sombras más pronunciadas</li>
              <li>Bordes izquierdos azul claro en hover</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Ejemplo de Código -->
      <div class="mt-12 bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-4">
          💻 Código de Implementación
        </h3>

        <div class="space-y-4">
          <div>
            <h4 class="text-sm font-medium text-gray-300 mb-2">
              HTML con Apariencias:
            </h4>
            <pre
              class="bg-gray-900 p-4 rounded text-sm text-gray-200 overflow-x-auto"
            ><code>&lt;div
  data-micro-dash
  data-title="Mi Sitio Web"
  data-icon="globe-outline"
  data-ui-style="clean"
  data-default-appearance="classic"
  data-links='[
    {
      "id": "view-external-publication",
      "title": "Plataformas disponibles",
      "url": "./importExportFromTo.php",
      "icon": "globe-outline",
      "appearance": "classic"
    },
    {
      "id": "configure-links",
      "title": "Configurar enlaces",
      "url": "./configure-links.php",
      "icon": "link-outline",
      "appearance": "button"
    },
    {
      "id": "publication-settings",
      "title": "Ajustes de publicación",
      "url": "./publication-settings.php",
      "icon": "settings-outline"
    }
  ]'
&gt;
  &lt;!-- Loading placeholder --&gt;
&lt;/div&gt;</code></pre>
          </div>

          <div>
            <h4 class="text-sm font-medium text-gray-300 mb-2">
              Configuración Global:
            </h4>
            <pre
              class="bg-gray-900 p-4 rounded text-sm text-gray-200 overflow-x-auto"
            ><code>&lt;div
  data-micro-dash
  data-title="Dashboard de Acciones"
  data-icon="flash-outline"
  data-ui-style="blue"
  data-default-appearance="button"
  data-links='[...]'
&gt;
  &lt;!-- Todos los enlaces serán botones por defecto --&gt;
&lt;/div&gt;</code></pre>
          </div>
        </div>
      </div>

      <!-- Casos de Uso -->
      <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">
          🎯 Casos de Uso Recomendados
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-700 mb-2">
              📋 Apariencia Classic
            </h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Menús de navegación</strong> - Opciones secundarias
              </li>
              <li>
                ✅ <strong>Listas de enlaces</strong> - Referencias y recursos
              </li>
              <li>✅ <strong>Sidebars</strong> - Navegación lateral</li>
              <li>
                ✅ <strong>Dashboards informativos</strong> - Enlaces de
                consulta
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-2">🔘 Apariencia Button</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>
                ✅ <strong>Acciones principales</strong> - Botones de llamada a
                la acción
              </li>
              <li>
                ✅ <strong>Formularios</strong> - Envío y acciones críticas
              </li>
              <li>✅ <strong>Wizards</strong> - Pasos de procesos</li>
              <li>
                ✅ <strong>Paneles de control</strong> - Funciones importantes
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Comparación Visual -->
      <div
        class="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6"
      >
        <h3 class="text-lg font-semibold text-gray-700 mb-4">
          🔍 Comparación Visual de Apariencias
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h4 class="font-medium text-gray-700 mb-3">Classic</h4>
            <div class="space-y-2">
              <div
                class="flex items-center space-x-3 p-2 rounded hover:bg-blue-50 cursor-pointer border-l-3 border-l-transparent hover:border-l-blue-400"
              >
                <ion-icon name="link-outline" class="text-blue-500"></ion-icon>
                <span class="text-blue-600">Enlace clásico</span>
              </div>
              <div
                class="flex items-center space-x-3 p-2 rounded hover:bg-blue-50 cursor-pointer border-l-3 border-l-transparent hover:border-l-blue-400"
              >
                <ion-icon
                  name="settings-outline"
                  class="text-blue-500"
                ></ion-icon>
                <span class="text-blue-600">Configuración</span>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h4 class="font-medium text-gray-700 mb-3">Button</h4>
            <div class="space-y-2">
              <div
                class="flex items-center space-x-3 p-2 rounded bg-blue-600 text-white cursor-pointer hover:bg-blue-700 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon name="rocket-outline" class="text-white"></ion-icon>
                <span class="text-white">Acción principal</span>
              </div>
              <div
                class="flex items-center space-x-3 p-2 rounded bg-blue-600 text-white cursor-pointer hover:bg-blue-700 border-l-3 border-l-transparent hover:border-l-blue-300"
              >
                <ion-icon
                  name="checkmark-outline"
                  class="text-white"
                ></ion-icon>
                <span class="text-white">Confirmar</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Simulación de funcionalidades
      console.log("🎨 MicroDash - Apariencias classic vs button cargado");

      // Simular hover effects
      document.querySelectorAll(".group").forEach((group) => {
        group.addEventListener("mouseenter", () => {
          console.log("🖱️ Hover en:", group.textContent.trim());
        });
      });
    </script>
  </body>
</html>
