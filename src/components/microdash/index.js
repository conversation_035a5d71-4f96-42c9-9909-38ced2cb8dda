// Este archivo inicializa el componente MicroDash y lo monta en el DOM
import { createApp } from "vue";
import MicroDash from "./MicroDash.vue";

console.log("🚀 Inicializando MicroDash...");

// Función para montar el componente en un elemento específico
function mountMicroDashComponent(element) {
  if (!element) {
    console.error("Elemento no válido para MicroDash");
    return null;
  }

  // Obtener props desde data-attributes
  const uiStyle = element.getAttribute("data-ui-style") || "clean";
  const title = element.getAttribute("data-title") || "";
  const icon = element.getAttribute("data-icon") || "";
  const description = element.getAttribute("data-description") || "";
  const showIcons = element.getAttribute("data-show-icons") !== "false";
  const showDescriptions =
    element.getAttribute("data-show-descriptions") !== "false";
  const showBadges = element.getAttribute("data-show-badges") !== "false";
  const compact = element.getAttribute("data-compact") === "true";
  const maxLinksPerSection = parseInt(
    element.getAttribute("data-max-links-per-section") || "10"
  );
  const allowCollapse = element.getAttribute("data-allow-collapse") !== "false";
  const defaultCollapsed =
    element.getAttribute("data-default-collapsed") === "true";
  const defaultAppearance =
    element.getAttribute("data-default-appearance") || "clean";

  // Obtener configuración de enlaces desde data-attributes o JSON
  let links = [];
  let sections = [];

  try {
    const linksData = element.getAttribute("data-links");
    if (linksData) {
      links = JSON.parse(linksData);
    }

    const sectionsData = element.getAttribute("data-sections");
    if (sectionsData) {
      sections = JSON.parse(sectionsData);
    }
  } catch (error) {
    console.warn("Error parsing links/sections data:", error);
  }

  // Generar ID único para esta instancia
  const instanceId = `micro-dash-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;

  console.log(`🏗️ Inicializando MicroDash ${instanceId} con props:`, {
    uiStyle,
    title,
    icon,
    description,
    showIcons,
    showDescriptions,
    showBadges,
    compact,
    maxLinksPerSection,
    allowCollapse,
    defaultCollapsed,
    defaultAppearance,
    linksCount: links.length,
    sectionsCount: sections.length,
  });

  // Crear un div para contener el componente
  const appContainer = document.createElement("div");
  appContainer.className = "micro-dash-app";
  appContainer.setAttribute("data-instance-id", instanceId);

  // Limpiar el contenido del target element y agregar el container
  element.innerHTML = "";
  element.appendChild(appContainer);

  // Montar el componente directamente
  const app = createApp(MicroDash, {
    uiStyle,
    title,
    icon,
    description,
    links,
    sections,
    showIcons,
    showDescriptions,
    showBadges,
    compact,
    maxLinksPerSection,
    allowCollapse,
    defaultCollapsed,
    instanceId, // Pasar el ID de instancia al componente
    defaultAppearance,
  });

  app.mount(appContainer);

  console.log(`✅ Componente MicroDash ${instanceId} montado correctamente`);

  // Almacenar referencia a la app en el elemento para posible cleanup
  element._microDashApp = app;
  element._microDashInstanceId = instanceId;

  return { app, instanceId };
}

// Función para montar todos los componentes MicroDash en el DOM
function mountAllMicroDashComponents() {
  const targetElements = document.querySelectorAll("[data-micro-dash]");

  if (targetElements.length === 0) {
    console.log("💡 No se encontraron elementos [data-micro-dash] en el DOM");
    return [];
  }

  console.log(
    `🔍 Encontrados ${targetElements.length} elementos MicroDash para montar`
  );

  const mountedInstances = [];

  targetElements.forEach((element, index) => {
    try {
      const instance = mountMicroDashComponent(element);
      if (instance) {
        mountedInstances.push(instance);
        console.log(
          `📌 Instancia ${index + 1}/${targetElements.length} montada: ${
            instance.instanceId
          }`
        );
      }
    } catch (error) {
      console.error(`❌ Error montando instancia ${index + 1}:`, error);
    }
  });

  console.log(
    `🎉 Total de instancias MicroDash montadas: ${mountedInstances.length}`
  );
  return mountedInstances;
}

// Función para limpiar una instancia específica
function unmountMicroDashInstance(element) {
  if (element && element._microDashApp) {
    try {
      element._microDashApp.unmount();
      element._microDashApp = null;
      element._microDashInstanceId = null;
      console.log(
        `🧹 Instancia MicroDash desmontada: ${element._microDashInstanceId}`
      );
    } catch (error) {
      console.error("Error desmontando instancia MicroDash:", error);
    }
  }
}

// Función para limpiar todas las instancias
function unmountAllMicroDashInstances() {
  const targetElements = document.querySelectorAll("[data-micro-dash]");
  targetElements.forEach(unmountMicroDashInstance);
  console.log("🧹 Todas las instancias MicroDash han sido desmontadas");
}

// Función para remontar todas las instancias (útil para SPA)
function remountAllMicroDashComponents() {
  console.log("🔄 Remontando todos los componentes MicroDash...");
  unmountAllMicroDashInstances();
  return mountAllMicroDashComponents();
}

// Inicializar cuando el DOM esté cargado
document.addEventListener("DOMContentLoaded", () => {
  // Cargar Ionicons si no están ya cargados
  if (!document.querySelector('script[src*="ionicons"]')) {
    const moduleScript = document.createElement("script");
    moduleScript.type = "module";
    moduleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js";
    document.head.appendChild(moduleScript);

    const noModuleScript = document.createElement("script");
    noModuleScript.noModule = true;
    noModuleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js";
    document.head.appendChild(noModuleScript);
  }

  // Montar todos los componentes MicroDash encontrados
  mountAllMicroDashComponents();
});

// Observador de mutaciones para detectar nuevos elementos MicroDash
let observer = null;

function setupMutationObserver() {
  if (typeof MutationObserver !== "undefined") {
    observer = new MutationObserver((mutations) => {
      let shouldRemount = false;

      mutations.forEach((mutation) => {
        // Verificar si se agregaron nuevos elementos MicroDash
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.hasAttribute && node.hasAttribute("data-micro-dash")) {
              shouldRemount = true;
            }
            if (node.querySelectorAll) {
              const microDashElements =
                node.querySelectorAll("[data-micro-dash]");
              if (microDashElements.length > 0) {
                shouldRemount = true;
              }
            }
          }
        });
      });

      // Remontar si se detectaron cambios
      if (shouldRemount) {
        console.log("🔄 Cambios detectados en el DOM, remontando MicroDash...");
        setTimeout(() => {
          mountAllMicroDashComponents();
        }, 100); // Pequeño delay para asegurar que el DOM esté estable
      }
    });

    // Observar cambios en el body
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    console.log("👁️ Observador de mutaciones configurado para MicroDash");
  }
}

// Configurar observador cuando el DOM esté listo
document.addEventListener("DOMContentLoaded", () => {
  setupMutationObserver();
});

// Exponer funciones para uso manual
window.MicroDashManager = {
  mountAll: mountAllMicroDashComponents,
  unmountAll: unmountAllMicroDashInstances,
  remountAll: remountAllMicroDashComponents,
  mountElement: mountMicroDashComponent,
  unmountElement: unmountMicroDashInstance,
};

// Exportar el componente para uso manual si es necesario
export default MicroDash;
