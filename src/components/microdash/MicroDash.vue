<template>
  <div class="mb-6">
    <div :class="containerClasses">
      <!-- Header con tí<PERSON>lo, <PERSON>cono y descripción -->
      <div v-if="title || icon" class="flex justify-between items-start">
        <div class="flex-1">
          <div class="flex justify-between items-center space-x-2">
            <h2 v-if="title" class="font-medium text-gray-700">
              {{ title }}
            </h2>
            <ion-icon v-if="icon" :name="icon" :class="iconClasses"></ion-icon>
          </div>
          <p v-if="description" class="mb-4 text-sm text-gray-500">
            {{ description }}
          </p>
        </div>
      </div>

      <!-- Contenido principal -->
      <div class="space-y-4">
        <!-- Secciones -->
        <div v-if="sections && sections.length > 0" class="space-y-3">
          <div
            v-for="(section, sectionIndex) in sections"
            :key="section.id"
            :class="[
              'overflow-hidden rounded-lg border border-gray-200',
              // Agregar separador visual entre secciones
              sectionIndex > 0 ? 'mt-4 pt-4 border-t-2 border-t-gray-100' : '',
            ]"
          >
            <!-- Header de sección -->
            <div
              v-if="section.title"
              :class="[
                'px-3 py-2 bg-gray-50 border-b border-gray-200',
                section.collapsible && allowCollapse
                  ? 'cursor-pointer hover:bg-gray-100'
                  : '',
              ]"
              @click="toggleSection(section)"
            >
              <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                  <ion-icon
                    v-if="section.icon"
                    :name="section.icon"
                    class="text-sm text-gray-500"
                  ></ion-icon>
                  <h4 class="text-xs font-medium text-gray-700">
                    {{ section.title }}
                  </h4>
                </div>
                <div
                  v-if="section.collapsible && allowCollapse"
                  class="flex items-center space-x-1"
                >
                  <ion-icon
                    :name="
                      isSectionCollapsed(section.id)
                        ? 'chevron-down'
                        : 'chevron-up'
                    "
                    class="text-xs text-gray-400 transition-transform duration-200"
                  ></ion-icon>
                </div>
              </div>
              <p v-if="section.description" class="mt-1 text-sm text-gray-500">
                {{ section.description }}
              </p>
            </div>

            <!-- Enlaces de la sección -->
            <div v-if="!isSectionCollapsed(section.id)" class="p-3 space-y-0">
              <div
                v-for="(link, linkIndex) in section.links.slice(
                  0,
                  maxLinksPerSection
                )"
                :key="link.id"
                :class="[
                  // Clases base comunes
                  'group flex items-center space-x-3 p-3 rounded-md transition-all duration-200',
                  // Apariencia específica
                  getLinkAppearanceClasses(link),
                  // Separadores elegantes para enlaces dentro de secciones
                  linkIndex > 0 ? 'border-t border-gray-100' : '',
                ]"
                @click="handleLinkClick(link)"
                @mouseenter="handleLinkHover(link)"
                @mouseleave="handleLinkHover(null)"
              >
                <!-- Ícono del enlace -->
                <div v-if="showIcons && link.icon" class="flex-shrink-0">
                  <ion-icon
                    :name="link.icon"
                    :class="[
                      'text-lg transition-colors duration-200',
                      getLinkIconClasses(link),
                    ]"
                  ></ion-icon>
                </div>

                <!-- Contenido del enlace -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center space-x-2">
                    <span
                      :class="[
                        'text-sm font-medium transition-colors duration-200',
                        getLinkTitleClasses(link),
                      ]"
                    >
                      {{ link.title }}
                    </span>
                    <span v-if="link.external" class="text-gray-400">
                      <ion-icon name="open-outline" class="text-xs"></ion-icon>
                    </span>
                  </div>
                  <p
                    v-if="showDescriptions && link.description"
                    class="mt-1 text-xs text-gray-500 line-clamp-2"
                  >
                    {{ link.description }}
                  </p>
                </div>

                <!-- Badge -->
                <div v-if="showBadges && link.badge" class="flex-shrink-0">
                  <span
                    :class="[
                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border',
                      getBadgeColorClasses(link.badgeColor),
                    ]"
                  >
                    {{ link.badge }}
                  </span>
                </div>
              </div>

              <!-- Enlace "Ver más" si hay más enlaces -->
              <div
                v-if="section.links.length > maxLinksPerSection"
                class="pt-3 mt-3 border-t border-gray-200"
              >
                <button
                  class="text-xs font-medium text-blue-600 transition-colors duration-200 hover:text-blue-700"
                  @click="showMoreLinks(section)"
                >
                  Ver {{ section.links.length - maxLinksPerSection }} enlaces
                  más →
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Enlaces simples (sin secciones) -->
        <div v-else-if="links && links.length > 0" class="space-y-0">
          <div
            v-for="(link, index) in links.slice(0, maxLinksPerSection)"
            :key="link.id"
            :class="[
              // Clases base comunes
              'group flex items-center space-x-3 p-3 rounded-md transition-all duration-200',
              // Apariencia específica
              getLinkAppearanceClasses(link),
              // Separadores elegantes
              index > 0 ? 'border-t border-gray-100' : '',
            ]"
            @click="handleLinkClick(link)"
            @mouseenter="handleLinkHover(link)"
            @mouseleave="handleLinkHover(null)"
          >
            <!-- Ícono del enlace -->
            <div v-if="showIcons && link.icon" class="flex-shrink-0">
              <ion-icon
                :name="link.icon"
                :class="[
                  'text-lg transition-colors duration-200',
                  getLinkIconClasses(link),
                ]"
              ></ion-icon>
            </div>

            <!-- Contenido del enlace -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2">
                <span
                  :class="[
                    'text-base font-medium transition-colors duration-200',
                    getLinkTitleClasses(link),
                  ]"
                >
                  {{ link.title }}
                </span>
                <span v-if="link.external" class="text-gray-400">
                  <ion-icon name="open-outline" class="text-xs"></ion-icon>
                </span>
              </div>
              <p
                v-if="showDescriptions && link.description"
                :class="[
                  'mt-1 text-xs text-gray-500 line-clamp-2',
                  getLinkDescriptionClasses(link),
                ]"
              >
                {{ link.description }}
              </p>
            </div>

            <!-- Badge -->
            <div v-if="showBadges && link.badge" class="flex-shrink-0">
              <span
                :class="[
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border',
                  getBadgeColorClasses(link.badgeColor),
                ]"
              >
                {{ link.badge }}
              </span>
            </div>
          </div>

          <!-- Enlace "Ver más" si hay más enlaces -->
          <div
            v-if="links.length > maxLinksPerSection"
            class="pt-3 mt-3 border-t border-gray-200"
          >
            <button
              class="text-xs font-medium text-blue-600 transition-colors duration-200 hover:text-blue-700"
              @click="showMoreLinks({ links })"
            >
              Ver {{ links.length - maxLinksPerSection }} enlaces más →
            </button>
          </div>
        </div>

        <!-- Estado vacío -->
        <div v-else class="py-8 text-center">
          <ion-icon
            name="folder-open-outline"
            class="mx-auto mb-2 text-3xl text-gray-400"
          ></ion-icon>
          <p class="text-sm text-gray-500">No hay enlaces configurados</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import type {
  MicroDashProps,
  MicroDashEmits,
  MicroDashLink,
  MicroDashSection,
  ComponentState,
} from "./types";
import { BADGE_COLOR_CLASSES } from "./types";

// Props con valores por defecto
const props = withDefaults(defineProps<MicroDashProps>(), {
  uiStyle: "clean",
  showIcons: true,
  showDescriptions: true,
  showBadges: true,
  compact: false,
  maxLinksPerSection: 10,
  allowCollapse: true,
  defaultCollapsed: false,
  instanceId: "", // ID único de la instancia
  defaultAppearance: "clean", // Apariencia por defecto para enlaces (usa los mismos estilos que uiStyle)
});

// Emits
const emit = defineEmits<MicroDashEmits>();

// Estado reactivo
const state = ref<ComponentState>({
  collapsedSections: new Set(),
  hoveredLink: null,
  activeSection: null,
});

// Computed properties
const containerClasses = computed(() => {
  const baseClasses = "p-4 rounded-lg";

  switch (props.uiStyle) {
    case "clean":
      return `${baseClasses} bg-transparent border-none shadow-none`;
    case "mulbin":
      return `${baseClasses} bg-gradient-to-r from-mulbin-50 to-mulbin-100 border border-mulbin-200`;
    case "blue":
      return `${baseClasses} bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200`;
    case "red":
      return `${baseClasses} bg-gradient-to-r from-red-50 to-red-100 border border-red-200`;
    case "green":
      return `${baseClasses} bg-gradient-to-r from-green-50 to-green-100 border border-green-200`;
    default:
      return `${baseClasses} bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200`;
  }
});

const iconClasses = computed(() => {
  switch (props.uiStyle) {
    case "clean":
      return "text-lg text-gray-600";
    case "mulbin":
      return "text-lg text-mulbin-600";
    case "blue":
      return "text-lg text-blue-600";
    case "red":
      return "text-lg text-red-600";
    case "green":
      return "text-lg text-green-600";
    default:
      return "text-lg text-blue-600";
  }
});

// Métodos
const getLinkAppearanceClasses = (link: MicroDashLink): string => {
  const appearance = link.appearance || props.defaultAppearance;

  if (link.disabled) {
    return "opacity-50 cursor-not-allowed";
  }

  // Usar el mismo sistema de estilos que uiStyle
  switch (appearance) {
    case "clean":
      return "hover:bg-gray-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-gray-400";
    case "soft-blue":
      return "bg-blue-50 hover:bg-blue-100 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-blue-300";
    case "blue":
      return "bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-blue-300";
    case "mulbin":
      return "bg-mulbin-600 text-white hover:bg-mulbin-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-mulbin-300";
    case "red":
      return "bg-red-600 text-white hover:bg-red-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-red-300";
    case "green":
      return "bg-green-600 text-white hover:bg-green-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-green-300";
    case "default":
    default:
      return "bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-blue-300";
  }
};

const getLinkIconClasses = (link: MicroDashLink): string => {
  const appearance = link.appearance || props.defaultAppearance;

  if (link.disabled) {
    return "text-gray-400";
  }

  switch (appearance) {
    case "clean":
      return "text-gray-500 group-hover:text-gray-600";
    case "soft-blue":
      return "text-blue-500 group-hover:text-blue-600";
    case "blue":
      return "text-white group-hover:text-blue-100";
    case "mulbin":
      return "text-white group-hover:text-mulbin-100";
    case "red":
      return "text-white group-hover:text-red-100";
    case "green":
      return "text-white group-hover:text-green-100";
    case "default":
    default:
      return "text-white group-hover:text-blue-100";
  }
};

const getLinkTitleClasses = (link: MicroDashLink): string => {
  const appearance = link.appearance || props.defaultAppearance;

  if (link.disabled) {
    return "text-gray-400";
  }

  switch (appearance) {
    case "clean":
      return "text-gray-600 group-hover:text-gray-700";
    case "soft-blue":
      return "text-blue-600 group-hover:text-blue-700";
    case "blue":
      return "text-white group-hover:text-blue-100";
    case "mulbin":
      return "text-white group-hover:text-mulbin-100";
    case "red":
      return "text-white group-hover:text-red-100";
    case "green":
      return "text-white group-hover:text-green-100";
    case "default":
    default:
      return "text-white group-hover:text-blue-100";
  }
};

const getLinkDescriptionClasses = (link: MicroDashLink): string => {
  const appearance = link.appearance || props.defaultAppearance;

  if (link.disabled) {
    return "text-gray-400";
  }

  switch (appearance) {
    case "clean":
      return "text-gray-500 group-hover:text-gray-600";
    case "soft-blue":
      return "text-blue-500 group-hover:text-blue-600";
    case "blue":
      return "text-white group-hover:text-blue-100";
    case "mulbin":
      return "text-white group-hover:text-mulbin-100";
    case "red":
      return "text-white group-hover:text-red-100";
    case "green":
      return "text-white group-hover:text-green-100";
    case "default":
    default:
      return "text-white group-hover:text-blue-100";
  }
};

const handleLinkClick = (link: MicroDashLink) => {
  if (link.disabled) return;

  emit("linkClicked", link);

  // Log con ID de instancia para debugging
  console.log(`🔗 [${props.instanceId}] Enlace clickeado:`, link.title);

  if (link.callback) {
    link.callback();
  } else if (link.url) {
    if (link.external) {
      window.open(link.url, "_blank");
    } else {
      window.location.href = link.url;
    }
  }
};

const handleLinkHover = (link: MicroDashLink | null) => {
  state.value.hoveredLink = link?.id || null;
  if (link) {
    emit("linkHovered", link);
    // Log con ID de instancia para debugging
    console.log(`🖱️ [${props.instanceId}] Hover en:`, link.title);
  }
};

const toggleSection = (section: MicroDashSection) => {
  if (!section.collapsible || !props.allowCollapse) return;

  const isCollapsed = isSectionCollapsed(section.id);

  if (isCollapsed) {
    state.value.collapsedSections.delete(section.id);
  } else {
    state.value.collapsedSections.add(section.id);
  }

  emit("sectionToggled", section, !isCollapsed);

  // Log con ID de instancia para debugging
  console.log(
    `📁 [${props.instanceId}] Sección ${section.title} ${
      isCollapsed ? "expandida" : "colapsada"
    }`
  );
};

const isSectionCollapsed = (sectionId: string): boolean => {
  return state.value.collapsedSections.has(sectionId);
};

const showMoreLinks = (
  section: MicroDashSection | { links: MicroDashLink[] }
) => {
  // Aquí podrías implementar un modal o expandir la sección
  console.log(`Mostrando más enlaces para: ${section.links.length} total`);
};

const getBadgeColorClasses = (color?: string): string => {
  if (!color || !(color in BADGE_COLOR_CLASSES)) {
    return BADGE_COLOR_CLASSES.gray;
  }
  return BADGE_COLOR_CLASSES[color as keyof typeof BADGE_COLOR_CLASSES];
};

// Lifecycle hooks
onMounted(() => {
  // Configurar secciones colapsadas por defecto
  if (props.defaultCollapsed && props.sections) {
    props.sections.forEach((section) => {
      if (section.collapsible && section.collapsed) {
        state.value.collapsedSections.add(section.id);
      }
    });
  }

  // Log de inicialización con ID de instancia
  console.log(
    `🚀 [${props.instanceId}] MicroDash inicializado con ${
      props.sections?.length || 0
    } secciones y ${props.links?.length || 0} enlaces`
  );
});

onUnmounted(() => {
  // Log de cleanup con ID de instancia
  console.log(`🧹 [${props.instanceId}] MicroDash desmontado`);
});

// Exponer métodos públicos del componente
defineExpose({
  toggleSection,
  isSectionCollapsed: (sectionId: string) => isSectionCollapsed(sectionId),
  expandAllSections: () => {
    state.value.collapsedSections.clear();
  },
  collapseAllSections: () => {
    if (props.sections) {
      props.sections.forEach((section) => {
        if (section.collapsible) {
          state.value.collapsedSections.add(section.id);
        }
      });
    }
  },
});
</script>

<style scoped>
/* Transiciones suaves */
.group {
  transition: all 0.2s ease;
}

.group:hover {
  transform: translateY(-1px);
}

/* Animaciones para los íconos */
ion-icon {
  transition: all 0.2s ease;
}

/* Line clamp para descripciones largas */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Estilos para separadores elegantes */
.border-l-3 {
  border-left-width: 3px;
}

/* Transiciones suaves para bordes */
.group {
  transition: all 0.2s ease, border-left-color 0.2s ease;
}

/* Efectos de hover mejorados */
.group:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Separadores entre elementos */
.border-t.border-gray-100 {
  border-top-color: #f3f4f6;
  border-top-width: 1px;
}

/* Separadores entre secciones */
.border-t-2.border-t-gray-100 {
  border-top-color: #f9fafb;
  border-top-width: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .space-y-4 > div {
    padding: 0.75rem;
  }

  .text-sm {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .text-xs {
    font-size: 0.625rem;
    line-height: 0.875rem;
  }
}
</style>
