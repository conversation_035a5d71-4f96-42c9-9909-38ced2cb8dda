<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MicroDash - Múltiples Instancias</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      type="module"
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"
    ></script>
    <style>
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-7xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
        🚀 MicroDash - Múltiples Instancias
      </h1>

      <div class="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 class="text-lg font-semibold text-blue-800 mb-3">
          ✨ Características de Múltiples Instancias
        </h2>
        <ul class="text-blue-700 space-y-1">
          <li>
            ✅ <strong>Auto-detección</strong> - Se montan automáticamente todos
            los elementos con <code>data-micro-dash</code>
          </li>
          <li>
            ✅ <strong>IDs únicos</strong> - Cada instancia tiene un
            identificador único para debugging
          </li>
          <li>
            ✅ <strong>Configuración independiente</strong> - Cada instancia
            puede tener diferentes props y estilos
          </li>
          <li>
            ✅ <strong>Observador de mutaciones</strong> - Detecta nuevos
            elementos agregados dinámicamente
          </li>
          <li>
            ✅ <strong>API de gestión</strong> - Control total vía
            <code>window.MicroDashManager</code>
          </li>
        </ul>
      </div>

      <!-- Grid de múltiples instancias -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <!-- Instancia 1: Dashboard de Citas -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700">
            📅 Dashboard de Citas
          </h3>
          <div
            data-micro-dash
            data-title="Citas"
            data-icon="calendar-outline"
            data-ui-style="clean"
            data-links='[
              {
                "id": "new-appointment",
                "title": "Registrar una nueva cita",
                "url": "/citas/nueva",
                "icon": "add-circle-outline"
              },
              {
                "id": "pending-appointments",
                "title": "Ver citas solicitadas que no he confirmado",
                "url": "/citas/pendientes",
                "icon": "time-outline"
              },
              {
                "id": "future-appointments",
                "title": "Ver mis citas hechas de hoy en adelante",
                "url": "/citas/futuras",
                "icon": "calendar-outline"
              }
            ]'
            class="micro-dash-container"
          >
            <!-- Loading placeholder -->
            <div class="flex justify-center items-center p-8">
              <div
                class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
              ></div>
              <span class="ml-2 text-sm text-gray-600">Cargando citas...</span>
            </div>
          </div>
        </div>

        <!-- Instancia 2: Gestión de Inmuebles -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700">
            🏠 Gestión de Inmuebles
          </h3>
          <div
            data-micro-dash
            data-title="Inmuebles"
            data-icon="home-outline"
            data-ui-style="blue"
            data-sections='[
              {
                "id": "properties",
                "title": "Propiedades",
                "icon": "business-outline",
                "collapsible": true,
                "links": [
                  {
                    "id": "add-property",
                    "title": "Agregar nueva propiedad",
                    "url": "/propiedades/nueva",
                    "icon": "add-circle-outline",
                    "badge": "Nuevo",
                    "badgeColor": "green"
                  },
                  {
                    "id": "list-properties",
                    "title": "Ver todas las propiedades",
                    "url": "/propiedades",
                    "icon": "list-outline"
                  }
                ]
              }
            ]'
            class="micro-dash-container"
          >
            <!-- Loading placeholder -->
            <div class="flex justify-center items-center p-8">
              <div
                class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
              ></div>
              <span class="ml-2 text-sm text-gray-600"
                >Cargando inmuebles...</span
              >
            </div>
          </div>
        </div>

        <!-- Instancia 3: Accesos Rápidos -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700">
            ⚡ Accesos Rápidos
          </h3>
          <div
            data-micro-dash
            data-title="Accesos"
            data-icon="flash-outline"
            data-ui-style="mulbin"
            data-links='[
              {
                "id": "dashboard",
                "title": "Dashboard Principal",
                "url": "/dashboard",
                "icon": "home-outline"
              },
              {
                "id": "profile",
                "title": "Mi Perfil",
                "url": "/profile",
                "icon": "person-outline"
              },
              {
                "id": "settings",
                "title": "Configuración",
                "url": "/settings",
                "icon": "settings-outline"
              }
            ]'
            class="micro-dash-container"
          >
            <!-- Loading placeholder -->
            <div class="flex justify-center items-center p-8">
              <div
                class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
              ></div>
              <span class="ml-2 text-sm text-gray-600"
                >Cargando accesos...</span
              >
            </div>
          </div>
        </div>

        <!-- Instancia 4: Reportes -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700">📊 Reportes</h3>
          <div
            data-micro-dash
            data-title="Reportes"
            data-icon="bar-chart-outline"
            data-ui-style="green"
            data-sections='[
              {
                "id": "sales",
                "title": "Ventas",
                "icon": "trending-up-outline",
                "collapsible": true,
                "links": [
                  {
                    "id": "daily-sales",
                    "title": "Ventas del día",
                    "url": "/reportes/ventas/diario",
                    "icon": "calendar-outline"
                  },
                  {
                    "id": "monthly-sales",
                    "title": "Ventas del mes",
                    "url": "/reportes/ventas/mensual",
                    "icon": "calendar-outline"
                  }
                ]
              },
              {
                "id": "analytics",
                "title": "Analíticas",
                "icon": "analytics-outline",
                "collapsible": true,
                "links": [
                  {
                    "id": "user-stats",
                    "title": "Estadísticas de usuarios",
                    "url": "/reportes/usuarios",
                    "icon": "people-outline"
                  }
                ]
              }
            ]'
            class="micro-dash-container"
          >
            <!-- Loading placeholder -->
            <div class="flex justify-center items-center p-8">
              <div
                class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
              ></div>
              <span class="ml-2 text-sm text-gray-600"
                >Cargando reportes...</span
              >
            </div>
          </div>
        </div>

        <!-- Instancia 5: Administración -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700">🔧 Administración</h3>
          <div
            data-micro-dash
            data-title="Admin"
            data-icon="shield-outline"
            data-ui-style="red"
            data-sections='[
              {
                "id": "users",
                "title": "Usuarios",
                "icon": "people-outline",
                "collapsible": true,
                "links": [
                  {
                    "id": "list-users",
                    "title": "Listar usuarios",
                    "url": "/admin/usuarios",
                    "icon": "list-outline"
                  },
                  {
                    "id": "add-user",
                    "title": "Crear usuario",
                    "url": "/admin/usuarios/nuevo",
                    "icon": "person-add-outline",
                    "badge": "Nuevo",
                    "badgeColor": "green"
                  }
                ]
              }
            ]'
            class="micro-dash-container"
          >
            <!-- Loading placeholder -->
            <div class="flex justify-center items-center p-8">
              <div
                class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
              ></div>
              <span class="ml-2 text-sm text-gray-600">Cargando admin...</span>
            </div>
          </div>
        </div>

        <!-- Instancia 6: Notificaciones -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700">🔔 Notificaciones</h3>
          <div
            data-micro-dash
            data-title="Alertas"
            data-icon="notifications-outline"
            data-ui-style="yellow"
            data-links='[
              {
                "id": "unread",
                "title": "No leídas (3)",
                "url": "/notificaciones/no-leidas",
                "icon": "mail-unread-outline",
                "badge": "3",
                "badgeColor": "red"
              },
              {
                "id": "all",
                "title": "Todas las notificaciones",
                "url": "/notificaciones",
                "icon": "mail-outline"
              },
              {
                "id": "settings",
                "title": "Configurar notificaciones",
                "url": "/notificaciones/configuracion",
                "icon": "settings-outline"
              }
            ]'
            class="micro-dash-container"
          >
            <!-- Loading placeholder -->
            <div class="flex justify-center items-center p-8">
              <div
                class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
              ></div>
              <span class="ml-2 text-sm text-gray-600"
                >Cargando notificaciones...</span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Panel de Control -->
      <div class="mt-12 bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-4">
          🎮 Panel de Control - MicroDashManager
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onclick="window.MicroDashManager.mountAll()"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded font-medium transition-colors"
          >
            🔄 Remontar Todos
          </button>

          <button
            onclick="window.MicroDashManager.unmountAll()"
            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded font-medium transition-colors"
          >
            🧹 Desmontar Todos
          </button>

          <button
            onclick="addNewInstance()"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium transition-colors"
          >
            ➕ Agregar Nueva
          </button>

          <button
            onclick="showInstancesInfo()"
            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded font-medium transition-colors"
          >
            ℹ️ Info Instancias
          </button>
        </div>

        <div class="mt-4 text-sm text-gray-300">
          <p><strong>Funciones disponibles:</strong></p>
          <ul class="list-disc list-inside mt-2 space-y-1">
            <li>
              <code>MicroDashManager.mountAll()</code> - Montar todas las
              instancias
            </li>
            <li>
              <code>MicroDashManager.unmountAll()</code> - Desmontar todas las
              instancias
            </li>
            <li>
              <code>MicroDashManager.remountAll()</code> - Remontar todas las
              instancias
            </li>
            <li>
              <code>MicroDashManager.mountElement(element)</code> - Montar
              elemento específico
            </li>
            <li>
              <code>MicroDashManager.unmountElement(element)</code> - Desmontar
              elemento específico
            </li>
          </ul>
        </div>
      </div>

      <!-- Información de Instancias -->
      <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">
          📊 Información de Instancias
        </h3>

        <div id="instances-info" class="text-sm text-gray-600">
          <p>
            Haz clic en "Info Instancias" para ver la información detallada.
          </p>
        </div>
      </div>
    </div>

    <script>
      // Función para agregar una nueva instancia dinámicamente
      function addNewInstance() {
        const container = document.querySelector(".grid");
        const newInstance = document.createElement("div");
        newInstance.className = "space-y-4";

        const instanceId = `dynamic-${Date.now()}`;

        newInstance.innerHTML = `
          <h3 class="text-lg font-semibold text-gray-700">🆕 Instancia Dinámica</h3>
          <div
            data-micro-dash
            data-title="Nueva Instancia"
            data-icon="add-circle-outline"
            data-ui-style="purple"
            data-links='[
              {
                "id": "dynamic-link-1",
                "title": "Enlace dinámico 1",
                "url": "/nuevo/enlace1",
                "icon": "link-outline"
              },
              {
                "id": "dynamic-link-2",
                "title": "Enlace dinámico 2",
                "url": "/nuevo/enlace2",
                "icon": "link-outline"
              }
            ]'
            class="micro-dash-container"
          >
            <div class="flex justify-center items-center p-8">
              <div class="w-8 h-8 rounded-full border-b-2 border-purple-600 animate-spin"></div>
              <span class="ml-2 text-sm text-gray-600">Cargando dinámico...</span>
            </div>
          </div>
        `;

        container.appendChild(newInstance);

        // El observador de mutaciones detectará automáticamente el nuevo elemento
        console.log("🆕 Nueva instancia agregada al DOM");
      }

      // Función para mostrar información de las instancias
      function showInstancesInfo() {
        const instances = document.querySelectorAll("[data-micro-dash]");
        const infoContainer = document.getElementById("instances-info");

        let info = `<p><strong>Total de instancias encontradas: ${instances.length}</strong></p>`;
        info += '<div class="mt-4 space-y-2">';

        instances.forEach((instance, index) => {
          const title = instance.getAttribute("data-title") || "Sin título";
          const style = instance.getAttribute("data-ui-style") || "clean";
          const instanceId = instance._microDashInstanceId || "No montada";
          const isMounted = instance._microDashApp
            ? "✅ Montada"
            : "❌ No montada";

          info += `
            <div class="border border-gray-200 rounded p-3">
              <p><strong>Instancia ${index + 1}:</strong> ${title}</p>
              <p><strong>Estilo:</strong> ${style}</p>
              <p><strong>ID:</strong> ${instanceId}</p>
              <p><strong>Estado:</strong> ${isMounted}</p>
            </div>
          `;
        });

        info += "</div>";
        infoContainer.innerHTML = info;
      }

      // Simulación de funcionalidades
      console.log("🚀 MicroDash - Ejemplo de múltiples instancias cargado");

      // Verificar que MicroDashManager esté disponible
      setTimeout(() => {
        if (window.MicroDashManager) {
          console.log(
            "✅ MicroDashManager disponible:",
            window.MicroDashManager
          );
        } else {
          console.log("❌ MicroDashManager no disponible");
        }
      }, 1000);
    </script>
  </body>
</html>
