# 🔧 Guía de Integración: MicroDash

Esta guía te ayudará a integrar el componente `MicroDash` en tu sistema paso a paso, siguiendo las mejores prácticas y patrones establecidos.

## 📋 Prerrequisitos

- ✅ Sistema Mulbin configurado
- ✅ Vite configurado para builds
- ✅ Acceso a `panel.html` para incluir scripts
- ✅ Conocimiento básico de HTML y JavaScript

## 🚀 Paso 1: Configuración en Vite

### 1.1 Agregar entrada en vite.config.ts

```typescript
// panel4-templates/vite.config.ts
rollupOptions: {
  input: {
    // ... otros componentes existentes
    sidebarCardInmuebles: "./src/components/sidebar-card-inmuebles/index.js",
    multipsCard: "./src/components/multips/index.js",
    // ✅ NUEVA ENTRADA
    microDash: "./src/components/microdash/index.js",
  },
  // ... resto de la configuración
}
```

### 1.2 Verificar la configuración

```bash
cd panel4-templates
npm run build
```

**Resultado esperado**:

```
✓ built in X.XXs
dist/assets/microDash-XXXXXXX.js                  XX.XX kB │ gzip:   X.XX kB
dist/assets/microDash-XXXXXXX.css                 X.XX kB │ gzip:   X.XX kB
```

## 🚀 Paso 2: Inclusión de Scripts

### 2.1 Modificar panel.html

```html
<!-- panel4-templates/panel.html -->

<!-- Para páginas de inicio -->
{{#is_home}}
<script
  type="module"
  src="./src/components/sidebar-card-inmuebles/index.js"
></script>
<script type="module" src="./src/components/multips/index.js"></script>
<!-- ✅ AGREGAR ESTE SCRIPT -->
<script type="module" src="./src/components/microdash/index.js"></script>
{{/is_home}}

<!-- Para otras páginas -->
{{^is_home}}
<script
  type="module"
  src="./src/components/sidebar-multibolsa/index.js"
></script>
<script type="module" src="./src/components/multips/index.js"></script>
<!-- ✅ AGREGAR ESTE SCRIPT -->
<script type="module" src="./src/components/microdash/index.js"></script>
{{/is_home}}
```

### 2.2 Verificar la inclusión

Después de modificar `panel.html`, ejecuta:

```bash
npm run build
```

Y verifica que el script se incluya en el build final.

## 🚀 Paso 3: Integración en Templates

### 3.1 Ejemplo Básico - Dashboard de Citas

```html
<!-- Reemplazar o agregar en tu template -->
<section class="micro-dash-section">
  <div
    data-micro-dash
    data-title="Citas"
    data-ui-style="clean"
    data-links='[
      {
        "id": "new-appointment",
        "title": "Registrar una nueva cita",
        "url": "/citas/nueva",
        "icon": "add-circle-outline"
      },
      {
        "id": "pending-appointments",
        "title": "Ver citas solicitadas que no he confirmado",
        "url": "/citas/pendientes",
        "icon": "time-outline"
      },
      {
        "id": "future-appointments",
        "title": "Ver mis citas hechas de hoy en adelante",
        "url": "/citas/futuras",
        "icon": "calendar-outline"
      }
    ]'
    class="micro-dash-container"
  >
    <!-- Loading placeholder -->
    <div class="flex justify-center items-center p-8">
      <div
        class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
      ></div>
      <span class="ml-2 text-sm text-gray-600">Cargando dashboard...</span>
    </div>
  </div>
</section>
```

### 3.2 Ejemplo Avanzado - Con Secciones

```html
<section class="micro-dash-section">
  <div
    data-micro-dash
    data-title="Gestión de Inmuebles"
    data-icon="home-outline"
    data-description="Acceso rápido a funciones principales"
    data-ui-style="blue"
    data-sections='[
      {
        "id": "properties",
        "title": "Propiedades",
        "icon": "business-outline",
        "description": "Gestiona tu inventario de propiedades",
        "collapsible": true,
        "links": [
          {
            "id": "add-property",
            "title": "Agregar nueva propiedad",
            "url": "/propiedades/nueva",
            "icon": "add-circle-outline",
            "badge": "Nuevo",
            "badgeColor": "green"
          },
          {
            "id": "list-properties",
            "title": "Ver todas las propiedades",
            "url": "/propiedades",
            "icon": "list-outline",
            "description": "Gestiona tu inventario completo"
          },
          {
            "id": "property-stats",
            "title": "Estadísticas de propiedades",
            "url": "/propiedades/estadisticas",
            "icon": "bar-chart-outline",
            "badge": "Hot",
            "badgeColor": "red"
          }
        ]
      },
      {
        "id": "marketing",
        "title": "Marketing",
        "icon": "megaphone-outline",
        "description": "Herramientas de promoción",
        "collapsible": true,
        "links": [
          {
            "id": "create-campaign",
            "title": "Crear campaña",
            "url": "/marketing/campanas/nueva",
            "icon": "create-outline"
          },
          {
            "id": "view-analytics",
            "title": "Ver analíticas",
            "url": "/marketing/analiticas",
            "icon": "analytics-outline"
          }
        ]
      }
    ]'
    class="micro-dash-container"
  >
    <!-- Loading placeholder -->
    <div class="flex justify-center items-center p-8">
      <div
        class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
      ></div>
      <span class="ml-2 text-sm text-gray-600">Cargando dashboard...</span>
    </div>
  </div>
</section>
```

## 🚀 Paso 4: Personalización de Estilos

### 4.1 Estilos CSS Personalizados

```css
/* Agregar a tu archivo CSS principal */
.micro-dash-section {
  margin-bottom: 1.5rem;
}

.micro-dash-container {
  /* Estilos personalizados del contenedor */
}

/* Personalizar colores de hover */
.micro-dash-container .group:hover {
  background-color: var(--custom-hover-color, #f0f9ff) !important;
}

/* Personalizar badges */
.micro-dash-container .badge-custom {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

/* Personalizar transiciones */
.micro-dash-container .group {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 4.2 Variables CSS Personalizadas

```css
:root {
  /* Colores principales */
  --micro-dash-primary: #3b82f6;
  --micro-dash-hover: #dbeafe;
  --micro-dash-border: #e5e7eb;

  /* Colores de texto */
  --micro-dash-text: #374151;
  --micro-dash-text-secondary: #6b7280;

  /* Colores de badges */
  --micro-dash-badge-blue: #dbeafe;
  --micro-dash-badge-green: #dcfce7;
  --micro-dash-badge-red: #fee2e2;
  --micro-dash-badge-yellow: #fef3c7;
  --micro-dash-badge-purple: #f3e8ff;
  --micro-dash-badge-gray: #f3f4f6;
}
```

## 🚀 Paso 5: Integración con SidebarCardInmuebles

### 5.1 Colocación en el Dashboard

Para mantener la consistencia visual, coloca `MicroDash` junto a `SidebarCardInmuebles`:

```html
<!-- En tu template de dashboard -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- SidebarCardInmuebles (2 columnas) -->
  <div class="lg:col-span-2">
    <div
      data-sidebar-card-inmuebles
      data-ui-style="clean"
      data-total-properties="1247"
      data-status="publicado"
    >
      <!-- Loading placeholder -->
    </div>
  </div>

  <!-- MicroDash (1 columna) -->
  <div class="lg:col-span-1">
    <div
      data-micro-dash
      data-title="Accesos Rápidos"
      data-icon="flash-outline"
      data-ui-style="clean"
      data-links='[
        {"id": "dashboard", "title": "Dashboard Principal", "url": "/dashboard", "icon": "home-outline"},
        {"id": "profile", "title": "Mi Perfil", "url": "/profile", "icon": "person-outline"},
        {"id": "settings", "title": "Configuración", "url": "/settings", "icon": "settings-outline"}
      ]'
    >
      <!-- Loading placeholder -->
    </div>
  </div>
</div>
```

### 5.2 Estilos Consistentes

```css
/* Asegurar consistencia visual */
.micro-dash-container {
  /* Mismo espaciado que SidebarCardInmuebles */
  margin-bottom: 1.5rem;
}

.micro-dash-container .p-4 {
  /* Mismo padding interno */
  padding: 1rem;
}

.micro-dash-container h3 {
  /* Mismo estilo de título */
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.75rem;
}
```

## 🚀 Paso 6: Casos de Uso Específicos

### 6.1 Dashboard de Administración

```html
<div
  data-micro-dash
  data-title="Panel de Administración"
  data-icon="shield-outline"
  data-ui-style="blue"
  data-sections='[
    {
      "id": "users",
      "title": "Usuarios",
      "icon": "people-outline",
      "collapsible": true,
      "links": [
        {"id": "list-users", "title": "Listar Usuarios", "url": "/admin/users", "icon": "list-outline"},
        {"id": "add-user", "title": "Crear Usuario", "url": "/admin/users/new", "icon": "person-add-outline", "badge": "Nuevo", "badgeColor": "green"},
        {"id": "roles", "title": "Gestionar Roles", "url": "/admin/roles", "icon": "key-outline"}
      ]
    },
    {
      "id": "system",
      "title": "Sistema",
      "icon": "settings-outline",
      "collapsible": true,
      "links": [
        {"id": "logs", "title": "Ver Logs", "url": "/admin/logs", "icon": "document-text-outline"},
        {"id": "backup", "title": "Backup", "url": "/admin/backup", "icon": "cloud-upload-outline", "badge": "Importante", "badgeColor": "red"}
      ]
    }
  ]'
>
  <!-- Loading placeholder -->
</div>
```

### 6.2 Navegación de Usuario

```html
<div
  data-micro-dash
  data-title="Mi Cuenta"
  data-icon="person-circle-outline"
  data-ui-style="clean"
  data-links='[
    {"id": "profile", "title": "Editar Perfil", "url": "/perfil", "icon": "person-outline"},
    {"id": "preferences", "title": "Preferencias", "url": "/preferencias", "icon": "options-outline"},
    {"id": "notifications", "title": "Notificaciones", "url": "/notificaciones", "icon": "notifications-outline"},
    {"id": "security", "title": "Seguridad", "url": "/seguridad", "icon": "lock-closed-outline", "badge": "!", "badgeColor": "yellow"},
    {"id": "help", "title": "Ayuda y Soporte", "url": "/ayuda", "icon": "help-circle-outline"}
  ]'
>
  <!-- Loading placeholder -->
</div>
```

### 6.3 Dashboard de Ventas

```html
<div
  data-micro-dash
  data-title="Ventas y Reportes"
  data-icon="trending-up-outline"
  data-ui-style="green"
  data-sections='[
    {
      "id": "sales",
      "title": "Ventas",
      "icon": "cash-outline",
      "collapsible": true,
      "links": [
        {"id": "new-sale", "title": "Nueva Venta", "url": "/ventas/nueva", "icon": "add-circle-outline", "badge": "Nuevo", "badgeColor": "green"},
        {"id": "sales-list", "title": "Lista de Ventas", "url": "/ventas", "icon": "list-outline"},
        {"id": "pending-sales", "title": "Ventas Pendientes", "url": "/ventas/pendientes", "icon": "time-outline", "badge": "3", "badgeColor": "red"}
      ]
    },
    {
      "id": "reports",
      "title": "Reportes",
      "icon": "bar-chart-outline",
      "collapsible": true,
      "links": [
        {"id": "daily-report", "title": "Reporte Diario", "url": "/reportes/diario", "icon": "calendar-outline"},
        {"id": "monthly-report", "title": "Reporte Mensual", "url": "/reportes/mensual", "icon": "calendar-outline"},
        {"id": "custom-report", "title": "Reporte Personalizado", "url": "/reportes/personalizado", "icon": "create-outline"}
      ]
    }
  ]'
>
  <!-- Loading placeholder -->
</div>
```

## 🚀 Paso 7: Testing y Verificación

### 7.1 Verificación del Build

```bash
cd panel4-templates
npm run build
```

**Verificar en consola**:

```
🚀 Inicializando MicroDash...
🏗️ Inicializando MicroDash con props: {
  uiStyle: "clean",
  title: "Mi Dashboard",
  linksCount: 3,
  sectionsCount: 0
}
✅ Componente MicroDash montado correctamente
```

### 7.2 Verificación Visual

- ✅ Componente se renderiza correctamente
- ✅ Enlaces son clickeables
- ✅ Secciones se pueden colapsar/expandir
- ✅ Hover effects funcionan
- ✅ Responsive en diferentes pantallas
- ✅ Consistencia visual con SidebarCardInmuebles

### 7.3 Verificación de Funcionalidad

```javascript
// En la consola del navegador
// Verificar que el componente esté montado
document.querySelector("[data-micro-dash]");

// Verificar que los enlaces funcionen
document.querySelectorAll("[data-micro-dash] a").forEach((link) => {
  console.log("Enlace encontrado:", link.textContent, link.href);
});

// Verificar eventos de hover
document.querySelectorAll("[data-micro-dash] .group").forEach((group) => {
  group.addEventListener("mouseenter", () => {
    console.log("Hover en:", group.textContent);
  });
});
```

## 🚀 Paso 8: Troubleshooting Común

### 8.1 Componente no se renderiza

**Síntoma**: Solo se muestra el loading placeholder

**Solución**:

1. Verificar que el script esté incluido en `panel.html`
2. Verificar que esté configurado en `vite.config.ts`
3. Ejecutar `npm run build`
4. Verificar logs en consola del navegador

### 8.2 Enlaces no funcionan

**Síntoma**: Los enlaces no son clickeables

**Solución**:

1. Verificar formato JSON en `data-links` o `data-sections`
2. Verificar que las URLs sean válidas
3. Verificar que no haya errores de JavaScript en consola

### 8.3 Estilos no se aplican

**Síntoma**: El componente no tiene el estilo esperado

**Solución**:

1. Verificar valor de `data-ui-style`
2. Verificar que Tailwind CSS esté cargado
3. Verificar que no haya conflictos de CSS

### 8.4 Secciones no se colapsan

**Síntoma**: Las secciones no se pueden colapsar/expandir

**Solución**:

1. Verificar que `data-allow-collapse="true"`
2. Verificar que las secciones tengan `"collapsible": true`
3. Verificar que no haya errores de JavaScript

## 🚀 Paso 9: Optimización y Performance

### 9.1 Lazy Loading

```html
<!-- Cargar solo cuando sea necesario -->
<div data-micro-dash data-lazy="true" data-links="[...]">
  <!-- Loading placeholder -->
</div>
```

### 9.2 Caché de Configuración

```javascript
// En tu JavaScript principal
const microDashConfig = {
  // Configuración común para todos los MicroDash
  defaultStyle: "clean",
  showIcons: true,
  showDescriptions: true,
  showBadges: true,
};

// Aplicar configuración global
document.querySelectorAll("[data-micro-dash]").forEach((element) => {
  Object.entries(microDashConfig).forEach(([key, value]) => {
    if (!element.hasAttribute(`data-${key}`)) {
      element.setAttribute(`data-${key}`, value);
    }
  });
});
```

### 9.3 Debounce para Hover Events

```javascript
// Optimizar eventos de hover
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Aplicar debounce a eventos de hover
const debouncedHover = debounce((link) => {
  console.log("Hover en:", link.title);
}, 100);
```

## 🚀 Paso 10: Mantenimiento y Actualizaciones

### 10.1 Monitoreo de Performance

```javascript
// Agregar métricas de performance
const microDashMetrics = {
  renderTime: 0,
  clickCount: 0,
  hoverCount: 0,
};

// Medir tiempo de renderizado
const startTime = performance.now();
// ... después del renderizado
microDashMetrics.renderTime = performance.now() - startTime;
console.log("MicroDash renderizado en:", microDashMetrics.renderTime, "ms");
```

### 10.2 Logs de Debug

```javascript
// Habilitar logs detallados en desarrollo
if (process.env.NODE_ENV === "development") {
  console.log("🔍 MicroDash Debug Mode");
  console.log("Props recibidos:", props);
  console.log("Estado del componente:", state);
}
```

### 10.3 Actualizaciones del Componente

```bash
# Para actualizar el componente
cd panel4-templates
git pull origin main
npm run build

# Verificar cambios
git log --oneline -5
```

## 📚 Recursos Adicionales

### Documentación Relacionada

- [README.md](./README.md) - Documentación completa del componente
- [ejemplo-uso.html](./ejemplo-uso.html) - Demo visual y ejemplos
- [types.ts](./types.ts) - Interfaces TypeScript

### Componentes Relacionados

- `SidebarCardInmuebles` - Componente de referencia para estilos
- `MultipsCard` - Componente de tips y sugerencias

### Herramientas de Desarrollo

- **Vue DevTools** - Para debugging del componente
- **Tailwind CSS IntelliSense** - Para autocompletado de clases
- **Ionic Icons** - Para selección de íconos

---

**Estado de la guía**: ✅ COMPLETADA  
**Última actualización**: {{ fecha_actual }}  
**Versión del componente**: 1.0.0
