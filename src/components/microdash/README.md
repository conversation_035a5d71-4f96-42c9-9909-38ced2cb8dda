# 🚀 MicroDash - Componente de Micro Dashboards

Un componente Vue 3 flexible y configurable para crear micro dashboards con enlaces, secciones y navegación rápida. Perfecto para integrar en sidebars, paneles de control o cualquier área donde necesites acceso rápido a funciones del sistema.

## ✨ Características Principales

- 🎯 **Enlaces configurables** - URLs, callbacks o ambos
- 📁 **Secciones organizadas** - Con soporte para colapsar/expandir
- 🎨 **Múltiples estilos** - clean, blue, mulbin, red, green
- 🔧 **Íconos personalizables** - Ionic Icons integrados
- 🏷️ **Badges con colores** - Para destacar elementos importantes
- 📱 **Responsive design** - Adaptativo a diferentes pantallas
- ⚡ **Auto-montaje** - Se integra automáticamente en el DOM
- 🎭 **Hover effects** - Transiciones suaves y feedback visual
- 🔄 **Múltiples instancias** - Soporte completo para varias instancias en el mismo DOM
- 👁️ **Observador de mutaciones** - Detecta nuevos elementos agregados dinámicamente
- 🎨 **Sistema de apariencias** - Classic (default) y Button para diferentes usos

## 🏗️ Estructura del Proyecto

```
src/components/microdash/
├── MicroDash.vue                    # Componente principal
├── types.ts                         # Interfaces TypeScript
├── index.js                         # Sistema de auto-montaje
├── README.md                        # Esta documentación
├── ejemplo-uso.html                 # Demo visual y ejemplos
├── ejemplo-multiples-instancias.html # Demo de múltiples instancias
└── INTEGRACION.md                   # Guía de integración
```

## 🚀 Uso Básico

### Integración Simple

```html
<div
  data-micro-dash
  data-title="Accesos Rápidos"
  data-ui-style="clean"
  data-links='[
    {
      "id": "dashboard",
      "title": "Ir al Dashboard",
      "url": "/dashboard",
      "icon": "home-outline"
    },
    {
      "id": "profile",
      "title": "Mi Perfil",
      "url": "/profile",
      "icon": "person-outline"
    }
  ]'
>
  <!-- Loading placeholder -->
  <div class="flex justify-center items-center p-8">
    <div
      class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
    ></div>
    <span class="ml-2 text-sm text-gray-600">Cargando dashboard...</span>
  </div>
</div>
```

### Con Secciones Organizadas

```html
<div
  data-micro-dash
  data-title="Gestión de Inmuebles"
  data-icon="home-outline"
  data-ui-style="blue"
  data-sections='[
    {
      "id": "properties",
      "title": "Propiedades",
      "icon": "business-outline",
      "collapsible": true,
      "links": [
        {
          "id": "add-property",
          "title": "Agregar Propiedad",
          "url": "/propiedades/nueva",
          "icon": "add-circle-outline",
          "badge": "Nuevo",
          "badgeColor": "green"
        }
      ]
    }
  ]'
>
  <!-- Loading placeholder -->
</div>
```

## 🎨 **Sistema de Apariencias Unificado**

### Características del Sistema Unificado

- ✅ **Sistema unificado** - Las apariencias usan los mismos estilos que uiStyle
- ✅ **Consistencia total** - No hay duplicación de conceptos
- ✅ **Flexibilidad completa** - Todos los estilos disponibles para apariencias
- ✅ **Mantenibilidad** - Un solo sistema de estilos para todo
- ✅ **Lógica coherente** - clean = transparente, blue = azul sólido, etc.

### Apariencias Disponibles

**📋 Clean (Default)**

- **Fondo**: Transparente con hover gris claro (`hover:bg-gray-50`)
- **Texto**: Gris (`text-gray-600`) con hover más oscuro (`hover:text-gray-700`)
- **Íconos**: Grises (`text-gray-500`) con hover más oscuro (`hover:text-gray-600`)
- **Separadores**: Bordes izquierdos grises en hover (`hover:border-l-gray-400`)
- **Casos de uso**: Navegación, menús, referencias, sidebars

**🔵 Soft-Blue**

- **Fondo**: Azul muy claro (`bg-blue-25`) con hover azul claro (`hover:bg-blue-50`)
- **Texto**: Azul (`text-blue-600`) con hover más oscuro (`hover:text-blue-700`)
- **Íconos**: Azules (`text-blue-500`) con hover más oscuro (`hover:text-blue-600`)
- **Separadores**: Bordes izquierdos azules en hover (`hover:border-l-blue-300`)
- **Casos de uso**: Enlaces discretos, documentación, ayuda, recursos secundarios

**🔵 Blue**

- **Fondo**: Azul sólido (`bg-blue-600`) con hover más oscuro (`hover:bg-blue-700`)
- **Texto**: Blanco (`text-white`) con hover azul claro (`hover:text-blue-100`)
- **Íconos**: Blancos (`text-white`) con hover azul claro (`hover:text-blue-100`)
- **Separadores**: Bordes izquierdos azul claro en hover (`hover:border-l-blue-300`)
- **Casos de uso**: Acciones principales, funciones críticas, formularios

**🟣 Mulbin**

- **Fondo**: Púrpura sólido (`bg-purple-600`) con hover más oscuro (`hover:bg-purple-700`)
- **Texto**: Blanco (`text-white`) con hover púrpura claro (`hover:text-purple-100`)
- **Íconos**: Blancos (`text-white`) con hover púrpura claro (`hover:text-purple-100`)
- **Separadores**: Bordes izquierdos púrpura claro en hover (`hover:border-l-purple-300`)
- **Casos de uso**: Funciones premium, marca corporativa, características avanzadas

**🔴 Red**

- **Fondo**: Rojo sólido (`bg-red-600`) con hover más oscuro (`hover:bg-red-700`)
- **Texto**: Blanco (`text-white`) con hover rojo claro (`hover:text-red-100`)
- **Íconos**: Blancos (`text-white`) con hover rojo claro (`hover:text-red-100`)
- **Separadores**: Bordes izquierdos rojo claro en hover (`hover:border-l-red-300`)
- **Casos de uso**: Alertas críticas, acciones destructivas, notificaciones importantes

**🟢 Green**

- **Fondo**: Verde sólido (`bg-green-600`) con hover más oscuro (`hover:bg-green-700`)
- **Texto**: Blanco (`text-white`) con hover verde claro (`hover:text-green-100`)
- **Íconos**: Blancos (`text-white`) con hover verde claro (`hover:text-green-100`)
- **Separadores**: Bordes izquierdos verde claro en hover (`hover:border-l-green-300`)
- **Casos de uso**: Confirmaciones, éxito, acciones positivas, estados exitosos

**🔘 Default**

- **Fondo**: Azul sólido (mismo que blue)
- **Texto**: Blanco con hover azul claro
- **Íconos**: Blancos con hover azul claro
- **Casos de uso**: Apariencia estándar del sistema

### Configuración de Apariencias

#### Configuración Global

```html
<div
  data-micro-dash
  data-title="Mi Dashboard"
  data-default-appearance="blue"
  data-links="[...]"
>
  <!-- Todos los enlaces serán azules por defecto -->
</div>
```

#### Configuración por Enlace

```html
<div
  data-micro-dash
  data-title="Mi Sitio Web"
  data-default-appearance="clean"
  data-links='[
    {
      "id": "link-1",
      "title": "Enlace limpio",
      "url": "/clean",
      "icon": "link-outline",
      "appearance": "clean"
    },
    {
      "id": "link-2",
      "title": "Documentación",
      "url": "/docs",
      "icon": "document-outline",
      "appearance": "soft-blue"
    },
    {
      "id": "link-3",
      "title": "Acción principal",
      "url": "/action",
      "icon": "rocket-outline",
      "appearance": "blue"
    },
    {
      "id": "link-4",
      "title": "Función premium",
      "url": "/premium",
      "icon": "star-outline",
      "appearance": "mulbin"
    },
    {
      "id": "link-5",
      "title": "Alerta crítica",
      "url": "/alert",
      "icon": "warning-outline",
      "appearance": "red"
    },
    {
      "id": "link-6",
      "title": "Confirmar",
      "url": "/confirm",
      "icon": "checkmark-outline",
      "appearance": "green"
    }
  ]'
>
  <!-- Loading placeholder -->
</div>
```

### Ejemplo Visual de Apariencias

```html
<!-- Apariencia Clean -->
<div
  class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-gray-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-gray-400"
>
  <ion-icon
    name="link-outline"
    class="text-lg text-gray-500 group-hover:text-gray-600"
  ></ion-icon>
  <span class="text-base font-medium text-gray-600 group-hover:text-gray-700"
    >Enlace limpio</span
  >
</div>

<!-- Apariencia Soft-Blue -->
<div
  class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 hover:bg-blue-50 cursor-pointer hover:shadow-sm border-l-3 border-l-transparent hover:border-l-blue-300 bg-blue-25"
>
  <ion-icon
    name="document-outline"
    class="text-lg text-blue-500 group-hover:text-blue-600"
  ></ion-icon>
  <span class="text-base font-medium text-blue-600 group-hover:text-blue-700"
    >Documentación</span
  >
</div>

<!-- Apariencia Blue -->
<div
  class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-blue-300"
>
  <ion-icon
    name="rocket-outline"
    class="text-lg text-white group-hover:text-blue-100"
  ></ion-icon>
  <span class="text-base font-medium text-white group-hover:text-blue-100"
    >Acción principal</span
  >
</div>

<!-- Apariencia Mulbin -->
<div
  class="group flex items-center space-x-3 p-3 rounded-md transition-all duration-200 bg-purple-600 text-white hover:bg-purple-700 cursor-pointer hover:shadow-lg hover:scale-105 border-l-3 border-l-transparent hover:border-l-purple-300"
>
  <ion-icon
    name="star-outline"
    class="text-lg text-white group-hover:text-purple-100"
  ></ion-icon>
  <span class="text-base font-medium text-white group-hover:text-purple-100"
    >Función premium</span
  >
</div>
```

### Beneficios del Sistema Unificado

**🎯 Consistencia**

- Mismo sistema de colores para uiStyle y appearance
- No hay duplicación de conceptos
- Lógica coherente y predecible
- Mantenimiento simple y centralizado

**🚀 Flexibilidad**

- Todos los estilos disponibles para apariencias
- Configuración por enlace individual
- Configuración global por instancia
- Combinaciones ilimitadas de uiStyle y appearance

## 🔄 **Múltiples Instancias**

### Características de Múltiples Instancias

- ✅ **Auto-detección** - Se montan automáticamente todos los elementos con `data-micro-dash`
- ✅ **IDs únicos** - Cada instancia tiene un identificador único para debugging
- ✅ **Configuración independiente** - Cada instancia puede tener diferentes props y estilos
- ✅ **Observador de mutaciones** - Detecta nuevos elementos agregados dinámicamente
- ✅ **API de gestión** - Control total vía `window.MicroDashManager`

### Ejemplo de Múltiples Instancias

```html
<!-- Instancia 1: Dashboard de Citas -->
<div
  data-micro-dash
  data-title="Citas"
  data-icon="calendar-outline"
  data-ui-style="clean"
  data-links="[...]"
>
  <!-- Loading placeholder -->
</div>

<!-- Instancia 2: Gestión de Inmuebles -->
<div
  data-micro-dash
  data-title="Inmuebles"
  data-icon="home-outline"
  data-ui-style="blue"
  data-sections="[...]"
>
  <!-- Loading placeholder -->
</div>

<!-- Instancia 3: Accesos Rápidos -->
<div
  data-micro-dash
  data-title="Accesos"
  data-icon="flash-outline"
  data-ui-style="mulbin"
  data-links="[...]"
>
  <!-- Loading placeholder -->
</div>
```

### MicroDashManager API

```javascript
// Acceder al gestor global
const manager = window.MicroDashManager;

// Montar todas las instancias
manager.mountAll();

// Desmontar todas las instancias
manager.unmountAll();

// Remontar todas las instancias
manager.remountAll();

// Montar elemento específico
const element = document.querySelector("[data-micro-dash]");
manager.mountElement(element);

// Desmontar elemento específico
manager.unmountElement(element);
```

### Logs de Múltiples Instancias

```
🚀 Inicializando MicroDash...
🔍 Encontrados 3 elementos MicroDash para montar
🏗️ Inicializando MicroDash micro-dash-1703123456789-abc123def con props: {...}
✅ Componente MicroDash micro-dash-1703123456789-abc123def montado correctamente
📌 Instancia 1/3 montada: micro-dash-1703123456789-abc123def
🎉 Total de instancias MicroDash montadas: 3
```

## ⚙️ Props y Configuración

### Props Principales

| Prop          | Tipo                 | Default   | Descripción                    |
| ------------- | -------------------- | --------- | ------------------------------ |
| `uiStyle`     | `string`             | `'clean'` | Estilo visual del componente   |
| `title`       | `string`             | `''`      | Título principal del dashboard |
| `icon`        | `string`             | `''`      | Ícono del título (Ionic Icons) |
| `description` | `string`             | `''`      | Descripción del dashboard      |
| `links`       | `MicroDashLink[]`    | `[]`      | Array de enlaces simples       |
| `sections`    | `MicroDashSection[]` | `[]`      | Array de secciones con enlaces |
| `instanceId`  | `string`             | `''`      | ID único de la instancia       |

### Props de Configuración

| Prop                 | Tipo      | Default | Descripción                          |
| -------------------- | --------- | ------- | ------------------------------------ |
| `showIcons`          | `boolean` | `true`  | Mostrar íconos en los enlaces        |
| `showDescriptions`   | `boolean` | `true`  | Mostrar descripciones en los enlaces |
| `showBadges`         | `boolean` | `true`  | Mostrar badges                       |
| `compact`            | `boolean` | `false` | Modo compacto                        |
| `maxLinksPerSection` | `number`  | `10`    | Máximo de enlaces por sección        |
| `allowCollapse`      | `boolean` | `true`  | Permitir colapsar secciones          |
| `defaultCollapsed`   | `boolean` | `false` | Secciones colapsadas por defecto     |

## 🎨 Estilos Disponibles

### `clean` (Predeterminado)

- **Background**: Transparente
- **Border**: Ninguno
- **Shadow**: Ninguno
- **Uso**: Para fondos personalizados

### `blue`

- **Background**: Gradiente azul claro
- **Border**: `border-blue-200`
- **Colores**: Azules

### `mulbin`

- **Background**: Gradiente mulbin claro
- **Border**: `border-mulbin-200`
- **Colores**: Mulbin

### `red`

- **Background**: Gradiente rojo claro
- **Border**: `border-red-200`
- **Colores**: Rojos

### `green`

- **Background**: Gradiente verde claro
- **Border**: `border-green-200`
- **Colores**: Verdes

## 🔗 Estructura de Enlaces

### MicroDashLink

```typescript
interface MicroDashLink {
  id: string; // Identificador único
  title: string; // Texto del enlace
  url?: string; // URL de destino
  callback?: () => void; // Función callback
  icon?: string; // Ícono (Ionic Icons)
  description?: string; // Descripción opcional
  badge?: string; // Texto del badge
  badgeColor?: "blue" | "green" | "red" | "yellow" | "purple" | "gray";
  external?: boolean; // Enlace externo
  disabled?: boolean; // Enlace deshabilitado
}
```

### MicroDashSection

```typescript
interface MicroDashSection {
  id: string; // Identificador único
  title: string; // Título de la sección
  links: MicroDashLink[]; // Array de enlaces
  collapsible?: boolean; // Se puede colapsar
  collapsed?: boolean; // Colapsada por defecto
  icon?: string; // Ícono de la sección
  description?: string; // Descripción de la sección
}
```

## 📱 Data Attributes

### Básicos

- `data-micro-dash` - Identifica el elemento para auto-montaje
- `data-title` - Título del dashboard
- `data-icon` - Ícono del título
- `data-description` - Descripción del dashboard
- `data-ui-style` - Estilo visual

### Configuración

- `data-show-icons` - Mostrar/ocultar íconos
- `data-show-descriptions` - Mostrar/ocultar descripciones
- `data-show-badges` - Mostrar/ocultar badges
- `data-compact` - Modo compacto
- `data-max-links-per-section` - Máximo enlaces por sección
- `data-allow-collapse` - Permitir colapsar secciones
- `data-default-collapsed` - Secciones colapsadas por defecto

### Datos

- `data-links` - JSON string con array de enlaces
- `data-sections` - JSON string con array de secciones

## 🎯 Casos de Uso

### 1. Dashboard de Citas (como en la imagen)

```html
<div
  data-micro-dash
  data-title="Citas"
  data-ui-style="clean"
  data-links='[
    {"id": "new", "title": "Registrar una nueva cita", "url": "/citas/nueva"},
    {"id": "pending", "title": "Ver citas solicitadas que no he confirmado", "url": "/citas/pendientes"},
    {"id": "future", "title": "Ver mis citas hechas de hoy en adelante", "url": "/citas/futuras"}
  ]'
>
  <!-- Loading placeholder -->
</div>
```

### 2. Navegación de Inmuebles

```html
<div
  data-micro-dash
  data-title="Gestión de Inmuebles"
  data-icon="home-outline"
  data-ui-style="blue"
  data-sections='[
    {
      "id": "properties",
      "title": "Propiedades",
      "icon": "business-outline",
      "collapsible": true,
      "links": [
        {"id": "add", "title": "Agregar nueva propiedad", "url": "/propiedades/nueva", "icon": "add-circle-outline", "badge": "Nuevo", "badgeColor": "green"},
        {"id": "list", "title": "Ver todas las propiedades", "url": "/propiedades", "icon": "list-outline", "description": "Gestiona tu inventario completo"}
      ]
    }
  ]'
>
  <!-- Loading placeholder -->
</div>
```

### 3. Accesos Rápidos del Sistema

```html
<div
  data-micro-dash
  data-title="Accesos Rápidos"
  data-icon="flash-outline"
  data-ui-style="mulbin"
  data-links='[
    {"id": "dashboard", "title": "Dashboard Principal", "url": "/dashboard", "icon": "home-outline"},
    {"id": "profile", "title": "Mi Perfil", "url": "/profile", "icon": "person-outline"},
    {"id": "settings", "title": "Configuración", "url": "/settings", "icon": "settings-outline"},
    {"id": "help", "title": "Ayuda", "url": "/help", "icon": "help-circle-outline", "badge": "?", "badgeColor": "blue"}
  ]'
>
  <!-- Loading placeholder -->
</div>
```

## 🔧 API del Componente

### Métodos Públicos

```typescript
// Referencia al componente
const microDashRef = ref();

// Expandir todas las secciones
microDashRef.value.expandAllSections();

// Colapsar todas las secciones
microDashRef.value.collapseAllSections();

// Toggle de una sección específica
microDashRef.value.toggleSection(sectionId);

// Verificar si una sección está colapsada
const isCollapsed = microDashRef.value.isSectionCollapsed(sectionId);
```

### Eventos Emitidos

```typescript
// Enlace clickeado
@linkClicked="(link) => console.log('Enlace clickeado:', link)"

// Sección toggled
@sectionToggled="(section, collapsed) => console.log('Sección:', section.title, 'Colapsada:', collapsed)"

// Enlace hover
@linkHovered="(link) => console.log('Enlace hover:', link.title)"
```

## 🎨 Personalización Avanzada

### CSS Custom Properties

```css
:root {
  --micro-dash-primary: #3b82f6;
  --micro-dash-hover: #dbeafe;
  --micro-dash-border: #e5e7eb;
  --micro-dash-text: #374151;
  --micro-dash-text-secondary: #6b7280;
}
```

### Estilos Personalizados

```vue
<style scoped>
/* Personalizar colores de hover */
.group:hover {
  background-color: var(--custom-hover-color) !important;
}

/* Personalizar transiciones */
.group {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Personalizar badges */
.badge-custom {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}
</style>
```

## 📱 Responsive Design

El componente se adapta automáticamente a diferentes tamaños de pantalla:

- **Desktop**: Layout completo con todas las funcionalidades
- **Tablet**: Ajustes de espaciado y tamaños de fuente
- **Mobile**: Modo compacto con scroll horizontal si es necesario

### Breakpoints

- `sm`: 640px - Ajustes de padding y espaciado
- `md`: 768px - Layout de grid para secciones
- `lg`: 1024px - Layout completo con hover effects

## 🚀 Integración con el Sistema

### 1. Configurar en Vite

```typescript
// vite.config.ts
rollupOptions: {
  input: {
    // ... otros componentes
    microDash: "./src/components/microdash/index.js",
  },
}
```

### 2. Incluir en panel.html

```html
<!-- Para páginas de inicio -->
{{#is_home}}
<script type="module" src="./src/components/microdash/index.js"></script>
{{/is_home}}

<!-- Para otras páginas -->
{{^is_home}}
<script type="module" src="./src/components/microdash/index.js"></script>
{{/is_home}}
```

### 3. Build del Componente

```bash
cd panel4-templates
npm run build
```

## 🧪 Testing y Verificación

### Logs de Consola

```
🚀 Inicializando MicroDash...
🔍 Encontrados 3 elementos MicroDash para montar
🏗️ Inicializando MicroDash micro-dash-1703123456789-abc123def con props: {...}
✅ Componente MicroDash micro-dash-1703123456789-abc123def montado correctamente
📌 Instancia 1/3 montada: micro-dash-1703123456789-abc123def
🎉 Total de instancias MicroDash montadas: 3
```

### Verificación Visual

- ✅ Componente se renderiza correctamente
- ✅ Enlaces son clickeables
- ✅ Secciones se pueden colapsar/expandir
- ✅ Hover effects funcionan
- ✅ Responsive en diferentes pantallas
- ✅ Múltiples instancias funcionan independientemente

## 🔮 Futuras Mejoras

### Funcionalidades Planificadas

- **Drag & Drop** - Reordenar enlaces y secciones
- **Temas personalizados** - Colores y estilos por usuario
- **Animaciones avanzadas** - Transiciones más elaboradas
- **Integración con API** - Cargar enlaces dinámicamente
- **Modo oscuro** - Tema dark/light
- **Accesibilidad** - Mejoras para lectores de pantalla

### Escalabilidad

- **Múltiples instancias** - ✅ Ya implementado
- **Configuración global** - Estilos por defecto del sistema
- **Plugins** - Sistema de extensiones
- **Templates** - Plantillas predefinidas

## 📚 Ejemplos Completos

### Dashboard de Administración

```html
<div
  data-micro-dash
  data-title="Panel de Administración"
  data-icon="shield-outline"
  data-ui-style="blue"
  data-sections='[
    {
      "id": "users",
      "title": "Usuarios",
      "icon": "people-outline",
      "collapsible": true,
      "links": [
        {"id": "list-users", "title": "Listar Usuarios", "url": "/admin/users", "icon": "list-outline"},
        {"id": "add-user", "title": "Crear Usuario", "url": "/admin/users/new", "icon": "person-add-outline", "badge": "Nuevo", "badgeColor": "green"},
        {"id": "roles", "title": "Gestionar Roles", "url": "/admin/roles", "icon": "key-outline"}
      ]
    },
    {
      "id": "system",
      "title": "Sistema",
      "icon": "settings-outline",
      "collapsible": true,
      "links": [
        {"id": "logs", "title": "Ver Logs", "url": "/admin/logs", "icon": "document-text-outline"},
        {"id": "backup", "title": "Backup", "url": "/admin/backup", "icon": "cloud-upload-outline", "badge": "Importante", "badgeColor": "red"}
      ]
    }
  ]'
>
  <!-- Loading placeholder -->
</div>
```

## 🤝 Contribución

Para contribuir al componente:

1. **Fork** del repositorio
2. **Crear** una rama para tu feature
3. **Implementar** los cambios
4. **Testear** en diferentes navegadores
5. **Crear** un Pull Request

## 📄 Licencia

Este componente es parte del sistema Mulbin y sigue las mismas políticas de licencia.

---

**Versión**: 1.0.0  
**Estado**: ✅ Listo para producción  
**Última actualización**: {{ fecha_actual }}
