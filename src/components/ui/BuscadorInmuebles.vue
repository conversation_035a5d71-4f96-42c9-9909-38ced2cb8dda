<template>
  <div class="buscador-inmuebles">
    <!-- Buscador principal -->
    <div class="relative">
      <input
        type="text"
        :placeholder="placeholder"
        :class="inputClasses"
        v-model="searchQuery"
        @input="onSearchInput"
      />
      <div
        class="flex absolute inset-y-0 left-0 items-center pl-4 pointer-events-none"
      >
        <ion-icon
          name="search-outline"
          class="text-xl text-gray-400"
        ></ion-icon>
      </div>

      <!-- Botón limpiar búsqueda -->
      <div
        v-if="searchQuery"
        class="flex absolute inset-y-0 right-0 items-center pr-4"
      >
        <button
          @click="limpiarBusqueda"
          class="p-1 text-gray-400 rounded-full hover:text-gray-600 hover:bg-gray-100"
          title="Limpiar búsqueda"
        >
          <ion-icon name="close-outline" class="text-lg"></ion-icon>
        </button>
      </div>
    </div>

    <!-- Contador de resultados (opcional) -->
    <div
      v-if="mostrarContador && totalResultados !== null"
      class="flex items-center justify-between mt-2 text-sm text-gray-600"
    >
      <span>
        {{ totalResultados }} {{ textoResultados }}
        <span v-if="searchQuery" class="font-medium">
          para "{{ searchQuery }}"
        </span>
      </span>

      <!-- Botón limpiar si hay búsqueda activa -->
      <button
        v-if="searchQuery"
        @click="limpiarBusqueda"
        class="px-2 py-1 text-xs text-gray-500 bg-gray-100 rounded hover:bg-gray-200"
      >
        Limpiar
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from "vue";

export default defineComponent({
  name: "BuscadorInmuebles",

  props: {
    // Valor del modelo (v-model)
    modelValue: {
      type: String,
      default: "",
    },

    // Texto del placeholder
    placeholder: {
      type: String,
      default: "¿Qué buscas? Casa, departamento, terreno...",
    },

    // Clases CSS personalizadas para el input
    inputClasses: {
      type: String,
      default:
        "py-3 pr-12 pl-12 w-full text-lg rounded-lg border border-gray-300 focus:ring-2 focus:ring-mulbin-500 focus:border-mulbin-500",
    },

    // Mostrar contador de resultados
    mostrarContador: {
      type: Boolean,
      default: false,
    },

    // Total de resultados encontrados
    totalResultados: {
      type: Number,
      default: null,
    },

    // Texto para describir los resultados
    textoResultados: {
      type: String,
      default: "resultados encontrados",
    },

    // Debounce delay en milisegundos
    debounceDelay: {
      type: Number,
      default: 300,
    },
  },

  emits: ["update:modelValue", "search", "clear"],

  setup(props, { emit }) {
    const searchQuery = ref(props.modelValue);
    let debounceTimeout: ReturnType<typeof setTimeout> | null = null;

    // Watcher para sincronizar con v-model
    watch(
      () => props.modelValue,
      (newValue) => {
        searchQuery.value = newValue;
      }
    );

    // Watcher para emitir cambios con debounce
    watch(searchQuery, (newValue) => {
      // Emitir inmediatamente para v-model
      emit("update:modelValue", newValue);

      // Emitir evento de búsqueda con debounce
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }

      debounceTimeout = setTimeout(() => {
        emit("search", newValue);
      }, props.debounceDelay);
    });

    // Manejar input de búsqueda
    const onSearchInput = () => {
      // El watcher ya maneja la lógica
    };

    // Limpiar búsqueda
    const limpiarBusqueda = () => {
      searchQuery.value = "";
      emit("clear");
    };

    return {
      searchQuery,
      onSearchInput,
      limpiarBusqueda,
    };
  },
});
</script>

<style scoped>
.buscador-inmuebles {
  @apply w-full;
}

/* Transiciones suaves */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Efectos hover */
button:hover {
  @apply transition-all;
}
</style>
