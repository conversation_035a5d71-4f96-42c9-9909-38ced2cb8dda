<template>
  <Teleport to="body">
    <Transition name="modal-backdrop" appear>
      <div
        v-if="modelValue"
        class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm"
        @click="handleBackdropClick"
      >
        <Transition name="modal-content" appear>
          <div
            v-if="modelValue"
            :class="[
              'relative w-full max-w-md mx-auto bg-white rounded-xl shadow-2xl',
              'transform transition-all duration-300 ease-out',
              sizeClasses,
            ]"
            @click.stop
          >
            <!-- Header -->
            <div
              v-if="title || showCloseButton"
              :class="[
                'flex items-center justify-between p-6 border-b border-gray-100',
                headerColorClasses,
              ]"
            >
              <div class="flex items-center space-x-3">
                <!-- Icono según el tipo -->
                <div
                  v-if="showIcon"
                  :class="[
                    'flex items-center justify-center w-10 h-10 rounded-full',
                    iconBackgroundClasses,
                  ]"
                >
                  <ion-icon
                    :name="iconName"
                    :class="['text-xl', iconColorClasses]"
                  ></ion-icon>
                </div>

                <div>
                  <h3
                    v-if="title"
                    :class="['text-lg font-semibold', titleColorClasses]"
                  >
                    {{ title }}
                  </h3>
                  <p v-if="subtitle" class="text-sm text-gray-500">
                    {{ subtitle }}
                  </p>
                </div>
              </div>

              <button
                v-if="showCloseButton"
                @click="close"
                class="flex items-center justify-center w-8 h-8 text-gray-400 transition-colors rounded-full hover:text-gray-600 hover:bg-gray-100"
              >
                <ion-icon name="close-outline" class="text-xl"></ion-icon>
              </button>
            </div>

            <!-- Content -->
            <div class="p-6">
              <div v-if="message" :class="['text-gray-700', messageClasses]">
                <p v-html="message"></p>
              </div>

              <!-- Slot para contenido personalizado -->
              <slot></slot>
            </div>

            <!-- Footer / Actions -->
            <div
              v-if="showActions"
              class="flex justify-end px-6 py-4 space-x-3 bg-gray-50 rounded-b-xl"
            >
              <!-- Botón cancelar -->
              <button
                v-if="showCancelButton"
                @click="cancel"
                :disabled="loading"
                class="px-4 py-2 text-sm font-medium text-gray-700 transition-colors bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50"
              >
                {{ cancelText }}
              </button>

              <!-- Botón confirmar -->
              <button
                v-if="showConfirmButton"
                @click="confirm"
                :disabled="loading"
                :class="[
                  'px-4 py-2 text-sm font-medium text-white transition-colors rounded-lg',
                  'focus:outline-none focus:ring-2 focus:ring-offset-2',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  'flex items-center space-x-2',
                  confirmButtonClasses,
                ]"
              >
                <ion-icon
                  v-if="loading"
                  name="reload-outline"
                  class="animate-spin"
                ></ion-icon>
                <span>{{ confirmText }}</span>
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, computed } from "vue";

export type ModalType = "info" | "success" | "warning" | "error" | "confirm";
export type ModalSize = "sm" | "md" | "lg" | "xl";

export default defineComponent({
  name: "BaseModal",

  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String as () => ModalType,
      default: "info",
    },
    size: {
      type: String as () => ModalSize,
      default: "md",
    },
    title: {
      type: String,
      default: "",
    },
    subtitle: {
      type: String,
      default: "",
    },
    message: {
      type: String,
      default: "",
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
    showCloseButton: {
      type: Boolean,
      default: true,
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    showCancelButton: {
      type: Boolean,
      default: true,
    },
    showConfirmButton: {
      type: Boolean,
      default: true,
    },
    cancelText: {
      type: String,
      default: "Cancelar",
    },
    confirmText: {
      type: String,
      default: "Aceptar",
    },
    loading: {
      type: Boolean,
      default: false,
    },
    closeOnBackdrop: {
      type: Boolean,
      default: true,
    },
  },

  emits: ["update:modelValue", "confirm", "cancel", "close"],

  setup(props, { emit }) {
    // Clases de tamaño
    const sizeClasses = computed(() => {
      switch (props.size) {
        case "sm":
          return "max-w-sm";
        case "md":
          return "max-w-md";
        case "lg":
          return "max-w-lg";
        case "xl":
          return "max-w-xl";
        default:
          return "max-w-md";
      }
    });

    // Configuración de iconos según el tipo
    const iconName = computed(() => {
      switch (props.type) {
        case "success":
          return "checkmark-circle";
        case "warning":
          return "warning";
        case "error":
          return "close-circle";
        case "confirm":
          return "help-circle";
        default:
          return "information-circle";
      }
    });

    // Clases de colores para el icono
    const iconColorClasses = computed(() => {
      switch (props.type) {
        case "success":
          return "text-green-600";
        case "warning":
          return "text-amber-600";
        case "error":
          return "text-red-600";
        case "confirm":
          return "text-blue-600";
        default:
          return "text-blue-600";
      }
    });

    // Clases de fondo para el icono
    const iconBackgroundClasses = computed(() => {
      switch (props.type) {
        case "success":
          return "bg-green-100";
        case "warning":
          return "bg-amber-100";
        case "error":
          return "bg-red-100";
        case "confirm":
          return "bg-blue-100";
        default:
          return "bg-blue-100";
      }
    });

    // Clases de color para el header
    const headerColorClasses = computed(() => {
      switch (props.type) {
        case "error":
          return "bg-red-50";
        case "warning":
          return "bg-amber-50";
        case "success":
          return "bg-green-50";
        default:
          return "";
      }
    });

    // Clases de color para el título
    const titleColorClasses = computed(() => {
      switch (props.type) {
        case "error":
          return "text-red-900";
        case "warning":
          return "text-amber-900";
        case "success":
          return "text-green-900";
        default:
          return "text-gray-900";
      }
    });

    // Clases para el mensaje
    const messageClasses = computed(() => {
      switch (props.type) {
        case "error":
          return "text-red-700";
        case "warning":
          return "text-amber-700";
        case "success":
          return "text-green-700";
        default:
          return "text-gray-700";
      }
    });

    // Clases para el botón de confirmar
    const confirmButtonClasses = computed(() => {
      switch (props.type) {
        case "success":
          return "bg-green-600 hover:bg-green-700 focus:ring-green-500";
        case "warning":
          return "bg-amber-600 hover:bg-amber-700 focus:ring-amber-500";
        case "error":
          return "bg-red-600 hover:bg-red-700 focus:ring-red-500";
        case "confirm":
          return "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500";
        default:
          return "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500";
      }
    });

    // Métodos
    const close = () => {
      emit("update:modelValue", false);
      emit("close");
    };

    const confirm = () => {
      emit("confirm");
      if (props.type !== "confirm") {
        close();
      }
    };

    const cancel = () => {
      emit("cancel");
      close();
    };

    const handleBackdropClick = () => {
      if (props.closeOnBackdrop) {
        close();
      }
    };

    return {
      sizeClasses,
      iconName,
      iconColorClasses,
      iconBackgroundClasses,
      headerColorClasses,
      titleColorClasses,
      messageClasses,
      confirmButtonClasses,
      close,
      confirm,
      cancel,
      handleBackdropClick,
    };
  },
});
</script>

<style scoped>
/* Transiciones para el backdrop */
.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
  transition: opacity 0.25s ease;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
  opacity: 0;
}

/* Transiciones para el contenido */
.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}
</style>
