<template>
  <div class="p-8 space-y-8 bg-gray-50 min-h-screen">
    <div class="mx-auto max-w-4xl">
      <h1 class="mb-8 text-3xl font-bold text-center text-gray-900">
        Sistema de Tooltips - Demostración
      </h1>

      <!-- Ejemplos usando el componente Tooltip -->
      <div class="p-6 bg-white rounded-lg shadow-md">
        <h2 class="mb-4 text-xl font-semibold text-gray-800">
          Componente Tooltip
        </h2>

        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          <!-- Tooltip básico -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">Tooltip Básico</h3>
            <Tooltip content="Este es un tooltip básico" placement="top">
              <button
                class="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
              >
                Hover para ver tooltip
              </button>
            </Tooltip>
          </div>

          <!-- Diferentes posiciones -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">Posiciones</h3>
            <div class="flex flex-wrap gap-2">
              <Tooltip content="Tooltip arriba" placement="top">
                <button
                  class="px-3 py-1 text-sm text-white bg-green-500 rounded"
                >
                  Top
                </button>
              </Tooltip>
              <Tooltip content="Tooltip abajo" placement="bottom">
                <button
                  class="px-3 py-1 text-sm text-white bg-green-500 rounded"
                >
                  Bottom
                </button>
              </Tooltip>
              <Tooltip content="Tooltip izquierda" placement="left">
                <button
                  class="px-3 py-1 text-sm text-white bg-green-500 rounded"
                >
                  Left
                </button>
              </Tooltip>
              <Tooltip content="Tooltip derecha" placement="right">
                <button
                  class="px-3 py-1 text-sm text-white bg-green-500 rounded"
                >
                  Right
                </button>
              </Tooltip>
            </div>
          </div>

          <!-- Diferentes temas -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">Temas</h3>
            <div class="flex flex-wrap gap-2">
              <Tooltip content="Tema oscuro (default)" theme="dark">
                <button
                  class="px-3 py-1 text-sm text-white bg-gray-800 rounded"
                >
                  Dark
                </button>
              </Tooltip>
              <Tooltip content="Tema claro" theme="light">
                <button
                  class="px-3 py-1 text-sm text-gray-800 bg-gray-200 rounded"
                >
                  Light
                </button>
              </Tooltip>
              <Tooltip content="Tema Mulbin" theme="mulbin">
                <button
                  class="px-3 py-1 text-sm text-white rounded bg-mulbin-600"
                >
                  Mulbin
                </button>
              </Tooltip>
            </div>
          </div>

          <!-- Diferentes triggers -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">Triggers</h3>
            <div class="flex flex-wrap gap-2">
              <Tooltip content="Aparece al hacer hover" trigger="hover">
                <button
                  class="px-3 py-1 text-sm text-white bg-purple-500 rounded"
                >
                  Hover
                </button>
              </Tooltip>
              <Tooltip content="Aparece al hacer focus" trigger="focus">
                <button
                  class="px-3 py-1 text-sm text-white bg-purple-500 rounded"
                >
                  Focus
                </button>
              </Tooltip>
              <Tooltip content="Aparece al hacer click" trigger="click">
                <button
                  class="px-3 py-1 text-sm text-white bg-purple-500 rounded"
                >
                  Click
                </button>
              </Tooltip>
            </div>
          </div>

          <!-- Sin flecha -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">Sin Flecha</h3>
            <Tooltip content="Este tooltip no tiene flecha" :arrow="false">
              <button
                class="px-4 py-2 text-white bg-red-500 rounded hover:bg-red-600"
              >
                Sin flecha
              </button>
            </Tooltip>
          </div>

          <!-- Contenido HTML -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">Contenido HTML</h3>
            <Tooltip
              content="<strong>Texto en negrita</strong><br><em>Texto en cursiva</em><br><span style='color: red;'>Texto rojo</span>"
              :is-html="true"
              :max-width="250"
            >
              <button
                class="px-4 py-2 text-white bg-indigo-500 rounded hover:bg-indigo-600"
              >
                HTML content
              </button>
            </Tooltip>
          </div>
        </div>
      </div>

      <!-- Ejemplos usando la directiva v-tooltip -->
      <div class="p-6 bg-white rounded-lg shadow-md">
        <h2 class="mb-4 text-xl font-semibold text-gray-800">
          Directiva v-tooltip
        </h2>

        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          <!-- Tooltip simple -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">Tooltip Simple</h3>
            <button
              v-tooltip="'Tooltip usando directiva'"
              class="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
            >
              Simple tooltip
            </button>
          </div>

          <!-- Con modificadores -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">Con Modificadores</h3>
            <div class="flex flex-wrap gap-2">
              <button
                v-tooltip.bottom="'Tooltip abajo'"
                class="px-3 py-1 text-sm text-white bg-green-500 rounded"
              >
                .bottom
              </button>
              <button
                v-tooltip.left.light="'Tooltip claro'"
                class="px-3 py-1 text-sm text-white bg-yellow-500 rounded"
              >
                .left.light
              </button>
              <button
                v-tooltip.right.mulbin="'Tooltip Mulbin'"
                class="px-3 py-1 text-sm text-white bg-pink-500 rounded"
              >
                .right.mulbin
              </button>
              <button
                v-tooltip.click="'Click para ver'"
                class="px-3 py-1 text-sm text-white bg-orange-500 rounded"
              >
                .click
              </button>
            </div>
          </div>

          <!-- HTML con directiva -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-2 font-medium text-gray-700">HTML con Directiva</h3>
            <button
              v-tooltip.html="'<strong>HTML</strong><br><em>en directiva</em>'"
              class="px-4 py-2 text-white bg-red-500 rounded hover:bg-red-600"
            >
              HTML tooltip
            </button>
          </div>
        </div>
      </div>

      <!-- Ejemplos prácticos -->
      <div class="p-6 bg-white rounded-lg shadow-md">
        <h2 class="mb-4 text-xl font-semibold text-gray-800">
          Ejemplos Prácticos
        </h2>

        <div class="space-y-4">
          <!-- Iconos con tooltips -->
          <div class="flex items-center space-x-4">
            <h3 class="font-medium text-gray-700">Iconos con tooltips:</h3>
            <Tooltip content="Editar" placement="top">
              <button class="p-2 text-gray-600 rounded hover:bg-gray-100">
                <ion-icon name="create-outline" class="text-lg"></ion-icon>
              </button>
            </Tooltip>
            <Tooltip content="Eliminar" placement="top" theme="light">
              <button class="p-2 text-red-600 rounded hover:bg-red-50">
                <ion-icon name="trash-outline" class="text-lg"></ion-icon>
              </button>
            </Tooltip>
            <Tooltip content="Compartir" placement="top" theme="mulbin">
              <button class="p-2 text-blue-600 rounded hover:bg-blue-50">
                <ion-icon
                  name="share-social-outline"
                  class="text-lg"
                ></ion-icon>
              </button>
            </Tooltip>
          </div>

          <!-- Badges con tooltips -->
          <div class="flex items-center space-x-4">
            <h3 class="font-medium text-gray-700">Badges informativos:</h3>
            <Tooltip content="Estado: Activo desde hace 2 días">
              <span
                class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full"
              >
                Activo
              </span>
            </Tooltip>
            <Tooltip content="Pendiente de aprobación del administrador">
              <span
                class="px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full"
              >
                Pendiente
              </span>
            </Tooltip>
            <Tooltip content="Deshabilitado por inactividad">
              <span
                class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full"
              >
                Inactivo
              </span>
            </Tooltip>
          </div>

          <!-- Formulario con tooltips de ayuda -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="mb-3 font-medium text-gray-700">
              Formulario con ayuda:
            </h3>
            <div class="space-y-3">
              <div class="flex items-center space-x-2">
                <label class="text-sm font-medium text-gray-700">Email:</label>
                <Tooltip
                  content="Ingresa tu email principal. Lo usaremos para notificaciones importantes."
                >
                  <ion-icon
                    name="help-circle-outline"
                    class="text-gray-400"
                  ></ion-icon>
                </Tooltip>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  class="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div class="flex items-center space-x-2">
                <label class="text-sm font-medium text-gray-700"
                  >Contraseña:</label
                >
                <Tooltip
                  content="Debe tener al menos 8 caracteres, incluir mayúsculas, minúsculas y números."
                  placement="right"
                  :max-width="200"
                >
                  <ion-icon
                    name="help-circle-outline"
                    class="text-gray-400"
                  ></ion-icon>
                </Tooltip>
                <input
                  type="password"
                  placeholder="••••••••"
                  class="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Control manual -->
      <div class="p-6 bg-white rounded-lg shadow-md">
        <h2 class="mb-4 text-xl font-semibold text-gray-800">Control Manual</h2>

        <div class="flex items-center space-x-4">
          <Tooltip
            ref="manualTooltip"
            content="Este tooltip se controla manualmente"
            trigger="manual"
            placement="top"
          >
            <button class="px-4 py-2 text-white bg-gray-600 rounded">
              Tooltip Manual
            </button>
          </Tooltip>

          <button
            @click="showManualTooltip"
            class="px-3 py-2 text-sm text-green-700 bg-green-100 rounded hover:bg-green-200"
          >
            Mostrar
          </button>

          <button
            @click="hideManualTooltip"
            class="px-3 py-2 text-sm text-red-700 bg-red-100 rounded hover:bg-red-200"
          >
            Ocultar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Tooltip from "./Tooltip.vue";

const manualTooltip = ref();

const showManualTooltip = () => {
  manualTooltip.value?.show();
};

const hideManualTooltip = () => {
  manualTooltip.value?.hide();
};
</script>
