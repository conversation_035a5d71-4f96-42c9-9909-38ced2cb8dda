<template>
  <div class="p-8 space-y-6">
    <h2 class="text-2xl font-bold text-gray-800">Ejemplo Rápido de Tooltips</h2>

    <div class="flex flex-wrap gap-4">
      <!-- Tooltip básico -->
      <Tooltip content="¡Hola! Soy un tooltip básico">
        <button
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Hover básico
        </button>
      </Tooltip>

      <!-- Tooltip con tema Mulbin -->
      <Tooltip
        content="Tooltip con tema Mulbin"
        theme="mulbin"
        placement="bottom"
      >
        <button
          class="px-4 py-2 bg-mulbin-500 text-white rounded hover:bg-mulbin-600"
        >
          Tema Mulbin
        </button>
      </Tooltip>

      <!-- Tooltip claro -->
      <Tooltip content="Tooltip con tema claro" theme="light" placement="left">
        <button
          class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
        >
          Tema claro
        </button>
      </Tooltip>

      <!-- Tooltip con click -->
      <Tooltip
        content="¡Haz click para ver este tooltip!"
        trigger="click"
        placement="right"
      >
        <button
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Click me
        </button>
      </Tooltip>
    </div>

    <div class="mt-8">
      <h3 class="text-lg font-semibold mb-4">Iconos con tooltips</h3>
      <div class="flex space-x-4">
        <Tooltip content="Configuración del sistema">
          <ion-icon
            name="settings-outline"
            class="text-2xl text-gray-600 cursor-pointer hover:text-gray-800"
          ></ion-icon>
        </Tooltip>

        <Tooltip content="Notificaciones (3 nuevas)" theme="mulbin">
          <ion-icon
            name="notifications-outline"
            class="text-2xl text-gray-600 cursor-pointer hover:text-gray-800"
          ></ion-icon>
        </Tooltip>

        <Tooltip content="Ayuda y soporte" theme="light">
          <ion-icon
            name="help-circle-outline"
            class="text-2xl text-gray-600 cursor-pointer hover:text-gray-800"
          ></ion-icon>
        </Tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Tooltip from "./Tooltip.vue";
</script>
