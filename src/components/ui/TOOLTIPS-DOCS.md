# Sistema de Tooltips - Documentación

## 📋 Descripción

Sistema moderno de tooltips para Vue 3 usando **Floating UI**, que reemplaza los tooltips nativos del navegador con una solución más potente y personalizable.

## 🚀 Características

- ✅ **Posicionamiento inteligente** - Se reposiciona automáticamente para permanecer visible
- ✅ **<PERSON><PERSON><PERSON><PERSON> triggers** - Hover, focus, click, o control manual
- ✅ **Temas personalizables** - Dark, light, y tema Mulbin
- ✅ **Contenido HTML** - Soporte para contenido rich text
- ✅ **Flechas direccionales** - Con posicionamiento automático
- ✅ **Accesibilidad** - Compatible con lectores de pantalla
- ✅ **TypeScript** - Completamente tipado
- ✅ **Responsive** - Se adapta a pantallas móviles

## 📦 Instalación

El sistema ya está instalado y configurado. Los componentes están disponibles en:

```typescript
import { Tooltip, useTooltip, vTooltipPlugin } from "../ui";
```

## 🎯 Uso Básico

### 1. Componente Tooltip

```vue
<template>
  <Tooltip content="Este es un tooltip básico">
    <button>Hover me</button>
  </Tooltip>
</template>
```

### 2. Directiva v-tooltip (Próximamente)

```vue
<template>
  <button v-tooltip="'Tooltip simple'">Click me</button>
</template>
```

## 🔧 API del Componente

### Props

| Prop        | Tipo                                        | Default   | Descripción                                      |
| ----------- | ------------------------------------------- | --------- | ------------------------------------------------ |
| `content`   | `string`                                    | `''`      | Contenido del tooltip                            |
| `placement` | `Placement`                                 | `'top'`   | Posición: `top`, `bottom`, `left`, `right`, etc. |
| `offset`    | `number`                                    | `8`       | Distancia del elemento trigger                   |
| `delay`     | `number`                                    | `300`     | Delay en ms para mostrar/ocultar                 |
| `disabled`  | `boolean`                                   | `false`   | Deshabilitar tooltip                             |
| `theme`     | `'dark' \| 'light' \| 'mulbin'`             | `'dark'`  | Tema visual                                      |
| `maxWidth`  | `number`                                    | `200`     | Ancho máximo en px                               |
| `arrow`     | `boolean`                                   | `true`    | Mostrar flecha                                   |
| `trigger`   | `'hover' \| 'focus' \| 'click' \| 'manual'` | `'hover'` | Evento que activa el tooltip                     |
| `isHtml`    | `boolean`                                   | `false`   | Permitir contenido HTML                          |

### Eventos

| Evento  | Descripción                           |
| ------- | ------------------------------------- |
| `@show` | Se emite cuando el tooltip se muestra |
| `@hide` | Se emite cuando el tooltip se oculta  |

### Métodos (via ref)

```vue
<template>
  <Tooltip ref="tooltipRef" content="Tooltip manual" trigger="manual">
    <button>Manual tooltip</button>
  </Tooltip>
  <button @click="tooltipRef.show()">Mostrar</button>
  <button @click="tooltipRef.hide()">Ocultar</button>
</template>

<script setup>
const tooltipRef = ref();
</script>
```

## 🎨 Ejemplos de Uso

### Posiciones

```vue
<template>
  <Tooltip content="Arriba" placement="top">
    <button>Top</button>
  </Tooltip>

  <Tooltip content="Abajo" placement="bottom">
    <button>Bottom</button>
  </Tooltip>

  <Tooltip content="Izquierda" placement="left">
    <button>Left</button>
  </Tooltip>

  <Tooltip content="Derecha" placement="right">
    <button>Right</button>
  </Tooltip>
</template>
```

### Temas

```vue
<template>
  <!-- Tema oscuro (default) -->
  <Tooltip content="Tema oscuro" theme="dark">
    <button>Dark</button>
  </Tooltip>

  <!-- Tema claro -->
  <Tooltip content="Tema claro" theme="light">
    <button>Light</button>
  </Tooltip>

  <!-- Tema Mulbin -->
  <Tooltip content="Tema Mulbin" theme="mulbin">
    <button>Mulbin</button>
  </Tooltip>
</template>
```

### Diferentes Triggers

```vue
<template>
  <!-- Hover (default) -->
  <Tooltip content="Aparece al hacer hover">
    <button>Hover</button>
  </Tooltip>

  <!-- Focus -->
  <Tooltip content="Aparece al hacer focus" trigger="focus">
    <input placeholder="Focus me" />
  </Tooltip>

  <!-- Click -->
  <Tooltip content="Aparece al hacer click" trigger="click">
    <button>Click</button>
  </Tooltip>
</template>
```

### Contenido HTML

```vue
<template>
  <Tooltip
    content="<strong>Texto en negrita</strong><br><em>Texto en cursiva</em>"
    :is-html="true"
    :max-width="250"
  >
    <button>HTML Content</button>
  </Tooltip>
</template>
```

### Tooltip Dinámico

```vue
<template>
  <Tooltip :content="dynamicContent" :theme="dynamicTheme">
    <button>Dynamic Tooltip</button>
  </Tooltip>
</template>

<script setup>
const dynamicContent = computed(
  () => `Estado: ${isActive.value ? "Activo" : "Inactivo"}`
);

const dynamicTheme = computed(() => (isActive.value ? "mulbin" : "light"));
</script>
```

## 🎯 Casos de Uso Comunes

### 1. Iconos con Información

```vue
<template>
  <div class="flex items-center space-x-2">
    <span>Configuración</span>
    <Tooltip content="Aquí puedes cambiar la configuración del sistema">
      <ion-icon name="help-circle-outline" class="text-gray-400"></ion-icon>
    </Tooltip>
  </div>
</template>
```

### 2. Botones de Acción

```vue
<template>
  <div class="flex space-x-2">
    <Tooltip content="Editar elemento">
      <button class="p-2 text-blue-600 hover:bg-blue-50 rounded">
        <ion-icon name="create-outline"></ion-icon>
      </button>
    </Tooltip>

    <Tooltip content="Eliminar elemento" theme="light">
      <button class="p-2 text-red-600 hover:bg-red-50 rounded">
        <ion-icon name="trash-outline"></ion-icon>
      </button>
    </Tooltip>
  </div>
</template>
```

### 3. Estados con Badges

```vue
<template>
  <div class="flex space-x-2">
    <Tooltip content="Proceso completado exitosamente">
      <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
        Completado
      </span>
    </Tooltip>

    <Tooltip content="Esperando aprobación del administrador">
      <span
        class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full"
      >
        Pendiente
      </span>
    </Tooltip>
  </div>
</template>
```

### 4. Formularios con Ayuda

```vue
<template>
  <div class="space-y-4">
    <div class="flex items-center space-x-2">
      <label>Email:</label>
      <Tooltip
        content="Ingresa tu email principal. Lo usaremos para notificaciones importantes."
        placement="right"
        :max-width="200"
      >
        <ion-icon name="help-circle-outline" class="text-gray-400"></ion-icon>
      </Tooltip>
      <input type="email" class="border rounded px-3 py-1" />
    </div>
  </div>
</template>
```

## 🔨 Composable useTooltip

Para casos avanzados, puedes usar directamente el composable:

```vue
<template>
  <div>
    <button
      ref="reference"
      @mouseenter="show('Mi tooltip personalizado')"
      @mouseleave="hide"
    >
      Custom Tooltip
    </button>

    <div
      v-if="isVisible"
      ref="floating"
      :style="floatingStyles"
      :class="tooltipClasses"
    >
      {{ content }}
    </div>
  </div>
</template>

<script setup>
import { useTooltip } from "../composables/useTooltip";

const {
  reference,
  floating,
  isVisible,
  content,
  floatingStyles,
  tooltipClasses,
  show,
  hide,
} = useTooltip({
  theme: "mulbin",
  placement: "top",
});
</script>
```

## 🎨 Personalización de Estilos

### Temas Personalizados

Puedes extender los temas existentes modificando el composable `useTooltip.ts`:

```typescript
const themeClasses = {
  dark: "bg-gray-900 text-white border border-gray-700",
  light: "bg-white text-gray-900 border border-gray-200",
  mulbin: "bg-mulbin-600 text-white border border-mulbin-700",
  // Agregar tema personalizado
  custom: "bg-purple-600 text-white border border-purple-700",
};
```

## 🔄 Migración desde Tooltips Nativos

### Antes (tooltip nativo)

```vue
<button title="Opciones de contacto">
  <ion-icon name="menu"></ion-icon>
</button>
```

### Después (nuevo sistema)

```vue
<Tooltip content="Opciones de contacto" theme="mulbin">
  <button>
    <ion-icon name="menu"></ion-icon>
  </button>
</Tooltip>
```

## 🐛 Troubleshooting

### Tooltip no aparece

- Verifica que el contenido no esté vacío
- Asegúrate de que `disabled` no esté en `true`
- Revisa que el trigger sea el correcto

### Posicionamiento incorrecto

- Floating UI se encarga automáticamente del posicionamiento
- Si hay problemas, verifica que no haya CSS que interfiera con `z-index`

### Performance

- Los tooltips usan `Teleport` para renderizar en `body`
- Se optimizan automáticamente con `autoUpdate`
- No hay límite en la cantidad de tooltips por página

## 📱 Responsive

Los tooltips se adaptan automáticamente a pantallas pequeñas:

- Ajustan su posición para permanecer visibles
- Reducen su `maxWidth` en móviles
- Mantienen la accesibilidad en dispositivos táctiles

## ♿ Accesibilidad

- Compatible con lectores de pantalla
- Navegación por teclado funcional
- Atributos ARIA apropiados
- Contraste adecuado en todos los temas

## 🚀 Próximas Funcionalidades

- [ ] Directiva `v-tooltip` completa
- [ ] Animaciones personalizables
- [ ] Tooltip con contenido de componente Vue
- [ ] Tooltip sticky/persistente
- [ ] Integración con sistema de notificaciones

---

**¿Necesitas ayuda?** Consulta los ejemplos en `TooltipDemo.vue` o revisa la implementación en los componentes existentes.
