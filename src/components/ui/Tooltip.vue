<template>
  <div class="tooltip-wrapper">
    <!-- Elemento trigger (slot por defecto) -->
    <div
      ref="reference"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @focus="handleFocus"
      @blur="handleBlur"
      @click="handleClick"
      class="inline-block"
    >
      <slot />
    </div>

    <!-- Tooltip flotante -->
    <Teleport to="body">
      <div
        v-if="isVisible && content"
        ref="floating"
        :style="{ ...floatingStyles, maxWidth: `${maxWidth}px` }"
        :class="[
          tooltipClasses,
          { 'opacity-0': !isVisible, 'opacity-100': isVisible },
        ]"
      >
        <!-- Contenido del tooltip -->
        <div v-if="!isHtml" class="whitespace-pre-wrap">{{ content }}</div>
        <div v-else v-html="content"></div>

        <!-- Flecha -->
        <svg
          v-if="arrow && isVisible"
          ref="arrowRef"
          :style="arrowStyles"
          :class="arrowClasses"
          width="8"
          height="8"
          viewBox="0 0 8 8"
        >
          <path d="M0,0 L4,4 L8,0 Z" />
        </svg>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { onUnmounted, watch } from "vue";
import { useTooltip } from "../../composables/useTooltip";
import type { Placement } from "@floating-ui/vue";

export interface TooltipProps {
  content?: string;
  placement?: Placement;
  offset?: number;
  delay?: number;
  disabled?: boolean;
  theme?: "dark" | "light" | "mulbin";
  maxWidth?: number;
  arrow?: boolean;
  trigger?: "hover" | "focus" | "click" | "manual";
  isHtml?: boolean;
}

const props = withDefaults(defineProps<TooltipProps>(), {
  content: "",
  placement: "top",
  offset: 8,
  delay: 300,
  disabled: false,
  theme: "dark",
  maxWidth: 200,
  arrow: true,
  trigger: "hover",
  isHtml: false,
});

const emit = defineEmits<{
  show: [];
  hide: [];
}>();

// Usar el composable de tooltip
const {
  reference,
  floating,
  arrowRef,
  isVisible,
  content,
  floatingStyles,
  arrowStyles,
  tooltipClasses,
  arrowClasses,
  maxWidth,
  show,
  hide,
  cleanup,
} = useTooltip({
  placement: props.placement,
  offset: props.offset,
  delay: props.delay,
  disabled: props.disabled,
  theme: props.theme,
  maxWidth: props.maxWidth,
  arrow: props.arrow,
});

// Manejadores de eventos
const handleMouseEnter = () => {
  if (props.trigger === "hover" && props.content) {
    show(props.content);
    emit("show");
  }
};

const handleMouseLeave = () => {
  if (props.trigger === "hover") {
    hide();
    emit("hide");
  }
};

const handleFocus = () => {
  if (props.trigger === "focus" && props.content) {
    show(props.content);
    emit("show");
  }
};

const handleBlur = () => {
  if (props.trigger === "focus") {
    hide();
    emit("hide");
  }
};

const handleClick = () => {
  if (props.trigger === "click" && props.content) {
    if (isVisible.value) {
      hide();
      emit("hide");
    } else {
      show(props.content);
      emit("show");
    }
  }
};

// Métodos públicos para control manual
const showTooltip = (text?: string) => {
  show(text || props.content);
  emit("show");
};

const hideTooltip = () => {
  hide();
  emit("hide");
};

// Watcher para actualizar contenido cuando cambie la prop
watch(
  () => props.content,
  (newContent) => {
    if (isVisible.value && newContent) {
      content.value = newContent;
    }
  }
);

// Limpiar al desmontar
onUnmounted(() => {
  cleanup();
});

// Exponer métodos para uso con ref
defineExpose({
  show: showTooltip,
  hide: hideTooltip,
  isVisible,
});
</script>

<style scoped>
.tooltip-wrapper {
  display: inline-block;
}
</style>
