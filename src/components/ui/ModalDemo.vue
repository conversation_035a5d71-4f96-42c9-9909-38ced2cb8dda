<template>
  <div class="p-6 space-y-4 bg-white rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold text-gray-900">Sistema de Modales</h2>
    <p class="text-gray-600">
      Demostración del sistema de modales reutilizable
    </p>

    <!-- Botones de demostración -->
    <div class="grid grid-cols-2 gap-4 md:grid-cols-3">
      <!-- Modal de información -->
      <button
        @click="showInfoModal"
        class="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
      >
        <ion-icon name="information-circle-outline" class="mr-2"></ion-icon>
        Información
      </button>

      <!-- Modal de éxito -->
      <button
        @click="showSuccessModal"
        class="px-4 py-2 text-white bg-green-600 rounded-lg hover:bg-green-700"
      >
        <ion-icon name="checkmark-circle-outline" class="mr-2"></ion-icon>
        Éxito
      </button>

      <!-- Modal de advertencia -->
      <button
        @click="showWarningModal"
        class="px-4 py-2 text-white bg-amber-600 rounded-lg hover:bg-amber-700"
      >
        <ion-icon name="warning-outline" class="mr-2"></ion-icon>
        Advertencia
      </button>

      <!-- Modal de error -->
      <button
        @click="showErrorModal"
        class="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700"
      >
        <ion-icon name="close-circle-outline" class="mr-2"></ion-icon>
        Error
      </button>

      <!-- Modal de confirmación -->
      <button
        @click="showConfirmModal"
        class="px-4 py-2 text-white bg-purple-600 rounded-lg hover:bg-purple-700"
      >
        <ion-icon name="help-circle-outline" class="mr-2"></ion-icon>
        Confirmación
      </button>

      <!-- Modal con loading -->
      <button
        @click="showLoadingModal"
        class="px-4 py-2 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700"
      >
        <ion-icon name="reload-outline" class="mr-2"></ion-icon>
        Con Loading
      </button>
    </div>

    <!-- Ejemplos de uso en código -->
    <div class="mt-8">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Ejemplos de uso</h3>

      <div class="space-y-4">
        <div class="p-4 bg-gray-50 rounded-lg">
          <h4 class="font-medium text-gray-900 mb-2">
            Modal de información simple
          </h4>
          <pre
            class="text-sm text-gray-700 overflow-x-auto"
          ><code>import { useModal } from '@/components/ui'

const { alert } = useModal()

// Mostrar modal de información
alert('Este es un mensaje informativo', 'Información')</code></pre>
        </div>

        <div class="p-4 bg-gray-50 rounded-lg">
          <h4 class="font-medium text-gray-900 mb-2">Modal de confirmación</h4>
          <pre
            class="text-sm text-gray-700 overflow-x-auto"
          ><code>import { useModal } from '@/components/ui'

const { confirm } = useModal()

// Mostrar modal de confirmación
confirm(
  '¿Estás seguro de eliminar este elemento?',
  async () => {
    // Lógica cuando el usuario confirma
    await eliminarElemento()
  },
  'Confirmar eliminación'
)</code></pre>
        </div>

        <div class="p-4 bg-gray-50 rounded-lg">
          <h4 class="font-medium text-gray-900 mb-2">Modal de éxito/error</h4>
          <pre
            class="text-sm text-gray-700 overflow-x-auto"
          ><code>import { useModal } from '@/components/ui'

const { success, error } = useModal()

try {
  await operacionAsincrona()
  success('Operación completada exitosamente', '¡Perfecto!')
} catch (err) {
  error('Ocurrió un error inesperado', 'Error')
}</code></pre>
        </div>
      </div>
    </div>

    <!-- Modal Global (debe estar en el componente raíz) -->
    <GlobalModal />
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { useModal, GlobalModal } from "./index";

export default defineComponent({
  name: "ModalDemo",

  components: {
    GlobalModal,
  },

  setup() {
    const { alert, success, warning, error, confirm } = useModal();

    const showInfoModal = () => {
      alert(
        "Este es un modal informativo que muestra información importante al usuario.",
        "Información"
      );
    };

    const showSuccessModal = () => {
      success(
        "La operación se completó exitosamente. Todo funcionó como se esperaba.",
        "¡Éxito!"
      );
    };

    const showWarningModal = () => {
      warning(
        "Ten cuidado con esta acción. Asegúrate de que realmente quieres continuar.",
        "Advertencia"
      );
    };

    const showErrorModal = () => {
      error(
        "Ocurrió un error inesperado. Por favor, inténtalo de nuevo más tarde.",
        "Error"
      );
    };

    const showConfirmModal = () => {
      confirm(
        "¿Estás seguro de que quieres realizar esta acción? Esta operación no se puede deshacer.",
        () => {
          success(
            "¡Confirmado! La acción se ejecutó correctamente.",
            "¡Listo!"
          );
        },
        "Confirmar acción"
      );
    };

    const showLoadingModal = () => {
      confirm(
        "Esta operación tomará unos segundos. ¿Deseas continuar?",
        async () => {
          // Simular operación asíncrona
          await new Promise((resolve) => setTimeout(resolve, 3000));
          success("Operación completada con éxito", "¡Terminado!");
        },
        "Operación con loading"
      );
    };

    return {
      showInfoModal,
      showSuccessModal,
      showWarningModal,
      showErrorModal,
      showConfirmModal,
      showLoadingModal,
    };
  },
});
</script>
