# 📢 Siste<PERSON> de Modales, Alertas y Confirmaciones Personalizadas

## 1. ¿Qué es?

El sistema de modales de `panel4-templates` reemplaza el uso de `window.alert()` y `window.confirm()` por **modales visuales centralizados**, accesibles desde cualquier componente Vue. Esto permite una experiencia de usuario moderna, consistente y personalizable.

---

## 2. ¿Cómo se usa?

### a) Importar el composable

```ts
import { useModal } from "@/components/ui";
const { alert, confirm, success, error, warning } = useModal();
```

### b) Mostrar una alerta informativa

```ts
alert("Este es un mensaje informativo", "Título opcional");
```

### c) Mostrar una confirmación

```ts
confirm(
  "¿Estás seguro de eliminar este elemento?",
  async () => {
    // Lógica cuando el usuario confirma
    await eliminar<PERSON>lement<PERSON>();
  },
  "Confirmar eliminación" // Título opcional
);
```

### d) Mostrar mensajes de éxito o error

```ts
success("Operación completada exitosamente", "¡Perfecto!");
error("Ocurrió un error inesperado", "Error");
```

### e) Ejemplo real de confirmación en favoritos

En `InmueblesFavoritos.vue`:

```ts
showConfirm(
  "¿Estás seguro de que quieres limpiar todos los favoritos?",
  () => {
    favoritosService.limpiarFavoritos();
    inmueblesFavoritos.value = [];
    showSuccess("Todos los favoritos han sido eliminados.", "¡Listo!");
  },
  "Confirmar eliminación"
);
```

---

## 3. ¿Cómo funciona internamente?

- El composable `useModal.ts` expone funciones (`alert`, `confirm`, `success`, `error`, `warning`) que abren un modal global.
- El componente `GlobalModal.vue` debe estar presente en el árbol raíz (usualmente en el layout principal).
- El modal es altamente configurable: íconos, colores, textos, loading, callbacks, etc.
- El sistema es reactivo y soporta operaciones asíncronas en la confirmación.

---

## 4. Ventajas

- **Consistencia visual**: Todos los mensajes y confirmaciones siguen el mismo diseño.
- **Personalización**: Puedes cambiar títulos, textos, colores, íconos y acciones.
- **Soporte asíncrono**: Permite operaciones que requieren espera (ej. llamadas a API).
- **Accesibilidad**: Mejor experiencia para usuarios móviles y de escritorio.

---

## 5. Buenas prácticas

- **Evita** el uso de `window.alert()` y `window.confirm()` en nuevos desarrollos.
- Usa siempre el sistema de modales para mensajes al usuario y confirmaciones de acciones importantes.
- Si necesitas un modal personalizado, puedes usar directamente `showModal` desde el composable.

---

## 6. Ejemplo visual y de código

Consulta el componente [`ModalDemo.vue`](./ModalDemo.vue) para ver ejemplos interactivos y fragmentos de código listos para copiar.

---
