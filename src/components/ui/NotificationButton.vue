<template>
  <button
    @click="handleClick"
    :class="[
      'flex items-center px-3 py-1 text-sm font-medium rounded-full relative',
      isActive
        ? activeClasses
        : inactiveClasses,
    ]"
  >
    <ion-icon :name="icon" class="mr-1"></ion-icon>
    <span class="hidden sm:block">{{ label }}</span>

    <!-- Badge de notificaciones -->
    <div
      v-if="notificationCount > 0"
      class="absolute -top-2 -right-2 flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold text-white bg-red-500 rounded-full border-2 border-white shadow-sm"
    >
      {{ notificationCount > 99 ? "99+" : notificationCount }}
    </div>
  </button>
</template>

<script setup lang="ts">
interface Props {
  icon: string;
  label: string;
  notificationCount?: number;
  isActive?: boolean;
  activeClasses?: string;
  inactiveClasses?: string;
}

const props = withDefaults(defineProps<Props>(), {
  notificationCount: 0,
  isActive: false,
  activeClasses: 'bg-white text-mulbin-600',
  inactiveClasses: 'bg-mulbin-600 text-white hover:bg-mulbin-700',
});

const emit = defineEmits<{
  click: [];
}>();

const handleClick = () => {
  emit('click');
};
</script>

<script lang="ts">
export default {
  name: 'NotificationButton'
};
</script>