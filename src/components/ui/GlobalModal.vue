<template>
  <BaseModal
    v-model="modalState.show"
    :type="modalState.type"
    :size="modalState.size"
    :title="modalState.title"
    :subtitle="modalState.subtitle"
    :message="modalState.message"
    :show-icon="modalState.showIcon"
    :show-close-button="modalState.showCloseButton"
    :show-actions="modalState.showActions"
    :show-cancel-button="modalState.showCancelButton"
    :show-confirm-button="modalState.showConfirmButton"
    :cancel-text="modalState.cancelText"
    :confirm-text="modalState.confirmText"
    :loading="modalState.loading"
    :close-on-backdrop="modalState.closeOnBackdrop"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    @close="closeModal"
  >
    <!-- Slot para contenido personalizado si es necesario -->
    <slot></slot>
  </BaseModal>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import BaseModal from "./BaseModal.vue";
import { useModal } from "../../composables/useModal";

export default defineComponent({
  name: "GlobalModal",

  components: {
    BaseModal,
  },

  setup() {
    const { modalState, handleConfirm, handleCancel, closeModal } = useModal();

    return {
      modalState,
      handleConfirm,
      handleCancel,
      closeModal,
    };
  },
});
</script>
