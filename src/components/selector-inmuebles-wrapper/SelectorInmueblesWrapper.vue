<template>
  <SelectorInmuebles
    :initial-has-inmueble="hasInmueble"
    :initial-selected-inmueble="selectedInmueble"
    :external-inmuebles="adaptedInmuebles"
    :auto-load="false"
    :textos-inmueble-seleccionado="textosInmuebleSeleccionado"
    :textos-publicacion-general="textosPublicacionGeneral"
    @update:hasInmueble="$emit('update:hasInmueble', $event)"
    @update:selectedInmueble="$emit('update:selectedInmueble', $event)"
    @add-inmueble="$emit('add-inmueble')"
  />
</template>

<script lang="ts">
import { defineComponent, computed } from "vue";
import SelectorInmuebles from "./SelectorInmuebles.vue";

interface Property {
  id?: number;
  key: string;
  name: string;
  image: string;
  operacion?: string;
  precio?: number;
  moneda?: string;
  tipo?: string;
  colonia?: string;
  ciudad?: string; // 🆕 NUEVO: Ciudad del inmueble
  imagenes?: string[]; // 🆕 NUEVO: Array de todas las imágenes del inmueble
}

export default defineComponent({
  name: "SelectorInmueblesWrapper",

  components: {
    SelectorInmuebles,
  },

  props: {
    hasInmueble: {
      type: Boolean,
      required: true,
    },
    selectedInmueble: {
      type: Object as () => Property | null,
      default: null,
    },
    availableInmuebles: {
      type: Array as () => any[],
      required: true,
    },
    textosInmuebleSeleccionado: {
      type: Object,
      default: () => ({
        titulo: "Publicación con inmueble",
        descripcion:
          "Esta publicación estará relacionada con uno de tus inmuebles",
      }),
    },
    textosPublicacionGeneral: {
      type: Object,
      default: () => ({
        titulo: "Publicación general",
        descripcion: "",
      }),
    },
  },

  emits: ["update:hasInmueble", "update:selectedInmueble", "add-inmueble"],

  setup(props) {
    // Función utilitaria para optimizar URLs de imágenes
    const optimizeImageUrl = (imageUrl: string): string => {
      if (!imageUrl || typeof imageUrl !== "string") {
        return "/images/placeholder-property.jpg";
      }

      // Reemplazar /alta/ por /peque/ para optimizar el tamaño
      return imageUrl.replace("/alta/", "/peque/");
    };

    // Adaptar la estructura de datos de inmuebles a la esperada por SelectorInmuebles
    const adaptedInmuebles = computed(() => {
      const result = props.availableInmuebles.map((inmueble) => ({
        id: inmueble.id,
        key: inmueble.key || inmueble.clave,
        name: inmueble.name || inmueble.titulo || inmueble.nombre,
        image: optimizeImageUrl(
          inmueble.image ||
            inmueble.imagenPrincipal ||
            "/images/placeholder-property.jpg"
        ),
        operacion: inmueble.operacion,
        precio: inmueble.precio,
        moneda: inmueble.moneda,
        tipo: inmueble.tipo,
        colonia: inmueble.colonia,
        ciudad: inmueble.ciudad, // 🆕 NUEVO: Pasar ciudad del inmueble
        // 🆕 NUEVO: Pasar todas las imágenes del inmueble
        imagenes: inmueble.imagenes || [],
      }));

      return result;
    });

    console.log("📋 Props para SelectorInmueblesWrapper:", {
      hasInmueble: props.hasInmueble,
      selectedInmueble: props.selectedInmueble,
      availableInmuebles: props.availableInmuebles.length + " inmuebles",
    });

    return {
      adaptedInmuebles,
    };
  },
});
</script>
