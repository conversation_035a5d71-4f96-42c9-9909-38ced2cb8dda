<template>
  <div class="mt-2 selector-inmuebles-container">
    <!-- Toggle público/privado para inmuebles -->
    <div class="mb-2">
      <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
        <div class="flex items-center">
          <ion-icon
            :name="hasSelectedInmueble ? 'home' : 'home-outline'"
            class="visible mr-2 text-lg"
            :class="hasSelectedInmueble ? 'text-mulbin-600' : 'text-gray-400'"
          ></ion-icon>
          <span class="text-sm font-medium text-gray-700">
            {{
              hasSelectedInmueble
                ? "Publicación con inmueble"
                : "Publicar con inmueble"
            }}
          </span>
        </div>
        <label class="inline-flex relative items-center cursor-pointer">
          <input
            type="checkbox"
            v-model="hasSelectedInmueble"
            class="sr-only peer"
            @change="handleToggleInmueble"
          />
          <div
            class="w-11 h-6 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer bg-gray-200 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-mulbin-600"
          ></div>
        </label>
      </div>
      <!-- Descripción del modo seleccionado -->
      <div class="text-xs text-gray-500">
        <span v-if="hasSelectedInmueble">
          {{ textosInmuebleSeleccionado.descripcion }}
        </span>
        <span v-else>
          {{ textosPublicacionGeneral.descripcion }}
        </span>
      </div>
    </div>

    <!-- Selector de inmueble (visible solo cuando está habilitado) -->
    <div v-if="hasSelectedInmueble" class="space-y-3">
      <!-- Buscador de inmuebles (solo visible cuando no hay inmueble seleccionado) -->
      <div v-if="!selectedInmueble" class="relative">
        <input
          ref="searchInput"
          v-model="searchQuery"
          type="text"
          placeholder="Buscar por clave o nombre del inmueble..."
          class="px-3 py-2 pr-10 w-full rounded border border-gray-300 focus:ring-2 focus:ring-mulbin-500 focus:border-mulbin-500"
          @input="handleSearch"
          @focus="showDropdown = true"
          @keydown="handleKeydown"
        />
        <div
          class="flex absolute inset-y-0 right-0 items-center pr-3 pointer-events-none"
        >
          <ion-icon
            v-if="searchLoading"
            name="sync-outline"
            class="text-gray-400 animate-spin"
          ></ion-icon>
          <ion-icon
            v-else
            name="search-outline"
            class="text-gray-400"
          ></ion-icon>
        </div>
      </div>

      <!-- Dropdown con resultados (solo visible cuando no hay inmueble seleccionado) -->
      <div
        v-if="
          !selectedInmueble &&
          showDropdown &&
          (filteredInmuebles.length || searchQuery)
        "
        class="relative z-10"
      >
        <div
          class="overflow-hidden absolute w-full max-h-60 bg-white rounded-md border border-gray-300 shadow-lg"
        >
          <!-- Resultados de búsqueda -->
          <div v-if="filteredInmuebles.length" class="py-1">
            <div
              v-for="(inmueble, index) in filteredInmuebles"
              :key="inmueble.key"
              class="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-50"
              :class="{ 'bg-blue-50': index === highlightedIndex }"
              @click="selectInmueble(inmueble)"
            >
              <div class="flex-shrink-0 mr-3">
                <img
                  :src="inmueble.image || '/images/placeholder-property.jpg'"
                  :alt="inmueble.name"
                  class="object-cover w-10 h-10 rounded"
                  @error="handleImageError"
                />
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 truncate">
                  {{ inmueble.name }}
                </div>
                <div class="text-xs text-gray-500 truncate">
                  Clave: {{ inmueble.key }}
                </div>
                <div class="flex items-center mt-1 space-x-2">
                  <!-- Promoción -->
                  <span
                    v-if="inmueble.operacion"
                    class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-mulbin-100 text-mulbin-800"
                  >
                    {{ inmueble.operacion }}
                  </span>
                  <!-- Precio -->
                  <span
                    v-if="inmueble.precio"
                    class="font-mono text-xs text-gray-500"
                  >
                    ${{ formatPrice(inmueble.precio) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Sin resultados -->
          <div
            v-else-if="searchQuery && !searchLoading"
            class="px-3 py-2 text-sm text-gray-500"
          >
            No se encontraron inmuebles con "{{ searchQuery }}"
          </div>

          <!-- Cargando -->
          <div
            v-else-if="searchLoading"
            class="px-3 py-2 text-sm text-gray-500"
          >
            <div class="flex items-center">
              <ion-icon
                name="sync-outline"
                class="mr-2 animate-spin"
              ></ion-icon>
              Buscando inmuebles...
            </div>
          </div>
        </div>
      </div>

      <!-- Inmueble seleccionado -->
      <div
        v-if="selectedInmueble"
        class="p-3 bg-blue-50 rounded-lg border border-blue-200"
      >
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <div class="flex-shrink-0 mr-3">
              <img
                :src="
                  selectedInmueble.image || '/images/placeholder-property.jpg'
                "
                :alt="selectedInmueble.name"
                class="object-cover w-12 h-12 rounded"
                @error="handleImageError"
              />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-gray-900">
                {{ selectedInmueble.name }}
              </div>
              <div class="text-xs text-gray-500">
                Clave: {{ selectedInmueble.key }}
              </div>
              <div class="flex items-center mt-1 space-x-2">
                <!-- Promoción -->
                <span
                  v-if="selectedInmueble.operacion"
                  class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-mulbin-100 text-mulbin-800"
                >
                  {{ selectedInmueble.operacion }}
                </span>
                <!-- Precio -->
                <span
                  v-if="selectedInmueble.precio"
                  class="font-mono text-xs text-gray-500"
                >
                  ${{ formatPrice(selectedInmueble.precio) }}
                </span>
              </div>
            </div>
          </div>
          <button
            @click="clearSelection"
            class="p-1 text-gray-400 hover:text-gray-600"
            title="Quitar inmueble"
          >
            <ion-icon
              name="close-outline"
              size="small"
              class="p-0.5 rounded-lg text-mulbin-400 bg-mulbin-50 hover:bg-mulbin-100"
            ></ion-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted } from "vue";

interface Inmueble {
  id?: number;
  key: string;
  name: string;
  image: string;
  operacion?: string; // venta, renta, traspaso
  precio?: number;
  moneda?: string;
  tipo?: string; // casa, departamento, terreno, etc.
  colonia?: string;
  ciudad?: string; // 🆕 NUEVO: Ciudad del inmueble
  imagenes?: string[]; // 🆕 NUEVO: Array de todas las imágenes del inmueble
}

export default defineComponent({
  name: "SelectorInmuebles",

  props: {
    initialHasInmueble: {
      type: Boolean,
      default: false,
    },
    initialSelectedInmueble: {
      type: Object as () => Inmueble | null,
      default: null,
    },
    externalInmuebles: {
      type: Array as () => Inmueble[],
      default: () => [],
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    textosInmuebleSeleccionado: {
      type: Object,
      default: () => ({
        titulo: "Publicación con inmueble",
        descripcion:
          "Esta publicación estará relacionada con uno de tus inmuebles",
      }),
    },
    textosPublicacionGeneral: {
      type: Object,
      default: () => ({
        titulo: "Publicación general",
        descripcion: "",
      }),
    },
  },

  emits: [
    "update:hasInmueble",
    "update:selectedInmueble",
    "inmueble-change",
    "add-inmueble",
  ],

  setup(props, { emit }) {
    // Estados del componente
    const hasSelectedInmueble = ref(props.initialHasInmueble);
    const selectedInmueble = ref<Inmueble | null>(
      props.initialSelectedInmueble
    );
    const availableInmuebles = ref<Inmueble[]>([]);
    const searchQuery = ref("");
    const showDropdown = ref(false);
    const searchLoading = ref(false);
    const highlightedIndex = ref(-1);

    // Referencias del DOM
    const searchInput = ref<HTMLInputElement>();

    // Función utilitaria para optimizar URLs de imágenes
    const optimizeImageUrl = (imageUrl: string): string => {
      if (!imageUrl || typeof imageUrl !== "string") {
        return "/images/placeholder-property.jpg";
      }

      // Reemplazar /alta/ por /peque/ para optimizar el tamaño
      const optimizedUrl = imageUrl.replace("/alta/", "/peque/");

      console.log("🖼️ Imagen optimizada:", {
        original: imageUrl,
        optimized: optimizedUrl,
        wasOptimized: imageUrl !== optimizedUrl,
      });

      return optimizedUrl;
    };

    // Función para formatear precio con separadores de miles
    const formatPrice = (precio: number): string => {
      if (!precio) return "";
      return precio.toLocaleString("es-MX");
    };

    // Computed para inmuebles filtrados
    const filteredInmuebles = computed(() => {
      if (!searchQuery.value.trim()) {
        return availableInmuebles.value.slice(0, 10); // Mostrar primeros 10
      }

      const searchTerm = searchQuery.value.toLowerCase().trim();
      return availableInmuebles.value
        .filter((inmueble) => {
          const name = inmueble.name?.toLowerCase() || "";
          const key = inmueble.key?.toLowerCase() || "";
          return name.includes(searchTerm) || key.includes(searchTerm);
        })
        .slice(0, 10); // Limitar resultados
    });

    // Manejar toggle de inmueble
    const handleToggleInmueble = () => {
      if (!hasSelectedInmueble.value) {
        // Si se desactiva, limpiar selección
        selectedInmueble.value = null;
        searchQuery.value = "";
        showDropdown.value = false;
      }
      emit("update:hasInmueble", hasSelectedInmueble.value);
      emit("inmueble-change", {
        hasInmueble: hasSelectedInmueble.value,
        selectedInmueble: selectedInmueble.value,
      });
    };

    // Manejar búsqueda
    let searchTimeout: ReturnType<typeof setTimeout> | null = null;
    const handleSearch = () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      searchTimeout = setTimeout(() => {
        if (searchQuery.value.trim() && availableInmuebles.value.length === 0) {
          loadInmuebles();
        }
      }, 300);
    };

    // Cargar inmuebles desde API
    const loadInmuebles = async () => {
      try {
        searchLoading.value = true;

        // Simular carga por ahora - después se conectará con el API real
        // Se puede usar el mismo endpoint que SidebarCardInmuebles
        const response = await fetch("/msi-v5/owner/inmuebles", {
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const data = await response.json();
          // Mapear datos del backend al formato esperado
          availableInmuebles.value = (data.inmuebles || []).map(
            (inmueble: any) => ({
              id: inmueble.id,
              key: inmueble.clave || inmueble.key,
              name: inmueble.titulo || inmueble.name,
              image: optimizeImageUrl(
                inmueble.imagenPrincipal ||
                  inmueble.image ||
                  "/images/placeholder-property.jpg"
              ),
              operacion: inmueble.operacion,
              precio: inmueble.precio,
              moneda: inmueble.moneda,
              tipo: inmueble.tipo,
              colonia: inmueble.colonia,
              ciudad: inmueble.ciudad, // 🆕 NUEVO: Incluir ciudad del inmueble
              // 🆕 NUEVO: Incluir todas las imágenes del inmueble
              imagenes: inmueble.imagenes || [],
            })
          );
        }
      } catch (error) {
        console.error("Error al cargar inmuebles:", error);
        availableInmuebles.value = [];
      } finally {
        searchLoading.value = false;
      }
    };

    // Seleccionar inmueble
    const selectInmueble = (inmueble: Inmueble) => {
      selectedInmueble.value = inmueble;
      searchQuery.value = "";
      showDropdown.value = false;
      highlightedIndex.value = -1;

      emit("update:selectedInmueble", inmueble);
      emit("inmueble-change", {
        hasInmueble: hasSelectedInmueble.value,
        selectedInmueble: inmueble,
      });
    };

    // Limpiar selección
    const clearSelection = () => {
      selectedInmueble.value = null;
      searchQuery.value = "";

      emit("update:selectedInmueble", null);
      emit("inmueble-change", {
        hasInmueble: hasSelectedInmueble.value,
        selectedInmueble: null,
      });
    };

    // Manejar navegación con teclado
    const handleKeydown = (event: KeyboardEvent) => {
      if (!showDropdown.value || !filteredInmuebles.value.length) return;

      switch (event.key) {
        case "ArrowDown":
          event.preventDefault();
          highlightedIndex.value = Math.min(
            highlightedIndex.value + 1,
            filteredInmuebles.value.length - 1
          );
          break;
        case "ArrowUp":
          event.preventDefault();
          highlightedIndex.value = Math.max(highlightedIndex.value - 1, 0);
          break;
        case "Enter":
          event.preventDefault();
          if (highlightedIndex.value >= 0) {
            selectInmueble(filteredInmuebles.value[highlightedIndex.value]);
          }
          break;
        case "Escape":
          showDropdown.value = false;
          highlightedIndex.value = -1;
          break;
      }
    };

    // Manejar error de imagen
    const handleImageError = (event: Event) => {
      const img = event.target as HTMLImageElement;
      img.src = "/images/placeholder-property.jpg";
    };

    // Cerrar dropdown cuando se hace clic fuera
    const handleClickOutside = (event: Event) => {
      const target = event.target as HTMLElement;
      if (!target.closest(".selector-inmuebles-container")) {
        showDropdown.value = false;
      }
    };

    // Watchers
    watch(
      () => props.initialHasInmueble,
      (newValue) => {
        // 🆕 SOLUCION BUG: Sincronizar estado local con cambios del prop
        console.log("🔄 Sincronizando hasSelectedInmueble:", {
          estadoLocal: hasSelectedInmueble.value,
          propNuevo: newValue,
          cambioDetectado: hasSelectedInmueble.value !== newValue,
        });

        if (hasSelectedInmueble.value !== newValue) {
          hasSelectedInmueble.value = newValue;

          // Si se desactiva desde el padre, limpiar selección
          if (!newValue) {
            selectedInmueble.value = null;
            searchQuery.value = "";
            showDropdown.value = false;
          }
        }
      },
      { immediate: false }
    );

    watch(
      () => props.externalInmuebles,
      (newInmuebles) => {
        if (newInmuebles.length > 0) {
          // Optimizar imágenes de inmuebles externos también
          availableInmuebles.value = newInmuebles.map((inmueble) => ({
            ...inmueble,
            image: optimizeImageUrl(
              inmueble.image || "/images/placeholder-property.jpg"
            ),
            // 🆕 NUEVO: Asegurar que campos estén presentes
            imagenes: inmueble.imagenes || [],
            ciudad: inmueble.ciudad || "",
          }));
        }
      },
      { immediate: true }
    );

    watch(
      selectedInmueble,
      (newValue) => {
        emit("update:selectedInmueble", newValue);
      },
      { deep: true }
    );

    // Ciclo de vida
    onMounted(() => {
      if (props.autoLoad && props.externalInmuebles.length === 0) {
        loadInmuebles();
      }

      // Agregar listener para cerrar dropdown
      document.addEventListener("click", handleClickOutside);
    });

    return {
      // Estados
      hasSelectedInmueble,
      selectedInmueble,
      searchQuery,
      showDropdown,
      searchLoading,
      highlightedIndex,
      searchInput,

      // Computed
      filteredInmuebles,

      // Métodos
      handleToggleInmueble,
      handleSearch,
      selectInmueble,
      clearSelection,
      handleKeydown,
      handleImageError,
      loadInmuebles,
      formatPrice,
    };
  },
});
</script>

<style scoped>
.selector-inmuebles-container {
  position: relative;
}

/* Estilos para el toggle switch */
.selector-inmuebles-container input[type="checkbox"]:checked + div {
  background-color: #3b82f6;
}

.selector-inmuebles-container input[type="checkbox"]:checked + div:after {
  transform: translateX(100%);
}

/* Animaciones */
.selector-inmuebles-container * {
  transition: all 0.2s ease-in-out;
}

/* Estilos del dropdown */
.selector-inmuebles-container .max-h-60 {
  overflow-y: auto;
}

/* Scrollbar personalizado */
.selector-inmuebles-container .max-h-60::-webkit-scrollbar {
  width: 6px;
}

.selector-inmuebles-container .max-h-60::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.selector-inmuebles-container .max-h-60::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.selector-inmuebles-container .max-h-60::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
