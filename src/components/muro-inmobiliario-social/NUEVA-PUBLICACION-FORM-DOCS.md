# 📝 NuevaPublicacionForm.vue - Documentación Técnica Completa

> Sistema integral de publicaciones inmobiliarias con tipos dinámicos, selector de inmuebles y gestión de audiencia

## 📋 Tabla de Contenidos

- [🎯 Descripción General](#-descripción-general)
- [🏗️ Arquitectura del Sistema](#️-arquitectura-del-sistema)
- [🔧 Tipos de Publicación](#-tipos-de-publicación)
- [🏠 Sistema de Inmuebles](#-sistema-de-inmuebles)
- [👥 Sistema de Socios](#-sistema-de-socios)
- [🔄 Estados y Validación](#-estados-y-validación)
- [📡 Integración con APIs](#-integración-con-apis)
- [🎨 Interfaz de Usuario](#-interfaz-de-usuario)
- [⚙️ Componentes Hijos](#️-componentes-hijos)
- [🔍 Flujos de Usuario](#-flujos-de-usuario)
- [🧪 Testing y Debugging](#-testing-y-debugging)
- [💡 Mejores Prácticas](#-mejores-prácticas)

---

## 🎯 Descripción General

### ¿Qué es NuevaPublicacionForm.vue?

**NuevaPublicacionForm** es un sistema completo de creación de publicaciones inmobiliarias que soporta **4 tipos diferentes de publicaciones**, cada uno con su propia lógica y funcionalidades específicas.

### 🆕 Novedades de la Versión Actual

- ✅ **4 tipos de publicación** con comportamientos específicos
- ✅ **Selector de inmuebles** condicionado por tipo
- ✅ **Textos dinámicos** que cambian según contexto
- ✅ **Validación inteligente** sin campo precio requerido
- ✅ **Integración dual** con APIs de socios e inmuebles
- ✅ **Optimización de imágenes** automática
- ✅ **Título automático** para publicaciones de inmuebles
- ✅ **Visualización completa** de promoción y precio en selector
- ✅ **UI limpia** con ocultación inteligente de controles

### Características Principales

- 🎯 **Tipos específicos**: Inmueble, Cliente, Invitación, Noticia
- 🏠 **Gestión de inmuebles**: Búsqueda, selección y vinculación
- 🤖 **Títulos automáticos**: Generación automática para inmuebles
- 👥 **Audiencia dirigida**: Público vs socios específicos
- 🔄 **Estados reactivos**: Loading, validación, errores
- 📱 **Diseño adaptativo**: Mobile-first responsive
- ⚡ **Performance optimizada**: Carga condicional de datos

---

## 🏗️ Arquitectura del Sistema

### Stack Tecnológico

```typescript
Vue 3.3+ (Composition API)
TypeScript (strict mode)
SelectorSociosWrapper (componente hijo)
SelectorInmueblesWrapper (componente hijo)
Axios (HTTP client)
Tailwind CSS (utilidades)
inmobiliarioService (DDP backend)
```

### Estructura de Archivos

```
src/components/muro-inmobiliario-social/
├── NuevaPublicacionForm.vue           # 🎯 Componente principal
├── NUEVA-PUBLICACION-FORM-DOCS.md     # 📖 Esta documentación
├── SelectorInmueblesWrapper.vue       # 🏠 Nuevo sistema de inmuebles
└── SelectorSociosWrapper.vue          # 👥 Sistema de socios existente

src/types/
└── inmobiliario.ts                    # 📋 Tipos TypeScript actualizados

src/services/
└── inmobiliarioService.js             # 📡 Cliente DDP backend
```

### Imports y Dependencias

```typescript
import {
  defineComponent,
  ref,
  reactive,
  onMounted,
  computed,
  watch,
} from "vue";
import type { NuevoPost, Socio } from "../../types/inmobiliario";
import SelectorSociosWrapper from "../selector-socios-wrapper/SelectorSociosWrapper.vue";
import SelectorInmueblesWrapper from "../selector-inmuebles-wrapper/SelectorInmueblesWrapper.vue";
import inmobiliarioService from "../../services/inmobiliarioService";
import { useModal } from "../ui";
import axios from "axios";
```

---

## 🔧 Tipos de Publicación

### Configuración de Tipos

El sistema maneja **4 tipos principales** de publicaciones, cada uno con comportamiento específico:

```typescript
const tiposPublicacion = {
  inmueble: {
    icono: "🏠",
    titulo: "Inmueble",
    descripcion:
      "Publica uno de tus inmuebles en el Feed de la Multibolsa Inmobiliaria.",
  },
  cliente: {
    icono: "🔍",
    titulo: "Cliente buscando",
    descripcion:
      "Si tienes un cliente que busca un inmueble con características específicas, puedes publicarlo aquí.",
  },
  invitacion: {
    icono: "🤝",
    titulo: "Invitación / Colaboración",
    descripcion:
      "Comparte eventos, open house o propuestas para colaborar con colegas.",
  },
  noticia: {
    icono: "📣",
    titulo: "Noticia / Aviso",
    descripcion:
      "Difunde noticias, actualizaciones importantes o información relevante del sector inmobiliario.",
  },
};
```

### Lógica por Tipo de Publicación

| Tipo              | Selector Inmuebles         | Estado Inicial                | Uso Principal           |
| ----------------- | -------------------------- | ----------------------------- | ----------------------- |
| **🏠 Inmueble**   | ✅ Visible + Habilitado    | `hasSelectedInmueble = true`  | Promocionar propiedades |
| **🤝 Invitación** | ✅ Visible + Deshabilitado | `hasSelectedInmueble = false` | Open House / Eventos    |
| **🔍 Cliente**    | ❌ Oculto                  | `hasSelectedInmueble = false` | Búsquedas específicas   |
| **📣 Noticia**    | ❌ Oculto                  | `hasSelectedInmueble = false` | Avisos generales        |

### Manejo de Cambios de Tipo

```typescript
const handleTypeChange = () => {
  if (newPost.type === "inmueble") {
    // Para inmuebles: habilitar automáticamente el selector
    hasSelectedInmueble.value = true;
    console.log("🏠 Tipo inmueble: selector habilitado automáticamente");
  } else if (newPost.type === "invitacion") {
    // Para invitaciones: mostrar selector pero deshabilitado por defecto
    hasSelectedInmueble.value = false;
    selectedInmueble.value = null;
    console.log("🤝 Tipo invitación: selector disponible pero deshabilitado");
  } else {
    // Para otros tipos: ocultar selector y resetear
    hasSelectedInmueble.value = false;
    selectedInmueble.value = null;
    console.log("🔄 Tipo", newPost.type, ": selector oculto");
  }
};
```

---

## 🤖 Sistema de Título Automático

### Funcionalidad de Título Automático para Inmuebles

Cuando se publica un **inmueble**, el sistema genera automáticamente el título basándose en los datos del inmueble seleccionado.

#### Formato del Título Automático

```text
[TIPO] [OPERACIÓN] con clave [CLAVE]
```

**Ejemplo:**

- `Casa en venta con clave MLS-001`
- `Departamento en renta con clave DEP-456`
- `Terreno en venta con clave TER-789`

#### Implementación Técnica

```typescript
const generateInmuebleTitle = (inmueble: any): string => {
  if (!inmueble) return "";

  const tipo = inmueble.tipo || "Inmueble";
  const operacion = inmueble.operacion || "en venta";
  const clave = inmueble.key || inmueble.clave || "XXX";

  // Capitalizar primera letra del tipo
  const tipoCapitalizado =
    tipo.charAt(0).toUpperCase() + tipo.slice(1).toLowerCase();

  return `${tipoCapitalizado} ${operacion} con clave ${clave}`;
};
```

#### Comportamiento en la Interfaz

- **Campo de título**: Se vuelve **solo lectura** cuando se selecciona un inmueble
- **Indicador visual**: Muestra badge "🤖 Automático" junto al label
- **Texto de ayuda**: Explica que el título se genera automáticamente
- **Estilos especiales**: Fondo azul claro y texto azul para diferenciarlo

#### Cuándo se Genera

1. **Al seleccionar un inmueble** en publicaciones tipo "inmueble"
2. **Al cambiar de tipo** a "inmueble" si ya hay un inmueble seleccionado
3. **Al cambiar de inmueble** seleccionado en publicaciones tipo "inmueble"

#### Limpieza Automática

- Se limpia el título cuando se cambia a otros tipos de publicación
- Se resetea cuando se deselecciona el inmueble
- Se actualiza dinámicamente al cambiar de inmueble

---

## 🏠 Sistema de Inmuebles

### Nuevo Componente: SelectorInmueblesWrapper

**Funcionalidad**: Permite seleccionar inmuebles del catálogo del usuario para vincular a publicaciones.

#### Cuándo se Muestra

- **Tipo "Inmueble"**: Siempre visible y habilitado automáticamente
- **Tipo "Invitación"**: Visible pero deshabilitado (usuario puede activar para Open House)
- **Otros tipos**: No se muestra

#### Estructura de Datos

```typescript
// Estructura del inmueble seleccionado
interface Inmueble {
  id?: number;
  key: string; // Clave única del inmueble
  name: string; // Título/nombre del inmueble
  image: string; // URL de imagen principal (optimizada)
  precio?: number; // Precio del inmueble
  operacion?: string; // venta, renta, traspaso
  tipo?: string; // casa, departamento, terreno, etc.
}
```

#### Visualización de Inmuebles

El selector ahora muestra información completa del inmueble:

- **Nombre del inmueble**: Título principal
- **Clave**: Identificador único con tipografía monoespaciada
- **Promoción**: Badge con el tipo de operación (venta, renta, traspaso)
- **Precio**: Formato con separadores de miles y tipografía monoespaciada

**Ejemplo visual:**

```
🏠 Casa en Las Águilas
    Clave: charlie-2
    [venta] $2,500,000
```

#### Carga de Inmuebles

```typescript
const loadUserInmuebles = async () => {
  try {
    loadingInmuebles.value = true;

    // Usar API PHP para obtener inmuebles del usuario
    const response = await axios.get("/msi-v5/owner/inmuebles");

    if (response.data && response.data.statusCode === 200) {
      // Procesar y optimizar inmuebles del backend
      availableInmuebles.value = (response.data.data.inmuebles || []).map(
        (inmueble: any) => ({
          id: inmueble.id,
          key: inmueble.clave || inmueble.key,
          name: inmueble.titulo || inmueble.name,
          image: optimizeImageUrl(
            inmueble.imagenPrincipal || "/images/placeholder-property.jpg"
          ),
          precio: inmueble.precio,
          operacion: inmueble.operacion,
          tipo: inmueble.tipo,
        })
      );
    }
  } catch (error) {
    console.error("Error al cargar inmuebles:", error);
    availableInmuebles.value = [];
  } finally {
    loadingInmuebles.value = false;
  }
};
```

#### Optimización de Imágenes

```typescript
const optimizeImageUrl = (imageUrl: string): string => {
  if (!imageUrl || typeof imageUrl !== "string") {
    return "/images/placeholder-property.jpg";
  }

  // Reemplazar /alta/ por /peque/ para optimizar el tamaño
  const optimizedUrl = imageUrl.replace("/alta/", "/peque/");

  return optimizedUrl;
};
```

#### Formateo de Precios

```typescript
// Función para formatear precio con separadores de miles
const formatPrice = (precio: number): string => {
  if (!precio) return "";
  return precio.toLocaleString("es-MX");
};
```

**Ejemplos de formateo:**

- `2500000` → `2,500,000`
- `1250000` → `1,250,000`
- `890000` → `890,000`

### Textos Dinámicos por Contexto

```typescript
const getTextosInmuebleParaTipo = (tipo: string) => {
  if (tipo === "inmueble") {
    return {
      titulo: "Publicación con inmueble",
      descripcion:
        "Esta publicación estará relacionada con uno de tus inmuebles",
    };
  } else if (tipo === "invitacion") {
    return {
      titulo: "Invitación con inmueble",
      descripcion:
        "Invita a colegas a un Open House o evento relacionado con este inmueble",
    };
  }
  return textosInmuebleSeleccionado;
};
```

---

## 👥 Sistema de Socios

### Componente: SelectorSociosWrapper

**Funcionalidad**: Gestiona la audiencia de la publicación (pública vs socios específicos).

#### Carga de Socios

```typescript
const loadUserFriends = async () => {
  try {
    loadingFriends.value = true;

    // Usar API PHP para obtener socios
    const response = await axios.get("/msi-v5/owner/socios");

    if (response.data && response.data.statusCode === 200) {
      // Procesar etiquetas del backend
      const tagsBackend = response.data.data.tags || [];
      const etiquetasDisponibles = tagsBackend.map((tag: any) => {
        let color = "#7C3AED";
        let backgroundColor = "#EDE9FE";

        // Extraer colores del estilo CSS
        if (tag.style) {
          const bgMatch = tag.style.match(/background-color:\s*([^;]+);?/);
          const borderMatch = tag.style.match(/border:\s*1px solid ([^;]+);?/);
          if (bgMatch) backgroundColor = bgMatch[1].trim();
          if (borderMatch) color = borderMatch[1].trim();
        }

        return {
          id: tag.id.toString(),
          nombre: tag.tag,
          color,
          backgroundColor,
          descripcion: tag.description || undefined,
        };
      });

      // Mapear socios a estructura esperada
      availableFriends.value = (response.data.data.socios || [])
        .filter((socio: any) => socio.tipo === "directo") // Solo socios directos
        .map((socio: any) => {
          // Procesar etiquetas del socio
          const etiquetas = (socio.tags || [])
            .map((tagId: any) =>
              etiquetasDisponibles.find((e: any) => e.id == tagId.toString())
            )
            .filter(Boolean);

          return {
            _id: socio.id,
            id: socio.id,
            name: socio.nombre,
            company: socio.empresa,
            location: socio.ubicacion,
            avatar: socio.avatar,
            email: socio.email,
            phone: socio.telefono,
            wa: socio.wa,
            telegram: socio.telegram,
            verified: socio.verified || false,
            etiquetas: etiquetas,
          };
        });
    }
  } catch (error) {
    console.error("Error al cargar socios:", error);
    availableFriends.value = [];
  } finally {
    loadingFriends.value = false;
  }
};
```

#### Filtrado de Socios

```typescript
const filteredFriends = computed(() => {
  if (!searchFriends.value.trim()) {
    return availableFriends.value;
  }

  const searchTerm = searchFriends.value.toLowerCase().trim();
  return availableFriends.value.filter((friend) => {
    const name = friend.name?.toLowerCase() || "";
    const company = friend.company?.toLowerCase() || "";
    return name.includes(searchTerm) || company.includes(searchTerm);
  });
});
```

---

## 🔄 Estados y Validación

### Estados Reactivos

```typescript
// Estados del formulario principal
const isCreating = ref(false); // Creando publicación
const isPublicPost = ref(true); // Audiencia pública/privada

// Estados para socios
const availableFriends = ref<Socio[]>([]);
const selectedFriends = ref<string[]>([]);
const loadingFriends = ref(false);
const searchFriends = ref("");

// Estados para inmuebles
const hasSelectedInmueble = ref(false);
const selectedInmueble = ref<any>(null);
const availableInmuebles = ref<any[]>([]);
const loadingInmuebles = ref(false);

// Datos del post
const newPost = reactive<NuevoPost>({
  type: "inmueble", // Tipo por defecto
  title: "",
  description: "",
  images: [],
  relatedInmueble: null, // NUEVO: Inmueble vinculado
});
```

### Sistema de Validación

#### Campos Requeridos

```typescript
const handleCreatePost = async () => {
  // ✅ Validar solo campos esenciales (precio fue removido)
  if (!newPost.title || !newPost.description) {
    warning("Por favor completa los campos requeridos", "Campos requeridos");
    return;
  }

  // ✅ No hay validación de precio - viene del inmueble si está presente
  // ✅ Título y descripción son suficientes para todos los tipos
};
```

#### Validaciones por Tipo

| Campo           | Inmueble           | Cliente          | Invitación               | Noticia          |
| --------------- | ------------------ | ---------------- | ------------------------ | ---------------- |
| **Título**      | 🤖 Automático      | ✅ Requerido     | ✅ Requerido             | ✅ Requerido     |
| **Descripción** | ✅ Requerido       | ✅ Requerido     | ✅ Requerido             | ✅ Requerido     |
| **Inmueble**    | 🔄 Auto-habilitado | ❌ No disponible | 🤝 Opcional              | ❌ No disponible |
| **Precio**      | 🏠 Del inmueble    | ❌ No aplica     | 🏠 Del inmueble (si hay) | ❌ No aplica     |

---

## 📡 Integración con APIs

### Endpoints Utilizados

```typescript
// 1. Obtener socios del usuario
GET /msi-v5/owner/socios
Response: {
  statusCode: 200,
  data: {
    socios: Socio[],
    tags: Tag[]
  }
}

// 2. Obtener inmuebles del usuario
GET /msi-v5/owner/inmuebles
Response: {
  statusCode: 200,
  data: {
    inmuebles: Inmueble[]
  }
}

// 3. Crear publicación (DDP)
inmobiliarioService.createPostWithTargets(postData, targetUserIds)
```

### Preparación de Datos para Backend

```typescript
const postData = {
  ...newPost,
  relatedInmueble: hasSelectedInmueble.value ? selectedInmueble.value : null,
};

console.log("📝 Creando post con datos:", {
  title: postData.title,
  type: postData.type,
  relatedInmueble: postData.relatedInmueble,
  targetUserIds,
  isPublic: isPublicPost.value,
});

await inmobiliarioService.createPostWithTargets(postData, targetUserIds);
```

---

## 🎨 Interfaz de Usuario

### Estructura Visual del Template

```vue
<template>
  <div v-if="show" class="nueva-publicacion-form">
    <!-- 🎯 Header -->
    <div class="flex justify-between items-center mb-3">
      <h3 class="font-semibold text-blue-800">Nueva publicación</h3>
      <button @click="$emit('close')">❌</button>
    </div>

    <div class="space-y-3">
      <!-- 🎭 Selector de Tipo con Descripción Dinámica -->
      <div>
        <label>🔖 Tipo de publicación</label>
        <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <select v-model="newPost.type" @change="handleTypeChange">
            <option v-for="(config, typeKey) in tiposPublicacion">
              {{ config.icono }} {{ config.titulo }}
            </option>
          </select>

          <!-- 📝 Descripción dinámica -->
          <div class="mt-2">
            <p class="text-xs text-blue-600">
              {{ tiposPublicacion[newPost.type].descripcion }}
            </p>
          </div>

          <!-- 🏠 Selector de Inmuebles (condicional) -->
          <div v-if="newPost.type === 'inmueble' || newPost.type === 'invitacion'">
            <SelectorInmueblesWrapper ... />
          </div>
        </div>
      </div>

      <!-- 📝 Datos de la Publicación -->
      <div>
        <label>✍️ Publicación</label>
        <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <input v-model="newPost.title" placeholder="Título..." />
          <textarea v-model="newPost.description" placeholder="Descripción..." />
        </div>
      </div>

      <!-- 👥 Audiencia -->
      <div>
        <label>👥 Audiencia de la publicación</label>
        <SelectorSociosWrapper ... />
      </div>

      <!-- 🚀 Botones de Acción -->
      <div class="flex justify-end space-x-2">
        <button @click="$emit('close')" class="btn-secondary">
          Cancelar
        </button>
        <button @click="handleCreatePost" :disabled="isCreating" class="btn-primary">
          <span v-if="isCreating">🔄 Publicando...</span>
          <span v-else">✅ Publicar</span>
        </button>
      </div>
    </div>
  </div>
</template>
```

### Estilos y Animaciones

```css
.nueva-publicacion-form {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estados de focus */
.nueva-publicacion-form input:focus,
.nueva-publicacion-form select:focus,
.nueva-publicacion-form textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Efectos de botones */
.nueva-publicacion-form button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 640px) {
  .nueva-publicacion-form .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
```

---

## ⚙️ Componentes Hijos

### 1. SelectorInmueblesWrapper.vue

**Propósito**: Gestionar la selección de inmuebles para vincular a publicaciones.

#### Props

```typescript
props: {
  hasInmueble: Boolean,              // Si está habilitado el selector
  selectedInmueble: Object,          // Inmueble actualmente seleccionado
  availableInmuebles: Array,         // Lista de inmuebles disponibles
  textosInmuebleSeleccionado: Object, // Textos para modo "con inmueble"
  textosPublicacionGeneral: Object,   // Textos para modo "sin inmueble"
}
```

#### Eventos

```typescript
@update:hasInmueble="hasSelectedInmueble = $event"
@update:selectedInmueble="selectedInmueble = $event"
@add-inmueble="$emit('add-inmueble')"
```

#### Funcionalidades

- ✅ **Toggle habilitado/deshabilitado** con textos contextuales
- ✅ **Búsqueda en tiempo real** por clave o nombre del inmueble
- ✅ **Dropdown con resultados** limitado a 10 items
- ✅ **Vista del inmueble seleccionado** con imagen optimizada
- ✅ **Navegación por teclado** (↑↓, Enter, Escape)
- ✅ **Manejo de imágenes fallback** para placeholders
- ✅ **UI limpia** con ocultación automática del input al seleccionar
- ✅ **Limpieza automática** del título al quitar selección

#### Comportamiento de la Interfaz

**Estados de la UI:**

1. **Sin inmueble seleccionado:**

   - ✅ Input de búsqueda visible
   - ✅ Dropdown visible al buscar
   - ✅ Título editable manualmente

2. **Con inmueble seleccionado:**

   - ❌ Input de búsqueda oculto (UI más limpia)
   - ❌ Dropdown oculto
   - ✅ Tarjeta del inmueble seleccionado visible
   - ✅ Título automático generado y readonly

3. **Al quitar inmueble (botón 🗑️):**
   - ✅ Regresa al estado "Sin inmueble seleccionado"
   - ✅ Input de búsqueda se muestra nuevamente
   - ✅ Título se limpia automáticamente

### 2. SelectorSociosWrapper.vue

**Propósito**: Gestionar la audiencia de la publicación (público vs socios específicos).

#### Props

```typescript
props: {
  isPublic: Boolean,                 // Publicación pública vs privada
  selectedSocios: Array,             // IDs de socios seleccionados
  availableFriends: Array,           // Lista de socios disponibles
  textosAudienciaPrivada: Object,    // Textos para audiencia privada
  textosAudienciaPublica: Object,    // Textos para audiencia pública
}
```

#### Eventos

```typescript
@update:isPublic="isPublicPost = $event"
@update:selectedSocios="selectedFriends = $event"
@add-socio="$emit('add-socio')"
```

#### Funcionalidades

- ✅ **Toggle público/privado** con descripciones claras
- ✅ **Búsqueda y filtrado** de socios por nombre y empresa
- ✅ **Soporte para etiquetas** con colores personalizados
- ✅ **Selección múltiple** con indicadores visuales
- ✅ **Integración con sistema legacy** mediante adaptación de datos

---

## 🔍 Flujos de Usuario

### Flujo 1: Publicar Inmueble (Tipo Inmueble)

```mermaid
flowchart TD
    A[Usuario abre formulario] --> B[Tipo 'Inmueble' por defecto]
    B --> C[Selector de inmuebles se habilita automáticamente]
    C --> D[Usuario busca y selecciona inmueble]
    D --> E[Usuario completa título y descripción]
    E --> F[Usuario configura audiencia]
    F --> G[Usuario hace clic en 'Publicar']
    G --> H[Validación: ✅ título, ✅ descripción]
    H --> I[POST con inmueble vinculado]
    I --> J[✅ Publicación creada exitosamente]
```

### Flujo 2: Invitación a Open House (Tipo Invitación)

```mermaid
flowchart TD
    A[Usuario selecciona tipo 'Invitación'] --> B[Selector de inmuebles visible pero deshabilitado]
    B --> C[Usuario activa toggle 'Publicar con inmueble']
    C --> D[Usuario selecciona inmueble del Open House]
    D --> E[Usuario describe el evento]
    E --> F[Usuario selecciona socios invitados]
    F --> G[POST con inmueble e invitados específicos]
    G --> H[✅ Invitación enviada]
```

### Flujo 3: Cliente Buscando (Tipo Cliente)

```mermaid
flowchart TD
    A[Usuario selecciona tipo 'Cliente'] --> B[Selector de inmuebles se oculta]
    B --> C[Usuario describe necesidades del cliente]
    C --> D[Usuario configura audiencia]
    D --> E[POST sin inmueble vinculado]
    E --> F[✅ Búsqueda publicada]
```

### Flujo 4: Noticia del Sector (Tipo Noticia)

```mermaid
flowchart TD
    A[Usuario selecciona tipo 'Noticia'] --> B[Formulario se simplifica]
    B --> C[Usuario redacta la noticia]
    C --> D[Usuario define audiencia]
    D --> E[POST tipo noticia]
    E --> F[✅ Noticia compartida]
```

---

## 🧪 Testing y Debugging

### Logs de Debugging Implementados

```typescript
// 🔍 Debug de cambio de tipo
console.log("🔄 Tipo de publicación cambiado a:", newPost.type);

// 🏠 Debug de inmuebles
console.log("🏠 Tipo inmueble: selector habilitado automáticamente");

// 🤝 Debug de invitaciones
console.log("🤝 Tipo invitación: selector disponible pero deshabilitado");

// 📝 Debug de creación de post
console.log("📝 Creando post con datos:", {
  title: postData.title,
  type: postData.type,
  relatedInmueble: postData.relatedInmueble,
  targetUserIds,
  isPublic: isPublicPost.value,
});
```

### Casos de Prueba

| Escenario                     | Input                                                   | Expectativa                                     | Verificación                                        |
| ----------------------------- | ------------------------------------------------------- | ----------------------------------------------- | --------------------------------------------------- |
| **Inmueble válido**           | Tipo=inmueble, inmueble seleccionado, descripción       | ✅ Post creado con inmueble y título automático | `relatedInmueble` presente, título formato correcto |
| **Título automático**         | Seleccionar inmueble casa en venta clave ABC-123        | ✅ Título = "Casa en venta con clave ABC-123"   | Campo título readonly, badge visible                |
| **Cambio de inmueble**        | Cambiar a departamento en renta clave DEF-456           | ✅ Título actualizado automáticamente           | Título = "Departamento en renta con clave DEF-456"  |
| **UI limpia al seleccionar**  | Seleccionar inmueble desde dropdown                     | ✅ Input de búsqueda se oculta                  | UI más limpia, solo tarjeta visible                 |
| **Quitar inmueble**           | Hacer clic en botón 🗑️ de inmueble seleccionado         | ✅ Input reaparece, título se limpia            | Input visible, título vacío y editable              |
| **Cambio a otro tipo**        | Cambiar de inmueble a cliente                           | ✅ Título se limpia, campo editable             | Campo título vacío y editable                       |
| **Invitación sin inmueble**   | Tipo=invitación, sin activar toggle                     | ✅ Post creado sin inmueble                     | `relatedInmueble = null`                            |
| **Invitación con Open House** | Tipo=invitación, toggle activado, inmueble seleccionado | ✅ Post con inmueble, título manual             | `relatedInmueble` presente, título editable         |
| **Cliente buscando**          | Tipo=cliente, descripción completa                      | ✅ Post sin opciones de inmueble                | Selector oculto, título editable                    |
| **Campos vacíos**             | Solo descripción vacía (título automático presente)     | ❌ Error de validación                          | Warning mostrado                                    |
| **Error de API socios**       | Fallo en `/msi-v5/owner/socios`                         | ⚠️ Lista vacía                                  | Sin crash, array vacío                              |
| **Error de API inmuebles**    | Fallo en `/msi-v5/owner/inmuebles`                      | ⚠️ Lista vacía                                  | Sin crash, array vacío                              |

### Comandos de Testing

```bash
# 1. Testing visual completo
npm run dev
# Abrir http://localhost:5173/panel.html

# 2. Testing de tipos
# - Cambiar entre los 4 tipos
# - Verificar comportamiento del selector de inmuebles
# - Comprobar textos dinámicos
# - Verificar título automático en tipo "inmueble"

# 3. Testing de título automático
# - Seleccionar inmueble en tipo "inmueble"
# - Verificar que el título se genera automáticamente
# - Cambiar de inmueble y verificar actualización del título
# - Cambiar a otro tipo y verificar que el título se limpia
# - Verificar indicadores visuales (badge, readonly, estilos)

# 4. Testing de validación
# - Enviar formulario vacío
# - Probar sin seleccionar inmueble en tipo 'inmueble'
# - Verificar mensajes de error
# - Comprobar que título automático cuenta como válido

# 5. Testing de integración
# - Verificar carga de socios
# - Verificar carga de inmuebles
# - Probar creación de posts de cada tipo
# - Verificar que el título automático se incluye en el POST
```

---

## 💡 Mejores Prácticas

### 1. Gestión de Estados por Tipo

```typescript
// ✅ BUENO: Lógica específica por tipo
const handleTypeChange = () => {
  switch (newPost.type) {
    case "inmueble":
      hasSelectedInmueble.value = true; // Auto-habilitar
      break;
    case "invitacion":
      hasSelectedInmueble.value = false; // Disponible pero deshabilitado
      selectedInmueble.value = null;
      break;
    default:
      hasSelectedInmueble.value = false; // Ocultar
      selectedInmueble.value = null;
  }
};

// ❌ MALO: Lógica genérica sin distinción
const handleTypeChange = () => {
  // Solo resetear, sin comportamiento específico
  hasSelectedInmueble.value = false;
};
```

### 2. Validación Inteligente

```typescript
// ✅ BUENO: Validación mínima necesaria
if (!newPost.title || !newPost.description) {
  warning("Por favor completa los campos requeridos");
  return;
}

// ❌ MALO: Validación rígida obsoleta
if (!newPost.title || !newPost.description || !newPost.price) {
  // El precio ya no es requerido
}
```

### 3. Optimización de Performance

```typescript
// ✅ BUENO: Carga condicional de datos
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      if (availableFriends.value.length === 0) loadUserFriends();
      if (availableInmuebles.value.length === 0) loadUserInmuebles();
    }
  }
);

// ❌ MALO: Carga siempre
onMounted(() => {
  loadUserFriends(); // Innecesario si no se muestra
  loadUserInmuebles(); // Innecesario si no se muestra
});
```

### 4. Textos Dinámicos Contextuales

```typescript
// ✅ BUENO: Textos específicos por contexto
const getTextosInmuebleParaTipo = (tipo: string) => {
  if (tipo === "inmueble") return { descripcion: "...inmuebles..." };
  if (tipo === "invitacion") return { descripcion: "...Open House..." };
  return textosDefault;
};

// ❌ MALO: Textos estáticos
const textos = { descripcion: "Descripción genérica" };
```

---

## 🚀 Estructura de Datos

### Tipo NuevoPost (Actualizado)

```typescript
interface NuevoPost {
  type: "inmueble" | "cliente" | "invitacion" | "noticia";
  title: string;
  description: string;
  images: string[];
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  propertyType?: string;
  features?: string[];
  contactPreference?: string;
  urgency?: string;
  targetUserIds?: string[];
  relatedInmueble?: {
    // 🆕 NUEVO CAMPO
    id?: number;
    key: string;
    name: string;
    image: string;
    precio?: number;
    operacion?: string;
    tipo?: string;
  } | null;
}
```

### Props del Componente

```typescript
props: {
  show: {
    type: Boolean,
    default: false,
    // Controla visibilidad del formulario
  },
  token: {
    type: String,
    required: true,
    // Token de autenticación
  },
  textosAudienciaPrivada: {
    type: Object,
    required: true,
    // Configuración de textos para audiencia privada
  },
  textosAudienciaPublica: {
    type: Object,
    required: true,
    // Configuración de textos para audiencia pública
  },
}
```

### Eventos Emitidos

```typescript
emits: [
  "close", // Solicita cerrar formulario
  "post-created", // Publicación creada exitosamente
  "error", // Error durante creación
  "add-socio", // Solicita modal de agregar socio
  "add-inmueble", // 🆕 Solicita modal de agregar inmueble
];
```

---

## 🔧 Configuración y Uso

### Integración en Componente Padre

```vue
<template>
  <NuevaPublicacionForm
    :show="showNewPostForm"
    :token="token"
    :textos-audiencia-privada="textosAudienciaPrivada"
    :textos-audiencia-publica="textosAudienciaPublica"
    @close="showNewPostForm = false"
    @post-created="handlePostCreated"
    @error="handleFormError"
    @add-socio="showAddFriendModal = true"
    @add-inmueble="showAddPropertyModal = true"
  />
</template>

<script>
const handlePostCreated = async () => {
  showNewPostForm.value = false;
  await fetchPosts(); // Recargar lista de publicaciones
  success("Publicación creada exitosamente", "¡Éxito!");
};

const handleFormError = (error) => {
  console.error("Error en formulario:", error);
  // Manejar error específico si es necesario
};
</script>
```

### Configuración de Textos

```typescript
const textosAudienciaPrivada = {
  titulo: "Solo a socios determinados",
  descripcion:
    "Solo tus socios seleccionados verán esta publicación en su muro inmobiliario",
};

const textosAudienciaPublica = {
  titulo: "Todos mis socios",
  descripcion:
    "Todos tus socios verán esta publicación en su muro inmobiliario",
};
```

---

## 📊 Métricas y Performance

### Métricas Objetivo

- ⏱️ **Tiempo de carga inicial**: < 300ms
- 🔄 **Cambio de tipo**: < 100ms
- 📝 **Validación**: < 50ms
- 🚀 **Creación de post**: < 3s
- 👥 **Carga de socios**: < 1.5s
- 🏠 **Carga de inmuebles**: < 1.5s

### Optimizaciones Implementadas

- 🔄 **Carga condicional** de datos solo cuando es necesario
- 🖼️ **Optimización automática** de imágenes (/alta/ → /peque/)
- 💫 **Estados de loading** específicos para mejor UX
- 🎨 **Animaciones CSS** puras sin JavaScript
- 📱 **Diseño responsivo** con Tailwind CSS

---

## 🔮 Futuras Mejoras

### Funcionalidades Planificadas

1. **📎 Upload de imágenes** - Múltiples imágenes por publicación
2. **📍 Geolocalización** - Selector de ubicación en mapa
3. **💾 Borradores** - Guardar progreso automáticamente
4. **🔔 Notificaciones** - Push notifications a socios seleccionados
5. **📊 Analytics** - Métricas de visualización y engagement
6. **🎯 Templates** - Plantillas predefinidas por tipo
7. **🔍 Vista previa** - Preview antes de publicar

### Mejoras UX Sugeridas

- **Autocompletado inteligente** basado en historial
- **Validación en tiempo real** con feedback inmediato
- **Atajos de teclado** para navegación rápida
- **Modo oscuro** siguiendo tema del sistema
- **Accesibilidad mejorada** con ARIA labels

---

**💡 El sistema NuevaPublicacionForm representa una solución completa y extensible para la gestión de publicaciones inmobiliarias, con tipos específicos, validación inteligente y una arquitectura modular que facilita futuras expansiones.**

---

**📧 Soporte:** Para dudas sobre implementación o desarrollo de nuevas funcionalidades, consulta la documentación de componentes relacionados o contacta al equipo de desarrollo.
