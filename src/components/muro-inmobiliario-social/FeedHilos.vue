<template>
  <div class="p-4">
    <!-- Header del Feed -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">
          <ion-icon
            name="chatbubbles-outline"
            class="mr-2 text-mulbin-600"
          ></ion-icon>
          Mis Hilos de Interés
        </h3>
        <p class="text-sm text-gray-600">
          Todas las conversaciones donde participas
        </p>
      </div>

      <!-- Estadísticas -->
      <div v-if="feedStats" class="text-right">
        <p class="text-sm font-medium text-gray-900">
          {{ feedStats.totalHilos }} hilo{{
            feedStats.totalHilos !== 1 ? "s" : ""
          }}
        </p>
        <p
          v-if="feedStats.unreadMessages > 0"
          class="text-xs font-medium text-red-600"
        >
          {{ feedStats.unreadMessages }} mensaje{{
            feedStats.unreadMessages !== 1 ? "s" : ""
          }}
          sin leer
        </p>
        <p v-else class="text-xs text-green-600">Todo al día</p>
      </div>
    </div>

    <!-- Controles de filtro y ordenamiento -->
    <div
      v-if="false"
      class="flex flex-wrap gap-3 p-3 mb-4 bg-gray-50 rounded-lg"
    >
      <!-- Filtro por estado -->
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Mostrar:</label>
        <select
          v-model="filterUnread"
          @change="loadFeed"
          class="px-2 py-1 text-sm rounded border border-gray-300"
        >
          <option :value="false">Todos los hilos</option>
          <option :value="true">Solo con mensajes nuevos</option>
        </select>
      </div>

      <!-- Ordenamiento -->
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Ordenar por:</label>
        <select
          v-model="sortBy"
          @change="loadFeed"
          class="px-2 py-1 text-sm rounded border border-gray-300"
        >
          <option value="lastMessage">Último mensaje</option>
          <option value="created">Fecha de creación</option>
          <option value="activity">Más activos</option>
          <option value="alphabetical">Alfabético</option>
        </select>
      </div>

      <!-- Botón refrescar -->
      <button
        @click="loadFeed"
        :disabled="loading"
        class="px-3 py-1 text-sm font-medium text-white rounded bg-mulbin-600 hover:bg-mulbin-700 disabled:opacity-50"
      >
        <ion-icon
          name="refresh-outline"
          :class="{ 'animate-spin': loading }"
          class="visible mr-1"
        ></ion-icon>
        Actualizar
      </button>
    </div>

    <!-- Loading inicial -->
    <div
      v-if="loading && feedHilos.length === 0"
      class="flex flex-col items-center py-12"
    >
      <div
        class="w-8 h-8 rounded-full border-4 border-blue-200 animate-spin border-t-mulbin-600"
      ></div>
      <p class="mt-4 text-sm text-gray-600">Cargando tu feed de hilos...</p>
    </div>

    <!-- Lista de hilos -->
    <div v-else-if="feedHilos.length > 0" class="space-y-3">
      <div
        v-for="hilo in feedHilos"
        :key="hilo._id"
        @click="abrirHilo(hilo)"
        class="p-4 bg-white rounded-lg border border-gray-200 shadow-sm transition-all cursor-pointer hover:shadow-md hover:border-mulbin-300"
        :class="{
          'border-l-4 border-l-red-400 bg-red-50': hilo.unreadMessagesCount > 0,
          'hover:bg-blue-50': hilo.unreadMessagesCount === 0,
        }"
      >
        <div class="flex items-start space-x-4">
          <!-- Avatar del socio -->
          <div class="relative flex-shrink-0">
            <img
              :src="getSocioAvatar(hilo)"
              :alt="getSocioName(hilo)"
              class="w-12 h-12 rounded-full border-2 border-white shadow-md"
            />
            <!-- Indicador de mensajes no leídos -->
            <div
              v-if="hilo.unreadMessagesCount > 0"
              class="flex absolute -top-1 -right-1 justify-center items-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full"
            >
              {{
                hilo.unreadMessagesCount > 9 ? "9+" : hilo.unreadMessagesCount
              }}
            </div>
          </div>

          <!-- Información del hilo -->
          <div class="flex-1 min-w-0">
            <!-- Línea superior: Título y fecha -->
            <div class="flex justify-between items-start mb-1">
              <div class="flex flex-1 items-center min-w-0">
                <h4 class="text-sm font-semibold text-gray-900 truncate">
                  {{ hilo.titulo }}
                </h4>
                <!-- Badge de mensajes sin leer junto al título -->
                <span
                  v-if="hilo.unreadMessagesCount > 0 && false"
                  class="px-2 py-0.5 ml-2 text-xs font-bold text-white whitespace-nowrap bg-red-500 rounded-full"
                >
                  {{ hilo.unreadMessagesCount }} nuevo{{
                    hilo.unreadMessagesCount !== 1 ? "s" : ""
                  }}
                </span>
              </div>
              <span class="ml-2 text-xs text-gray-500 whitespace-nowrap">
                {{ formatearFecha(hilo.lastMessageAt || hilo.createdAt) }}
              </span>
            </div>

            <!-- Información del socio y publicación -->
            <div class="flex items-center mb-2 space-x-2 text-xs text-gray-600">
              <span class="flex items-center">
                <ion-icon name="person-outline" class="mr-1"></ion-icon>
                {{ getSocioName(hilo) }}
              </span>
              <span>•</span>
              <span class="flex items-center truncate">
                <ion-icon name="home-outline" class="mr-1"></ion-icon>
                {{ limpiarTituloHilo(hilo.postCache?.title) }}
              </span>
            </div>

            <!-- Referencia privada si existe (solo visible para el creador) -->
            <p
              v-if="hilo.referenciaPrivada && hilo.creatorId === currentUserId"
              class="mb-2 text-xs italic text-gray-500 truncate"
            >
              📝 {{ hilo.referenciaPrivada }}
            </p>

            <!-- Estadísticas del hilo -->
            <div class="flex items-center space-x-4 text-xs text-gray-500">
              <span class="flex items-center">
                <ion-icon
                  name="chatbubble-ellipses-outline"
                  class="mr-1 text-blue-500"
                ></ion-icon>
                {{ hilo.mensajesCount || 0 }} mensaje{{
                  (hilo.mensajesCount || 0) !== 1 ? "s" : ""
                }}
              </span>
              <span class="flex items-center">
                <ion-icon
                  name="time-outline"
                  class="mr-1 text-green-500"
                ></ion-icon>
                {{
                  formatearTiempoTranscurrido(
                    hilo.lastMessageAt || hilo.createdAt
                  )
                }}
              </span>
              <span class="flex items-center">
                <ion-icon
                  name="cash-outline"
                  class="mr-1 text-amber-500"
                ></ion-icon>
                ${{ formatPrice(hilo.postCache?.price || 0) }}
              </span>
            </div>
          </div>

          <!-- Indicador de acción -->
          <div class="flex-shrink-0">
            <ion-icon
              name="chevron-forward-outline"
              class="text-gray-400 transition-colors group-hover:text-mulbin-600"
            ></ion-icon>
          </div>
        </div>
      </div>

      <!-- Botón cargar más -->
      <div v-if="hasMore" class="py-4 text-center">
        <button
          @click="loadMore"
          :disabled="loadingMore"
          class="px-4 py-2 text-sm font-medium rounded-lg border text-mulbin-600 border-mulbin-600 hover:bg-mulbin-50 disabled:opacity-50"
        >
          <ion-icon
            v-if="loadingMore"
            name="reload-outline"
            class="mr-1 animate-spin"
          ></ion-icon>
          <ion-icon v-else name="chevron-down-outline" class="mr-1"></ion-icon>
          {{ loadingMore ? "Cargando..." : "Cargar más hilos" }}
        </button>
      </div>
    </div>

    <!-- Estado vacío -->
    <div v-else class="flex flex-col items-center py-16">
      <div class="relative mb-6">
        <div
          class="flex justify-center items-center w-24 h-24 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full"
        >
          <ion-icon
            name="chatbubbles-outline"
            class="text-4xl text-gray-400"
          ></ion-icon>
        </div>
        <div
          class="absolute inset-0 w-24 h-24 bg-blue-200 rounded-full opacity-20 animate-ping"
        ></div>
      </div>

      <h4 class="mb-2 text-lg font-semibold text-gray-900">
        {{ filterUnread ? "Sin mensajes nuevos" : "Sin hilos de conversación" }}
      </h4>

      <p class="max-w-sm text-sm leading-relaxed text-center text-gray-500">
        {{
          filterUnread
            ? "No tienes mensajes sin leer en tus hilos de interés."
            : "Aún no has participado en ningún hilo de interés. Comienza creando conversaciones sobre publicaciones que te interesen."
        }}
      </p>

      <!-- Botón para ir a publicaciones -->
      <!-- <button
        v-if="!filterUnread"
        @click="$emit('ir-a-publicaciones')"
        class="px-4 py-2 mt-6 text-sm font-medium text-white rounded-lg bg-mulbin-600 hover:bg-mulbin-700"
      >
        <ion-icon name="search-outline" class="mr-1"></ion-icon>
        Explorar publicaciones
      </button> -->
    </div>

    <!-- Error state -->
    <div v-if="error" class="p-4 bg-red-50 rounded-lg border border-red-200">
      <div class="flex items-center">
        <ion-icon name="alert-circle" class="mr-2 text-red-600"></ion-icon>
        <div>
          <h4 class="text-sm font-medium text-red-800">
            Error al cargar hilos
          </h4>
          <p class="text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
      <button
        @click="loadFeed"
        class="px-3 py-1 mt-3 text-sm font-medium text-red-600 rounded border border-red-600 hover:bg-red-50"
      >
        Reintentar
      </button>
    </div>

    <!-- Modal de chat (si se necesita aquí) -->
    <ChatHiloModal
      :show="showChatModal"
      :hilo-seleccionado="hiloSeleccionado"
      :token="token"
      @close="cerrarChat"
      @error="handleChatError"
      @success="handleChatSuccess"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, computed } from "vue";
import hilosInteresService from "../../services/hilosInteresService";
import inmobiliarioService from "../../services/inmobiliarioService";
import ddpService from "../../services/ddpService"; // 🆕 AGREGADO: Para getCurrentUserId
import ChatHiloModal from "./ChatHiloModal.vue";

export default defineComponent({
  name: "FeedHilos",

  components: {
    ChatHiloModal,
  },

  props: {
    token: {
      type: String,
      required: true,
    },
  },

  emits: ["ir-a-publicaciones", "error", "success"],

  setup(props, { emit }) {
    // Estados
    const loading = ref(false);
    const loadingMore = ref(false);
    const error = ref("");
    const feedHilos = ref<any[]>([]);
    const feedStats = ref<any>(null);
    const hasMore = ref(true);
    const currentLimit = ref(20);

    // Filtros y ordenamiento
    const filterUnread = ref(false);
    const sortBy = ref("lastMessage");

    // Chat
    const showChatModal = ref(false);
    const hiloSeleccionado = ref<any>(null);

    // Observers (manejados automáticamente por DDP reactivo)

    // Computed
    const currentUserId = computed(() => {
      // 🔧 CORREGIDO: Usar ddpService en lugar de inmobiliarioService
      return ddpService.getCurrentUserId();
    });

    // Métodos
    const loadFeed = async () => {
      if (!currentUserId.value) {
        error.value = "Usuario no autenticado";
        return;
      }

      try {
        loading.value = true;
        error.value = "";

        console.log("📋 Cargando feed de hilos...", {
          userId: currentUserId.value,
          filterUnread: filterUnread.value,
          sortBy: sortBy.value,
          limit: currentLimit.value,
        });

        // 🔧 CORREGIDO: Usar métodos específicos de hilosInteresService
        // Primero conectar DDP con el token
        await inmobiliarioService.connectWithToken(props.token);

        // Suscribirse al feed del usuario
        await hilosInteresService.subscribeToFeedForUser(currentUserId.value!, {
          limit: currentLimit.value,
          sortBy: sortBy.value as
            | "lastMessage"
            | "created"
            | "activity"
            | "alphabetical",
          filterUnread: filterUnread.value,
        });

        // Obtener datos desde la colección local (DDP reactivo)
        const feedData = hilosInteresService.getFeedFromCollection(
          currentUserId.value!
        );

        // 📊 CALCULAR CONTADORES REALES DE MENSAJES NO LEÍDOS
        const feedDataWithCounters = await Promise.all(
          feedData.map(async (hilo: any) => {
            try {
              const unreadCount = await ddpService.call(
                "hilosInteres.getUnreadCountForHilo",
                hilo.id // ✅ CORREGIDO: usar hilo.id en lugar de hilo._id
              );
              console.log(
                `📊 Hilo ${hilo.id}: ${unreadCount} mensajes sin leer`
              );
              return {
                ...hilo,
                unreadMessagesCount: unreadCount || 0,
              };
            } catch (error) {
              console.warn(
                `⚠️ Error obteniendo contador para hilo ${hilo.id}:`,
                error
              );
              return {
                ...hilo,
                unreadMessagesCount: 0,
              };
            }
          })
        );

        feedHilos.value = feedDataWithCounters || [];
        hasMore.value = feedData.length >= currentLimit.value;

        console.log(`✅ Feed cargado: ${feedHilos.value.length} hilos`);
        console.log(
          `🔍 DEBUG: Estructura del primer hilo:`,
          feedHilos.value[0]
        );

        // Cargar estadísticas
        await loadStats();
      } catch (err) {
        console.error("❌ Error cargando feed:", err);
        error.value = err instanceof Error ? err.message : "Error desconocido";
      } finally {
        loading.value = false;
      }
    };

    const loadStats = async () => {
      if (!currentUserId.value) return;

      try {
        // 🔧 CORREGIDO: Usar método específico de hilosInteresService
        const stats = await hilosInteresService.getFeedStats(
          currentUserId.value!
        );
        feedStats.value = stats;
        console.log("📊 Estadísticas del feed:", stats);
      } catch (err) {
        console.warn("⚠️ Error cargando estadísticas:", err);
      }
    };

    const loadMore = async () => {
      if (loadingMore.value || !hasMore.value) return;

      try {
        loadingMore.value = true;
        currentLimit.value += 20;

        // 🔧 CORREGIDO: Actualizar suscripción con nuevo límite
        await hilosInteresService.subscribeToFeedForUser(currentUserId.value!, {
          limit: currentLimit.value,
          sortBy: sortBy.value as
            | "lastMessage"
            | "created"
            | "activity"
            | "alphabetical",
          filterUnread: filterUnread.value,
        });

        // Obtener datos actualizados desde la colección
        const feedData = hilosInteresService.getFeedFromCollection(
          currentUserId.value!
        );

        // 📊 CALCULAR CONTADORES REALES DE MENSAJES NO LEÍDOS
        const feedDataWithCounters = await Promise.all(
          feedData.map(async (hilo: any) => {
            try {
              const unreadCount = await ddpService.call(
                "hilosInteres.getUnreadCountForHilo",
                hilo.id // ✅ CORREGIDO: usar hilo.id en lugar de hilo._id
              );
              return {
                ...hilo,
                unreadMessagesCount: unreadCount || 0,
              };
            } catch (error) {
              console.warn(
                `⚠️ Error obteniendo contador para hilo ${hilo.id}:`,
                error
              );
              return {
                ...hilo,
                unreadMessagesCount: 0,
              };
            }
          })
        );

        feedHilos.value = feedDataWithCounters || [];
        hasMore.value = feedData.length >= currentLimit.value;

        console.log(`📦 Más hilos cargados: ${feedHilos.value.length} total`);
      } catch (err) {
        console.error("❌ Error cargando más hilos:", err);
      } finally {
        loadingMore.value = false;
      }
    };

    const abrirHilo = (hilo: any) => {
      hiloSeleccionado.value = hilo;
      showChatModal.value = true;
    };

    const cerrarChat = () => {
      showChatModal.value = false;
      hiloSeleccionado.value = null;

      // Recargar feed para actualizar contadores
      setTimeout(() => {
        loadFeed();
      }, 500);
    };

    const handleChatError = (message: string) => {
      emit("error", message);
    };

    const handleChatSuccess = (message: string) => {
      emit("success", message);
    };

    // Helpers
    const getSocioName = (hilo: any): string => {
      // Determinar quién es el "socio" (el otro participante)
      const isCreator = hilo.creatorId === currentUserId.value;
      const socioCache = isCreator ? hilo.postAuthorCache : hilo.creatorCache;

      if (socioCache?.firstName || socioCache?.lastName) {
        return `${socioCache.firstName || ""} ${
          socioCache.lastName || ""
        }`.trim();
      }
      return socioCache?.name || "Usuario";
    };

    const getSocioAvatar = (hilo: any): string => {
      const isCreator = hilo.creatorId === currentUserId.value;
      const socioCache = isCreator ? hilo.postAuthorCache : hilo.creatorCache;
      return socioCache?.avatar || "/default-avatar.png";
    };

    const formatearFecha = (fecha: string | Date): string => {
      const d = new Date(fecha);
      const now = new Date();
      const diffMs = now.getTime() - d.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        return d.toLocaleTimeString("es-MX", {
          hour: "2-digit",
          minute: "2-digit",
        });
      } else if (diffDays === 1) {
        return "Ayer";
      } else if (diffDays < 7) {
        return `${diffDays}d`;
      } else {
        return d.toLocaleDateString("es-MX", {
          day: "2-digit",
          month: "short",
        });
      }
    };

    const formatearTiempoTranscurrido = (fecha: string | Date): string => {
      const d = new Date(fecha);
      const now = new Date();
      const diffMs = now.getTime() - d.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffMinutes < 1) {
        return "Ahora";
      } else if (diffMinutes < 60) {
        return `${diffMinutes}min`;
      } else if (diffHours < 24) {
        return `${diffHours}h`;
      } else if (diffDays < 7) {
        return `${diffDays}d`;
      } else {
        return formatearFecha(fecha);
      }
    };

    const formatPrice = (price: number): string => {
      return price.toLocaleString("es-MX");
    };

    // Función para limpiar el título del hilo
    const limpiarTituloHilo = (titulo: string): string => {
      if (!titulo) return "Publicación";

      return titulo
        .replace(/\(\(/g, "") // Eliminar '(('
        .replace(/\)\)/g, "") // Eliminar '))'
        .replace(/\*/g, "") // Eliminar '*'
        .replace(/~/g, "") // Eliminar '~'
        .replace(/_/g, "") // Eliminar '_'
        .trim();
    };

    // Lifecycle
    onMounted(async () => {
      console.log("🚀 FeedHilos montado, cargando datos...");
      await loadFeed();
    });

    onUnmounted(() => {
      console.log("🧹 Limpiando FeedHilos...");
      // Las suscripciones DDP se limpian automáticamente cuando el componente se desmonta
    });

    return {
      // Estados
      loading,
      loadingMore,
      error,
      feedHilos,
      feedStats,
      hasMore,
      filterUnread,
      sortBy,
      showChatModal,
      hiloSeleccionado,
      currentUserId,

      // Métodos
      loadFeed,
      loadMore,
      abrirHilo,
      cerrarChat,
      handleChatError,
      handleChatSuccess,

      // Helpers
      getSocioName,
      getSocioAvatar,
      formatearFecha,
      formatearTiempoTranscurrido,
      formatPrice,
      limpiarTituloHilo,
    };
  },
});
</script>

<style scoped>
/* Animaciones personalizadas */
.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Hover effects */
.group:hover .opacity-0 {
  opacity: 1;
}

/* Loading skeleton effect */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.shimmer {
  animation: shimmer 1.5s ease-in-out infinite;
  background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
  background-size: 800px 104px;
}
</style>
