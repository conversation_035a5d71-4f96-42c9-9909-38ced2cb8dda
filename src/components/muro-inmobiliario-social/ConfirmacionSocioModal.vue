<template>
  <div
    v-if="modelValue"
    class="fixed inset-0 z-50 flex items-center justify-center"
  >
    <div
      class="absolute inset-0 bg-black opacity-50"
      @click="cerrarModal"
    ></div>
    <div
      class="relative z-10 w-full max-w-md p-6 mx-4 rounded-lg shadow-xl"
      :class="
        accion === 'aceptar'
          ? 'bg-blue-50'
          : accion === 'cancelar'
          ? 'bg-orange-50'
          : accion === 'eliminar'
          ? 'bg-red-50'
          : 'bg-red-100'
      "
    >
      <div class="flex items-center justify-between mb-4">
        <h3
          class="text-xl font-semibold"
          :class="
            accion === 'aceptar'
              ? 'text-blue-900'
              : accion === 'cancelar'
              ? 'text-orange-900'
              : accion === 'eliminar'
              ? 'text-red-900'
              : 'text-red-900'
          "
        >
          {{
            accion === "aceptar"
              ? "Confirmar aceptación"
              : accion === "cancelar"
              ? "Confirmar cancelación"
              : accion === "eliminar"
              ? "Eliminar socio"
              : "Confirmar rechazo"
          }}
        </h3>
        <button
          @click="cerrarModal"
          :class="
            accion === 'aceptar'
              ? 'text-blue-500 hover:text-blue-700'
              : accion === 'cancelar'
              ? 'text-orange-500 hover:text-orange-700'
              : accion === 'eliminar'
              ? 'text-red-500 hover:text-red-700'
              : 'text-red-500 hover:text-red-700'
          "
        >
          <ion-icon name="close-outline" size="small"></ion-icon>
        </button>
      </div>

      <div class="mb-6">
        <div class="flex items-center mb-4">
          <img
            v-if="socio?.avatar"
            :src="socio.avatar"
            :alt="socio?.nombre"
            class="w-16 h-16 mr-4 rounded-full"
          />
          <div
            v-else
            class="flex items-center justify-center w-16 h-16 mr-4 bg-gray-200 rounded-full"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-10 h-10 text-gray-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <div>
            <div
              class="font-medium"
              :class="
                accion === 'aceptar'
                  ? 'text-blue-900'
                  : accion === 'cancelar'
                  ? 'text-orange-900'
                  : accion === 'eliminar'
                  ? 'text-red-900'
                  : 'text-red-900'
              "
            >
              {{ socio?.nombre }}
            </div>
            <div
              class="text-sm"
              :class="
                accion === 'aceptar'
                  ? 'text-blue-700'
                  : accion === 'cancelar'
                  ? 'text-orange-700'
                  : accion === 'eliminar'
                  ? 'text-red-700'
                  : 'text-red-700'
              "
            >
              {{ socio?.empresa }}
            </div>
          </div>
        </div>

        <p v-if="accion === 'aceptar'" class="text-blue-800">
          ¿Estás seguro de que deseas aceptar a
          <strong>{{ socio?.nombre }}</strong> como socio directo?
        </p>
        <p v-else-if="accion === 'cancelar'" class="text-orange-800">
          ¿Estás seguro de que deseas cancelar tu solicitud de sociedad con
          <strong>{{ socio?.nombre }}</strong
          >?
        </p>
        <p v-else-if="accion === 'eliminar'" class="text-red-800">
          ¿Estás seguro de que deseas eliminar a
          <strong>{{ socio?.nombre }}</strong> como socio? Esta acción no se
          puede deshacer.
        </p>
        <p v-else class="text-red-800">
          ¿Estás seguro de que deseas rechazar la solicitud de
          <strong>{{ socio?.nombre }}</strong
          >?
        </p>
      </div>

      <div class="flex justify-end space-x-3">
        <button
          @click="cerrarModal"
          :class="[
            'px-4 py-2 rounded-lg border',
            accion === 'aceptar'
              ? 'border-blue-400 text-blue-700 bg-blue-50 hover:bg-blue-200'
              : accion === 'cancelar'
              ? 'border-orange-400 text-orange-700 bg-orange-50 hover:bg-orange-200'
              : accion === 'eliminar'
              ? 'border-red-400 text-red-700 bg-red-50 hover:bg-red-200'
              : 'border-red-400 text-red-700 bg-red-50 hover:bg-red-200',
          ]"
        >
          Cancelar
        </button>
        <button
          @click="confirmar"
          :class="[
            'px-4 py-2 text-white rounded-lg',
            accion === 'aceptar'
              ? 'bg-blue-600 hover:bg-blue-700'
              : accion === 'cancelar'
              ? 'bg-orange-600 hover:bg-orange-700'
              : accion === 'eliminar'
              ? 'bg-red-600 hover:bg-red-700'
              : 'bg-red-600 hover:bg-red-700',
          ]"
        >
          {{
            accion === "aceptar"
              ? "Aceptar"
              : accion === "cancelar"
              ? "Cancelar solicitud"
              : accion === "eliminar"
              ? "Eliminar socio"
              : "Rechazar"
          }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";

interface Socio {
  id: string;
  nombre: string;
  empresa: string;
  ubicacion: string;
  avatar?: string;
  email?: string;
  telefono?: string;
  tipo: "directo" | "indirecto" | "pendiente" | "porAutorizar";
  desde?: string;
  bolsas?: Record<string, { nombre: string; descripcion: string }>;
}

export default defineComponent({
  name: "ConfirmacionSocioModal",

  props: {
    modelValue: {
      type: Boolean,
      required: true,
    },
    socio: {
      type: Object as PropType<Socio | null>,
      default: null,
    },
    accion: {
      type: String as PropType<
        "aceptar" | "rechazar" | "cancelar" | "eliminar"
      >,
      default: "aceptar",
      validator: (value: string) =>
        ["aceptar", "rechazar", "cancelar", "eliminar"].includes(value),
    },
  },

  emits: ["update:modelValue", "confirmar"],

  setup(_, { emit }) {
    const cerrarModal = () => {
      emit("update:modelValue", false);
    };

    const confirmar = () => {
      emit("confirmar");
      cerrarModal();
    };

    return {
      cerrarModal,
      confirmar,
    };
  },
});
</script>
