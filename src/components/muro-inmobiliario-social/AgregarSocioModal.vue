<template>
  <div
    v-if="modelValue"
    class="fixed inset-0 z-50 flex items-center justify-center p-4"
  >
    <div
      class="absolute inset-0 bg-black opacity-50"
      @click="cerrarModal"
    ></div>
    <div
      class="relative z-10 w-full max-w-lg h-[90vh] overflow-y-auto rounded-lg shadow-xl bg-gradient-to-br from-blue-50 to-indigo-100"
    >
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center">
            <div class="p-2 mr-3 bg-blue-600 rounded-full">
              <ion-icon name="person-add" class="text-xl text-white"></ion-icon>
            </div>
            <h3 class="text-xl font-semibold text-blue-900">
              Agregar nuevo socio
            </h3>
          </div>
          <button
            @click="cerrarModal"
            class="text-blue-500 transition-colors hover:text-blue-700"
          >
            <ion-icon name="close-outline" size="small"></ion-icon>
          </button>
        </div>

        <!-- Formulario de invitación -->
        <div v-if="mostrandoInvitacion" class="mb-4">
          <div class="flex items-center mb-3">
            <button
              @click="volverAlBuscador"
              class="p-2 mr-3 text-blue-600 transition-colors rounded-full hover:bg-blue-100"
            >
              <ion-icon name="arrow-back-outline" class="text-xl"></ion-icon>
            </button>
            <h4 class="text-lg font-semibold text-blue-900">
              Invitar a alguien nuevo
            </h4>
          </div>

          <div class="space-y-3">
            <!-- Nombre -->
            <div>
              <label class="block mb-2 text-sm font-medium text-gray-700">
                Nombre completo *
              </label>
              <input
                v-model="datosInvitacion.nombre"
                type="text"
                placeholder="Ej: Juan Pérez"
                class="w-full px-4 py-3 transition-all border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              />
            </div>

            <!-- Email -->
            <div>
              <label class="block mb-2 text-sm font-medium text-gray-700">
                Email *
              </label>
              <input
                v-model="datosInvitacion.email"
                type="email"
                placeholder="Ej: <EMAIL>"
                class="w-full px-4 py-3 transition-all border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              />
            </div>

            <!-- Teléfono -->
            <div>
              <label class="block mb-2 text-sm font-medium text-gray-700">
                Número de teléfono *
              </label>
              <input
                v-model="datosInvitacion.telefono"
                type="tel"
                placeholder="+52 55 1234 5678"
                x-mask="+52 99 9999 9999"
                class="w-full px-4 py-3 transition-all border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              />
            </div>

            <!-- Mensaje -->
            <div>
              <label class="block mb-2 text-sm font-medium text-gray-700">
                Mensaje personal *
              </label>
              <input
                v-model="datosInvitacion.mensaje"
                type="text"
                placeholder="Escribe un mensaje personalizado breve para la invitación..."
                class="w-full px-4 py-3 text-sm transition-all border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              />
              <!-- <textarea
                v-model="datosInvitacion.mensaje"
                rows="5"
                placeholder="El mensaje se generará automáticamente cuando escribas el nombre..."
                class="w-full px-4 py-3 text-sm transition-all border-2 border-gray-200 rounded-lg resize-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              ></textarea> -->
            </div>

            <!-- Botón enviar -->
            <div class="pt-3">
              <button
                @click="enviarInvitacion"
                :disabled="enviandoInvitacion"
                class="flex items-center justify-center w-full px-6 py-2.5 text-white transition-all bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ion-icon
                  :name="
                    enviandoInvitacion
                      ? 'reload-outline'
                      : 'paper-plane-outline'
                  "
                  :class="['mr-2', { 'animate-spin': enviandoInvitacion }]"
                ></ion-icon>
                {{
                  enviandoInvitacion
                    ? "Enviando invitación..."
                    : "Enviar invitación"
                }}
              </button>
            </div>
          </div>
        </div>

        <!-- Buscador con autocomplete -->
        <div v-else class="relative mb-6">
          <label class="block mb-2 text-sm font-medium text-blue-800">
            Buscar persona para agregar como socio
          </label>
          <div class="relative">
            <input
              ref="searchInput"
              type="text"
              placeholder="Escribe nombre, email o empresa..."
              class="w-full px-4 py-3 pl-12 transition-all border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              v-model="searchQuery"
              @input="buscarPersonas"
              @focus="showSuggestions = true"
              @keydown.arrow-down="navigateDown"
              @keydown.arrow-up="navigateUp"
              @keydown.enter="selectHighlighted"
              @keydown.escape="hideSuggestions"
            />
            <div class="absolute inset-y-0 left-0 flex items-center pl-4">
              <ion-icon
                :name="loading ? 'reload-outline' : 'search-outline'"
                :class="['text-blue-400 visible', { 'animate-spin': loading }]"
              ></ion-icon>
            </div>

            <!-- Indicador de carga en el input -->
            <div
              v-if="loading"
              class="absolute inset-y-0 right-0 flex items-center pr-4"
            >
              <div
                class="w-4 h-4 border-2 border-blue-600 rounded-full border-t-transparent animate-spin"
              ></div>
            </div>
          </div>

          <!-- Lista de sugerencias -->
          <div
            v-if="showSuggestions && (searchResults.length > 0 || noResults)"
            class="absolute z-20 w-full mt-2 overflow-y-auto bg-white border-2 border-blue-200 rounded-lg shadow-lg"
          >
            <!-- Resultados de búsqueda -->
            <div v-if="searchResults.length > 0">
              <div
                class="px-4 py-2 text-xs font-semibold text-blue-600 border-b bg-blue-50"
              >
                {{ searchResults.length }} resultado{{
                  searchResults.length !== 1 ? "s" : ""
                }}
                encontrado{{ searchResults.length !== 1 ? "s" : "" }}
              </div>
              <div
                v-for="(person, index) in searchResults"
                :key="person.contrato"
                :class="[
                  'flex items-center justify-between p-4 cursor-pointer transition-colors border-b border-gray-100',
                  highlightedIndex === index
                    ? 'bg-blue-100'
                    : 'hover:bg-gray-50',
                ]"
                @click="enviarSolicitud(person)"
                @mouseenter="highlightedIndex = index"
              >
                <div class="flex items-center flex-1">
                  <img
                    v-if="person.avatar"
                    :src="person.avatar"
                    :alt="person.nombre"
                    class="w-12 h-12 mr-4 border-2 border-blue-200 rounded-full"
                  />
                  <div
                    v-else
                    class="flex items-center justify-center w-12 h-12 mr-4 rounded-full bg-gradient-to-br from-blue-400 to-blue-600"
                  >
                    <ion-icon
                      name="person"
                      class="text-xl text-white"
                    ></ion-icon>
                  </div>
                  <div class="flex-1">
                    <div
                      class="font-semibold text-gray-900"
                      v-html="highlightMatch(person.nombre, searchQuery)"
                    ></div>
                    <div
                      class="text-sm text-gray-600"
                      v-html="highlightMatch(person.empresa, searchQuery)"
                    ></div>
                    <div class="flex items-center mt-1 text-xs text-gray-500">
                      <ion-icon name="location-outline" class="mr-1"></ion-icon>
                      {{ person.ubicacion }}
                    </div>
                  </div>
                </div>
                <div class="ml-4">
                  <button
                    class="flex items-center px-4 py-2 text-sm font-medium text-white transition-colors bg-blue-600 rounded-full hover:bg-blue-700"
                    :disabled="enviandoSolicitud === person.contrato"
                  >
                    <ion-icon
                      :name="
                        enviandoSolicitud === person.contrato
                          ? 'reload-outline'
                          : 'person-add-outline'
                      "
                      :class="[
                        'mr-1 visible',
                        {
                          'animate-spin': enviandoSolicitud === person.contrato,
                        },
                      ]"
                    ></ion-icon>
                    {{
                      enviandoSolicitud === person.contrato
                        ? "Enviando..."
                        : "Agregar"
                    }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Mensaje sin resultados -->
            <div
              v-else-if="noResults && searchQuery.length >= 3"
              class="p-6 text-center border-b border-gray-100"
            >
              <ion-icon
                name="search-outline"
                class="mb-2 text-3xl text-gray-300"
              ></ion-icon>
              <p class="mb-1 text-sm text-gray-500">
                No se encontraron resultados para
              </p>
              <p class="font-medium text-gray-700">"{{ searchQuery }}"</p>
            </div>

            <!-- Mensaje para búsquedas muy cortas -->
            <div
              v-else-if="noResults && searchQuery.length < 3"
              class="p-6 text-center border-b border-gray-100"
            >
              <ion-icon
                name="information-circle-outline"
                class="mb-2 text-3xl text-blue-300"
              ></ion-icon>
              <p class="mb-1 text-sm text-gray-500">
                Escribe al menos 3 caracteres
              </p>
              <p class="text-xs text-gray-400">
                Para obtener mejores resultados de búsqueda
              </p>
            </div>

            <!-- Opción de invitar - SIEMPRE al final -->
            <div
              class="flex items-center justify-between p-4 transition-colors border-t-2 border-green-100 cursor-pointer hover:bg-green-50 group"
              @click="mostrarFormularioInvitacion"
            >
              <div class="flex items-center">
                <div
                  class="flex items-center justify-center w-12 h-12 mr-4 transition-transform rounded-full bg-gradient-to-br from-green-400 to-green-600 group-hover:scale-105"
                >
                  <ion-icon
                    name="person-add-outline"
                    class="text-xl text-white"
                  ></ion-icon>
                </div>
                <div>
                  <div
                    class="font-semibold text-gray-900 group-hover:text-green-700"
                  >
                    Invitar a alguien nuevo
                  </div>
                  <div class="text-sm text-gray-600">
                    ¿No encontraste a quien buscas? Invítalo a unirse
                  </div>
                </div>
              </div>
              <div class="ml-4">
                <button
                  class="flex items-center px-4 py-2 text-sm font-medium text-white transition-all bg-green-600 rounded-full hover:bg-green-700 hover:shadow-md"
                >
                  <ion-icon name="mail-outline" class="mr-1"></ion-icon>
                  Invitar
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Sugerencias rápidas -->
        <div
          v-if="
            !mostrandoInvitacion && !searchQuery && quickSuggestions.length > 0
          "
          class="mb-6"
        >
          <div class="flex items-center mb-3 text-sm font-medium text-blue-800">
            <ion-icon name="flash-outline" class="mr-2"></ion-icon>
            Sugerencias rápidas
          </div>
          <div class="grid grid-cols-1 gap-3 overflow-y-auto max-h-60">
            <div
              v-for="person in quickSuggestions"
              :key="person.contrato"
              class="flex items-center justify-between p-3 transition-all bg-white border border-blue-200 rounded-lg cursor-pointer hover:shadow-md"
              @click="enviarSolicitud(person)"
            >
              <div class="flex items-center">
                <img
                  v-if="person.avatar"
                  :src="person.avatar"
                  :alt="person.nombre"
                  class="w-10 h-10 mr-3 border border-blue-200 rounded-full"
                />
                <div
                  v-else
                  class="flex items-center justify-center w-10 h-10 mr-3 rounded-full bg-gradient-to-br from-blue-400 to-blue-600"
                >
                  <ion-icon name="person" class="text-white"></ion-icon>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ person.nombre }}
                  </div>
                  <div class="text-xs text-gray-500">{{ person.empresa }}</div>
                </div>
              </div>
              <button
                class="px-3 py-1 text-xs font-medium text-white transition-colors bg-blue-600 rounded-full hover:bg-blue-700"
                :disabled="enviandoSolicitud === person.contrato"
              >
                {{ enviandoSolicitud === person.contrato ? "..." : "Agregar" }}
              </button>
            </div>
          </div>
        </div>

        <!-- Mensaje de éxito/error -->
        <div
          v-if="mensaje"
          :class="[
            'p-4 rounded-lg mb-4 flex items-center',
            mensajeExito
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200',
          ]"
        >
          <ion-icon
            :name="mensajeExito ? 'checkmark-circle' : 'alert-circle'"
            class="mr-2 text-lg"
          ></ion-icon>
          {{ mensaje }}
        </div>

        <!-- Botones de acción -->
        <div class="flex justify-end space-x-3">
          <button
            @click="cerrarModal"
            class="px-6 py-2 text-blue-700 transition-colors bg-blue-100 border border-blue-300 rounded-lg hover:bg-blue-200"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, nextTick, watch } from "vue";
import axios from "axios";

interface Persona {
  id: string;
  contrato: string;
  nombre: string;
  empresa: string;
  ubicacion: string;
  avatar?: string;
  email?: string;
}

export default defineComponent({
  name: "AgregarSocioModal",

  props: {
    modelValue: {
      type: Boolean,
      required: true,
    },
  },

  emits: ["update:modelValue", "socio-added"],

  setup(props, { emit }) {
    const searchQuery = ref("");
    const searchResults = ref<Persona[]>([]);
    const quickSuggestions = ref<Persona[]>([]);
    const loading = ref(false);
    const noResults = ref(false);
    const showSuggestions = ref(false);
    const highlightedIndex = ref(-1);
    const enviandoSolicitud = ref<string | null>(null);
    const searchInput = ref<HTMLInputElement>();
    const mensaje = ref("");
    const mensajeExito = ref(true);

    // Estados para el formulario de invitación
    const mostrandoInvitacion = ref(false);
    const datosInvitacion = ref({
      nombre: "",
      email: "",
      telefono: "",
      mensaje: "",
    });
    const enviandoInvitacion = ref(false);

    let searchTimeout: number;

    // Función para buscar personas en la API
    const buscarPersonas = async () => {
      if (searchQuery.value.length < 2) {
        searchResults.value = [];
        noResults.value = false;
        showSuggestions.value = false;
        return;
      }

      // Debounce para evitar muchas llamadas
      clearTimeout(searchTimeout);
      searchTimeout = window.setTimeout(async () => {
        loading.value = true;
        noResults.value = false;

        try {
          const response = await axios.get(`/msi-v5/owner/socios/buscar`, {
            params: {
              q: searchQuery.value,
              limit: 10,
            },
          });

          if (response.data && response.data.statusCode === 200) {
            searchResults.value = response.data.data;
            noResults.value = searchResults.value.length === 0;
            showSuggestions.value = true;
            highlightedIndex.value = -1;
          }
        } catch (error) {
          console.error("Error buscando personas:", error);
          searchResults.value = [];
          noResults.value = true;
        } finally {
          loading.value = false;
        }
      }, 300);
    };

    // Función para mostrar formulario de invitación
    const mostrarFormularioInvitacion = () => {
      // Pre-llenar con datos de búsqueda si existen
      if (searchQuery.value) {
        datosInvitacion.value.nombre = searchQuery.value;
      }

      mostrandoInvitacion.value = true;
      mensaje.value = "";
    };

    // Función para volver al buscador
    const volverAlBuscador = () => {
      mostrandoInvitacion.value = false;
      // Limpiar datos del formulario
      datosInvitacion.value = {
        nombre: "",
        email: "",
        telefono: "",
        mensaje: "",
      };
      mensaje.value = "";
    };

    // Función para enviar invitación
    const enviarInvitacion = async () => {
      if (
        !datosInvitacion.value.nombre ||
        !datosInvitacion.value.email ||
        !datosInvitacion.value.telefono ||
        !datosInvitacion.value.mensaje
      ) {
        mensaje.value = "Por favor completa todos los campos requeridos";
        mensajeExito.value = false;
        return;
      }

      // Validar formato del teléfono (debe tener 10 dígitos después del +52)
      const telefonoLimpio = datosInvitacion.value.telefono.replace(
        /[^0-9]/g,
        ""
      );
      if (telefonoLimpio.length < 12 || !telefonoLimpio.startsWith("52")) {
        mensaje.value = "Por favor ingresa un número de teléfono válido";
        mensajeExito.value = false;
        return;
      }

      // Validar email básico
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(datosInvitacion.value.email)) {
        mensaje.value = "Por favor ingresa un email válido";
        mensajeExito.value = false;
        return;
      }

      enviandoInvitacion.value = true;

      try {
        // Llamada a la API para enviar la invitación
        const response = await axios.post(
          "/msi-v5/owner/socios/invitacion",
          datosInvitacion.value
        );

        console.log(response);

        // Simular envío exitoso por ahora
        await new Promise((resolve) => setTimeout(resolve, 1000));

        mensaje.value = `Invitación enviada a ${datosInvitacion.value.nombre} exitosamente`;
        mensajeExito.value = true;

        // Limpiar formulario después del éxito
        setTimeout(() => {
          volverAlBuscador();
        }, 2000);
      } catch (error) {
        console.error("Error enviando invitación:", error);
        mensaje.value = "Error al enviar la invitación. Intenta nuevamente.";
        mensajeExito.value = false;
      } finally {
        enviandoInvitacion.value = false;
      }
    };

    // Función para enviar solicitud de sociedad
    const enviarSolicitud = async (persona: Persona) => {
      enviandoSolicitud.value = persona.contrato;

      try {
        const response = await axios.post(
          `/msi-v5/owner/socios/${persona.contrato}/solicitar`,
          {},
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.data && response.data.statusCode === 200) {
          mensaje.value = `Solicitud enviada a ${persona.nombre} exitosamente`;
          mensajeExito.value = true;
          const data = response.data.data;

          // Emitir evento para actualizar la lista
          emit("socio-added", {
            ...persona,
            tipo: "pendiente",
            contrato: data.contrato_id || `temp-${Date.now()}`,
          });

          // Limpiar búsqueda
          searchQuery.value = "";
          searchResults.value = [];
          showSuggestions.value = false;
          searchInput.value?.focus();

          // // Cerrar modal después de un momento
          // setTimeout(() => {
          //   cerrarModal();
          // }, 2000);
        } else {
          mensaje.value =
            response.data.message || "Error al enviar la solicitud";
          mensajeExito.value = false;
        }
      } catch (error: any) {
        console.error("Error enviando solicitud:", error);
        mensaje.value =
          error.response?.data?.message || "Error al enviar la solicitud";
        mensajeExito.value = false;
      } finally {
        enviandoSolicitud.value = null;
      }
    };

    // Función para resaltar coincidencias en el texto
    const highlightMatch = (text: string, query: string): string => {
      if (!query || !text) return text;

      const regex = new RegExp(`(${query})`, "gi");
      return text.replace(
        regex,
        '<mark class="px-1 bg-yellow-200 rounded">$1</mark>'
      );
    };

    // Navegación con teclado
    const navigateDown = () => {
      if (highlightedIndex.value < searchResults.value.length - 1) {
        highlightedIndex.value++;
      }
    };

    const navigateUp = () => {
      if (highlightedIndex.value > 0) {
        highlightedIndex.value--;
      }
    };

    const selectHighlighted = () => {
      if (
        highlightedIndex.value >= 0 &&
        searchResults.value[highlightedIndex.value]
      ) {
        enviarSolicitud(searchResults.value[highlightedIndex.value]);
      }
    };

    const hideSuggestions = () => {
      showSuggestions.value = false;
      highlightedIndex.value = -1;
    };

    const cerrarModal = () => {
      // Limpiar estado del buscador
      searchQuery.value = "";
      searchResults.value = [];
      showSuggestions.value = false;
      mensaje.value = "";
      highlightedIndex.value = -1;

      // Limpiar estado del formulario de invitación
      mostrandoInvitacion.value = false;
      datosInvitacion.value = {
        nombre: "",
        email: "",
        telefono: "",
        mensaje: "",
      };
      enviandoInvitacion.value = false;

      emit("update:modelValue", false);
    };

    // Función para generar mensaje personalizado
    const generarMensajePersonalizado = (nombre: string): string => {
      if (!nombre.trim()) return "";

      const nombreCapitalizado = nombre
        .trim()
        .split(" ")
        .map(
          (palabra) =>
            palabra.charAt(0).toUpperCase() + palabra.slice(1).toLowerCase()
        )
        .join(" ");

      return `${nombreCapitalizado}, me gustaría invitarte a formar parte de mi red profesional de socios inmobiliarios.`;
    };

    // Watcher para actualizar el mensaje cuando cambia el nombre
    watch(
      () => datosInvitacion.value.nombre,
      (nuevoNombre) => {
        // Solo actualizar si el mensaje está vacío o es el mensaje generado anteriormente
        const mensajeActual = datosInvitacion.value.mensaje;
        const esVacio = !mensajeActual.trim();
        const esMensajeGenerado = mensajeActual.includes(
          ", me gustaría invitarte a formar parte de mi red profesional de socios inmobiliarios."
        );

        if (esVacio || esMensajeGenerado) {
          datosInvitacion.value.mensaje =
            generarMensajePersonalizado(nuevoNombre);
        }
      }
    );

    // Al montar el componente
    onMounted(async () => {
      // await obtenerSugerenciasRapidas();

      // Usar watch para detectar cuando el modal se abre
      watch(
        () => props.modelValue,
        (newValue) => {
          if (newValue) {
            nextTick(() => {
              if (searchInput.value) {
                searchInput.value.focus();
              }
            });
          }
        }
      );
    });

    return {
      searchQuery,
      searchResults,
      quickSuggestions,
      loading,
      noResults,
      showSuggestions,
      highlightedIndex,
      enviandoSolicitud,
      searchInput,
      mensaje,
      mensajeExito,
      buscarPersonas,
      enviarSolicitud,
      highlightMatch,
      navigateDown,
      navigateUp,
      selectHighlighted,
      hideSuggestions,
      cerrarModal,
      // Para formulario de invitación
      mostrandoInvitacion,
      datosInvitacion,
      enviandoInvitacion,
      mostrarFormularioInvitacion,
      volverAlBuscador,
      enviarInvitacion,
    };
  },
});
</script>
