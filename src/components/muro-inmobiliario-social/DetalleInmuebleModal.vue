<template>
  <!-- Overlay del modal -->
  <div
    v-if="modelValue"
    class="flex fixed inset-0 z-50 justify-center items-center p-4 bg-black bg-opacity-50"
    @click="cerrarModal"
  >
    <!-- Contenido del modal -->
    <div
      class="overflow-hidden relative w-full max-w-5xl h-[90vh] bg-white rounded-lg shadow-xl flex flex-col"
      @click.stop
    >
      <!-- Header del modal -->
      <div
        class="flex sticky top-0 z-10 flex-shrink-0 justify-between items-center p-4 bg-white border-b"
      >
        <h2 class="text-xl font-bold text-gray-900">Detalle del Inmueble</h2>
        <button
          @click="cerrarModal"
          class="p-2 text-gray-400 rounded-full hover:text-gray-600 hover:bg-gray-100"
        >
          <ion-icon name="close" class="text-2xl"></ion-icon>
        </button>
      </div>

      <!-- Contenido del modal - área scrolleable -->
      <div v-if="inmueble" class="overflow-y-auto flex-1 pb-20">
        <!-- <PERSON><PERSON><PERSON> de imágenes -->
        <div class="relative">
          <img
            :src="imagenActual"
            :alt="inmueble.titulo"
            class="object-cover w-full h-80 cursor-pointer md:h-96"
            @click="abrirVisorImagenes"
          />

          <!-- Navegación de imágenes -->
          <div
            v-if="inmueble.imagenes.length > 1"
            class="absolute bottom-4 left-1/2 transform -translate-x-1/2"
          >
            <div class="flex space-x-2">
              <button
                v-for="(_, index) in inmueble.imagenes"
                :key="index"
                @click="imagenActualIndex = index"
                :class="[
                  'w-3 h-3 rounded-full transition-all duration-200',
                  imagenActualIndex === index
                    ? 'bg-white scale-125'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75',
                ]"
              ></button>
            </div>
          </div>

          <!-- Navegación con flechas -->
          <div
            v-if="inmueble.imagenes.length > 1"
            class="flex absolute inset-y-0 left-0 items-center"
          >
            <button
              @click="imagenAnterior"
              class="p-2 ml-4 text-white bg-black bg-opacity-50 rounded-full transition-all hover:bg-opacity-75"
            >
              <ion-icon name="chevron-back-outline" class="text-xl"></ion-icon>
            </button>
          </div>
          <div
            v-if="inmueble.imagenes.length > 1"
            class="flex absolute inset-y-0 right-0 items-center"
          >
            <button
              @click="imagenSiguiente"
              class="p-2 mr-4 text-white bg-black bg-opacity-50 rounded-full transition-all hover:bg-opacity-75"
            >
              <ion-icon
                name="chevron-forward-outline"
                class="text-xl"
              ></ion-icon>
            </button>
          </div>

          <!-- 🆕 NUEVO: Badges de operaciones múltiples -->
          <div class="absolute top-4 left-4">
            <!-- Operación prioritaria (más grande y destacada) -->
            <div class="mb-1">
              <span
                :class="[
                  'inline-block px-3 py-1.5 text-sm font-bold text-white rounded-lg shadow-lg',
                  getOperacionBadgeColor(
                    getOperacionPrioritaria(inmueble).tipo,
                    true
                  ),
                ]"
              >
                {{ getOperacionPrioritaria(inmueble).tipo?.toUpperCase() }}
                <span class="ml-1 text-xs opacity-90">
                  ({{
                    formatPriceWithCurrency(getOperacionPrioritaria(inmueble))
                  }})
                </span>
              </span>
            </div>

            <!-- Operaciones secundarias (más pequeñas) -->
            <div
              v-if="getOperacionesSecundarias(inmueble).length > 0"
              class="flex flex-wrap gap-1"
            >
              <span
                v-for="operacion in getOperacionesSecundarias(inmueble)"
                :key="operacion.tipo"
                :class="[
                  'inline-block px-2 py-0.5 text-xs font-medium text-white rounded opacity-80 hover:opacity-100 transition-opacity',
                  getOperacionBadgeColor(operacion.tipo, false),
                ]"
                :title="`${operacion.tipo}: ${formatPriceWithCurrency(
                  operacion
                )}`"
              >
                {{ operacion.tipo.toUpperCase() }}
              </span>
            </div>
          </div>

          <!-- Botón favorito -->
          <div v-if="false" class="absolute top-4 right-4">
            <button
              @click="toggleFavorito"
              class="p-2 bg-white bg-opacity-80 rounded-full shadow-lg transition-all hover:bg-opacity-100"
              :class="inmueble.esFavorito ? 'text-yellow-500' : 'text-gray-400'"
            >
              <ion-icon
                :name="inmueble.esFavorito ? 'star' : 'star-outline'"
                class="text-xl"
              ></ion-icon>
            </button>
          </div>

          <!-- Contador de imágenes -->
          <div
            v-if="inmueble.imagenes.length > 1"
            class="absolute top-4 left-1/2 transform -translate-x-1/2"
          >
            <div
              class="px-3 py-1 text-sm text-white bg-black bg-opacity-50 rounded-full"
            >
              {{ imagenActualIndex + 1 }} / {{ inmueble.imagenes.length }}
            </div>
          </div>

          <!-- Indicador de que se puede hacer clic para ampliar -->
          <div class="absolute right-4 bottom-4">
            <div
              class="flex items-center px-2 py-1 text-xs text-white bg-black bg-opacity-50 rounded-full"
            >
              <ion-icon name="expand-outline" class="mr-1"></ion-icon>
              <span class="hidden sm:inline">Clic para ampliar</span>
              <span class="sm:hidden">Toca para ampliar</span>
            </div>
          </div>
        </div>

        <!-- Información principal -->
        <div class="p-6">
          <!-- Precio y título -->
          <div class="flex justify-between items-start mb-6">
            <div class="flex-1">
              <h1 class="mb-3 text-3xl font-bold text-gray-900">
                {{ inmueble.titulo.replace(/\(\(/g, "").replace(/\)\)/g, "") }}
              </h1>
              <p class="text-lg leading-relaxed text-gray-600">
                {{ inmueble.descripcion }}
              </p>
            </div>

            <!-- 🆕 NUEVO: Sección de precios múltiples -->
            <div class="flex-shrink-0 ml-6 min-w-0 text-right">
              <!-- Precio prioritario (más grande) -->
              <div class="mb-2">
                <p class="text-4xl font-bold text-green-600">
                  {{
                    formatPriceWithCurrency(getOperacionPrioritaria(inmueble))
                  }}
                </p>
                <p
                  v-if="getOperacionPrioritaria(inmueble).tipo === 'renta'"
                  class="mt-1 text-sm text-gray-500"
                >
                  {{ getOperacionPrioritaria(inmueble).periodo || "/ mes" }}
                </p>
                <p
                  v-if="getOperacionPrioritaria(inmueble).comision > 0"
                  class="mt-0.5 text-xs text-gray-400"
                >
                  {{ getOperacionPrioritaria(inmueble).comision }}% comisión
                </p>
              </div>

              <!-- Precios secundarios (más pequeños) -->
              <div
                v-if="getOperacionesSecundarias(inmueble).length > 0"
                class="pt-2 space-y-1 border-t border-gray-200"
              >
                <div
                  v-for="operacion in getOperacionesSecundarias(inmueble)"
                  :key="operacion.tipo"
                  class="text-right"
                >
                  <p class="text-lg font-semibold text-gray-700">
                    {{ formatPriceWithCurrency(operacion) }}
                  </p>
                  <p class="text-xs text-gray-500 capitalize">
                    {{ operacion.tipo }}
                    <span v-if="operacion.tipo === 'renta'">
                      {{
                        operacion.periodo ? ` ${operacion.periodo}` : " / mes"
                      }}
                    </span>
                  </p>
                  <p
                    v-if="operacion.comision > 0"
                    class="text-xs text-gray-400"
                  >
                    {{ operacion.comision }}% comisión
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Características principales -->
          <div class="grid grid-cols-2 gap-4 mb-8 md:grid-cols-4">
            <div
              v-if="inmueble.recamaras"
              class="p-4 text-center bg-gray-50 rounded-lg transition-colors hover:bg-gray-100"
            >
              <ion-icon
                name="bed-outline"
                class="mb-2 text-3xl text-gray-600"
              ></ion-icon>
              <p class="text-lg font-medium text-gray-900">
                {{ inmueble.recamaras }}
              </p>
              <p class="text-sm text-gray-500">Recámaras</p>
            </div>
            <div
              v-if="inmueble.banos"
              class="p-4 text-center bg-gray-50 rounded-lg transition-colors hover:bg-gray-100"
            >
              <ion-icon
                name="water-outline"
                class="mb-2 text-3xl text-gray-600"
              ></ion-icon>
              <p class="text-lg font-medium text-gray-900">
                {{ inmueble.banos }}
              </p>
              <p class="text-sm text-gray-500">Baños</p>
            </div>
            <div
              v-if="inmueble.area"
              class="p-4 text-center bg-gray-50 rounded-lg transition-colors hover:bg-gray-100"
            >
              <ion-icon
                name="resize-outline"
                class="mb-2 text-3xl text-gray-600"
              ></ion-icon>
              <p class="text-lg font-medium text-gray-900">
                {{ inmueble.area }} m²
              </p>
              <p class="text-sm text-gray-500">Área</p>
            </div>
            <div
              class="p-4 text-center bg-gray-50 rounded-lg transition-colors hover:bg-gray-100"
            >
              <ion-icon
                name="location-outline"
                class="mb-2 text-3xl text-gray-600"
              ></ion-icon>
              <p class="text-lg font-medium text-gray-900">
                {{ getLocationLabel(inmueble.ubicacion) }}
              </p>
              <p class="text-sm text-gray-500">Ubicación</p>
            </div>
          </div>

          <!-- 🆕 NUEVO: Sección de operaciones disponibles -->
          <div
            v-if="inmueble.operaciones && inmueble.operaciones.length > 1"
            class="mb-8"
          >
            <h3 class="mb-4 text-xl font-semibold text-gray-900">
              Operaciones Disponibles
            </h3>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div
                v-for="operacion in inmueble.operaciones"
                :key="operacion.tipo"
                :class="[
                  'p-4 rounded-lg border-2 transition-all duration-200',
                  operacion.tipo === getOperacionPrioritaria(inmueble).tipo
                    ? 'border-green-300 bg-green-50'
                    : 'border-gray-200 bg-gray-50 hover:border-gray-300',
                ]"
              >
                <div class="flex justify-between items-start mb-2">
                  <div class="flex items-center">
                    <span
                      :class="[
                        'inline-block px-2 py-1 text-xs font-bold text-white rounded',
                        getOperacionBadgeColor(
                          operacion.tipo,
                          operacion.tipo ===
                            getOperacionPrioritaria(inmueble).tipo
                        ),
                      ]"
                    >
                      {{ operacion.tipo.toUpperCase() }}
                    </span>
                    <span
                      v-if="
                        operacion.tipo ===
                        getOperacionPrioritaria(inmueble).tipo
                      "
                      class="px-2 py-0.5 ml-2 text-xs font-medium text-green-700 bg-green-100 rounded"
                    >
                      Principal
                    </span>
                  </div>
                </div>

                <div class="space-y-1">
                  <p class="text-2xl font-bold text-gray-900">
                    {{ formatPriceWithCurrency(operacion) }}
                  </p>
                  <div class="text-sm text-gray-600">
                    <p v-if="operacion.tipo === 'renta'">
                      <ion-icon name="time-outline" class="mr-1"></ion-icon>
                      {{ operacion.periodo || "Mensual" }}
                    </p>
                    <p v-if="operacion.comision > 0">
                      <ion-icon
                        name="trending-up-outline"
                        class="mr-1"
                      ></ion-icon>
                      {{ operacion.comision }}% comisión
                    </p>
                    <p v-if="operacion.moneda && operacion.moneda !== 'MXN'">
                      <ion-icon name="card-outline" class="mr-1"></ion-icon>
                      Moneda: {{ operacion.moneda }}
                    </p>
                    <p>
                      <ion-icon
                        :name="
                          operacion.disponible
                            ? 'checkmark-circle-outline'
                            : 'close-circle-outline'
                        "
                        :class="
                          operacion.disponible
                            ? 'text-green-600'
                            : 'text-red-600'
                        "
                        class="mr-1"
                      ></ion-icon>
                      {{
                        operacion.disponible ? "Disponible" : "No disponible"
                      }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Información adicional del inmueble -->
          <div class="mb-8">
            <h3 class="mb-4 text-xl font-semibold text-gray-900">
              Información Adicional
            </h3>
            <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
              <div class="flex items-center text-gray-600">
                <ion-icon
                  name="calendar-outline"
                  class="mr-2 text-lg"
                ></ion-icon>
                <span class="text-sm"
                  >Publicado: {{ formatFecha(inmueble.fechaCreacion) }}</span
                >
              </div>
              <div class="flex items-center text-gray-600">
                <ion-icon name="home-outline" class="mr-2 text-lg"></ion-icon>
                <span class="text-sm"
                  >Tipo: {{ formatTipo(inmueble.tipo) }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer fijo con información del corredor -->
      <div
        v-if="inmueble"
        class="absolute right-0 bottom-0 left-0 bg-white border-t shadow-lg"
      >
        <div class="p-4">
          <div class="flex justify-between items-center">
            <!-- Información del corredor -->
            <div class="flex flex-1 items-center">
              <img
                :src="inmueble.socio.avatar || `${mulbinUrl}/images/avatar.png`"
                :alt="inmueble.socio.nombre"
                class="mr-3 w-12 h-12 rounded-full border-2 border-gray-200"
              />
              <div class="flex-1 min-w-0">
                <p class="font-semibold text-gray-900 truncate">
                  {{ inmueble.socio.nombre }}
                </p>
                <p class="text-sm text-gray-600 truncate">
                  {{ inmueble.socio.empresa }}
                </p>
                <div
                  v-if="inmueble.socio.telefono"
                  class="flex items-center mt-1"
                >
                  <ion-icon
                    name="call-outline"
                    class="mr-1 text-xs text-gray-400"
                  ></ion-icon>
                  <span class="text-xs text-gray-500">{{
                    inmueble.socio.telefono
                  }}</span>
                </div>
              </div>
            </div>

            <!-- Botones de contacto -->
            <div class="flex ml-4 space-x-2">
              <button
                v-if="inmueble.socio.whatsapp"
                @click="$emit('contactar', inmueble.socio, 'whatsapp')"
                class="flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg shadow-md transition-colors hover:bg-green-700"
              >
                <ion-icon name="logo-whatsapp" class="mr-2"></ion-icon>
                WhatsApp
              </button>
              <button
                v-if="inmueble.socio.telefono"
                @click="$emit('contactar', inmueble.socio, 'telefono')"
                class="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg shadow-md transition-colors hover:bg-blue-700"
              >
                <ion-icon name="call-outline" class="mr-2"></ion-icon>
                Llamar
              </button>
              <button
                v-if="inmueble.socio.email"
                @click="$emit('contactar', inmueble.socio, 'email')"
                class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg shadow-md transition-colors hover:bg-gray-200"
              >
                <ion-icon name="mail-outline" class="mr-2"></ion-icon>
                Email
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Visor de imágenes en pantalla completa -->
  <div
    v-if="mostrarVisorImagenes"
    class="fixed inset-0 z-[100] bg-black bg-opacity-95 flex items-center justify-center"
    @click="cerrarVisorImagenes"
  >
    <!-- Contenedor principal del visor -->
    <div
      class="flex relative justify-center items-center w-full h-full"
      @click.stop
    >
      <!-- Imagen principal -->
      <div
        class="flex relative justify-center items-center max-w-full max-h-full"
        ref="imageContainer"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <img
          :src="inmueble?.imagenes[imagenVisorIndex] || ''"
          :alt="`Imagen ${imagenVisorIndex + 1}`"
          class="object-contain max-w-full max-h-full select-none"
          style="max-height: 90vh; max-width: 90vw"
          @dragstart.prevent
        />

        <!-- Indicador de swipe en móviles -->
        <div
          v-if="esMobile && inmueble && inmueble.imagenes.length > 1"
          class="absolute bottom-4 left-1/2 px-3 py-1 text-sm text-white bg-black bg-opacity-50 rounded-full transform -translate-x-1/2"
        >
          Desliza para navegar
        </div>
      </div>

      <!-- Botón cerrar -->
      <button
        @click="cerrarVisorImagenes"
        class="absolute top-4 right-4 z-10 p-3 text-white bg-black bg-opacity-50 rounded-full transition-all hover:bg-opacity-75"
      >
        <ion-icon name="close" class="text-2xl"></ion-icon>
      </button>

      <!-- Contador de imágenes -->
      <div
        v-if="inmueble && inmueble.imagenes.length > 1"
        class="absolute top-4 left-1/2 px-4 py-2 text-white bg-black bg-opacity-50 rounded-full transform -translate-x-1/2"
      >
        {{ imagenVisorIndex + 1 }} / {{ inmueble.imagenes.length }}
      </div>

      <!-- Navegación con flechas (solo en desktop) -->
      <div v-if="!esMobile && inmueble && inmueble.imagenes.length > 1">
        <button
          @click="imagenVisorAnterior"
          class="absolute left-4 top-1/2 p-3 text-white bg-black bg-opacity-50 rounded-full transition-all transform -translate-y-1/2 hover:bg-opacity-75"
        >
          <ion-icon name="chevron-back-outline" class="text-3xl"></ion-icon>
        </button>
        <button
          @click="imagenVisorSiguiente"
          class="absolute right-4 top-1/2 p-3 text-white bg-black bg-opacity-50 rounded-full transition-all transform -translate-y-1/2 hover:bg-opacity-75"
        >
          <ion-icon name="chevron-forward-outline" class="text-3xl"></ion-icon>
        </button>
      </div>

      <!-- Thumbnails en la parte inferior -->
      <div
        v-if="inmueble && inmueble.imagenes.length > 1"
        class="flex overflow-x-auto absolute bottom-4 left-1/2 space-x-2 max-w-xs transform -translate-x-1/2"
      >
        <button
          v-for="(imagen, index) in inmueble.imagenes"
          :key="index"
          @click="imagenVisorIndex = index"
          :class="[
            'flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all',
            imagenVisorIndex === index
              ? 'border-white scale-110'
              : 'border-transparent opacity-60 hover:opacity-80',
          ]"
        >
          <img
            :src="imagen"
            :alt="`Thumbnail ${index + 1}`"
            class="object-cover w-full h-full"
          />
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  watch,
  onMounted,
  onUnmounted,
} from "vue";
import favoritosService from "../../services/favoritosService";

interface Socio {
  id: string;
  nombre: string;
  empresa: string;
  avatar?: string;
  telefono?: string;
  whatsapp?: string;
  email?: string;
}

interface Operacion {
  tipo: "venta" | "renta" | "traspaso";
  precio: number;
  precio_formateado: string;
  comision: number;
  moneda: string;
  disponible: boolean;
  periodo?: string;
}

interface Inmueble {
  id: string;
  titulo: string;
  descripcion: string;
  operaciones: Operacion[]; // 🆕 NUEVO: Array de operaciones
  operacion_principal: "venta" | "renta" | "traspaso"; // 🆕 COMPATIBILIDAD
  precio_principal: number; // 🆕 COMPATIBILIDAD
  precio: number; // 🔄 DEPRECATED: Mantenido para compatibilidad
  operacion: "venta" | "renta" | "traspaso"; // 🔄 DEPRECATED: Mantenido para compatibilidad
  tipo: string;
  ubicacion: string;
  recamaras?: number;
  banos?: number;
  area?: number;
  imagenPrincipal?: string;
  imagenes: string[];
  socio: Socio;
  esFavorito: boolean;
  fechaCreacion: string;
}

export default defineComponent({
  name: "DetalleInmuebleModal",

  props: {
    modelValue: {
      type: Boolean,
      required: true,
    },
    inmueble: {
      type: Object as () => Inmueble | null,
      default: null,
    },
  },

  emits: ["update:modelValue", "contactar"],

  setup(props, { emit }) {
    const mulbinUrl = import.meta.env.VITE_MULBIN_URL;
    const imagenActualIndex = ref(0);

    // Visor de imágenes
    const mostrarVisorImagenes = ref(false);
    const imagenVisorIndex = ref(0);
    const imageContainer = ref<HTMLElement | null>(null);

    // Touch/swipe handling
    const touchStartX = ref(0);
    const touchStartY = ref(0);
    const touchEndX = ref(0);
    const touchEndY = ref(0);
    const minSwipeDistance = 50;

    // Detectar si es móvil
    const esMobile = ref(false);

    // Imagen actual en la galería
    const imagenActual = computed(() => {
      if (!props.inmueble || !props.inmueble.imagenes.length) {
        return `${mulbinUrl}/images/no-image.png`;
      }
      return props.inmueble.imagenes[imagenActualIndex.value];
    });

    // Detectar dispositivo móvil
    const detectarMobile = () => {
      esMobile.value = window.innerWidth <= 768 || "ontouchstart" in window;
    };

    // Resetear índice de imagen cuando cambia el inmueble
    watch(
      () => props.inmueble,
      () => {
        imagenActualIndex.value = 0;
        imagenVisorIndex.value = 0;
      }
    );

    // Navegación de imágenes en el modal principal
    const imagenAnterior = () => {
      if (!props.inmueble) return;
      imagenActualIndex.value =
        imagenActualIndex.value === 0
          ? props.inmueble.imagenes.length - 1
          : imagenActualIndex.value - 1;
    };

    const imagenSiguiente = () => {
      if (!props.inmueble) return;
      imagenActualIndex.value =
        imagenActualIndex.value === props.inmueble.imagenes.length - 1
          ? 0
          : imagenActualIndex.value + 1;
    };

    // Navegación de imágenes en el visor
    const imagenVisorAnterior = () => {
      if (!props.inmueble) return;
      imagenVisorIndex.value =
        imagenVisorIndex.value === 0
          ? props.inmueble.imagenes.length - 1
          : imagenVisorIndex.value - 1;
    };

    const imagenVisorSiguiente = () => {
      if (!props.inmueble) return;
      imagenVisorIndex.value =
        imagenVisorIndex.value === props.inmueble.imagenes.length - 1
          ? 0
          : imagenVisorIndex.value + 1;
    };

    // Abrir visor de imágenes
    const abrirVisorImagenes = () => {
      imagenVisorIndex.value = imagenActualIndex.value;
      mostrarVisorImagenes.value = true;
      document.body.style.overflow = "hidden"; // Prevenir scroll del body
    };

    // Cerrar visor de imágenes
    const cerrarVisorImagenes = () => {
      mostrarVisorImagenes.value = false;
      document.body.style.overflow = ""; // Restaurar scroll del body
    };

    // Manejo de eventos táctiles
    const handleTouchStart = (e: TouchEvent) => {
      touchStartX.value = e.touches[0].clientX;
      touchStartY.value = e.touches[0].clientY;
    };

    const handleTouchMove = (e: TouchEvent) => {
      // Prevenir scroll durante el swipe
      e.preventDefault();
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (!props.inmueble || props.inmueble.imagenes.length <= 1) return;

      touchEndX.value = e.changedTouches[0].clientX;
      touchEndY.value = e.changedTouches[0].clientY;

      const deltaX = touchStartX.value - touchEndX.value;
      const deltaY = Math.abs(touchStartY.value - touchEndY.value);

      // Solo procesar swipe horizontal si el movimiento vertical es menor
      if (deltaY < 100 && Math.abs(deltaX) > minSwipeDistance) {
        if (deltaX > 0) {
          // Swipe izquierda - siguiente imagen
          imagenVisorSiguiente();
        } else {
          // Swipe derecha - imagen anterior
          imagenVisorAnterior();
        }
      }
    };

    // Manejo de teclas
    const handleKeydown = (e: KeyboardEvent) => {
      if (!mostrarVisorImagenes.value || !props.inmueble) return;

      switch (e.key) {
        case "Escape":
          cerrarVisorImagenes();
          break;
        case "ArrowLeft":
          e.preventDefault();
          imagenVisorAnterior();
          break;
        case "ArrowRight":
          e.preventDefault();
          imagenVisorSiguiente();
          break;
      }
    };

    // Cerrar modal
    const cerrarModal = () => {
      emit("update:modelValue", false);
    };

    // 🆕 NUEVO: Toggle favorito con persistencia
    const toggleFavorito = () => {
      if (!props.inmueble) return;

      const operacionPrioritaria = getOperacionPrioritaria(props.inmueble);

      const exito = favoritosService.toggleFavorito({
        id: props.inmueble.id,
        titulo: props.inmueble.titulo,
        precio: operacionPrioritaria.precio, // 🆕 Usar precio de operación prioritaria
        operacion: operacionPrioritaria.tipo, // 🆕 Usar operación prioritaria
        ubicacion: props.inmueble.ubicacion,
      });

      if (exito) {
        // Actualizar estado local solo si el toggle fue exitoso
        props.inmueble.esFavorito = !props.inmueble.esFavorito;

        console.log(
          `⭐ Inmueble ${props.inmueble.id} ${
            props.inmueble.esFavorito ? "agregado a" : "removido de"
          } favoritos desde modal (${
            operacionPrioritaria.tipo
          }: ${formatPriceWithCurrency(operacionPrioritaria)})`
        );
      } else {
        console.warn(
          "⚠️ No se pudo actualizar el estado de favorito desde modal"
        );
      }
    };

    // 🆕 NUEVO: Obtener operación prioritaria para mostrar en el modal
    const getOperacionPrioritaria = (inmueble: Inmueble): Operacion => {
      if (!inmueble.operaciones || inmueble.operaciones.length === 0) {
        // Fallback a datos de compatibilidad
        return {
          tipo: inmueble.operacion || inmueble.operacion_principal,
          precio: inmueble.precio || inmueble.precio_principal || 0,
          precio_formateado: `$${formatPrice(
            inmueble.precio || inmueble.precio_principal || 0
          )}`,
          comision: 0,
          moneda: "MXN",
          disponible: true,
        };
      }

      // Usar la primera operación (ya ordenada por prioridad en el backend)
      return inmueble.operaciones[0];
    };

    // 🆕 NUEVO: Obtener operaciones secundarias (no prioritarias)
    const getOperacionesSecundarias = (inmueble: Inmueble): Operacion[] => {
      if (!inmueble.operaciones || inmueble.operaciones.length <= 1) {
        return [];
      }

      const prioritaria = getOperacionPrioritaria(inmueble);
      return inmueble.operaciones.filter((op) => op.tipo !== prioritaria.tipo);
    };

    // 🆕 NUEVO: Formatear precio con moneda
    const formatPriceWithCurrency = (operacion: Operacion): string => {
      if (operacion.precio_formateado) {
        return operacion.precio_formateado;
      }
      return `$${formatPrice(operacion.precio)} ${operacion.moneda || "MXN"}`;
    };

    // 🆕 NUEVO: Obtener color del badge según tipo de operación
    const getOperacionBadgeColor = (
      tipo: string,
      esPrioritaria: boolean = false
    ): string => {
      const baseColors = {
        venta: esPrioritaria ? "bg-green-600" : "bg-green-500",
        renta: esPrioritaria ? "bg-blue-600" : "bg-blue-500",
        traspaso: esPrioritaria ? "bg-orange-600" : "bg-orange-500",
      };
      return baseColors[tipo as keyof typeof baseColors] || "bg-gray-500";
    };

    // Formatear precio con validación
    const formatPrice = (price: number | undefined | null) => {
      if (price === undefined || price === null || isNaN(price)) {
        return "0";
      }
      return price.toLocaleString("es-MX");
    };

    // Formatear fecha
    const formatFecha = (fecha: string) => {
      if (!fecha) return "No disponible";
      try {
        return new Date(fecha).toLocaleDateString("es-MX", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      } catch {
        return "No disponible";
      }
    };

    // Formatear tipo de inmueble
    const formatTipo = (tipo: string) => {
      const tipos: Record<string, string> = {
        casa: "Casa",
        departamento: "Departamento",
        terreno: "Terreno",
        local: "Local Comercial",
        oficina: "Oficina",
        bodega: "Bodega",
        otro: "Otro",
      };
      return tipos[tipo] || tipo;
    };

    // Obtener label de ubicación
    const getLocationLabel = (location: string) => {
      const labels: Record<string, string> = {
        norte: "Zona Norte",
        sur: "Zona Sur",
        este: "Zona Este",
        oeste: "Zona Oeste",
        centro: "Centro",
      };
      return labels[location] || location;
    };

    // Lifecycle hooks
    onMounted(() => {
      detectarMobile();
      window.addEventListener("resize", detectarMobile);
      window.addEventListener("keydown", handleKeydown);
    });

    onUnmounted(() => {
      window.removeEventListener("resize", detectarMobile);
      window.removeEventListener("keydown", handleKeydown);
      document.body.style.overflow = ""; // Limpiar estilos al desmontar
    });

    return {
      mulbinUrl,
      imagenActualIndex,
      imagenActual,
      imagenAnterior,
      imagenSiguiente,
      // Visor de imágenes
      mostrarVisorImagenes,
      imagenVisorIndex,
      imageContainer,
      esMobile,
      abrirVisorImagenes,
      cerrarVisorImagenes,
      imagenVisorAnterior,
      imagenVisorSiguiente,
      handleTouchStart,
      handleTouchMove,
      handleTouchEnd,
      // Otros métodos
      cerrarModal,
      toggleFavorito,
      formatPrice,
      formatFecha,
      formatTipo,
      getLocationLabel,
      // 🆕 NUEVO: Funciones para manejo de operaciones múltiples
      getOperacionPrioritaria,
      getOperacionesSecundarias,
      formatPriceWithCurrency,
      getOperacionBadgeColor,
    };
  },
});
</script>

<style scoped>
/* Asegurar que el modal use toda la altura disponible */
.h-\[90vh\] {
  height: 90vh;
}

/* Z-index para el visor de imágenes */
.z-\[100\] {
  z-index: 100;
}

/* Transiciones suaves */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.transition-colors {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Hover effects mejorados */
.hover\:scale-125:hover {
  transform: scale(1.25);
}

/* Sombras personalizadas */
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Prevenir selección de texto en el visor */
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Mejorar el scroll de thumbnails */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
