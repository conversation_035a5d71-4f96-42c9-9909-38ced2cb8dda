import { createApp } from "vue";
import Alpine from "alpinejs";
import mask from "@alpinejs/mask";
import BaseMultibolsaInmobiliaria from "./BaseMultibolsaInmobiliaria.vue";
import ddpService from "../../services/ddpService";
import { createDDPWithPlugins, ws } from "../../services/simpleddpWrapper";

// Registrar el plugin x-mask
Alpine.plugin(mask);

// Inicializar Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Exponer una instancia global para pruebas
window.createTestMeteorConnection = () => {
  const opts = {
    endpoint:
      import.meta.env.VITE_METEOR_URL || "ws://localhost:3000/websocket",
    SocketConstructor: ws,
    reconnectInterval: 5000,
  };

  return createDDPWithPlugins(opts);
};

// Inicializar DDP de manera explícita para evitar problemas de carga
try {
  // Exponemos el servicio y creamos una instancia adicional para diagnóstico
  window.ddpService = ddpService;
  // window.testDDP = window.createTestMeteorConnection();

  console.log("Servicio DDP inicializado correctamente para MBI");
  // console.log("Instancia de prueba creada:", window.testDDP);
} catch (error) {
  console.error("Error inicializando servicio DDP para MBI:", error);
}

// Inicializar Vue cuando el DOM esté cargado
document.addEventListener("DOMContentLoaded", () => {
  // Cargar Ionicons si no están ya cargados
  if (!document.querySelector('script[src*="ionicons"]')) {
    const moduleScript = document.createElement("script");
    moduleScript.type = "module";
    moduleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js";
    document.body.appendChild(moduleScript);

    const noModuleScript = document.createElement("script");
    noModuleScript.noModule = true;
    noModuleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js";
    document.body.appendChild(noModuleScript);
  }

  // Buscar el elemento específico para BaseMultibolsaInmobiliaria
  const targetElement = document.querySelector(
    "[data-base-multibolsa-inmobiliaria]"
  );

  if (targetElement) {
    // Obtener el token del atributo data-token
    const token = targetElement.getAttribute("data-token") || "";
    const avatar =
      targetElement.getAttribute("data-user-avatar") ||
      "https://assets.mulbin.com/images/avatar.png";

    console.log(
      "🏗️ Inicializando BaseMultibolsaInmobiliaria con token:",
      token ? "✅ Presente" : "❌ Faltante"
    );

    // Crear y montar la aplicación Vue con el componente BaseMultibolsaInmobiliaria
    const app = createApp(BaseMultibolsaInmobiliaria, {
      token: token,
      avatar: avatar, // Datos del usuario logueado ya parseados
    });

    // Proporcionar el servicio DDP a toda la aplicación
    app.provide("ddpService", ddpService);

    app.mount(targetElement);

    console.log(
      "✅ Componente BaseMultibolsaInmobiliaria montado correctamente"
    );
  } else {
    console.error(
      "❌ No se encontró el elemento [data-base-multibolsa-inmobiliaria]"
    );
    console.log(
      "💡 Asegúrate de que el HTML contenga un elemento con el atributo data-base-multibolsa-inmobiliaria"
    );
  }
});

// Exportaciones de componentes - usando named exports para consistencia
export { BaseMultibolsaInmobiliaria };
