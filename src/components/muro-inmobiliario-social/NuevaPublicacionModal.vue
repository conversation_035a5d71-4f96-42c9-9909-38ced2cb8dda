<template>
  <BaseModal
    :model-value="show"
    size="xl"
    title="¿Qué quieres publicar?"
    :show-icon="false"
    :show-actions="false"
    :close-on-backdrop="false"
    @update:model-value="handleModalValueChange"
    @close="handleClose"
  >
    <template #default>
      <!-- Contenedor principal estilo redes sociales -->
      <div class="social-post-composer">
        <!-- Header con avatar y tipo de publicación -->
        <div class="flex items-center pb-3 mb-4 border-b border-gray-100">
          <div
            class="flex overflow-hidden flex-shrink-0 justify-center items-center mr-3 w-10 h-10 rounded-full"
          >
            <img
              :src="avatar || '/default-avatar.png'"
              alt="Mi avatar"
              class="object-cover w-full h-full"
            />
          </div>
          <div class="flex-1">
            <div class="flex items-center space-x-2">
              <span class="font-medium text-gray-900">Tu publicación</span>
              <!-- Selector de tipo como badge -->
              <div class="relative">
                <button
                  @click="showTypeSelector = !showTypeSelector"
                  class="flex items-center px-3 py-1 text-sm bg-gray-100 rounded-full transition-colors hover:bg-gray-200"
                >
                  <span class="mr-1">{{
                    tiposPublicacion[newPost.type].icono
                  }}</span>
                  <span class="hidden sm:inline">{{
                    tiposPublicacion[newPost.type].titulo
                  }}</span>
                  <ion-icon name="chevron-down" class="ml-1 text-xs"></ion-icon>
                </button>

                <!-- Dropdown de tipos de publicación -->
                <div
                  v-if="showTypeSelector"
                  class="absolute left-0 top-full z-10 mt-1 w-64 bg-white rounded-lg border border-gray-200 shadow-lg"
                >
                  <div class="p-2">
                    <div
                      v-for="(config, typeKey) in tiposPublicacion"
                      :key="typeKey"
                      @click="selectType(typeKey)"
                      class="flex items-center p-2 rounded-lg transition-colors cursor-pointer hover:bg-gray-50"
                    >
                      <span class="mr-3 text-2xl">{{ config.icono }}</span>
                      <div>
                        <div class="text-sm font-medium">
                          {{ config.titulo }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{ config.descripcion }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Campo principal de descripción (estilo Facebook/Twitter) -->
        <div class="mb-4 main-composer-area">
          <!-- Título oculto (solo visible para debug o inmuebles) -->
          <input
            v-if="false"
            type="text"
            v-model="newPost.title"
            class="hidden"
          />

          <!-- Área principal de texto -->
          <div class="relative">
            <textarea
              ref="mainTextArea"
              v-model="newPost.description"
              :placeholder="getPlaceholderText()"
              class="w-full min-h-[120px] text-lg border-none outline-none resize-none p-0 placeholder-gray-400"
              style="font-family: inherit; line-height: 1.5"
              @input="adjustTextareaHeight"
              @focus="isComposerFocused = true"
              @blur="isComposerFocused = false"
            ></textarea>
          </div>
        </div>

        <!-- Contenido adicional expandible -->
        <div v-if="hasSelectedInmueble && selectedInmueble" class="mb-4">
          <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <div
                  class="overflow-hidden mr-3 w-12 h-12 bg-gray-200 rounded-lg"
                >
                  <img
                    v-if="selectedInmueble.image"
                    :src="selectedInmueble.image"
                    :alt="selectedInmueble.name"
                    class="object-cover w-full h-full"
                  />
                  <div
                    v-else
                    class="flex justify-center items-center w-full h-full"
                  >
                    <ion-icon name="home" class="text-gray-400"></ion-icon>
                  </div>
                </div>
                <div>
                  <div class="text-sm font-medium">
                    {{ selectedInmueble.name }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ selectedInmueble.colonia }},
                    {{ selectedInmueble.ciudad }}
                  </div>
                </div>
              </div>
              <button
                @click="removeSelectedInmueble"
                class="flex justify-center items-center w-6 h-6 bg-gray-200 rounded-full transition-colors hover:bg-gray-300"
              >
                <ion-icon name="close" class="text-xs"></ion-icon>
              </button>
            </div>
          </div>
        </div>

        <!-- Selector de audiencia visible si no es público -->
        <div v-if="!isPublicPost && selectedFriends.length > 0" class="mb-4">
          <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <ion-icon name="people" class="mr-2 text-blue-600"></ion-icon>
                <span class="text-sm text-blue-700">
                  {{ selectedFriends.length }}
                  {{
                    selectedFriends.length === 1
                      ? "socio seleccionado"
                      : "socios seleccionados"
                  }}
                </span>
              </div>
              <button
                @click="showAudienceSelector = true"
                class="text-xs text-blue-600 hover:text-blue-800"
              >
                Editar
              </button>
            </div>
          </div>
        </div>

        <!-- Barra de herramientas inferior -->
        <div class="pt-4 border-t border-gray-100">
          <div class="flex justify-between items-center">
            <!-- Botones de herramientas -->
            <div class="flex items-center space-x-1">
              <!-- Botón de inmueble -->
              <button
                v-if="
                  newPost.type === 'inmueble' ||
                  newPost.type === 'invitacion'
                "
                @click="handleInmuebleButtonClick"
                :disabled="availableInmuebles.length === 0"
                :class="[
                  'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all',
                  availableInmuebles.length === 0
                    ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                    : hasSelectedInmueble
                    ? 'text-mulbin-600 bg-mulbin-50 hover:bg-mulbin-100'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50',
                ]"
              >
                <ion-icon name="home" class="mr-2"></ion-icon>
                <span class="hidden sm:inline">
                  {{
                    availableInmuebles.length === 0
                      ? "Sin inmuebles disponibles"
                      : hasSelectedInmueble
                      ? "Inmueble seleccionado"
                      : "Agregar inmueble"
                  }}
                </span>
              </button>

              <!-- Botón de audiencia -->
              <button
                @click="showAudienceSelector = true"
                :class="[
                  'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all hover:bg-gray-50',
                  !isPublicPost && selectedFriends.length > 0
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-gray-800',
                ]"
              >
                <ion-icon name="people" class="mr-2"></ion-icon>
                <span class="hidden sm:inline">
                  {{
                    isPublicPost
                      ? "Todos mis socios"
                      : `${selectedFriends.length} socios`
                  }}
                </span>
              </button>

              <!-- Botón de imágenes (futuro) -->
              <button
                v-if="false"
                disabled
                class="flex items-center px-3 py-2 text-sm font-medium text-gray-400 rounded-lg cursor-not-allowed"
              >
                <ion-icon name="image" class="mr-2"></ion-icon>
                <span class="hidden sm:inline">Fotos</span>
              </button>
            </div>

            <!-- Botón de publicar -->
            <button
              @click="handleCreatePost"
              :disabled="isCreating || !canPublish"
              :class="[
                'px-6 py-2 rounded-full text-sm font-medium transition-all',
                canPublish && !isCreating
                  ? 'bg-mulbin-600 hover:bg-mulbin-700 text-white shadow-sm hover:shadow-md'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed',
              ]"
            >
              <span v-if="isCreating" class="flex items-center">
                <ion-icon
                  name="sync-outline"
                  class="mr-2 animate-spin"
                ></ion-icon>
                Publicando...
              </span>
              <span v-else>Publicar</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Modal de selector de inmuebles -->
      <div
        v-if="showInmuebleSelector"
        class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
        @click.self="showInmuebleSelector = false"
      >
        <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 max-h-[80vh]">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Seleccionar inmueble</h3>
            <button
              @click="showInmuebleSelector = false"
              class="flex justify-center items-center w-8 h-8 bg-gray-100 rounded-full hover:bg-gray-200"
            >
              <ion-icon name="close"></ion-icon>
            </button>
          </div>

          <SelectorInmueblesWrapper
            :has-inmueble="hasSelectedInmueble"
            :selected-inmueble="selectedInmueble"
            :available-inmuebles="availableInmuebles"
            :textos-inmueble-seleccionado="
              getTextosInmuebleParaTipo(newPost.type)
            "
            :textos-publicacion-general="getTextosGeneralParaTipo(newPost.type)"
            @update:hasInmueble="hasSelectedInmueble = $event"
            @update:selectedInmueble="handleInmuebleSelected"
            @add-inmueble="$emit('add-inmueble')"
          />
        </div>
      </div>

      <!-- Modal de selector de audiencia -->
      <div
        v-if="showAudienceSelector"
        class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
      >
        <div
          class="bg-white rounded-xl p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto"
        >
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Seleccionar audiencia</h3>
            <button
              @click="showAudienceSelector = false"
              class="flex justify-center items-center w-8 h-8 bg-gray-100 rounded-full hover:bg-gray-200"
            >
              <ion-icon name="close"></ion-icon>
            </button>
          </div>

          <SelectorSociosWrapper
            :is-public="isPublicPost"
            :selected-socios="selectedFriends"
            :available-friends="availableFriends"
            :textos-audiencia-privada="textosAudienciaPrivada"
            :textos-audiencia-publica="textosAudienciaPublica"
            @update:isPublic="handleAudiencePublicChange"
            @update:selectedSocios="handleSelectedSociosChange"
            @add-socio="$emit('add-socio')"
          />

          <!-- Acción explícita para cerrar -->
          <div class="flex justify-end mt-4">
            <button
              type="button"
              @click="showAudienceSelector = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg border border-gray-200 hover:bg-gray-200"
            >
              Cerrar
            </button>
          </div>
        </div>
      </div>
    </template>
  </BaseModal>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, watch, nextTick } from "vue";
import type { NuevoPost, Socio } from "../../types/inmobiliario";
import BaseModal from "../ui/BaseModal.vue";
import SelectorSociosWrapper from "../selector-socios-wrapper/SelectorSociosWrapper.vue";
import SelectorInmueblesWrapper from "../selector-inmuebles-wrapper/SelectorInmueblesWrapper.vue";
import inmobiliarioService from "../../services/inmobiliarioService";
import { useModal } from "../ui";
import axios from "axios";

export default defineComponent({
  name: "NuevaPublicacionModal",

  components: {
    BaseModal,
    SelectorSociosWrapper,
    SelectorInmueblesWrapper,
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    token: {
      type: String,
      required: true,
    },
    textosAudienciaPrivada: {
      type: Object,
      required: true,
    },
    textosAudienciaPublica: {
      type: Object,
      required: true,
    },
    avatar: {
      type: String,
      default: "",
    },
  },

  emits: ["close", "post-created", "error", "add-socio", "add-inmueble"],

  setup(props, { emit }) {
    // Composables
    const { warning, error } = useModal();

    // Estados del formulario (copiados del componente original)
    const isCreating = ref(false);
    const isPublicPost = ref(true);
    const availableFriends = ref<Socio[]>([]);
    const selectedFriends = ref<string[]>([]);
    const loadingFriends = ref(false);
    const searchFriends = ref("");

    // Estados para inmuebles
    const hasSelectedInmueble = ref(false);
    const selectedInmueble = ref<any>(null);
    const availableInmuebles = ref<any[]>([]);
    const loadingInmuebles = ref(false);

    // Nuevos estados para la UI estilo redes sociales
    const showTypeSelector = ref(false);
    const showInmuebleSelector = ref(false);
    const showAudienceSelector = ref(false);
    const isComposerFocused = ref(false);
    const mainTextArea = ref<HTMLTextAreaElement | null>(null);

    // Datos del nuevo post
    const newPost = reactive<NuevoPost>({
      type: "inmueble",
      title: "",
      description: "",
      images: [],
      relatedInmueble: null,
    });

    // Computed para filtrar socios
    const filteredFriends = computed(() => {
      if (!searchFriends.value.trim()) {
        return availableFriends.value;
      }

      const searchTerm = searchFriends.value.toLowerCase().trim();
      return availableFriends.value.filter((friend) => {
        const name = friend.name?.toLowerCase() || "";
        const company = friend.company?.toLowerCase() || "";
        return name.includes(searchTerm) || company.includes(searchTerm);
      });
    });

    // Computed para validar si se puede publicar
    const canPublish = computed(() => {
      return newPost.description.trim().length > 0;
    });

    // Función para obtener el placeholder del texto principal
    const getPlaceholderText = () => {
      const placeholders = {
        inmueble:
          hasSelectedInmueble.value && selectedInmueble.value
            ? "¿Qué quieres compartir sobre este inmueble?"
            : "¿Qué inmueble quieres publicar? Adjúntalo con el botón de abajo...",
        cliente: "Describe el tipo de inmueble que busca tu cliente...",
        invitacion:
          hasSelectedInmueble.value && selectedInmueble.value
            ? "Comparte detalles sobre tu evento o propuesta relacionada con este inmueble..."
            : "Comparte detalles sobre tu evento o propuesta de colaboración...",
        noticia: "¿Qué noticia o información quieres compartir?",
        mulbin: "¿Qué quieres publicar?",
      };
      return placeholders[newPost.type] || "¿Qué quieres publicar?";
    };

    // Función para ajustar la altura del textarea
    const adjustTextareaHeight = () => {
      nextTick(() => {
        if (mainTextArea.value) {
          mainTextArea.value.style.height = "auto";
          mainTextArea.value.style.height = `${Math.max(
            120,
            mainTextArea.value.scrollHeight
          )}px`;
        }
      });
    };

    // Función para seleccionar tipo de publicación
    const selectType = (typeKey: string) => {
      newPost.type = typeKey as
        | "inmueble"
        | "cliente"
        | "invitacion"
        | "noticia";
      showTypeSelector.value = false;
      handleTypeChange();
    };

    // Función para manejar la selección de inmueble
    const handleInmuebleSelected = (inmueble: any) => {
      selectedInmueble.value = inmueble;
      showInmuebleSelector.value = false;

      // Generar título automático si es tipo inmueble
      if (newPost.type === "inmueble" && inmueble) {
        newPost.title = generateInmuebleTitle(inmueble);
      }
    };

    // Función para remover inmueble seleccionado
    const removeSelectedInmueble = () => {
      hasSelectedInmueble.value = false;
      selectedInmueble.value = null;
      if (newPost.type === "inmueble") {
        newPost.title = "";
      }
    };

    // Función para manejar cambios en la audiencia pública
    const handleAudiencePublicChange = (isPublic: boolean) => {
      isPublicPost.value = isPublic;
    };

    // Función para manejar cambios en socios seleccionados
    const handleSelectedSociosChange = (socios: string[]) => {
      selectedFriends.value = socios;
    };

    // Función para limpiar el formulario
    const resetForm = () => {
      Object.assign(newPost, {
        type: "inmueble",
        title: "",
        description: "",
        images: [],
        relatedInmueble: null,
      });
      isPublicPost.value = true;
      selectedFriends.value = [];
      hasSelectedInmueble.value = false;
      selectedInmueble.value = null;
      showTypeSelector.value = false;
      showInmuebleSelector.value = false;
      showAudienceSelector.value = false;
    };

    // Manejar cambio de valor del modal (cuando se cierra con X o backdrop)
    const handleModalValueChange = (value: boolean) => {
      if (!value) {
        handleClose();
      }
    };

    // Manejar cierre del modal
    const handleClose = () => {
      resetForm();
      emit("close");
    };

    // Cerrar selector de inmuebles sin selección: desmarcar toggle
    watch(
      () => showInmuebleSelector.value,
      (isOpen, wasOpen) => {
        if (!isOpen && wasOpen) {
          // Si se cerró y no hay inmueble elegido, aseguramos que quede "no seleccionado"
          if (!selectedInmueble.value) {
            hasSelectedInmueble.value = false;
          }
        }
      }
    );

    // Cargar lista de socios (copiado del componente original)
    const loadUserFriends = async () => {
      try {
        loadingFriends.value = true;
        const response = await axios.get("/msi-v5/owner/socios");

        if (response.data && response.data.statusCode === 200) {
          const tagsBackend = response.data.data.tags || [];
          const etiquetasDisponibles = tagsBackend.map((tag: any) => {
            let color = "#7C3AED";
            let backgroundColor = "#EDE9FE";
            if (tag.style) {
              const bgMatch = tag.style.match(/background-color:\s*([^;]+);?/);
              const borderMatch = tag.style.match(
                /border:\s*1px solid ([^;]+);?/
              );
              if (bgMatch) backgroundColor = bgMatch[1].trim();
              if (borderMatch) color = borderMatch[1].trim();
            }
            return {
              id: tag.id.toString(),
              nombre: tag.tag,
              color,
              backgroundColor,
              descripcion: tag.description || undefined,
            };
          });

          availableFriends.value = (response.data.data.socios || [])
            .filter((socio: any) => socio.tipo === "directo")
            .map((socio: any) => {
              const etiquetas = (socio.tags || [])
                .map((tagId: any) =>
                  etiquetasDisponibles.find(
                    (e: any) => e.id == tagId.toString()
                  )
                )
                .filter(Boolean);

              return {
                _id: socio.id,
                id: socio.id,
                name: socio.nombre,
                company: socio.empresa,
                location: socio.ubicacion,
                avatar: socio.avatar,
                email: socio.email,
                phone: socio.telefono,
                wa: socio.wa,
                telegram: socio.telegram,
                verified: socio.verified || false,
                etiquetas: etiquetas,
              };
            });
        } else {
          availableFriends.value = [];
        }
      } catch (error) {
        console.error("Error al cargar socios:", error);
        availableFriends.value = [];
      } finally {
        loadingFriends.value = false;
      }
    };

    // Función utilitaria para optimizar URLs de imágenes
    const optimizeImageUrl = (imageUrl: string): string => {
      if (!imageUrl || typeof imageUrl !== "string") {
        return "/images/placeholder-property.jpg";
      }
      return imageUrl.replace("/alta/", "/peque/");
    };

    // Cargar lista de inmuebles del usuario
    const loadUserInmuebles = async () => {
      try {
        loadingInmuebles.value = true;
        const response = await axios.get("/msi-v5/owner/inmuebles?limit=0");

        if (response.data && response.data.statusCode === 200) {
          availableInmuebles.value = (response.data.data.inmuebles || []).map(
            (inmueble: any) => ({
              id: inmueble.id,
              key: inmueble.clave || inmueble.key,
              name: inmueble.titulo || inmueble.name,
              image: optimizeImageUrl(
                inmueble.imagenPrincipal || "/images/placeholder-property.jpg"
              ),
              precio: inmueble.precio,
              moneda: inmueble.moneda,
              operacion: inmueble.operacion,
              tipo: inmueble.tipo,
              colonia: inmueble.colonia,
              ciudad: inmueble.ciudad,
              imagenes: inmueble.imagenes || [],
            })
          );
        } else {
          availableInmuebles.value = [];
        }
      } catch (error) {
        console.error("Error al cargar inmuebles:", error);
        availableInmuebles.value = [];
      } finally {
        loadingInmuebles.value = false;
      }
    };

    // Manejar click en botón de inmueble
    const handleInmuebleButtonClick = () => {
      if (availableInmuebles.value.length > 0) {
        showInmuebleSelector.value = true;
      }
      // Si no hay inmuebles disponibles, el botón está deshabilitado y no hace nada
    };

    // Crear nueva publicación (copiado del componente original)
    const handleCreatePost = async () => {
      if (!newPost.description.trim()) {
        warning(
          "Por favor escribe algo en tu publicación",
          "Contenido requerido"
        );
        return;
      }

      // Validación específica para publicaciones de tipo "inmueble"
      if (newPost.type === 'inmueble' && (!hasSelectedInmueble.value || !selectedInmueble.value)) {
        warning(
          "No es posible hacer una publicación del tipo 'Inmueble' sin inmueble adjunto. Puedes hacer una publicación de otro tipo si lo deseas.",
          "Inmueble requerido"
        );
        return;
      }

      try {
        isCreating.value = true;

        const targetUserIds = isPublicPost.value
          ? availableFriends.value.map((socio) => socio._id)
          : selectedFriends.value;

        const postData = {
          ...newPost,
          relatedInmueble: hasSelectedInmueble.value
            ? selectedInmueble.value
            : null,
        };

        await inmobiliarioService.createPostWithTargets(
          postData,
          targetUserIds
        );

        resetForm();
        emit("post-created");
      } catch (err) {
        console.error("Error al crear post:", err);
        error(
          "Error al crear la publicación, inténtalo de nuevo",
          "Error al publicar"
        );
        emit("error", err);
      } finally {
        isCreating.value = false;
      }
    };

    // Configuración de tipos de publicación
    const tiposPublicacion = {
      inmueble: {
        icono: "🏠",
        titulo: "Inmueble",
        descripcion:
          "Publica uno de tus inmuebles en el Feed de la Multibolsa Inmobiliaria.",
      },
      cliente: {
        icono: "🔍",
        titulo: "Cliente buscando",
        descripcion:
          "Si tienes un cliente que busca un inmueble con características específicas, puedes publicarlo aquí.",
      },
      invitacion: {
        icono: "🤝",
        titulo: "Invitación / Colaboración",
        descripcion:
          "Comparte eventos, open house o propuestas para colaborar con colegas.",
      },
      noticia: {
        icono: "📣",
        titulo: "Noticia / Aviso",
        descripcion:
          "Difunde noticias, actualizaciones importantes o información relevante del sector inmobiliario.",
      },
      // TODO: Implementar cuando se haga logueo como administrador de Mulbin
      // mulbin: {
      //   icono: "🧩",
      //   titulo: "Mulbin",
      //   descripcion: "Publicación del sistema Mulbin.",
      // },
    };

    // Obtener textos para inmueble según el tipo de publicación
    const getTextosInmuebleParaTipo = (tipo: string) => {
      if (tipo === "inmueble") {
        return {
          titulo: "Publicación con inmueble",
          descripcion:
            "Esta publicación estará relacionada con uno de tus inmuebles",
        };
      } else if (tipo === "invitacion") {
        return {
          titulo: "Invitación con inmueble",
          descripcion:
            "Invita a colegas a un Open House o evento relacionado con este inmueble",
        };
      }
      return {
        titulo: "Publicación con inmueble",
        descripcion:
          "Esta publicación estará relacionada con uno de tus inmuebles",
      };
    };

    // Obtener textos generales según el tipo de publicación
    const getTextosGeneralParaTipo = (tipo: string) => {
      if (tipo === "inmueble") {
        return {
          titulo: "Publicación general",
          descripcion: "Publicación sin inmueble específico",
        };
      } else if (tipo === "invitacion") {
        return {
          titulo: "Invitación general",
          descripcion:
            "Invitación o evento no relacionado con un inmueble específico",
        };
      }
      return {
        titulo: "Publicación general",
        descripcion: "",
      };
    };

    // Función para generar título automático para inmuebles
    const generateInmuebleTitle = (inmueble: any): string => {
      if (!inmueble) return "";

      const tipo = inmueble.tipo || "Inmueble";
      const operacion = inmueble.operacion || "en venta";
      const clave = inmueble.key || inmueble.clave || "KEY-PROP";
      const colonia = inmueble.colonia || "Colonia";

      const tipoCapitalizado =
        tipo.charAt(0).toUpperCase() + tipo.slice(1).toLowerCase();

      return `((${tipoCapitalizado} en ${operacion})) › ((${colonia})) › ((${clave}))`;
    };

    // Manejar cambio de tipo de publicación
    const handleTypeChange = () => {
      if (newPost.type === "inmueble") {
        // Para inmuebles: empezar sin seleccionar inmueble (el usuario puede elegir)
        hasSelectedInmueble.value = false;
        selectedInmueble.value = null;
        newPost.title = "";
        console.log("🏠 Tipo inmueble: sin inmueble preseleccionado");
      } else if (newPost.type === "invitacion") {
        // Para invitaciones: también empezar sin seleccionar inmueble (opcional)
        hasSelectedInmueble.value = false;
        selectedInmueble.value = null;
        newPost.title = "";
        console.log("🤝 Tipo invitación: sin inmueble preseleccionado");
      } else {
        // Para otros tipos: ocultar selector y resetear
        hasSelectedInmueble.value = false;
        selectedInmueble.value = null;
        newPost.title = "";
        console.log("🔄 Tipo", newPost.type, ": selector oculto");
      }

      console.log("🔄 Tipo de publicación cambiado a:", newPost.type);
    };

    // Cargar datos cuando se muestra el modal
    watch(
      () => props.show,
      (newValue: boolean) => {
        if (newValue) {
          if (availableFriends.value.length === 0) {
            loadUserFriends();
          }
          if (availableInmuebles.value.length === 0) {
            loadUserInmuebles();
          }
          handleTypeChange();

          // Enfocar el textarea principal cuando se abre el modal
          nextTick(() => {
            if (mainTextArea.value) {
              mainTextArea.value.focus();
              adjustTextareaHeight();
            }
          });
        }
      }
    );

    // Observar cambios en el inmueble seleccionado para generar título automático
    watch(
      () => selectedInmueble.value,
      (newInmueble) => {
        if (newPost.type === "inmueble") {
          if (newInmueble) {
            const nuevoTitulo = generateInmuebleTitle(newInmueble);
            newPost.title = nuevoTitulo;
          } else {
            newPost.title = "";
          }
        }
      }
    );

    return {
      // Estados
      isCreating,
      isPublicPost,
      availableFriends,
      selectedFriends,
      loadingFriends,
      searchFriends,
      newPost,

      // Estados para inmuebles
      hasSelectedInmueble,
      selectedInmueble,
      availableInmuebles,
      loadingInmuebles,

      // Estados UI estilo redes sociales
      showTypeSelector,
      showInmuebleSelector,
      showAudienceSelector,
      isComposerFocused,
      mainTextArea,

      // Configuración
      tiposPublicacion,

      // Props
      avatar: props.avatar,

      // Computed
      filteredFriends,
      canPublish,

      // Métodos
      handleModalValueChange,
      handleClose,
      handleCreatePost,
      handleTypeChange,
      getTextosInmuebleParaTipo,
      getTextosGeneralParaTipo,
      loadUserFriends,
      loadUserInmuebles,
      generateInmuebleTitle,
      getPlaceholderText,
      adjustTextareaHeight,
      selectType,
      handleInmuebleSelected,
      handleInmuebleButtonClick,
      removeSelectedInmueble,
      handleAudiencePublicChange,
      handleSelectedSociosChange,
    };
  },
});
</script>

<style scoped>
/* Estilos para el compositor estilo redes sociales */
.social-post-composer {
  background-color: white;
}

.main-composer-area textarea {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  resize: none;
  overflow: hidden;
  background: transparent;
}

.main-composer-area textarea:focus {
  outline: none;
  border: none;
  box-shadow: none;
}

.main-composer-area textarea::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

/* Estilos para el dropdown de tipos */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: opacity 0.2s, transform 0.2s;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* Animaciones suaves para botones */
button {
  transition: all 0.15s ease;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Estilos para espaciado consistente */
.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Override para que el modal sea más ancho en pantallas grandes */
@media (min-width: 768px) {
  .social-post-composer {
    min-width: 500px;
  }
}

/* Estilos para los modales internos */
.fixed.inset-0 {
  z-index: 60;
}
</style>
