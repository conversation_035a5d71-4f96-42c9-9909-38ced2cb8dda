<template>
  <div
    v-if="show"
    class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
  >
    <div
      class="relative p-6 mx-4 w-full max-w-md bg-white rounded-lg shadow-lg nuevo-hilo-modal"
    >
      <!-- Header -->
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
          Crear hilo de interés
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 transition-colors hover:text-gray-600"
        >
          <ion-icon name="close-outline" size="small"></ion-icon>
        </button>
      </div>

      <!-- Información del post -->
      <div v-if="post" class="p-3 mb-4 bg-gray-50 rounded-lg">
        <p class="text-sm font-medium text-gray-800">
          {{ limpiarTituloHilo(post.title) }}
        </p>
        <p class="text-xs text-gray-600">
          Socio: {{ getAuthorFullName(post.authorCache) }}
        </p>
      </div>

      <!-- Formulario -->
      <div class="space-y-4">
        <!-- Título del hilo -->
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">
            Título del hilo *
          </label>
          <input
            type="text"
            v-model="nuevoHilo.titulo"
            placeholder="Ej: Consulta sobre precio, Interés en intercambio..."
            class="px-3 py-2 w-full rounded border border-gray-300 transition-all focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200"
            maxlength="50"
          />
          <p class="mt-1 text-xs text-gray-500">
            {{ nuevoHilo.titulo.length }}/50 caracteres
          </p>
        </div>

        <!-- Referencia privada -->
        <div v-if="false">
          <label class="block mb-1 text-sm font-medium text-gray-700">
            Referencia privada
            <span class="text-xs text-gray-500">(solo visible para ti)</span>
          </label>
          <input
            type="text"
            v-model="nuevoHilo.referenciaPrivada"
            placeholder="Ej: Cliente Juan Pérez, Prospecto oficina..."
            class="px-3 py-2 w-full rounded border border-gray-300 transition-all focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200"
            maxlength="100"
          />
        </div>

        <!-- Mensaje inicial -->
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">
            Mensaje inicial *
          </label>
          <textarea
            v-model="nuevoHilo.mensajeInicial"
            rows="3"
            placeholder="Escribe tu consulta o comentario inicial..."
            class="px-3 py-2 w-full rounded border border-gray-300 transition-all resize-none focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200"
            maxlength="500"
          ></textarea>
          <p class="mt-1 text-xs text-gray-500">
            {{ nuevoHilo.mensajeInicial.length }}/500 caracteres
          </p>
        </div>
      </div>

      <!-- Botones -->
      <div class="flex justify-end mt-6 space-x-3">
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-gray-600 bg-gray-100 rounded transition-colors hover:bg-gray-200"
        >
          Cancelar
        </button>
        <button
          @click="handleCrearHilo"
          :disabled="
            !nuevoHilo.titulo.trim() ||
            !nuevoHilo.mensajeInicial.trim() ||
            isCreating
          "
          class="px-4 py-2 text-white rounded transition-colors bg-mulbin-600 hover:bg-mulbin-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isCreating" class="flex items-center">
            <ion-icon
              name="reload-outline"
              class="mr-1 animate-spin"
            ></ion-icon>
            Creando...
          </span>
          <span v-else>Crear hilo</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, watch } from "vue";
import type { PostInmobiliario } from "../../types/inmobiliario";
import inmobiliarioService from "../../services/inmobiliarioService";
import { useModal } from "../ui";

export default defineComponent({
  name: "NuevoHiloForm",

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    token: {
      type: String,
      required: true,
    },
    post: {
      type: Object as () => PostInmobiliario | null,
      default: null,
    },
  },

  emits: ["close", "hilo-created", "error", "abrir-chat"],

  setup(props, { emit }) {
    // Composables
    const { warning, error, success } = useModal();

    // Estados del formulario
    const isCreating = ref(false);

    // Datos del nuevo hilo
    const nuevoHilo = reactive({
      titulo: "",
      referenciaPrivada: "",
      mensajeInicial: "",
    });

    // 🆕 NUEVO: Helper para obtener nombre completo del autor
    const getAuthorFullName = (authorCache: any) => {
      if (authorCache.firstName || authorCache.lastName) {
        return `${authorCache.firstName || ""} ${
          authorCache.lastName || ""
        }`.trim();
      }
      return authorCache.name || "Usuario";
    };

    // Función para limpiar el título del hilo
    const limpiarTituloHilo = (titulo: string): string => {
      if (!titulo) return "Publicación";

      return titulo
        .replace(/\(\(/g, "") // Eliminar '(('
        .replace(/\)\)/g, "") // Eliminar '))'
        .replace(/\*/g, "") // Eliminar '*'
        .replace(/~/g, "") // Eliminar '~'
        .replace(/_/g, "") // Eliminar '_'
        .trim();
    };

    // 🆕 NUEVO: Helper para normalizar IDs de posts
    const normalizePostId = (post: any): string | null => {
      if (post._id && typeof post._id === "string") {
        return post._id;
      }
      if (post.id && typeof post.id === "string") {
        post._id = post.id;
        return post.id;
      }
      return null;
    };

    // 🆕 NUEVO: Función para verificar ventanas de chat abiertas
    const CHAT_WINDOWS_KEY = "mulbin_chat_windows_open";

    const getChatWindowsFromStorage = (): string[] => {
      try {
        const stored = localStorage.getItem(CHAT_WINDOWS_KEY);
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        console.warn("⚠️ Error leyendo chat windows del localStorage:", error);
        return [];
      }
    };

    const isChatWindowOpen = (hiloId: string): boolean => {
      if (!hiloId) return false;
      const currentWindows = getChatWindowsFromStorage();
      return currentWindows.includes(hiloId);
    };

    // 🆕 NUEVO: Función común para procesar hilo creado
    const procesarHiloCreado = (hiloRecienCreado: any) => {
      // Resetear formulario
      Object.assign(nuevoHilo, {
        titulo: "",
        referenciaPrivada: "",
        mensajeInicial: "",
      });

      // Emitir eventos
      emit("hilo-created", hiloRecienCreado);

      // Verificar si la ventana ya está abierta
      if (isChatWindowOpen(hiloRecienCreado._id)) {
        warning(
          "Esta conversación ya está abierta en otra ventana",
          "Conversación abierta"
        );
      } else {
        // Emitir evento para abrir chat automáticamente
        emit("abrir-chat", hiloRecienCreado);
      }

      // Cerrar modal
      emit("close");
    };

    // Crear nuevo hilo de interés
    const handleCrearHilo = async () => {
      // Validar campos requeridos
      if (
        !props.post ||
        !nuevoHilo.titulo.trim() ||
        !nuevoHilo.mensajeInicial.trim()
      ) {
        warning(
          "Por favor completa el título y mensaje inicial",
          "Campos requeridos"
        );
        return;
      }

      try {
        isCreating.value = true;

        // 🏠 NUEVO: Detectar si es un inmueble y usar el endpoint correcto
        if (props.post.esInmueble && props.post.inmuebleData) {
          // Usar endpoint específico para inmuebles
          const inmuebleData = {
            ...props.post.inmuebleData,
            titulo: nuevoHilo.titulo.trim(),
            mensajeInicial: nuevoHilo.mensajeInicial.trim(),
          };

          console.log("🏠 Creando hilo de inmueble con datos:", inmuebleData);

          // Conectar al servicio con token
          await inmobiliarioService.connectWithToken(props.token);
          const result = await inmobiliarioService.call(
            "hilosInteres.createFromInmueble",
            inmuebleData
          );
          
          console.log("✅ Hilo de inmueble creado exitosamente:", result);
          
          // Preparar objeto del hilo para el evento (inmueble)
          const hiloRecienCreado = {
            _id: result.hiloId,
            titulo: nuevoHilo.titulo.trim(),
            referenciaPrivada: inmuebleData.referenciaPrivada,
            postId: result.postId,
            postAuthorCache: props.post.authorCache,
            mensajesCount: 1,
            lastMessageAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            postExternalId: result.postExternalId, // ID del inmueble
          };

          // Continuar con el flujo común...
          procesarHiloCreado(hiloRecienCreado);
          return;
        }

        // Flujo original para posts normales
        const postId = normalizePostId(props.post);
        if (!postId) throw new Error("ID de post no válido");

        const hiloData = {
          postId: postId,
          titulo: nuevoHilo.titulo.trim(),
          referenciaPrivada: nuevoHilo.referenciaPrivada.trim(),
          mensajeInicial: nuevoHilo.mensajeInicial.trim(),
        };

        console.log("📝 Creando hilo de post con datos:", hiloData);

        // Conectar al servicio con token
        await inmobiliarioService.connectWithToken(props.token);
        const result = await inmobiliarioService.call(
          "hilosInteres.create",
          hiloData
        );

        console.log("✅ Hilo de post creado exitosamente:", result);

        // Preparar objeto del hilo para el evento (post normal)
        const hiloRecienCreado = {
          _id: result.hiloId,
          titulo: nuevoHilo.titulo.trim(),
          referenciaPrivada: nuevoHilo.referenciaPrivada.trim(),
          postId: postId,
          postAuthorCache: props.post.authorCache,
          mensajesCount: 1,
          lastMessageAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
        };

        // Continuar con el flujo común...
        procesarHiloCreado(hiloRecienCreado);
      } catch (err) {
        console.error("❌ Error creando hilo:", err);

        // Manejar error de límite máximo de hilos
        const isMaxHilosError =
          (err as any)?.error === "max-hilos-reached" ||
          (err as any)?.message?.includes?.("máximo de 3 hilos") ||
          (err as any)?.reason?.includes?.("máximo de 3 hilos");

        if (isMaxHilosError) {
          window.alert(
            "🚫 LÍMITE DE HILOS ALCANZADO\n\n" +
              "Has alcanzado el límite máximo de 3 hilos de interés por publicación.\n\n" +
              "Para crear un nuevo hilo, primero debes eliminar alguno de los hilos existentes.\n\n" +
              "💡 Sugerencias:\n" +
              "• Revisa tus hilos existentes\n" +
              "• Elimina conversaciones inactivas\n" +
              "• Consolida temas similares"
          );
        } else {
          // Otros errores
          const errorMessage =
            err instanceof Error ? err.message : "Error desconocido";
          error(`Error al crear hilo: ${errorMessage}`, "Error");
        }

        emit("error", err);
      } finally {
        isCreating.value = false;
      }
    };

    // Limpiar formulario cuando se cierra
    watch(
      () => props.show,
      (newValue: boolean) => {
        if (!newValue) {
          // Resetear formulario cuando se cierra
          Object.assign(nuevoHilo, {
            titulo: "",
            referenciaPrivada: "",
            mensajeInicial: "",
          });
        }
      }
    );

    return {
      // Estados
      isCreating,
      nuevoHilo,

      // Métodos
      handleCrearHilo,
      getAuthorFullName,
      limpiarTituloHilo,
    };
  },
});
</script>

<style scoped>
.nuevo-hilo-modal {
  animation: slideIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Estilos para los inputs y textarea */
.nuevo-hilo-modal input,
.nuevo-hilo-modal textarea {
  transition: all 0.2s ease-in-out;
}

.nuevo-hilo-modal input:focus,
.nuevo-hilo-modal textarea:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Estilos para los botones */
.nuevo-hilo-modal button {
  transition: all 0.2s ease-in-out;
}

.nuevo-hilo-modal button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nuevo-hilo-modal button:active:not(:disabled) {
  transform: translateY(0);
}

/* Estilos para el backdrop */
.nuevo-hilo-modal::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  z-index: -1;
}

/* Estilos responsivos */
@media (max-width: 640px) {
  .nuevo-hilo-modal {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .nuevo-hilo-modal .space-x-3 {
    flex-direction: column;
    gap: 0.75rem;
  }

  .nuevo-hilo-modal .space-x-3 > * {
    margin-left: 0;
    margin-right: 0;
  }
}
</style>
