# 🏢 BaseMultibolsaInmobiliaria.vue - Documentación Técnica

> Contenedor principal y coordinador de vistas para el ecosistema inmobiliario social

## 📋 Tabla de Contenidos

- [🎯 Descripción General](#-descripción-general)
- [🏗️ Arquitectura del Contenedor](#️-arquitectura-del-contenedor)
- [⚡ Sistema de Navegación](#-sistema-de-navegación)
- [🔄 Estados y Props](#-estados-y-props)
- [📡 Comunicación entre Componentes](#-comunicación-entre-componentes)
- [🎨 Interfaz de Usuario](#-interfaz-de-usuario)
- [🔧 Métodos de Coordinación](#-métodos-de-coordinación)
- [📊 Flujo de Datos](#-flujo-de-datos)
- [💡 Patrones de Diseño](#-patrones-de-diseño)

---

## 🎯 Descripción General

### Propósito

`BaseMultibolsaInmobiliaria.vue` actúa como **contenedor principal** y **coordinador central** del ecosistema inmobiliario social. Su responsabilidad core es gestionar la navegación entre cuatro vistas especializadas y coordinar la comunicación entre componentes independientes.

### Arquitectura de Alto Nivel

```mermaid
graph TD
    A[BaseMultibolsaInmobiliaria.vue] --> B[Feed - FeedPublicaciones.vue]
    A --> C[Inmuebles - Sistema Dual]
    A --> D[Hilos - FeedHilos.vue]
    A --> E[Mis Socios - MisSocios.vue]

    C --> F[FiltroInmueblesInteligente.vue]
    C --> G[Inmuebles.vue]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

### Responsabilidades Principales

- ✅ **Navegación centralizada** entre 4 vistas especializadas
- ✅ **Coordinación de estados** compartidos entre componentes
- ✅ **Gestión de props** y configuración global
- ✅ **Interfaz unificada** con header dinámico
- ✅ **Enrutamiento interno** de funcionalidades
- ✅ **Integración de sistemas** (filtros, favoritos, socios)

---

## 🏗️ Arquitectura del Contenedor

### Estructura de Componentes

```typescript
// Importaciones especializadas
import FiltroInmueblesInteligente from "./FiltroInmueblesInteligente.vue";
import MisSocios from "./MisSocios.vue";
import FeedHilos from "./FeedHilos.vue";
import Inmuebles from "./Inmuebles.vue";
import FeedPublicaciones from "./FeedPublicaciones.vue";
```

### Props Interface

```typescript
interface Props {
  token?: string; // Token DDP de autenticación
  textosAudienciaPrivada?: {
    // Configuración de audiencia privada
    titulo: string;
    descripcion: string;
  };
  textosAudienciaPublica?: {
    // Configuración de audiencia pública
    titulo: string;
    descripcion: string;
  };
}
```

### Estados Reactivos del Contenedor

```typescript
// Estado de navegación principal
const vistaActiva = ref<"filtro" | "socios" | "hilos" | "feed">("feed");

// Estados para sistema inteligente de inmuebles
const mostrarListadoInmuebles = ref(false);
const filtrosAplicados = ref({ ubicacion: "", operacion: "" });
const totalInmueblesEncontrados = ref(0);
const mostrandoFavoritosEnInmuebles = ref(false);

// Control de activación automática de favoritos
const activarFavoritosInmediatamente = ref(false);
```

---

## ⚡ Sistema de Navegación

### Vista Activa por Defecto

```typescript
// Inicialización predeterminada en Feed
const vistaActiva = ref<"filtro" | "socios" | "hilos" | "feed">("feed");
```

### Header Dinámico Unificado

```vue
<template>
  <div class="p-4 text-white bg-gradient-to-r to-indigo-800 from-mulbin-600">
    <div class="flex justify-between items-center">
      <!-- Título fijo con logo Mulbin -->
      <h2 class="flex items-center text-xl font-bold">
        <svg><!-- Logo SVG --></svg>
        <span
          >Multibolsa<span class="hidden sm:inline-block"
            >&nbsp;Inmobiliaria</span
          ></span
        >
      </h2>

      <!-- Navegación por pestañas -->
      <div class="flex space-x-3">
        <button
          v-for="vista in vistas"
          :key="vista.id"
          @click="cambiarVista(vista.id)"
          :class="getButtonClasses(vista.id)"
        >
          <ion-icon :name="vista.icon" class="mr-1"></ion-icon>
          <span class="hidden sm:block">{{ vista.label }}</span>
        </button>
      </div>
    </div>
  </div>
</template>
```

### Configuración de Vistas

```typescript
const vistas = [
  { id: "feed", icon: "newspaper-outline", label: "Feed" },
  { id: "filtro", icon: "search-outline", label: "Inmuebles" },
  { id: "hilos", icon: "chatbubbles-outline", label: "Hilos" },
  { id: "socios", icon: "people-outline", label: "Mis Socios" },
];
```

### Método de Cambio de Vista

```typescript
const cambiarVista = (vista: "filtro" | "socios" | "hilos" | "feed") => {
  vistaActiva.value = vista;

  // Reset de estados específicos al cambiar vista
  if (vista !== "filtro") {
    mostrarListadoInmuebles.value = false;
    activarFavoritosInmediatamente.value = false;
  }
};
```

---

## 🔄 Estados y Props

### Estados de Coordinación Central

```typescript
// Estados del sistema de filtros inteligentes de inmuebles
const mostrarListadoInmuebles = ref(false); // Toggle entre filtro y listado
const filtrosAplicados = ref({
  // Filtros activos aplicados
  ubicacion: "",
  operacion: "",
});
const totalInmueblesEncontrados = ref(0); // Contador de resultados
const mostrandoFavoritosEnInmuebles = ref(false); // Estado de vista favoritos

// Control de activación automática
const activarFavoritosInmediatamente = ref(false); // Activador de favoritos directo
```

### Props con Valores por Defecto

```typescript
const props = withDefaults(defineProps<Props>(), {
  token: "",
  textosAudienciaPrivada: () => ({
    titulo: "Solo a socios determinados",
    descripcion:
      "Solo tus socios seleccionados verán esta publicación en su muro inmobiliario",
  }),
  textosAudienciaPublica: () => ({
    titulo: "Todos mis socios",
    descripcion:
      "Todos tus socios verán esta publicación en su muro inmobiliario",
  }),
});
```

### Estados Reactivos Derivados

```typescript
// Referencias reactivas para componentes hijos
const textosAudienciaPrivada = ref(props.textosAudienciaPrivada);
const textosAudienciaPublica = ref(props.textosAudienciaPublica);
```

---

## 📡 Comunicación entre Componentes

### Patrón Props Down, Events Up

#### Comunicación con FeedPublicaciones

```vue
<FeedPublicaciones
  :token="props.token"
  :textos-audiencia-privada="textosAudienciaPrivada"
  :textos-audiencia-publica="textosAudienciaPublica"
/>
```

#### Comunicación con Sistema de Inmuebles

```vue
<!-- FiltroInmueblesInteligente (Vista inicial) -->
<FiltroInmueblesInteligente
  v-if="!mostrarListadoInmuebles"
  @cargarInmuebles="handleCargarInmuebles"
  @irASocios="irAMisSocios"
  @verFavoritos="handleVerFavoritos"
/>

<!-- Inmuebles (Vista de resultados) -->
<Inmuebles
  v-else
  :filtros-iniciales="filtrosAplicados"
  :activar-favoritos-inmediatamente="activarFavoritosInmediatamente"
  @total-updated="handleTotalUpdated"
  @ir-a-filtros="volverAFiltros"
  @favoritos-changed="handleFavoritosChanged"
  @favoritos-activados="handleFavoritosActivados"
/>
```

#### Comunicación con Otros Componentes

```vue
<!-- FeedHilos -->
<FeedHilos :token="props.token" />

<!-- MisSocios (Sin props adicionales) -->
<MisSocios />
```

### Manejadores de Eventos Centralizados

```typescript
// Sistema de filtros inteligentes de inmuebles
const handleCargarInmuebles = (filtros: {
  ubicacion: string;
  operacion: string;
}) => {
  filtrosAplicados.value = { ...filtros };
  mostrarListadoInmuebles.value = true;
};

const volverAFiltros = () => {
  mostrarListadoInmuebles.value = false;
  filtrosAplicados.value = { ubicacion: "", operacion: "" };
  totalInmueblesEncontrados.value = 0;
};

// Navegación entre vistas
const irAMisSocios = () => {
  cambiarVista("socios");
};

// Sistema de favoritos directo
const handleVerFavoritos = () => {
  mostrarListadoInmuebles.value = true;
  activarFavoritosInmediatamente.value = true;
};

// Manejadores de actualización
const handleTotalUpdated = (total: number) => {
  totalInmueblesEncontrados.value = total;
};

const handleFavoritosChanged = (mostrandoFavoritos: boolean) => {
  mostrandoFavoritosEnInmuebles.value = mostrandoFavoritos;
};

const handleFavoritosActivados = () => {
  activarFavoritosInmediatamente.value = false;
};
```

---

## 🎨 Interfaz de Usuario

### Header Responsivo con Gradiente

```vue
<div class="p-4 text-white bg-gradient-to-r to-indigo-800 from-mulbin-600">
  <div class="flex justify-between items-center">
    <!-- Logo y título siempre visible -->
    <h2 class="flex items-center text-xl font-bold">
      <!-- SVG Logo Mulbin -->
      <span>Multibolsa<span class="hidden sm:inline-block">&nbsp;Inmobiliaria</span></span>
    </h2>

    <!-- Navegación adaptativa -->
    <div class="flex space-x-3">
      <!-- Botones que se adaptan según vista activa -->
    </div>
  </div>
</div>
```

### Sistema de Pestañas con Estados Visuales

```typescript
const getButtonClasses = (vista: string) => [
  "flex items-center px-3 py-1 text-sm font-medium rounded-full",
  vistaActiva.value === vista
    ? "bg-white text-mulbin-600" // Estado activo
    : "bg-mulbin-600 text-white hover:bg-mulbin-700", // Estado inactivo
];
```

### Contenido Condicional por Vista

```vue
<div>
  <!-- Vista de Inmuebles (Sistema Dual) -->
  <div v-if="vistaActiva === 'filtro'" class="border-b border-gray-200">
    <!-- Header de filtros aplicados (condicional) -->
    <div v-if="mostrarListadoInmuebles && !mostrandoFavoritosEnInmuebles"
         class="p-4 text-white bg-gradient-to-r from-blue-500 to-indigo-600">
      <!-- Información de filtros activos y resultados -->
    </div>

    <!-- Componente dinámico según estado -->
    <FiltroInmueblesInteligente v-if="!mostrarListadoInmuebles" />
    <Inmuebles v-else />
  </div>

  <!-- Otras vistas (directas) -->
  <div v-if="vistaActiva === 'socios'" class="border-b border-gray-200">
    <MisSocios />
  </div>

  <div v-if="vistaActiva === 'hilos'" class="border-b border-gray-200">
    <FeedHilos :token="props.token" />
  </div>

  <div v-if="vistaActiva === 'feed'" class="border-b border-gray-200">
    <FeedPublicaciones :token="props.token" :textos-audiencia-privada="textosAudienciaPrivada" :textos-audiencia-publica="textosAudienciaPublica" />
  </div>
</div>
```

---

## 🔧 Métodos de Coordinación

### Helpers de Formato

```typescript
const obtenerLabelOperacion = (operacion: string): string => {
  const labels: Record<string, string> = {
    venta: "En Venta",
    renta: "En Renta",
    traspaso: "En Traspaso",
  };
  return labels[operacion] || operacion;
};
```

### Coordinación de Estados Complejos

```typescript
// Flujo completo de activación de favoritos desde filtro inteligente
const handleVerFavoritos = () => {
  // 1. Activar vista de inmuebles
  mostrarListadoInmuebles.value = true;

  // 2. Señal para activación automática de favoritos
  activarFavoritosInmediatamente.value = true;

  console.log("⭐ Navegando directamente a favoritos desde filtro inteligente");
};

// Reset de estado después de activación
const handleFavoritosActivados = () => {
  activarFavoritosInmediatamente.value = false;
  console.log("✅ Favoritos activados, reseteando prop");
};
```

---

## 📊 Flujo de Datos

### Diagrama de Comunicación

```mermaid
sequenceDiagram
    participant U as Usuario
    participant B as BaseMultibolsa
    participant F as FiltroInteligente
    participant I as Inmuebles
    participant P as FeedPublicaciones

    U->>B: Clic en "Feed"
    B->>B: cambiarVista('feed')
    B->>P: Montar FeedPublicaciones

    U->>B: Clic en "Inmuebles"
    B->>B: cambiarVista('filtro')
    B->>F: Montar FiltroInteligente

    F->>B: emit('cargarInmuebles', filtros)
    B->>B: handleCargarInmuebles()
    B->>I: Montar Inmuebles con filtros

    I->>B: emit('total-updated', count)
    B->>B: handleTotalUpdated()
```

### Estados Centralizados vs Distribuidos

| Estado             | Ubicación         | Razón                                            |
| ------------------ | ----------------- | ------------------------------------------------ |
| `vistaActiva`      | BaseMultibolsa    | Control central de navegación                    |
| `filtrosAplicados` | BaseMultibolsa    | Coordinación entre FiltroInteligente e Inmuebles |
| `posts`            | FeedPublicaciones | Responsabilidad específica del componente        |
| `socios`           | MisSocios         | Datos específicos del componente                 |
| `hilos`            | FeedHilos         | Funcionalidad independiente                      |

---

## 💡 Patrones de Diseño

### 1. Container/Presentational Pattern

```typescript
// BaseMultibolsa = Container (lógica, estado, coordinación)
// Componentes hijos = Presentational (UI, eventos)

// Container gestiona:
- Estados compartidos
- Navegación
- Comunicación entre componentes
- Configuración global

// Presentational manejan:
- UI específica
- Interacciones locales
- Estados internos
- Lógica de negocio específica
```

### 2. Event Bus Pattern (Implícito)

```typescript
// Eventos fluyen hacia arriba desde componentes hijos
// Props/configuración fluye hacia abajo desde contenedor

FiltroInteligente → emit('cargarInmuebles') → BaseMultibolsa
Inmuebles → emit('total-updated') → BaseMultibolsa
Inmuebles → emit('favoritos-changed') → BaseMultibolsa
```

### 3. State Lifting Pattern

```typescript
// Estados que necesitan ser compartidos se "levantan" al contenedor
const filtrosAplicados = ref({ ubicacion: "", operacion: "" }); // Compartido entre FiltroInteligente e Inmuebles
const activarFavoritosInmediatamente = ref(false); // Señal de FiltroInteligente a Inmuebles
```

### 4. Single Responsibility Principle

```typescript
// Cada componente tiene una responsabilidad clara:
BaseMultibolsa     → Coordinación y navegación
FeedPublicaciones  → Sistema social inmobiliario
Inmuebles         → Búsqueda y listado de propiedades
FeedHilos         → Gestión de conversaciones
MisSocios         → Administración de contactos
```

---

## 🚀 Puntos de Extensión

### Agregar Nueva Vista

```typescript
// 1. Agregar al tipo
type Vista = "filtro" | "socios" | "hilos" | "feed" | "nueva-vista";

// 2. Actualizar configuración
const vistas = [
  // ... vistas existentes
  { id: "nueva-vista", icon: "icon-name", label: "Nueva Vista" },
];

// 3. Agregar template condicional
<div v-if="vistaActiva === 'nueva-vista'" class="border-b border-gray-200">
  <NuevoComponente />
</div>;
```

### Integrar Nuevo Sistema

```typescript
// Seguir patrón de comunicación establecido:
// 1. Props down para configuración
// 2. Events up para acciones
// 3. Estados locales en BaseMultibolsa para coordinación
```

---

## 📈 Métricas de Rendimiento

| Métrica                  | Valor       | Descripción                             |
| ------------------------ | ----------- | --------------------------------------- |
| **Componentes cargados** | 1-2         | Solo vista activa + BaseMultibolsa      |
| **Memory footprint**     | Bajo        | Componentes inactivos no están montados |
| **First paint**          | Rápido      | Header renderiza inmediatamente         |
| **Navigation speed**     | Instantáneo | Toggle entre vistas sin recargas        |

---

## 🛡️ Consideraciones de Seguridad

### Validación de Props

```typescript
// Token validation se delega a componentes hijos
// BaseMultibolsa solo actúa como proxy seguro
:token="props.token"  // Passed-through sin exposición
```

### Estado Inmutable

```typescript
// Estados se modifican solo a través de métodos controlados
const cambiarVista = (vista: Vista) => {
  // Validación implícita por TypeScript
  vistaActiva.value = vista;
};
```

---

**BaseMultibolsaInmobiliaria.vue** implementa un patrón de contenedor robusto que coordina eficientemente múltiples sistemas especializados mientras mantiene separación clara de responsabilidades y comunicación unidireccional predecible.
