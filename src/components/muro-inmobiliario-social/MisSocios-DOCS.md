# 👥 MisSocios.vue - Documentación Técnica

> Subcomponente de gestión avanzada de socios y sistema de etiquetas tipo Notion

## 📋 Tabla de Contenidos

- [🎯 Descripción General](#-descripción-general)
- [🧩 Props y Estados](#-props-y-estados)
- [🔧 Métodos Principales](#-métodos-principales)
- [🔗 Endpoints y Flujo Backend](#-endpoints-y-flujo-backend)
- [🎨 Experiencia de Usuario (UX)](#-experiencia-de-usuario-ux)
- [🔄 Flujos Optimistas y Rollback](#-flujos-optimistas-y-rollback)
- [🔍 Ejemplo Visual](#-ejemplo-visual)
- [📚 Referencias](#-referencias)

---

## 🎯 Descripción General

`MisSocios.vue` es un subcomponente especializado para la gestión de socios en el sistema inmobiliario, con un sistema de etiquetas (tags) visual y funcional inspirado en Notion. Permite:

- <PERSON><PERSON><PERSON>, as<PERSON><PERSON>, buscar y eliminar etiquetas de forma rápida y visual.
- Filtrar socios por nombre, empresa, ubicación o nombre de etiqueta (insensible a acentos).
- Realizar todas las operaciones de etiquetas de forma optimista, con rollback automático en caso de error.
- Eliminar etiquetas de un solo socio o de todo el sistema (con confirmación y advertencia).

---

## 🧩 Props y Estados

### Props

- `showAddFriendModal` (`Boolean`): Controla la visibilidad del modal para agregar socio.

### Estados principales (reactivos)

- `socios`: Lista de socios con sus datos y etiquetas asignadas.
- `etiquetasDisponibles`: Array de todas las etiquetas disponibles en el sistema.
- `searchQuery`: Texto de búsqueda global (nombre, empresa, ubicación, etiquetas).
- `menuEtiquetasAbierto`: ID del socio cuyo menú de etiquetas está abierto.
- `filtroEtiquetas`: Texto de búsqueda/creación de etiquetas en el menú.
- `creandoEtiqueta`: Estado de loading al crear una etiqueta.
- `showConfirmDeleteTagModal`: Modal de confirmación para eliminar etiqueta globalmente.
- `eliminandoEtiqueta`: Estado de loading al eliminar etiqueta globalmente.

---

## 🔧 Métodos Principales

- `fetchSocios()`: Obtiene socios y etiquetas desde el backend y mapea IDs de tags a objetos completos.
- `asignarEtiqueta(socio, etiqueta)`: Asigna una etiqueta existente a un socio (optimista, rollback en error).
- `removerEtiqueta(socio, etiquetaId)`: Remueve una etiqueta de un socio (optimista, rollback en error y elimina la etiqueta si ya no está asignada a ningún socio).
- `crearNuevaEtiqueta(socio)`: Crea una nueva etiqueta y la asigna al socio (optimista, rollback en error).
- `eliminarEtiquetaCompleta()`: Elimina una etiqueta de todos los socios y del sistema (con confirmación y rollback en error).
- `mostrarConfirmacionEliminarEtiqueta(etiqueta)`: Abre el modal de confirmación para eliminar una etiqueta globalmente.

---

## 🔗 Endpoints y Flujo Backend

- `GET /msi-v5/owner/socios` — Devuelve arrays de socios y tags.
- `POST /msi-v5/owner/tags-socios` — Asigna etiqueta existente o crea y asigna nueva etiqueta a un socio.
- `DELETE /msi-v5/owner/tags-socios/{contrato_id}/{tag_id}` — Remueve etiqueta de un socio. Si la etiqueta queda sin relaciones, se elimina del sistema.
- `DELETE /msi-v5/owner/tags-socios/{tag_id}` — Elimina etiqueta completamente del sistema (de todos los socios).
- `POST /owner/socios/tag/create` — (Legacy) Crear nueva etiqueta.

---

## 🎨 Experiencia de Usuario (UX)

- **Menú de etiquetas tipo Notion**: Selector visual con colores, búsqueda y creación rápida.
- **Botón +Tag**: Permite agregar o crear etiquetas desde el menú contextual de cada socio.
- **Ícono de eliminar**: Siempre visible en mobile, solo en hover en desktop.
- **Modal de confirmación**: Para eliminar etiquetas globalmente, con advertencia de impacto.
- **Feedback inmediato**: Cambios reflejados instantáneamente en la UI (optimista).

---

## 🔄 Flujos Optimistas y Rollback

- **Optimista**: Todas las operaciones de asignación, creación y eliminación de etiquetas actualizan la UI de inmediato.
- **Rollback**: Si ocurre un error en la llamada al backend, se revierte el cambio y se muestra un mensaje de error al usuario.
- **Eliminación global**: Si una etiqueta se elimina completamente, se remueve de todos los socios y del menú. Si falla, se restaura en todos los lugares necesarios.

---

## 🔍 Ejemplo Visual

```vue
<!-- Ejemplo de menú de etiquetas -->
<div class="relative inline-block ml-1 sm:ml-2">
  <button class="px-1.5 py-0.5 text-xs rounded-md border border-dashed border-gray-400 text-gray-400 hover:border-gray-400 hover:text-gray-800">
    +Tag
  </button>
  <div class="absolute left-0 z-20 w-64 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
    <!-- Buscador y creador de etiquetas -->
    <input type="text" placeholder="Buscar o crear etiqueta..." />
    <!-- Lista de etiquetas -->
    <button v-for="etiqueta in etiquetasFiltradas" :key="etiqueta.id">
      <span :style="{ backgroundColor: etiqueta.backgroundColor, border: `1px solid ${etiqueta.color}` }">{{ etiqueta.nombre }}</span>
      <ion-icon name="trash-outline"></ion-icon>
    </button>
  </div>
</div>
```

---

## 📚 Referencias

- [MURO-INMOBILIARIO-SOCIAL-DOCS.md](./MURO-INMOBILIARIO-SOCIAL-DOCS.md)
- [README.md](../../../README.md)
- [API REST Backend](../../../../Meteor/MulbinComponents/app/API-REST-External-Backends.md)
