<template>
  <!-- Contenedor de comentarios -->
  <div v-if="showComments" class="p-3 mt-3 bg-gray-50 rounded">
    <!-- Lista de comentarios -->
    <div
      v-for="comment in comments"
      :key="comment._id || comment.id"
      class="mb-3"
    >
      <div class="flex items-start">
        <img
          :src="comment.authorCache.avatar"
          :alt="getAuthorFullName(comment.authorCache)"
          class="mr-2 w-7 h-7 rounded-full"
        />
        <div class="flex-1">
          <div class="relative p-2 bg-white rounded shadow-sm">
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <p class="text-xs font-semibold text-gray-900">
                  {{ getAuthorFullName(comment.authorCache) }}
                </p>
                <p class="text-sm text-gray-700">
                  {{ comment.text }}
                </p>
              </div>

              <!-- Menú contextual del comentario -->
              <div class="relative">
                <button
                  v-if="puedeGestionarComentario(comment)"
                  @click="toggleMenuComentario(getCommentId(comment))"
                  class="flex justify-center items-center w-5 h-5 text-gray-400 rounded-full hover:text-gray-600 hover:bg-gray-100 boton-menu-comentario"
                  title="Opciones del comentario"
                >
                  <ion-icon
                    name="ellipsis-horizontal"
                    class="text-xs"
                  ></ion-icon>
                </button>

                <!-- Menú desplegable del comentario -->
                <div
                  v-if="menuComentarioAbierto === getCommentId(comment)"
                  class="absolute right-0 top-full z-30 mt-1 w-48 bg-white rounded-lg border border-gray-200 shadow-lg menu-desplegable-comentario"
                >
                  <div class="py-1">
                    <!-- Opción para convertir a hilo -->
                    <button
                      v-if="puedeConvertirAHilo(comment)"
                      @click="convertirComentarioAHilo(comment)"
                      class="flex items-center px-3 py-2 w-full text-sm text-blue-700 hover:bg-blue-50"
                      :disabled="convirtiendoComentario"
                    >
                      <ion-icon
                        v-if="convirtiendoComentario"
                        name="reload-outline"
                        class="mr-2 text-blue-600 animate-spin"
                      ></ion-icon>
                      <ion-icon
                        v-else
                        name="chatbubbles-outline"
                        class="mr-2 text-blue-600"
                      ></ion-icon>
                      <span v-if="convirtiendoComentario">Convirtiendo...</span>
                      <span v-else>Convertir a Hilo</span>
                    </button>

                    <!-- Separador -->
                    <div
                      v-if="puedeConvertirAHilo(comment)"
                      class="my-1 border-t border-gray-100"
                    ></div>

                    <!-- Eliminar comentario -->
                    <button
                      @click="eliminarComentario(comment)"
                      class="flex items-center px-3 py-2 w-full text-sm text-red-700 hover:bg-red-50"
                    >
                      <ion-icon
                        name="trash-outline"
                        class="mr-2 text-red-600"
                      ></ion-icon>
                      <span v-if="esAutorDelComentario(comment)">Eliminar</span>
                      <span v-else>Eliminar comentario</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <p class="mt-1 text-xs text-gray-500">
            {{ formatDate(comment.createdAt) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Form para nuevo comentario -->
    <div
      class="flex gap-2 items-center p-3 mt-3 bg-white rounded-xl border border-gray-200 shadow-sm"
    >
      <input
        type="text"
        v-model="newComment"
        placeholder="Escribe un comentario..."
        class="flex-1 px-0 py-1 text-sm placeholder-gray-500 bg-transparent border-none outline-none focus:placeholder-gray-400"
        @keyup.enter="addComment"
      />
      <button
        @click="addComment"
        class="flex justify-center items-center w-8 h-8 text-white rounded-full bg-mulbin-600 hover:bg-mulbin-700 disabled:opacity-50 disabled:cursor-not-allowed"
        :disabled="!newComment.trim()"
        title="Enviar comentario"
      >
        <ion-icon name="send" class="text-sm"></ion-icon>
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  onUnmounted,
  computed,
  watch,
  type PropType,
} from "vue";
import type { PostInmobiliario } from "../../types/inmobiliario";
import inmobiliarioService from "../../services/inmobiliarioService";
import ddpService from "../../services/ddpService";
import { useModal } from "../ui";

export default defineComponent({
  name: "ComentariosPost",

  props: {
    post: {
      type: Object as () => PostInmobiliario,
      required: true,
    },
    showComments: {
      type: Boolean,
      default: false,
    },
    token: {
      type: String,
      required: true,
    },
    currentUserId: {
      type: [String, null] as PropType<string | null>,
      default: null,
    },
  },

  emits: [
    "toggle-comments",
    "comment-added",
    "comment-deleted",
    "comment-converted",
    "comments-updated",
    "error",
  ],

  setup(props, { emit }) {
    // Composables
    const { confirm, success, warning, error: showError } = useModal();

    // Estados locales
    const comments = ref<any[]>([]);
    const newComment = ref("");
    const menuComentarioAbierto = ref<string | null>(null);
    const convirtiendoComentario = ref(false);
    const loading = ref(false);

    // Referencias para limpieza
    let unsubscribeCommentsObserver: (() => void) | null = null;

    // Computed para normalizar ID del post
    const postId = computed(() => {
      return normalizePostId(props.post);
    });

    // Helper para normalizar IDs de posts
    const normalizePostId = (post: any): string | null => {
      if (post._id && typeof post._id === "string") {
        return post._id;
      }
      if (post.id && typeof post.id === "string") {
        post._id = post.id;
        return post.id;
      }
      return null;
    };

    // Helper para normalizar IDs de comentarios
    const normalizeCommentId = (comment: any): string | null => {
      if (comment._id && typeof comment._id === "string") {
        return comment._id;
      }
      if (comment.id && typeof comment.id === "string") {
        comment._id = comment.id;
        return comment.id;
      }
      return null;
    };

    // Helper para obtener ID de comentario
    const getCommentId = (comment: any): string => {
      return normalizeCommentId(comment) || "unknown";
    };

    // Helper para obtener nombre completo del autor
    const getAuthorFullName = (authorCache: any) => {
      if (authorCache.firstName || authorCache.lastName) {
        return `${authorCache.firstName || ""} ${
          authorCache.lastName || ""
        }`.trim();
      }
      return authorCache.name || "Usuario";
    };

    // Helper para formatear fecha
    const formatDate = (date: string | Date) => {
      const d = new Date(date);
      return d.toLocaleDateString("es-MX", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    };

    // Verificar si el usuario actual es el autor del comentario
    const esAutorDelComentario = (comment: any): boolean => {
      try {
        if (!props.currentUserId) {
          return false;
        }
        return comment.authorId === props.currentUserId;
      } catch (error) {
        console.error("❌ Error al verificar permisos del comentario:", error);
        return false;
      }
    };

    // Verificar si el usuario actual es el autor del post
    const esAutorDelPost = (): boolean => {
      try {
        if (!props.currentUserId) {
          return false;
        }
        return props.post.authorId === props.currentUserId;
      } catch (error) {
        console.error("❌ Error al verificar permisos del post:", error);
        return false;
      }
    };

    // Verificar si el usuario puede gestionar el comentario
    const puedeGestionarComentario = (comment: any): boolean => {
      try {
        if (!props.currentUserId) {
          return false;
        }

        // El autor del comentario siempre puede gestionarlo
        if (comment.authorId === props.currentUserId) {
          return true;
        }

        // El autor del post puede gestionar cualquier comentario en su publicación
        if (props.post.authorId === props.currentUserId) {
          return true;
        }

        return false;
      } catch (error) {
        console.error(
          "❌ Error al verificar permisos de gestión del comentario:",
          error
        );
        return false;
      }
    };

    // Verificar si se puede convertir comentario a hilo
    const puedeConvertirAHilo = (comment: any): boolean => {
      // No permitir conversión si el mismo usuario es autor del post Y del comentario
      if (esAutorDelPost() && esAutorDelComentario(comment)) {
        return false;
      }
      return true;
    };

    // Toggle menú contextual de comentario
    const toggleMenuComentario = (commentId: string) => {
      menuComentarioAbierto.value =
        menuComentarioAbierto.value === commentId ? null : commentId;
    };

    // Cerrar menú al hacer clic fuera
    const cerrarMenuComentarioAlClickFuera = (event: Event) => {
      const target = event.target as HTMLElement;
      const menuElement = target.closest(".menu-desplegable-comentario");
      const botonElement = target.closest(".boton-menu-comentario");

      if (!menuElement && !botonElement) {
        menuComentarioAbierto.value = null;
      }
    };

    // Configurar observer de comentarios siguiendo principios DDP
    const setupCommentsObserver = () => {
      try {
        if (!postId.value) return () => {};

        console.log(
          "🔄 Configurando observer de comentarios para post:",
          postId.value
        );

        const commentsCollection = ddpService.collection("comentariosPost");
        const observer = commentsCollection.onChange(() => {
          console.log("💬 Cambio detectado en comentarios");

          if (props.showComments && postId.value) {
            try {
              // Obtener comentarios actualizados desde la colección local
              const updatedComments = commentsCollection
                .filter(
                  (c: any) => c.postId === postId.value && c.active !== false
                )
                .fetch()
                .sort(
                  (a: any, b: any) =>
                    new Date(a.createdAt).getTime() -
                    new Date(b.createdAt).getTime()
                );

              // Actualizar comentarios locales reactivamente
              comments.value = updatedComments;

              console.log(
                `💬 Comentarios actualizados reactivamente: ${updatedComments.length}`
              );

              // Emitir evento para notificar al padre
              emit("comments-updated", {
                postId: postId.value,
                comments: updatedComments,
                commentsCount: updatedComments.length,
              });
            } catch (error) {
              console.error("❌ Error actualizando comentarios:", error);
            }
          }
        });

        // Retornar función de limpieza DDP
        return () => {
          if (observer && typeof observer.stop === "function") {
            observer.stop();
          }
        };
      } catch (error) {
        console.error("❌ Error configurando observer de comentarios:", error);
        return () => {};
      }
    };

    // Cargar comentarios iniciales siguiendo principios DDP
    const loadCommentsInitial = async () => {
      if (!props.showComments || !postId.value) return;

      try {
        loading.value = true;
        console.log("📡 Cargando comentarios iniciales:", postId.value);

        // Cargar comentarios iniciales via subscription
        const initialComments = await inmobiliarioService.getComments(
          postId.value
        );

        // Establecer estado inicial
        comments.value = initialComments;

        console.log(`✅ ${initialComments.length} comentarios cargados`);
      } catch (error) {
        console.error("❌ Error al cargar comentarios iniciales:", error);
        comments.value = [];
        emit("error", "Error al cargar comentarios");
      } finally {
        loading.value = false;
      }
    };

    // Agregar nuevo comentario
    const addComment = async () => {
      if (!newComment.value.trim() || !postId.value) return;

      try {
        console.log("💬 Agregando comentario:", newComment.value);

        // Solo agregar el comentario - la subscription se encarga del resto
        await inmobiliarioService.addComment(postId.value, newComment.value);

        // Limpiar input
        newComment.value = "";

        console.log("✅ Comentario agregado exitosamente");

        // Emitir evento para notificar al padre
        emit("comment-added");
      } catch (err) {
        console.error("❌ Error al agregar comentario:", err);
        showError("Error al agregar comentario", "Error");
        emit("error", "Error al agregar comentario");
      }
    };

    // Eliminar comentario
    const eliminarComentario = async (comment: any) => {
      menuComentarioAbierto.value = null;

      await confirm(
        `¿Estás seguro de que quieres eliminar este comentario?\n\n"${comment.text}"\n\nEsta acción no se puede deshacer.`,
        () => eliminarComentarioConfirmado(comment),
        "Eliminar comentario"
      );
    };

    // Función auxiliar para eliminar comentario después de confirmación
    const eliminarComentarioConfirmado = async (comment: any) => {
      try {
        const commentId = normalizeCommentId(comment);
        if (!commentId) {
          throw new Error("ID de comentario no válido");
        }

        console.log("🗑️ Eliminando comentario:", commentId);

        // Solo eliminar el comentario - la subscription se encarga del resto
        await inmobiliarioService.removeComment(commentId);

        console.log("✅ Comentario eliminado exitosamente");

        // Mostrar mensaje de éxito
        success("Comentario eliminado exitosamente", "¡Listo!");

        // Emitir evento para notificar al padre
        emit("comment-deleted", { commentId });
      } catch (error) {
        console.error("❌ Error al eliminar comentario:", error);

        const errorMessage =
          error instanceof Error
            ? `Error: ${error.message}`
            : "Error al eliminar el comentario";

        warning(errorMessage, "Error al eliminar");
        emit("error", errorMessage);
      }
    };

    // Convertir comentario a hilo de interés
    const convertirComentarioAHilo = async (comment: any) => {
      menuComentarioAbierto.value = null;

      // Validaciones previas
      if (!props.currentUserId) {
        warning(
          "Debes iniciar sesión para convertir comentarios a hilos",
          "Sin autorización"
        );
        return;
      }

      // Verificar que el usuario tiene permisos
      if (!esAutorDelComentario(comment) && !esAutorDelPost()) {
        warning(
          "Solo el autor del comentario o el autor de la publicación pueden convertir comentarios a hilos de interés",
          "Sin autorización"
        );
        return;
      }

      const commentId = normalizeCommentId(comment);
      if (!commentId) {
        warning("Error: ID de comentario no válido", "Error");
        return;
      }

      // Confirmación del usuario
      await confirm(
        `¿Convertir este comentario en un hilo de interés?\n\n"${comment.text}"\n\nEl comentario se eliminará y se creará un hilo privado.\n\n💬 El hilo se abrirá automáticamente para continuar la conversación.`,
        () => convertirComentarioConfirmado(commentId),
        "Convertir a Hilo"
      );
    };

    // Función auxiliar para convertir después de confirmación
    const convertirComentarioConfirmado = async (commentId: string) => {
      try {
        convirtiendoComentario.value = true;

        console.log(`🔄 Convirtiendo comentario ${commentId} a hilo...`);

        // Conectar con el servicio usando el token
        await inmobiliarioService.connectWithToken(props.token);

        // Llamar al método del backend
        const result = await inmobiliarioService.call(
          "hilosInteres.convertFromComment",
          commentId
        );

        if (result.success) {
          console.log(`✅ Comentario convertido a hilo: ${result.hiloId}`);

          // Mostrar mensaje de éxito
          success(
            `Comentario convertido a hilo de interés exitosamente.\n\nTítulo: "${result.titulo}"`,
            "¡Hilo Creado!"
          );

          // Emitir evento para notificar al padre sobre la conversión
          emit("comment-converted", {
            commentId,
            hiloId: result.hiloId,
            titulo: result.titulo,
            referenciaPrivada: result.referenciaPrivada,
          });
        } else {
          throw new Error(result.message || "Error al convertir comentario");
        }
      } catch (error) {
        console.error("❌ Error al convertir comentario a hilo:", error);

        let errorMessage = "Error al convertir el comentario a hilo de interés";

        // Verificar si es el error de límite máximo de hilos
        const isMaxHilosError =
          (error as any)?.error === "max-hilos-reached" ||
          (error as any)?.message?.includes?.("máximo de 3 hilos") ||
          (error as any)?.reason?.includes?.("máximo de 3 hilos");

        if (isMaxHilosError) {
          window.alert(
            "🚫 LÍMITE DE HILOS ALCANZADO\n\n" +
              "Has alcanzado el límite máximo de 3 hilos de interés por publicación.\n\n" +
              "Para crear un nuevo hilo, primero debes eliminar alguno de los hilos existentes."
          );
          return;
        }

        if (error instanceof Error) {
          if (error.message.includes("invalid-conversion")) {
            errorMessage =
              "No se puede convertir comentarios del autor de la publicación a hilos de interés.";
          } else if (error.message.includes("not-authorized")) {
            errorMessage =
              "Solo puedes convertir tus propios comentarios a hilos de interés.";
          } else if (error.message.includes("hilo-exists")) {
            errorMessage = error.message;
          } else {
            errorMessage = `Error: ${error.message}`;
          }
        }

        warning(errorMessage, "Error de Conversión");
        emit("error", errorMessage);
      } finally {
        convirtiendoComentario.value = false;
      }
    };

    // Observer de cambios en showComments siguiendo principios DDP
    watch(
      () => props.showComments,
      async (newValue: boolean) => {
        if (newValue) {
          // Cargar comentarios cuando se abren
          await loadCommentsInitial();
        } else {
          // Limpiar comentarios cuando se cierran
          comments.value = [];
          newComment.value = "";
          menuComentarioAbierto.value = null;
        }
      },
      { immediate: true }
    );

    // Configurar observer y event listeners al montar
    onMounted(() => {
      console.log("🔌 ComentariosPost montado para post:", postId.value);

      // Configurar observer reactivo UNA SOLA VEZ
      unsubscribeCommentsObserver = setupCommentsObserver();

      // Agregar event listener para cerrar menú
      document.addEventListener("click", cerrarMenuComentarioAlClickFuera);
    });

    // Limpiar al desmontar siguiendo principios DDP
    onUnmounted(() => {
      console.log("🧹 Limpiando ComentariosPost para post:", postId.value);

      // Limpiar observer DDP
      if (unsubscribeCommentsObserver) {
        unsubscribeCommentsObserver();
      }

      // Limpiar event listener
      document.removeEventListener("click", cerrarMenuComentarioAlClickFuera);
    });

    return {
      // Estados
      comments,
      newComment,
      menuComentarioAbierto,
      convirtiendoComentario,
      loading,

      // Computed
      postId,

      // Métodos para template
      getCommentId,
      getAuthorFullName,
      formatDate,
      esAutorDelComentario,
      puedeGestionarComentario,
      puedeConvertirAHilo,
      toggleMenuComentario,
      addComment,
      eliminarComentario,
      convertirComentarioAHilo,
    };
  },
});
</script>

<style scoped>
.boton-menu-comentario {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.relative:hover .boton-menu-comentario {
  opacity: 1;
}

.menu-desplegable-comentario {
  animation: slideDown 0.15s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
