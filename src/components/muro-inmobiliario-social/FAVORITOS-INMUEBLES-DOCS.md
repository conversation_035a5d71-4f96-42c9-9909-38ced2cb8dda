# Sistema de Favoritos de Inmuebles - Documentación

## 📋 Descripción General

El sistema de favoritos permite a los usuarios marcar inmuebles como favoritos y mantener esta información persistente usando `localStorage`. Los favoritos se sincronizan con el backend para obtener información actualizada de los inmuebles.

## 🏗️ Arquitectura

### Frontend (Vue.js)

- **Servicio**: `favoritosService.ts` - Maneja localStorage y operaciones CRUD
- **Componentes**:
  - `Inmuebles.vue` - Lista principal con botones de favoritos
  - `DetalleInmuebleModal.vue` - Modal con botón de favorito
  - `InmueblesFavoritos.vue` - Vista dedicada a favoritos

### Backend (PHP)

- **Endpoint**: `GET /msi-v5/owner/inmuebles/socios/favoritos?ids=1,2,3`
- **Action**: `ListInmueblesFavoritosOwnerAction.php`

## 🔧 Funcionalidades

### 1. Persistencia Local (localStorage)

```typescript
// Agregar favorito
favoritosService.agregarFavorito({
  id: "123",
  titulo: "Casa en venta",
  precio: 2500000,
  operacion: "venta",
  ubicacion: "Centro, Guadalajara",
});

// Verificar si es favorito
const esFavorito = favoritosService.esFavorito("123");

// Obtener todos los favoritos
const favoritos = favoritosService.getFavoritos();
```

### 2. Sincronización con Backend

- Los IDs se envían al endpoint como parámetro `ids` separados por coma
- El backend valida que los inmuebles pertenezcan a socios autorizados
- Retorna información completa + IDs no encontrados

### 3. Gestión de Favoritos Obsoletos

- Detecta inmuebles que ya no existen o no están publicados
- Permite limpiar automáticamente favoritos obsoletos
- Muestra advertencias al usuario

## 📡 API Endpoints

### GET /msi-v5/owner/inmuebles/socios/favoritos

**Parámetros:**

- `ids` (string): IDs separados por coma, ej: "123,456,789"

**Respuesta:**

```json
{
  "statusCode": 200,
  "data": {
    "inmuebles": [...],
    "total": 2,
    "total_solicitados": 3,
    "ids_no_encontrados": ["789"],
    "mensaje": "Algunos inmuebles favoritos ya no están disponibles"
  }
}
```

## 🎯 Casos de Uso

### 1. Usuario marca favorito

1. Usuario hace clic en estrella en `Inmuebles.vue`
2. Se llama `favoritosService.toggleFavorito()`
3. Se actualiza localStorage
4. Se actualiza estado visual del componente

### 2. Usuario ve sus favoritos

1. Usuario navega a vista de favoritos
2. `InmueblesFavoritos.vue` obtiene IDs desde localStorage
3. Hace llamada al backend con los IDs
4. Muestra inmuebles con información actualizada

### 3. Favorito obsoleto

1. Inmueble fue eliminado o despublicado
2. Backend retorna ID en `ids_no_encontrados`
3. Se muestra advertencia al usuario
4. Usuario puede limpiar favoritos obsoletos

## 🔒 Seguridad

- Solo se pueden obtener inmuebles de **socios directos autorizados**
- Validación de sesión mediante nginx proxy
- Los IDs se validan en el backend antes de consultar la base de datos

## ⚡ Rendimiento

- **localStorage**: Acceso instantáneo para verificar favoritos
- **Backend**: Solo consulta inmuebles específicos (no full scan)
- **Límite**: Máximo 100 favoritos por usuario
- **Cache**: Los datos se cachean en el componente Vue

## 🔄 Sistema de Reactividad

### Arquitectura de Eventos

El `favoritosService` implementa un **sistema de eventos personalizado** para reactividad en tiempo real:

```typescript
// Suscribirse a cambios
const unsubscribe = favoritosService.onChange(() => {
  // Se ejecuta cada vez que cambian los favoritos
  totalFavoritos.value = favoritosService.getFavoritos().length;
});

// Desuscribirse (importante para evitar memory leaks)
unsubscribe();
```

### Implementación en Componentes

```typescript
// En setup() de componentes Vue
onMounted(() => {
  // Suscribirse a cambios
  unsubscribeFavoritos = favoritosService.onChange(() => {
    // Actualizar estado reactivo
    totalFavoritos.value = favoritosService.getFavoritos().length;
  });
});

onUnmounted(() => {
  // CRÍTICO: Siempre desuscribirse para evitar memory leaks
  if (unsubscribeFavoritos) {
    unsubscribeFavoritos();
    unsubscribeFavoritos = null;
  }
});
```

### 🎯 **Componentes con Reactividad Completa**

#### 1. `Inmuebles.vue` - Contador del Botón

- ✅ **Contador reactivo**: El botón "Mis Favoritos" se actualiza inmediatamente
- ✅ **Badge dinámico**: Muestra/oculta el número según la cantidad
- ✅ **Sincronización**: Siempre refleja el estado actual de localStorage

#### 2. `InmueblesFavoritos.vue` - Estadísticas Header

- ✅ **Header estadísticas**: Se actualiza al eliminar/agregar favoritos
- ✅ **Contador total**: Refleja inmediatamente los cambios
- ✅ **Estadísticas por operación**: Se recalcula automáticamente
- ✅ **Fecha último agregado**: Se actualiza dinámicamente

### ⚠️ **IMPORTANTE para Desarrolladores**

**NO crear sistemas de eventos paralelos**. Este patrón ya está implementado y debe reutilizarse:

❌ **Evitar:**

```typescript
// NO hacer esto - crea sobrecarga
setInterval(() => {
  checkFavoritos();
}, 1000);

// NO hacer esto - eventos duplicados
window.addEventListener("storage", handler);
```

✅ **Usar en su lugar:**

```typescript
// Usar el sistema existente
favoritosService.onChange(callback);
```

### Cuándo Usar Cada Patrón

| Patrón             | Cuándo Usar                                    | Ejemplo                                             |
| ------------------ | ---------------------------------------------- | --------------------------------------------------- |
| `computed()`       | Datos que se calculan de estado reactivo       | `filteredItems = computed(() => items.filter(...))` |
| `ref + onChange()` | Datos externos que cambian (localStorage, API) | `totalFavoritos = ref()` + `service.onChange()`     |
| `watch()`          | Reaccionar a cambios de props/refs específicos | `watch(searchQuery, fetchResults)`                  |

### 🧪 **Testing de Reactividad**

Para verificar que la reactividad funciona correctamente:

1. **Abrir dos ventanas** del mismo componente
2. **Agregar favorito** en una ventana
3. **Verificar** que el contador se actualiza en ambas
4. **Remover favorito** desde lista de favoritos
5. **Verificar** que estadísticas se actualizan inmediatamente

### 🔍 **Debug de Eventos**

```typescript
// En DevTools Console
favoritosService.onChange(() => {
  console.log(
    "🔄 Favoritos cambiaron:",
    favoritosService.getFavoritos().length
  );
});
```

## 🛠️ Configuración

### Variables de Entorno

```typescript
// En panel4-templates/.env
VITE_MULBIN_URL=http://localhost:8020
```

### Constantes del Servicio

```typescript
// favoritosService.ts
private readonly STORAGE_KEY = 'mulbin_favoritos_inmuebles';
private readonly MAX_FAVORITOS = 100;
```

## 🔄 Flujo Completo

```mermaid
graph TD
    A[Usuario marca favorito] --> B[favoritosService.toggleFavorito()]
    B --> C[Actualizar localStorage]
    C --> D[Actualizar UI]

    E[Usuario ve favoritos] --> F[Obtener IDs desde localStorage]
    F --> G[GET /favoritos?ids=1,2,3]
    G --> H[Backend valida socios]
    H --> I[Consultar inmuebles específicos]
    I --> J[Retornar datos + obsoletos]
    J --> K[Mostrar en InmueblesFavoritos.vue]
```

## 🧪 Testing

### Manual Testing

1. Marcar varios inmuebles como favoritos
2. Navegar fuera y regresar (verificar persistencia)
3. Eliminar un inmueble desde backend
4. Verificar que se detecta como obsoleto
5. Limpiar favoritos obsoletos

### Comandos útiles

```bash
# Ver favoritos en localStorage (DevTools Console)
localStorage.getItem('mulbin_favoritos_inmuebles')

# Limpiar favoritos manualmente
localStorage.removeItem('mulbin_favoritos_inmuebles')

# Probar endpoint directamente
curl "http://localhost:8082/owner/inmuebles/socios/favoritos?ids=123,456"
```

## 🔧 Patrón Reutilizable para Otros Servicios

Este sistema de eventos puede aplicarse a otros servicios del proyecto:

```typescript
class MiServicio {
  private listeners: Array<() => void> = [];

  // Método para suscribirse a cambios
  onChange(callback: () => void): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  // Notificar cambios (llamar en métodos que modifican datos)
  private notifyChange(): void {
    this.listeners.forEach((callback) => {
      try {
        callback();
      } catch (error) {
        console.error("Error en listener:", error);
      }
    });
  }

  // En métodos que modifican datos
  agregarItem() {
    // ... lógica ...
    this.notifyChange(); // ← Agregar esta línea
  }
}
```

### Servicios Candidatos para este Patrón

- **PostsService**: Para reactividad en el muro social
- **NotificationsService**: Para contador de notificaciones
- **SociosService**: Para lista de socios en tiempo real
- **ChatService**: Para mensajes instantáneos

## 🚀 Próximas Mejoras

1. **Sincronización en tiempo real**: WebSockets para actualizar favoritos
2. **Favoritos compartidos**: Entre socios de la misma empresa
3. **Categorías**: Organizar favoritos por tipo o ubicación
4. **Exportar**: Generar PDF con lista de favoritos
5. **Notificaciones**: Alertas cuando un favorito cambia de precio
6. **Aplicar patrón**: Extender sistema de eventos a otros servicios

## 📝 Notas de Implementación

- **Compatibilidad**: IE11+ (localStorage support)
- **Fallback**: Si localStorage falla, funciona solo en sesión actual
- **Cleanup**: Favoritos obsoletos se pueden limpiar automáticamente cada 90 días
- **Límites**: 100 favoritos máximo para evitar problemas de rendimiento
