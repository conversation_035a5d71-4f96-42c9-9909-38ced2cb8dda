<template>
  <div class="feed-hilos-demo">
    <h2 class="text-xl font-bold mb-4">📋 Demo: Feed de Hilos de Interés</h2>

    <!-- Controles -->
    <div class="controls bg-gray-50 p-4 rounded-lg mb-6">
      <div class="flex gap-4 items-center">
        <button
          @click="loadFeed"
          :disabled="loading"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {{ loading ? "Cargando..." : "Cargar Feed" }}
        </button>

        <button
          @click="loadStats"
          :disabled="loadingStats"
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          {{ loadingStats ? "Cargando..." : "Ver Estadísticas" }}
        </button>

        <button
          @click="runMigration"
          class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
        >
          🔄 Migrar sociosIds
        </button>
      </div>
    </div>

    <!-- Estadísticas -->
    <div v-if="stats" class="stats bg-white p-4 rounded-lg shadow mb-6">
      <h3 class="font-semibold mb-3">📊 Estadísticas del Feed</h3>
      <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div class="stat-item">
          <div class="text-2xl font-bold text-blue-600">
            {{ stats.totalHilos }}
          </div>
          <div class="text-sm text-gray-600">Total hilos</div>
        </div>
        <div class="stat-item">
          <div class="text-2xl font-bold text-red-600">
            {{ stats.hilosConMensajesNoLeidos }}
          </div>
          <div class="text-sm text-gray-600">Con mensajes nuevos</div>
        </div>
        <div class="stat-item">
          <div class="text-2xl font-bold text-green-600">
            {{ stats.hilosCreados }}
          </div>
          <div class="text-sm text-gray-600">Hilos creados</div>
        </div>
      </div>
    </div>

    <!-- Lista de hilos -->
    <div class="hilos-list">
      <div v-if="loading && hilos.length === 0" class="text-center py-8">
        <div
          class="animate-spin w-8 h-8 border-2 border-blue-500 rounded-full mx-auto mb-4"
        ></div>
        <p>Cargando hilos del feed...</p>
      </div>

      <div
        v-else-if="hilos.length === 0"
        class="text-center py-8 text-gray-500"
      >
        No hay hilos en el feed
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="hilo in hilos"
          :key="hilo._id"
          class="hilo-card bg-white p-4 rounded-lg border shadow-sm"
        >
          <div class="flex justify-between items-start mb-2">
            <h3 class="font-semibold">{{ hilo.titulo }}</h3>
            <span class="text-xs bg-gray-100 px-2 py-1 rounded">
              {{ hilo.mensajesCount }} mensajes
            </span>
          </div>

          <div class="text-sm text-gray-600 mb-2">
            <strong>Ref:</strong> {{ hilo.referenciaPrivada }}
          </div>

          <div class="text-sm text-gray-500">
            <strong>Participantes:</strong>
            {{ hilo.sociosIds ? hilo.sociosIds.join(", ") : "N/A" }}
          </div>

          <div class="text-xs text-gray-400 mt-2">
            Último mensaje: {{ formatDate(hilo.lastMessageAt) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Resultado de migración -->
    <div
      v-if="migrationResult"
      class="migration-result bg-green-50 border border-green-200 p-4 rounded-lg mt-6"
    >
      <h3 class="font-semibold text-green-800 mb-2">✅ Migración Completada</h3>
      <p class="text-sm text-green-700">
        {{ migrationResult.migrated }}/{{ migrationResult.total }} hilos
        migrados exitosamente
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { HiloInteres, FeedHilosStats } from "../../types/hilosInteres";
import hilosInteresService from "../../services/hilosInteresService";
import ddpService from "../../services/ddpService";

// Props
const props = defineProps<{
  userId: string;
}>();

// Estados
const hilos = ref<HiloInteres[]>([]);
const stats = ref<FeedHilosStats | null>(null);
const loading = ref(false);
const loadingStats = ref(false);
const migrationResult = ref<any>(null);

// Métodos
const loadFeed = async () => {
  try {
    loading.value = true;
    console.log("📋 Cargando feed para usuario:", props.userId);

    // Usar el método del backend directamente
    const feedHilos = await ddpService.call(
      "hilosInteres.getFeedForUser",
      props.userId,
      {
        limit: 20,
        sortBy: "lastMessage",
      }
    );

    hilos.value = feedHilos;
    console.log("✅ Feed cargado:", feedHilos.length, "hilos");
  } catch (error) {
    console.error("❌ Error cargando feed:", error);
  } finally {
    loading.value = false;
  }
};

const loadStats = async () => {
  try {
    loadingStats.value = true;
    console.log("📊 Cargando estadísticas...");

    const feedStats = await hilosInteresService.getFeedStats(props.userId);
    stats.value = feedStats;
    console.log("✅ Estadísticas cargadas:", feedStats);
  } catch (error) {
    console.error("❌ Error cargando estadísticas:", error);
  } finally {
    loadingStats.value = false;
  }
};

const runMigration = async () => {
  try {
    console.log("🔄 Ejecutando migración de sociosIds...");

    const result = await ddpService.call("hilosInteres.migrateSociosIds");
    migrationResult.value = result;
    console.log("✅ Migración completada:", result);

    // Recargar feed después de migración
    await loadFeed();
  } catch (error) {
    console.error("❌ Error en migración:", error);
  }
};

const formatDate = (dateStr: string | Date): string => {
  try {
    const date = typeof dateStr === "string" ? new Date(dateStr) : dateStr;
    return date.toLocaleDateString() + " " + date.toLocaleTimeString();
  } catch {
    return "Fecha inválida";
  }
};
</script>

<style scoped>
.feed-hilos-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.hilo-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hilo-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
