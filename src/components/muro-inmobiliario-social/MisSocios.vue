<template>
  <div class="bg-white rounded-lg shadow-md">
    <!-- Componente interno: Modal para agregar socio -->
    <AgregarSocioModal
      v-model="showAddFriendModal"
      @socio-added="agregarSocio"
    />

    <!-- Barra superior con botón Agregar <PERSON>cio -->
    <div class="flex justify-between items-center p-4 bg-gray-50 border-b">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">
          <ion-icon
            name="people-outline"
            class="mr-2 text-mulbin-600"
          ></ion-icon>
          Mis Socios
        </h3>
        <p class="text-sm text-gray-600">
          Gestiona tu red de socios inmobiliarios
        </p>
      </div>
      <button
        @click="showAddFriendModal = true"
        class="flex items-center px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors bg-mulbin-600 hover:bg-mulbin-700 focus:outline-none focus:ring-2 focus:ring-mulbin-500 focus:ring-offset-2"
      >
        <ion-icon name="person-add-outline" class="mr-2"></ion-icon>
        Agregar <PERSON>
      </button>
    </div>

    <!-- Buscador de socios -->
    <div class="p-4 border-b">
      <div class="relative">
        <input
          type="text"
          placeholder="Buscar en mis socios..."
          class="py-2 pr-4 pl-10 w-full rounded-lg border border-gray-300 focus:border-mulbin-500 focus:ring-2 focus:ring-mulbin-200"
          v-model="searchQuery"
        />
        <div
          class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none"
        >
          <ion-icon name="search-outline" class="text-gray-400"></ion-icon>
        </div>
      </div>
    </div>

    <!-- Indicador de carga -->
    <div v-if="loading" class="p-8 text-center text-gray-500">
      <div class="flex justify-center">
        <div
          class="w-8 h-8 rounded-full border-4 border-gray-200 animate-spin border-t-mulbin-600"
        ></div>
      </div>
      <p class="mt-2">Cargando socios...</p>
    </div>

    <!-- Error de carga -->
    <div v-else-if="error" class="p-8 text-center text-red-500">
      <ion-icon name="alert-circle-outline" class="mb-2 text-5xl"></ion-icon>
      <p>{{ error }}</p>
      <button
        @click="fetchSocios"
        class="px-4 py-2 mt-4 text-white rounded-lg bg-mulbin-600 hover:bg-mulbin-700"
      >
        Reintentar
      </button>
    </div>

    <div v-else>
      <!-- Lista unificada de socios -->
      <div v-if="filteredSocios.length" class="divide-y divide-gray-100">
        <div
          v-for="socio in filteredSocios"
          :key="socio.id"
          :class="[
            'p-3 sm:p-4 transition-colors hover:bg-gray-50',
            {
              'bg-blue-50': socio.tipo === 'indirecto',
              'bg-yellow-50': socio.tipo === 'pendiente',
              'bg-yellow-100': socio.tipo === 'porAutorizar',
            },
          ]"
        >
          <div
            class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
          >
            <div class="flex items-center">
              <!-- Avatar del usuario si existe -->
              <img
                v-if="socio.avatar"
                :src="socio.avatar"
                :alt="socio.nombre"
                class="object-cover flex-shrink-0 mr-3 w-10 h-10 rounded-full sm:w-12 sm:h-12 sm:mr-4"
              />
              <!-- SVG predeterminado si no hay avatar -->
              <div
                v-else
                class="flex flex-shrink-0 justify-center items-center mr-3 w-10 h-10 text-gray-400 bg-gray-200 rounded-full sm:w-12 sm:h-12 sm:mr-4"
              >
                <img
                  :src="`${mulbinUrl}/images/avatar.png`"
                  alt="Avatar"
                  class="w-6 h-6 sm:w-8 sm:h-8"
                />
              </div>
              <div class="flex-1 min-w-0">
                <h3
                  :class="[
                    'font-medium text-gray-900 text-sm sm:text-base',
                    {
                      'flex items-center flex-wrap':
                        socio.bolsas ||
                        socio.tipo === 'indirecto' ||
                        socio.tipo === 'pendiente' ||
                        socio.tipo === 'porAutorizar',
                    },
                  ]"
                >
                  {{ socio.nombre }}

                  <!-- Mostrar las bolsas si existen -->
                  <template
                    v-if="socio.bolsas && Object.keys(socio.bolsas).length"
                  >
                    <span
                      v-for="(bolsa, key) in socio.bolsas"
                      :key="key"
                      :class="[
                        'ml-1 sm:ml-2 px-1.5 sm:px-2 py-0.5 text-xs rounded-full',
                        socio.tipo === 'indirecto'
                          ? 'bg-blue-200 text-blue-800'
                          : 'bg-green-200 text-green-800',
                      ]"
                      :title="bolsa.descripcion"
                    >
                      {{ bolsa.nombre }}
                    </span>
                  </template>

                  <!-- Mostrar "Indirecto" solo si no tiene bolsas y es de tipo indirecto -->
                  <span
                    v-else-if="socio.tipo === 'indirecto'"
                    class="px-1.5 py-0.5 ml-1 text-xs text-blue-800 bg-blue-200 rounded-full sm:ml-2 sm:px-2"
                  >
                    Indirecto
                  </span>

                  <!-- Mostrar "Pendiente" si es de tipo pendiente -->
                  <span
                    v-else-if="socio.tipo === 'pendiente'"
                    class="px-1.5 py-0.5 ml-1 text-xs text-yellow-800 bg-yellow-200 rounded-full sm:ml-2 sm:px-2"
                  >
                    Pendiente
                  </span>

                  <!-- Mostrar "Por Autorizar" si es de tipo porAutorizar -->
                  <span
                    v-else-if="socio.tipo === 'porAutorizar'"
                    class="px-1.5 py-0.5 ml-1 text-xs text-white bg-green-600 rounded-full sm:ml-2 sm:px-2"
                  >
                    Por Autorizar
                  </span>

                  <!-- Etiquetas personalizadas del socio -->
                  <template v-if="socio.etiquetas && socio.etiquetas.length">
                    <span
                      v-for="etiqueta in socio.etiquetas"
                      :key="etiqueta.id"
                      class="inline-flex relative items-center px-2 py-0.5 ml-1 text-xs rounded-md cursor-pointer sm:ml-2 group"
                      :style="{
                        backgroundColor: etiqueta.backgroundColor,
                        color: etiqueta.color,
                        border: `1px solid ${etiqueta.color}20`,
                      }"
                      @click="removerEtiqueta(socio, etiqueta.id)"
                      :title="`Clic para remover '${etiqueta.nombre}'`"
                    >
                      {{ etiqueta.nombre }}
                      <ion-icon
                        name="close-outline"
                        class="ml-1 text-xs opacity-100 transition-opacity lg:opacity-50 lg:group-hover:opacity-100"
                      ></ion-icon>
                    </span>
                  </template>

                  <!-- Botón +Tag para agregar etiquetas -->
                  <div class="inline-block relative ml-1 sm:ml-2">
                    <button
                      @click="toggleMenuEtiquetas(socio.id)"
                      class="px-1.5 py-0.5 text-xs text-gray-400 rounded-md border border-gray-400 border-dashed transition-colors sm:px-2 hover:border-gray-400 hover:text-gray-800 boton-etiquetas"
                      :class="{
                        'border-mulbin-500 text-mulbin-600':
                          menuEtiquetasAbierto === socio.id,
                      }"
                    >
                      +Tag
                    </button>

                    <!-- Menú desplegable de etiquetas -->
                    <div
                      v-if="menuEtiquetasAbierto === socio.id"
                      class="absolute left-0 z-20 mt-1 w-64 bg-white rounded-lg border border-gray-200 shadow-lg menu-etiquetas"
                    >
                      <!-- Buscador y creador de etiquetas -->
                      <div class="p-3 border-b border-gray-100">
                        <div class="flex items-center space-x-2">
                          <div class="relative flex-1">
                            <input
                              type="text"
                              v-model="filtroEtiquetas"
                              placeholder="Buscar o crear etiqueta..."
                              maxlength="25"
                              class="px-3 py-1.5 w-full text-sm rounded-md border border-gray-300 focus:ring-2 focus:ring-mulbin-500 focus:border-mulbin-500"
                              @click.stop
                            />
                          </div>
                          <button
                            v-if="filtroEtiquetas.trim()"
                            @click="crearNuevaEtiqueta(socio)"
                            :disabled="creandoEtiqueta"
                            class="flex justify-center items-center w-8 h-8 text-white rounded-md bg-mulbin-600 hover:bg-mulbin-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Crear nueva etiqueta"
                          >
                            <ion-icon
                              v-if="!creandoEtiqueta"
                              name="add-outline"
                              class="text-sm"
                            ></ion-icon>
                            <ion-icon
                              v-else
                              name="reload-outline"
                              class="text-sm animate-spin"
                            ></ion-icon>
                          </button>
                        </div>
                      </div>

                      <!-- Lista de etiquetas -->
                      <div class="overflow-y-auto max-h-48">
                        <div
                          v-if="etiquetasFiltradas.length === 0"
                          class="p-4 text-center text-gray-500"
                        >
                          <ion-icon
                            name="pricetag-outline"
                            class="mb-2 text-2xl"
                          ></ion-icon>
                          <p class="text-sm">
                            {{
                              filtroEtiquetas.trim()
                                ? "No se encontraron etiquetas"
                                : "No hay etiquetas disponibles"
                            }}
                          </p>
                          <p
                            v-if="filtroEtiquetas.trim()"
                            class="mt-1 text-xs text-gray-400"
                          >
                            Presiona + para crear "{{ filtroEtiquetas }}"
                          </p>
                        </div>

                        <button
                          v-for="etiqueta in etiquetasFiltradas"
                          :key="etiqueta.id"
                          @click="asignarEtiqueta(socio, etiqueta)"
                          class="flex items-center px-3 py-2 w-full text-sm transition-colors hover:bg-gray-50"
                          :disabled="
                            socio.etiquetas?.some((e) => e.id === etiqueta.id)
                          "
                        >
                          <span
                            class="inline-block mr-2 w-4 h-4 rounded"
                            :style="{
                              backgroundColor: etiqueta.backgroundColor,
                              border: `1px solid ${etiqueta.color}`,
                            }"
                          ></span>
                          <span
                            class="flex-1 text-left"
                            :class="{
                              'text-gray-400': socio.etiquetas?.some(
                                (e) => e.id === etiqueta.id
                              ),
                            }"
                          >
                            {{ etiqueta.nombre }}
                          </span>
                          <div class="flex items-center space-x-1">
                            <ion-icon
                              v-if="
                                socio.etiquetas?.some(
                                  (e) => e.id === etiqueta.id
                                )
                              "
                              name="checkmark-outline"
                              class="text-green-500"
                            ></ion-icon>
                            <button
                              @click.stop="
                                mostrarConfirmacionEliminarEtiqueta(etiqueta)
                              "
                              class="p-1 text-red-500 rounded hover:bg-red-50"
                              title="Eliminar etiqueta completamente"
                            >
                              <ion-icon
                                name="trash-outline"
                                class="text-xs"
                              ></ion-icon>
                            </button>
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>
                </h3>
                <div
                  class="flex flex-col mt-1 space-y-1 text-xs text-gray-500 sm:flex-row sm:items-center sm:text-sm sm:space-y-0"
                >
                  <span class="flex items-center sm:mr-3">
                    <ion-icon
                      name="briefcase-outline"
                      class="mr-1 text-sm"
                    ></ion-icon>
                    <span class="truncate">{{ socio.empresa }}</span>
                  </span>
                  <span class="flex items-center">
                    <ion-icon
                      name="location-outline"
                      class="mr-1 text-sm"
                    ></ion-icon>
                    <span class="truncate">{{ socio.ubicacion }}</span>
                  </span>
                </div>
              </div>
            </div>
            <div
              class="flex flex-shrink-0 justify-end items-center space-x-1 sm:justify-start sm:space-x-2"
            >
              <button
                class="p-1.5 text-green-500 rounded-full sm:p-2 hover:bg-yellow-50"
                @click="abrirWhatsApp(socio)"
              >
                <ion-icon
                  name="logo-whatsapp"
                  class="text-lg sm:text-xl"
                ></ion-icon>
              </button>
              <button
                class="p-1.5 text-blue-500 rounded-full sm:p-2 hover:bg-blue-50"
                @click="abrirTelefono(socio)"
              >
                <ion-icon
                  name="call-outline"
                  class="text-lg sm:text-xl"
                ></ion-icon>
              </button>

              <!-- Botón y menú desplegable para socios directos -->
              <div v-if="socio.tipo === 'directo'" class="relative">
                <button
                  @click="toggleMenu(socio.id)"
                  class="flex items-center px-2 py-1 space-x-1 text-xs font-medium text-white rounded-full sm:px-3 boton-menu-socio bg-mulbin-600 hover:bg-mulbin-700"
                >
                  <span class="hidden sm:inline"
                    >Socio desde {{ formatFecha(socio.desde) }}</span
                  >
                  <span class="sm:hidden">Socio</span>
                  <ion-icon
                    name="chevron-down-outline"
                    :class="{ 'rotate-180': menuAbierto === socio.id }"
                    class="visible text-sm text-white transition-transform duration-200"
                  ></ion-icon>
                </button>

                <!-- Menú desplegable -->
                <div
                  v-if="menuAbierto === socio.id"
                  class="absolute right-0 z-10 mt-1 w-48 bg-white rounded-lg border border-gray-200 shadow-lg menu-desplegable"
                >
                  <div class="py-1">
                    <!-- Ver su catálogo -->
                    <button
                      @click="verCatalogo(socio)"
                      class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <ion-icon name="home-outline" class="mr-2"></ion-icon>
                      Ver su catálogo
                    </button>
                    <!-- Ver publicaciones -->
                    <button
                      @click="verPublicaciones(socio)"
                      class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <ion-icon
                        name="document-text-outline"
                        class="mr-2"
                      ></ion-icon>
                      Ver publicaciones
                    </button>
                    <hr class="my-1 border border-indigo-300" />
                    <!-- Teléfono -->
                    <button
                      @click="abrirTelefono(socio)"
                      class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <ion-icon
                        name="call-outline"
                        class="mr-2 text-blue-600"
                      ></ion-icon>
                      Teléfono
                    </button>
                    <!-- WhatsApp -->
                    <button
                      @click="abrirWhatsApp(socio)"
                      class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <ion-icon
                        name="logo-whatsapp"
                        class="mr-2 text-green-600"
                      ></ion-icon>
                      WhatsApp
                    </button>
                    <!-- Telegram -->
                    <button
                      @click="abrirTelegram(socio)"
                      class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <ion-icon
                        name="paper-plane-outline"
                        class="mr-2 text-blue-500"
                      ></ion-icon>
                      Telegram
                    </button>
                    <!-- Email -->
                    <button
                      @click="abrirEmail(socio)"
                      class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <ion-icon
                        name="mail-outline"
                        class="mr-2 text-gray-600"
                      ></ion-icon>
                      Email
                    </button>
                    <hr class="my-1 border border-indigo-300" />
                    <!-- Eliminar como socio -->
                    <button
                      @click="mostrarConfirmacion(socio, 'eliminar')"
                      class="flex items-center px-4 py-2 w-full text-sm text-red-600 hover:bg-red-50"
                    >
                      <ion-icon name="trash-outline" class="mr-2"></ion-icon>
                      Eliminar como socio
                    </button>
                  </div>
                </div>
              </div>

              <button
                v-if="socio.tipo === 'indirecto'"
                @click="convertirADirecto(socio)"
                class="px-2 py-1 text-xs font-medium text-white rounded-full sm:px-3 bg-mulbin-600 hover:bg-blue-700"
              >
                <span class="hidden sm:inline">Hacer socio directo</span>
                <span class="sm:hidden">Directo</span>
              </button>
              <button
                v-if="socio.tipo === 'pendiente'"
                @click="mostrarConfirmacion(socio, 'cancelar')"
                class="px-2 py-1 text-xs font-medium text-white bg-red-600 rounded-full sm:px-3 hover:bg-red-700"
              >
                <span class="hidden sm:inline">Cancelar solicitud</span>
                <span class="sm:hidden">Cancelar</span>
              </button>
              <!-- Botones para aceptar o rechazar solicitud -->
              <div v-if="socio.tipo === 'porAutorizar'" class="flex space-x-1">
                <button
                  @click="aceptarSolicitud(socio)"
                  class="px-2 py-1 text-xs font-medium text-white bg-green-600 rounded-full sm:px-3 hover:bg-green-700"
                  title="Aceptar solicitud"
                >
                  <ion-icon name="checkmark-outline" class="sm:mr-1"></ion-icon>
                  <span class="hidden sm:inline">Aceptar</span>
                </button>
                <button
                  @click="mostrarConfirmacion(socio, 'rechazar')"
                  class="px-2 py-1 text-xs font-medium text-white bg-red-600 rounded-full sm:px-3 hover:bg-red-700"
                  title="Rechazar solicitud"
                >
                  <ion-icon name="close-outline" class="sm:mr-1"></ion-icon>
                  <span class="hidden sm:inline">Rechazar</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Estado vacío -->
      <div v-if="!filteredSocios.length" class="p-8 text-center text-gray-500">
        <ion-icon name="people-outline" class="mb-2 text-5xl"></ion-icon>
        <p>No tienes socios aún</p>
        <p class="mt-1 text-sm">Agrega algunos para comenzar a colaborar</p>
      </div>
    </div>

    <!-- Modal de confirmación -->
    <ConfirmacionSocioModal
      v-model="showConfirmModal"
      :socio="socioSeleccionado"
      :accion="accionSeleccionada"
      @confirmar="confirmarAccion"
    />

    <!-- Modal de confirmación para eliminar etiqueta -->
    <div
      v-if="showConfirmDeleteTagModal"
      class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
      @click="showConfirmDeleteTagModal = false"
    >
      <div
        class="p-6 mx-4 w-full max-w-md bg-white rounded-lg shadow-xl"
        @click.stop
      >
        <div class="flex items-center mb-4">
          <div
            class="flex justify-center items-center mr-4 w-12 h-12 bg-red-100 rounded-full"
          >
            <ion-icon
              name="warning-outline"
              class="text-2xl text-red-600"
            ></ion-icon>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">
              Eliminar Etiqueta
            </h3>
            <p class="text-sm text-gray-600">
              Esta acción no se puede deshacer
            </p>
          </div>
        </div>

        <div class="mb-6">
          <p class="text-gray-700">
            ¿Estás seguro de que quieres eliminar la etiqueta
            <span class="font-semibold">"{{ etiquetaAEliminar?.nombre }}"</span
            >?
          </p>
          <div
            class="p-3 mt-3 bg-yellow-50 rounded-md border border-yellow-200"
          >
            <div class="flex items-start">
              <ion-icon
                name="alert-circle-outline"
                class="mt-0.5 mr-2 text-yellow-600"
              ></ion-icon>
              <p class="text-sm text-yellow-800">
                <strong>Advertencia:</strong> Esta etiqueta se eliminará de
                todos los socios que la tengan asignada.
              </p>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <button
            @click="showConfirmDeleteTagModal = false"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            :disabled="eliminandoEtiqueta"
          >
            Cancelar
          </button>
          <button
            @click="eliminarEtiquetaCompleta"
            :disabled="eliminandoEtiqueta"
            class="flex items-center px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ion-icon
              v-if="eliminandoEtiqueta"
              name="reload-outline"
              class="mr-2 animate-spin"
            ></ion-icon>
            <ion-icon v-else name="trash-outline" class="mr-2"></ion-icon>
            {{ eliminandoEtiqueta ? "Eliminando..." : "Eliminar" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted } from "vue";
import AgregarSocioModal from "./AgregarSocioModal.vue";
import ConfirmacionSocioModal from "./ConfirmacionSocioModal.vue";
import axios from "axios";

interface Etiqueta {
  id: string;
  nombre: string;
  color: string;
  backgroundColor: string;
  descripcion?: string;
}

interface Socio {
  id: string;
  contrato: string;
  nombre: string;
  empresa: string;
  ubicacion: string;
  avatar: string;
  email?: string;
  telefono?: string;
  wa?: string;
  telegram?: string;
  tipo: "directo" | "indirecto" | "pendiente" | "porAutorizar";
  desde?: string; // Fecha de inicio de la relación
  bolsas?: Record<string, { nombre: string; descripcion: string }>;
  etiquetas?: Etiqueta[]; // Nueva propiedad para las etiquetas
}

export default defineComponent({
  name: "MisSocios",

  components: {
    AgregarSocioModal,
    ConfirmacionSocioModal,
  },

  setup() {
    const mulbinUrl = import.meta.env.VITE_MULBIN_URL;

    // Estado local para el modal (ya no depende de props)
    const showAddFriendModal = ref(false);

    const searchQuery = ref("");
    const socios = ref<Socio[]>([]);
    const loading = ref(false);
    const error = ref<string | null>(null);

    // Para el modal de confirmación
    const showConfirmModal = ref(false);
    const socioSeleccionado = ref<Socio | null>(null);
    const accionSeleccionada = ref<
      "aceptar" | "rechazar" | "cancelar" | "eliminar"
    >("aceptar");

    // Para el menú desplegable de socios directos
    const menuAbierto = ref<string | null>(null);

    // Estados para manejo de etiquetas
    const etiquetasDisponibles = ref<Etiqueta[]>([]);
    const menuEtiquetasAbierto = ref<string | null>(null);
    const filtroEtiquetas = ref("");
    const creandoEtiqueta = ref(false);

    // Estados para eliminar etiqueta completa
    const showConfirmDeleteTagModal = ref(false);
    const etiquetaAEliminar = ref<Etiqueta | null>(null);
    const eliminandoEtiqueta = ref(false);

    // Función para cerrar el menú al hacer clic fuera
    const cerrarMenuAlClickFuera = (event: Event) => {
      const target = event.target as HTMLElement;
      // Verificar si el clic fue fuera de cualquier menú desplegable
      const menuElement = target.closest(".menu-desplegable");
      const botonElement = target.closest(".boton-menu-socio");
      const menuEtiquetasElement = target.closest(".menu-etiquetas");
      const botonEtiquetasElement = target.closest(".boton-etiquetas");

      // Si no se hizo clic en el menú ni en el botón, cerrar el menú
      if (!menuElement && !botonElement) {
        menuAbierto.value = null;
      }

      // Si no se hizo clic en el menú de etiquetas ni en el botón, cerrar el menú de etiquetas
      if (!menuEtiquetasElement && !botonEtiquetasElement) {
        menuEtiquetasAbierto.value = null;
        filtroEtiquetas.value = "";
      }
    };

    // Función para obtener los socios desde la API
    const fetchSocios = async () => {
      loading.value = true;
      error.value = null;

      try {
        const response = await axios.get("/msi-v5/owner/socios");

        if (response.data && response.data.statusCode === 200) {
          // Procesar tags del backend
          const tagsBackend = response.data.data.tags || [];
          // Mapear a estructura de Etiqueta
          etiquetasDisponibles.value = tagsBackend.map((tag: any) => {
            // Extraer color y backgroundColor del style si existe
            let color = "#7C3AED";
            let backgroundColor = "#EDE9FE";
            if (tag.style) {
              // Extraer background-color y border-color
              const bgMatch = tag.style.match(/background-color:\s*([^;]+);?/);
              const borderMatch = tag.style.match(
                /border:\s*1px solid ([^;]+);?/
              );
              if (bgMatch) backgroundColor = bgMatch[1].trim();
              if (borderMatch) color = borderMatch[1].trim();
            }
            return {
              id: tag.id.toString(),
              nombre: tag.tag,
              color,
              backgroundColor,
              descripcion: tag.description || undefined,
            };
          });

          // Procesar socios y asignar objetos de etiqueta
          socios.value = (response.data.data.socios || []).map((socio: any) => {
            const etiquetas = (socio.tags || [])
              .map((tagId: any) =>
                etiquetasDisponibles.value.find((e) => e.id == tagId.toString())
              )
              .filter(Boolean);
            return {
              ...socio,
              etiquetas,
            };
          });
        } else {
          error.value = "Error al cargar los datos de socios";
        }
      } catch (err: any) {
        console.error("Error fetching socios:", err);

        // Si es un 403, redirigir a la raíz porque se perdió la sesión
        if (err.response?.status === 403) {
          window.location.href = "/";
          return;
        }

        error.value =
          "No se pudieron cargar los socios. Por favor intenta nuevamente.";
      } finally {
        loading.value = false;
      }
    };

    // Cargar socios cuando el componente se monta
    onMounted(() => {
      fetchSocios();
      // Agregar event listener para cerrar menú al hacer clic fuera
      document.addEventListener("click", cerrarMenuAlClickFuera);
    });

    // Limpiar event listener al desmontar el componente
    onUnmounted(() => {
      document.removeEventListener("click", cerrarMenuAlClickFuera);
    });

    // Función para normalizar texto (eliminar acentos)
    const normalizeText = (text: string): string => {
      return text
        .normalize("NFD") // Normalizar con descomposición canónica
        .replace(/[\u0300-\u036f]/g, "") // Eliminar diacríticos
        .toLowerCase(); // Convertir a minúsculas
    };

    // Socios filtrados por búsqueda
    const filteredSocios = computed(() => {
      // Normalizar el texto de búsqueda
      const normalizedQuery = normalizeText(searchQuery.value);

      return socios.value.filter((socio) => {
        // Normalizar los textos del socio
        const normalizedNombre = normalizeText(socio.nombre);
        const normalizedEmpresa = normalizeText(socio.empresa);
        const normalizedUbicacion = normalizeText(socio.ubicacion);

        // Buscar en las etiquetas del socio
        let matchesEtiquetas = false;
        if (socio.etiquetas && socio.etiquetas.length > 0) {
          matchesEtiquetas = socio.etiquetas.some((etiqueta) =>
            normalizeText(etiqueta.nombre).includes(normalizedQuery)
          );
        }

        // Realizar la búsqueda en los textos normalizados incluyendo etiquetas
        return (
          normalizedNombre.includes(normalizedQuery) ||
          normalizedEmpresa.includes(normalizedQuery) ||
          normalizedUbicacion.includes(normalizedQuery) ||
          matchesEtiquetas
        );
      });
    });

    // Método para agregar un nuevo socio
    const agregarSocio = async (nuevoSocio: Socio) => {
      try {
        // Aquí podríamos hacer una llamada a la API para agregar el socio
        // Por ahora solo lo agregamos al array local
        // socios.value.push(nuevoSocio);
        // Agregamos al inicio del array
        socios.value.unshift(nuevoSocio);
      } catch (err) {
        console.error("Error al agregar socio:", err);
        error.value =
          "No se pudo agregar el socio. Por favor intenta nuevamente.";
      }
    };

    // Método para convertir un socio indirecto en directo
    const convertirADirecto = async (socio: Socio) => {
      try {
        // Aquí podríamos hacer una llamada a la API para actualizar el socio
        // Por ahora solo lo actualizamos localmente
        socio.tipo = "directo";
      } catch (err) {
        console.error("Error al convertir socio:", err);
        error.value =
          "No se pudo convertir el socio. Por favor intenta nuevamente.";
      }
    };

    // Método para aceptar solicitud de socio
    const aceptarSolicitud = async (socio: Socio) => {
      try {
        const call = `/msi-v5/owner/socios/${socio.contrato}/autorizar`;
        const response = await axios.post(
          call,
          {},
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.data && response.data.statusCode === 200) {
          // Cambiar el tipo del socio a directo en el array local
          socio.tipo = "directo";
        } else {
          error.value = "Error al aceptar la solicitud";
        }
      } catch (err: any) {
        console.error("Error al aceptar solicitud:", err);

        // Si es un 403, redirigir a la raíz porque se perdió la sesión
        if (err.response?.status === 403) {
          window.location.href = "/";
          return;
        }

        error.value =
          "No se pudo aceptar la solicitud. Por favor intenta nuevamente.";
      }
    };

    // Método para rechazar solicitud de socio
    const rechazarSolicitud = async (socio: Socio) => {
      try {
        const call = `/msi-v5/owner/socios/${socio.contrato}/rechazar`;
        const response = await axios.post(
          call,
          {},
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.data && response.data.statusCode === 200) {
          // Eliminar el socio del array local
          const index = socios.value.findIndex((s) => s.id === socio.id);
          if (index !== -1) {
            socios.value.splice(index, 1);
          }
        } else {
          error.value = "Error al rechazar la solicitud";
        }
      } catch (err: any) {
        console.error("Error al rechazar solicitud:", err);

        // Si es un 403, redirigir a la raíz porque se perdió la sesión
        if (err.response?.status === 403) {
          window.location.href = "/";
          return;
        }

        error.value =
          "No se pudo rechazar la solicitud. Por favor intenta nuevamente.";
      }
    };

    // Método para cancelar solicitud de socio pendiente
    const cancelarSolicitud = async (socio: Socio) => {
      try {
        const call = `/msi-v5/owner/socios/${socio.contrato}/cancelar`;
        const response = await axios.post(
          call,
          {},
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.data && response.data.statusCode === 200) {
          // Eliminar el socio del array local
          const index = socios.value.findIndex((s) => s.id === socio.id);
          if (index !== -1) {
            socios.value.splice(index, 1);
          }
        } else {
          error.value = "Error al cancelar la solicitud";
        }
      } catch (err: any) {
        console.error("Error al cancelar solicitud:", err);

        // Si es un 403, redirigir a la raíz porque se perdió la sesión
        if (err.response?.status === 403) {
          window.location.href = "/";
          return;
        }

        error.value =
          "No se pudo cancelar la solicitud. Por favor intenta nuevamente.";
      }
    };

    // Formatear fecha con formato de fecha corta, incluye el día
    const formatFecha = (fecha: string | undefined) => {
      if (!fecha) return "N/A";
      const d = new Date(fecha);
      return d.toLocaleDateString("es-MX", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    };

    // Método para alternar el menú desplegable
    const toggleMenu = (socioId: string) => {
      menuAbierto.value = menuAbierto.value === socioId ? null : socioId;
    };

    // Método para ver el catálogo de un socio
    const verCatalogo = (socio: Socio) => {
      menuAbierto.value = null;
      // Aquí implementarías la lógica para ver el catálogo
      console.log("Ver catálogo de:", socio.nombre);
      // Ejemplo: window.open(`/catalogo/${socio.id}`, '_blank');
    };

    // Método para ver las publicaciones de un socio
    const verPublicaciones = (socio: Socio) => {
      menuAbierto.value = null;
      // Aquí implementarías la lógica para ver las publicaciones
      console.log("Ver publicaciones de:", socio.nombre);
      // Ejemplo: window.open(`/publicaciones/${socio.id}`, '_blank');
    };

    // Método para abrir teléfono
    const abrirTelefono = (socio: Socio) => {
      menuAbierto.value = null;
      if (socio.telefono) {
        const url = `tel:${socio.telefono}`;
        window.open(url, "_self");
      } else {
        error.value = "Este socio no tiene número de teléfono registrado";
      }
    };

    // Método para abrir WhatsApp
    const abrirWhatsApp = (socio: Socio) => {
      menuAbierto.value = null;
      if (socio.wa) {
        const mensaje = encodeURIComponent(
          `Hola ${socio.nombre}, espero te encuentres bien.`
        );
        const url = `https://wa.me/${socio.wa.replace(
          /[^0-9]/g,
          ""
        )}?text=${mensaje}`;
        window.open(url, "_blank");
      } else {
        error.value = "Este socio no tiene número de WhatsApp registrado";
      }
    };

    // Método para abrir Telegram
    const abrirTelegram = (socio: Socio) => {
      menuAbierto.value = null;
      if (socio.telegram) {
        const url = `https://t.me/${socio.telegram.replace(/[^0-9]/g, "")}`;
        window.open(url, "_blank");
      } else {
        error.value = "Este socio no tiene número de Telegram registrado";
      }
    };

    // Método para abrir email
    const abrirEmail = (socio: Socio) => {
      menuAbierto.value = null;
      if (socio.email) {
        const asunto = encodeURIComponent(`Contacto desde Mulbin`);
        const cuerpo = encodeURIComponent(
          `Hola ${socio.nombre},\n\nEspero te encuentres bien.\n\nSaludos.`
        );
        const url = `mailto:${socio.email}?subject=${asunto}&body=${cuerpo}`;
        window.open(url, "_self");
      } else {
        error.value = "Este socio no tiene email registrado";
      }
    };

    // Método para eliminar socio directo
    const eliminarSocio = async (socio: Socio) => {
      try {
        const call = `/msi-v5/owner/socios/${socio.contrato}/eliminar`;
        const response = await axios.post(
          call,
          {},
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.data && response.data.statusCode === 200) {
          // Eliminar el socio del array local
          const index = socios.value.findIndex((s) => s.id === socio.id);
          if (index !== -1) {
            socios.value.splice(index, 1);
          }
        } else {
          error.value = "Error al eliminar el socio";
        }
      } catch (err: any) {
        console.error("Error al eliminar socio:", err);

        // Si es un 403, redirigir a la raíz porque se perdió la sesión
        if (err.response?.status === 403) {
          window.location.href = "/";
          return;
        }

        error.value =
          "No se pudo eliminar el socio. Por favor intenta nuevamente.";
      }
    };

    // Función para mostrar el modal de confirmación
    const mostrarConfirmacion = (
      socio: Socio,
      accion: "aceptar" | "rechazar" | "cancelar" | "eliminar"
    ) => {
      socioSeleccionado.value = socio;
      accionSeleccionada.value = accion;
      showConfirmModal.value = true;
    };

    // Función para confirmar la acción seleccionada
    const confirmarAccion = async () => {
      if (!socioSeleccionado.value) return;

      if (accionSeleccionada.value === "aceptar") {
        await aceptarSolicitud(socioSeleccionado.value);
      } else if (accionSeleccionada.value === "rechazar") {
        await rechazarSolicitud(socioSeleccionado.value);
      } else if (accionSeleccionada.value === "cancelar") {
        await cancelarSolicitud(socioSeleccionado.value);
      } else if (accionSeleccionada.value === "eliminar") {
        await eliminarSocio(socioSeleccionado.value);
      }

      // Cerrar el modal
      showConfirmModal.value = false;
      socioSeleccionado.value = null;
    };

    // Funciones para manejo de etiquetas
    const etiquetasFiltradas = computed(() => {
      if (!filtroEtiquetas.value.trim()) {
        return etiquetasDisponibles.value;
      }

      const filtro = normalizeText(filtroEtiquetas.value);
      return etiquetasDisponibles.value.filter((etiqueta) =>
        normalizeText(etiqueta.nombre).includes(filtro)
      );
    });

    const toggleMenuEtiquetas = (socioId: string) => {
      menuEtiquetasAbierto.value =
        menuEtiquetasAbierto.value === socioId ? null : socioId;
      if (menuEtiquetasAbierto.value === null) {
        filtroEtiquetas.value = "";
      }
    };

    const asignarEtiqueta = async (socio: Socio, etiqueta: Etiqueta) => {
      try {
        if (!socio.etiquetas) {
          socio.etiquetas = [];
        }

        // Verificar si ya tiene la etiqueta
        const yaLaTiene = socio.etiquetas.some((e) => e.id === etiqueta.id);
        if (yaLaTiene) {
          return;
        }

        // 1. OPTIMISTIC UPDATE: Agregar inmediatamente a la UI
        socio.etiquetas.push(etiqueta);
        menuEtiquetasAbierto.value = null;
        filtroEtiquetas.value = "";

        // 2. Llamada al backend para asignar etiqueta existente
        const response = await axios.post("/msi-v5/owner/tags-socios", {
          socio_id: socio.contrato,
          tag_id: etiqueta.id, // Para etiquetas existentes enviamos el ID
        });

        // 3. Verificar respuesta del backend
        if (response.status === 200 || response.status === 201) {
          if (
            (response.data?.statusCode === 200 ||
              response.data?.statusCode === 201) &&
            response.data?.data
          ) {
            const { relation: relationResult } = response.data.data;

            // Verificar que la relación se creó o ya existía
            if (
              !relationResult ||
              (relationResult.result !== "created" &&
                relationResult.result !== "exists")
            ) {
              throw new Error(
                "No se pudo confirmar la asignación de la etiqueta"
              );
            }

            // Log para debug
            console.log("Relation result:", relationResult.result);
          } else {
            throw new Error(
              `El servidor no confirmó la asignación. Respuesta: ${JSON.stringify(
                response.data
              )}`
            );
          }
        } else {
          throw new Error(`Error del servidor: ${response.status}`);
        }
      } catch (err: any) {
        console.error("Error al asignar etiqueta:", err);

        // 4. ROLLBACK: Revertir cambios optimistas
        if (socio.etiquetas) {
          socio.etiquetas = socio.etiquetas.filter((e) => e.id !== etiqueta.id);
        }

        // Mostrar error al usuario
        const mensajeError =
          err.response?.data?.message || err.message || "Error desconocido";
        alert(
          `No se pudo asignar la etiqueta "${etiqueta.nombre}" al socio.\n\nError: ${mensajeError}\n\nPor favor intenta nuevamente.`
        );

        error.value =
          "No se pudo asignar la etiqueta. Por favor intenta nuevamente.";
      }
    };

    const removerEtiqueta = async (socio: Socio, etiquetaId: string) => {
      if (!socio.etiquetas) return;

      // Encontrar la etiqueta que se va a remover para el rollback
      const etiquetaARemover = socio.etiquetas.find((e) => e.id === etiquetaId);
      if (!etiquetaARemover) return;

      try {
        // 1. OPTIMISTIC UPDATE: Remover inmediatamente de la UI
        socio.etiquetas = socio.etiquetas.filter((e) => e.id !== etiquetaId);

        // 2. Llamada al backend para remover la relación
        const response = await axios.delete(
          `/msi-v5/owner/tags-socios/${socio.contrato}/${etiquetaId}`
        );

        // 3. Verificar respuesta del backend
        if (response.status === 200 || response.status === 204) {
          if (response.data?.statusCode === 200 && response.data?.data) {
            const { result, tag_relation_count } = response.data.data;

            // Verificar que la relación se eliminó
            if (result !== "deleted") {
              throw new Error(
                "No se pudo confirmar la eliminación de la etiqueta"
              );
            }

            // Si no quedan más relaciones de esta etiqueta, eliminarla de etiquetasDisponibles
            if (tag_relation_count === 0) {
              etiquetasDisponibles.value = etiquetasDisponibles.value.filter(
                (e) => e.id !== etiquetaId
              );
              console.log(
                `Etiqueta "${etiquetaARemover.nombre}" eliminada de disponibles (sin más asignaciones)`
              );
            }

            // Log para debug
            console.log("Remove result:", result);
            console.log("Tag relation count:", tag_relation_count);
          } else if (response.status === 204) {
            // 204 No Content - eliminación exitosa sin body
            console.log("Etiqueta removida exitosamente (204)");
          } else {
            throw new Error(
              `El servidor no confirmó la eliminación. Respuesta: ${JSON.stringify(
                response.data
              )}`
            );
          }
        } else {
          throw new Error(`Error del servidor: ${response.status}`);
        }
      } catch (err: any) {
        console.error("Error al remover etiqueta:", err);

        // 4. ROLLBACK: Restaurar la etiqueta en caso de error
        if (etiquetaARemover) {
          if (!socio.etiquetas) {
            socio.etiquetas = [];
          }
          socio.etiquetas.push(etiquetaARemover);

          // También restaurar en etiquetasDisponibles si no existe
          const existeEnDisponibles = etiquetasDisponibles.value.some(
            (e) => e.id === etiquetaId
          );
          if (!existeEnDisponibles) {
            etiquetasDisponibles.value.push(etiquetaARemover);
            console.log(
              `Etiqueta "${etiquetaARemover.nombre}" restaurada en disponibles (rollback)`
            );
          }
        }

        // Mostrar error al usuario
        const mensajeError =
          err.response?.data?.message || err.message || "Error desconocido";
        alert(
          `No se pudo remover la etiqueta "${etiquetaARemover?.nombre}" del socio.\n\nError: ${mensajeError}\n\nPor favor intenta nuevamente.`
        );

        error.value =
          "No se pudo remover la etiqueta. Por favor intenta nuevamente.";
      }
    };

    const crearNuevaEtiqueta = async (socio: Socio) => {
      if (!filtroEtiquetas.value.trim() || creandoEtiqueta.value) return;

      const nombreEtiqueta = filtroEtiquetas.value.trim();

      // Colores disponibles para nuevas etiquetas
      const coloresDisponibles = [
        { color: "#9333EA", backgroundColor: "#F3E8FF" },
        { color: "#EC4899", backgroundColor: "#FCE7F3" },
        { color: "#10B981", backgroundColor: "#ECFDF5" },
        { color: "#F59E0B", backgroundColor: "#FFFBEB" },
        { color: "#6366F1", backgroundColor: "#EEF2FF" },
        { color: "#EF4444", backgroundColor: "#FEF2F2" },
        // { color: "#B45309", backgroundColor: "#FEF3C7" }, // Dorado oscuro sobre fondo dorado claro
        // { color: "#92400E", backgroundColor: "#FFFBEB" }, // Ámbar oscuro sobre crema
        // { color: "#D97706", backgroundColor: "#FFF7ED" }, // Dorado medio sobre fondo muy claro
        // { color: "#78350F", backgroundColor: "#FEF3C7" }, // Dorado muy oscuro sobre dorado claro
        // { color: "#A16207", backgroundColor: "#FEFCE8" }, // Dorado oscuro sobre amarillo muy claro
      ];

      const colorAleatorio =
        coloresDisponibles[
          Math.floor(Math.random() * coloresDisponibles.length)
        ];

      // Crear etiqueta temporal con ID temporal
      const etiquetaTemporal: Etiqueta = {
        id: `temp-${Date.now()}`,
        nombre: nombreEtiqueta,
        color: colorAleatorio.color,
        backgroundColor: colorAleatorio.backgroundColor,
      };

      try {
        creandoEtiqueta.value = true;

        // 1. OPTIMISTIC UPDATE: Agregar inmediatamente a la UI
        etiquetasDisponibles.value.push(etiquetaTemporal);

        // Asignar directamente al socio sin llamar a asignarEtiqueta
        if (!socio.etiquetas) {
          socio.etiquetas = [];
        }
        socio.etiquetas.push(etiquetaTemporal);

        filtroEtiquetas.value = "";
        menuEtiquetasAbierto.value = null;

        // 2. Llamada al backend
        const response = await axios.post("/msi-v5/owner/tags-socios", {
          socio_id: socio.contrato,
          tag: nombreEtiqueta,
          style: `background-color: ${colorAleatorio.backgroundColor}; border: 1px solid ${colorAleatorio.color};`,
          description: null,
        });

        // 3. Verificar respuesta del backend, 200 ya existía la tag, 201 se creó la tag
        if (response.status === 200 || response.status === 201) {
          if (
            (response.data?.statusCode === 200 ||
              response.data?.statusCode === 201) &&
            response.data?.data
          ) {
            const { tag: tagResult, relation: relationResult } =
              response.data.data;

            // Verificar que tenemos los datos necesarios
            if (!tagResult?.tag || !relationResult) {
              throw new Error("Respuesta del servidor incompleta");
            }

            const tagFromBackend = tagResult.tag;

            // Crear etiqueta con datos reales del backend
            const etiquetaReal: Etiqueta = {
              id: tagFromBackend.id.toString(),
              nombre: tagFromBackend.tag,
              color: etiquetaTemporal.color, // Mantener color temporal
              backgroundColor: etiquetaTemporal.backgroundColor, // Mantener color temporal
              descripcion: tagFromBackend.description || undefined,
            };

            // Verificar si la etiqueta ya existe en etiquetasDisponibles
            const etiquetaExistente = etiquetasDisponibles.value.find(
              (e) => e.id === etiquetaReal.id
            );

            if (!etiquetaExistente) {
              // Si la etiqueta no existía, reemplazar la temporal
              const indiceTemp = etiquetasDisponibles.value.findIndex(
                (e) => e.id === etiquetaTemporal.id
              );
              if (indiceTemp !== -1) {
                etiquetasDisponibles.value[indiceTemp] = etiquetaReal;
              } else {
                // Si no encontramos la temporal, agregar la real
                etiquetasDisponibles.value.push(etiquetaReal);
              }
            } else {
              // Si la etiqueta ya existía, remover la temporal
              etiquetasDisponibles.value = etiquetasDisponibles.value.filter(
                (e) => e.id !== etiquetaTemporal.id
              );
            }

            // Actualizar en el socio: verificar si ya tiene esta etiqueta
            if (socio.etiquetas) {
              const yaLaTiene = socio.etiquetas.some(
                (e) => e.id === etiquetaReal.id
              );

              if (!yaLaTiene) {
                // Reemplazar temporal con real o agregar si no existe
                const indiceSocio = socio.etiquetas.findIndex(
                  (e) => e.id === etiquetaTemporal.id
                );
                if (indiceSocio !== -1) {
                  socio.etiquetas[indiceSocio] = etiquetaReal;
                } else {
                  socio.etiquetas.push(etiquetaReal);
                }
              } else {
                // Si ya la tenía, solo remover la temporal
                socio.etiquetas = socio.etiquetas.filter(
                  (e) => e.id !== etiquetaTemporal.id
                );
              }
            }

            // Log para debug
            console.log("Tag result:", tagResult.result);
            console.log("Relation result:", relationResult.result);
          } else {
            throw new Error(
              `El servidor no confirmó la operación. Respuesta: ${JSON.stringify(
                response.data
              )}`
            );
          }
        } else {
          throw new Error(`Error del servidor: ${response.status}`);
        }
      } catch (err: any) {
        console.error("Error al crear etiqueta:", err);

        // 4. ROLLBACK: Revertir cambios optimistas
        // Remover etiqueta temporal de la lista disponible
        etiquetasDisponibles.value = etiquetasDisponibles.value.filter(
          (e) => e.id !== etiquetaTemporal.id
        );

        // Remover etiqueta temporal del socio
        if (socio.etiquetas) {
          socio.etiquetas = socio.etiquetas.filter(
            (e) => e.id !== etiquetaTemporal.id
          );
        }

        // Mostrar error al usuario
        const mensajeError =
          err.response?.data?.message || err.message || "Error desconocido";
        alert(
          `No se pudo crear la etiqueta "${nombreEtiqueta}".\n\nError: ${mensajeError}\n\nPor favor intenta nuevamente.`
        );
      } finally {
        creandoEtiqueta.value = false;
      }
    };

    // Funciones para eliminar etiqueta completa
    const mostrarConfirmacionEliminarEtiqueta = (etiqueta: Etiqueta) => {
      etiquetaAEliminar.value = etiqueta;
      showConfirmDeleteTagModal.value = true;
      menuEtiquetasAbierto.value = null; // Cerrar menú de etiquetas
    };

    const eliminarEtiquetaCompleta = async () => {
      if (!etiquetaAEliminar.value || eliminandoEtiqueta.value) return;

      const etiquetaId = etiquetaAEliminar.value.id;
      const nombreEtiqueta = etiquetaAEliminar.value.nombre;

      // Guardar estado actual para rollback
      const sociosConEtiqueta = socios.value.filter((socio) =>
        socio.etiquetas?.some((e) => e.id === etiquetaId)
      );
      const etiquetaEnDisponibles = etiquetasDisponibles.value.find(
        (e) => e.id === etiquetaId
      );

      try {
        eliminandoEtiqueta.value = true;

        // 1. OPTIMISTIC UPDATE: Eliminar inmediatamente de la UI
        // Remover de todos los socios
        socios.value.forEach((socio) => {
          if (socio.etiquetas) {
            socio.etiquetas = socio.etiquetas.filter(
              (e) => e.id !== etiquetaId
            );
          }
        });

        // Remover de etiquetas disponibles
        etiquetasDisponibles.value = etiquetasDisponibles.value.filter(
          (e) => e.id !== etiquetaId
        );

        // 2. Llamada al backend para eliminar etiqueta completa
        const response = await axios.delete(
          `/msi-v5/owner/tags-socios/${etiquetaId}`
        );

        // 3. Verificar respuesta del backend
        if (response.status === 200 || response.status === 204) {
          if (response.data?.statusCode === 200 && response.data?.data) {
            const { result } = response.data.data;

            if (result !== "deleted") {
              throw new Error(
                "No se pudo confirmar la eliminación de la etiqueta"
              );
            }

            console.log(`Etiqueta "${nombreEtiqueta}" eliminada completamente`);
          } else if (response.status === 204) {
            console.log(
              `Etiqueta "${nombreEtiqueta}" eliminada completamente (204)`
            );
          } else {
            throw new Error(
              `El servidor no confirmó la eliminación. Respuesta: ${JSON.stringify(
                response.data
              )}`
            );
          }
        } else {
          throw new Error(`Error del servidor: ${response.status}`);
        }

        // Cerrar modal
        showConfirmDeleteTagModal.value = false;
        etiquetaAEliminar.value = null;
      } catch (err: any) {
        console.error("Error al eliminar etiqueta completa:", err);

        // 4. ROLLBACK: Restaurar estado anterior
        if (etiquetaEnDisponibles) {
          etiquetasDisponibles.value.push(etiquetaEnDisponibles);
        }

        sociosConEtiqueta.forEach((socioConEtiqueta) => {
          const socio = socios.value.find((s) => s.id === socioConEtiqueta.id);
          if (socio && etiquetaAEliminar.value) {
            if (!socio.etiquetas) socio.etiquetas = [];
            const yaLaTiene = socio.etiquetas.some(
              (e) => e.id === etiquetaAEliminar.value!.id
            );
            if (!yaLaTiene) {
              socio.etiquetas.push(etiquetaAEliminar.value);
            }
          }
        });

        // Mostrar error al usuario
        const mensajeError =
          err.response?.data?.message || err.message || "Error desconocido";
        alert(
          `No se pudo eliminar la etiqueta "${etiquetaAEliminar.value?.nombre}".\n\nError: ${mensajeError}\n\nPor favor intenta nuevamente.`
        );
      } finally {
        eliminandoEtiqueta.value = false;
      }
    };

    return {
      showAddFriendModal,
      searchQuery,
      socios,
      loading,
      error,
      mulbinUrl,
      filteredSocios,
      agregarSocio,
      convertirADirecto,
      cancelarSolicitud,
      aceptarSolicitud,
      rechazarSolicitud,
      formatFecha,
      fetchSocios,
      // Para el modal de confirmación
      showConfirmModal,
      socioSeleccionado,
      accionSeleccionada,
      mostrarConfirmacion,
      confirmarAccion,
      // Para el menú desplegable de socios directos
      menuAbierto,
      toggleMenu,
      verCatalogo,
      verPublicaciones,
      abrirTelefono,
      abrirWhatsApp,
      abrirTelegram,
      abrirEmail,
      eliminarSocio,
      cerrarMenuAlClickFuera,
      // Para el manejo de etiquetas
      etiquetasDisponibles,
      menuEtiquetasAbierto,
      filtroEtiquetas,
      creandoEtiqueta,
      etiquetasFiltradas,
      toggleMenuEtiquetas,
      asignarEtiqueta,
      removerEtiqueta,
      crearNuevaEtiqueta,
      // Para eliminar etiqueta completa
      showConfirmDeleteTagModal,
      etiquetaAEliminar,
      eliminandoEtiqueta,
      mostrarConfirmacionEliminarEtiqueta,
      eliminarEtiquetaCompleta,
    };
  },
});
</script>
