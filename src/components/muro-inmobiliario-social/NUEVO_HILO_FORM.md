# Documentación NuevoHiloForm.vue

## 📋 Descripción General

`NuevoHiloForm.vue` es un componente modal para crear hilos de interés (conversaciones) desde diferentes tipos de contenido. Su característica principal es la **detección automática del tipo de contenido** para usar el endpoint correcto de Meteor DDP, soportando tanto **posts normales** como **inmuebles/propiedades**.

## 🎯 Casos de Uso

- **Hilos desde posts normales**: Usando método `hilosInteres.create`
- **Hilos desde inmuebles**: Usando método `hilosInteres.createFromInmueble` (requiere `meteor_id` del socio)
- **Validación de límites**: Manejo del límite máximo de 3 hilos por publicación
- **Gestión de ventanas múltiples**: Prevención de duplicación de chats

## ⚙️ Requisitos Técnicos

### Vue.js Version

- **Vue 3** con **Composition API**
- **TypeScript** support required
- **Reactive refs** y **reactive objects**

### Dependencias Necesarias

```javascript
import inmobiliarioService from "../../services/inmobiliarioService";
import { useModal } from "../ui";
import type { PostInmobiliario } from "../../types/inmobiliario";
```

### Tipos TypeScript Requeridos

```typescript
// En @panel4-templates/src/types/inmobiliario.ts
interface PostInmobiliario {
  _id?: string;
  id?: string;
  title: string;
  authorCache: {
    firstName?: string;
    lastName?: string;
    name?: string;
    avatar?: string;
  };
  // 🏠 CAMPOS ESPECÍFICOS PARA INMUEBLES
  esInmueble?: boolean; // Flag para identificar tipo
  inmuebleData?: {
    inmuebleId: string;
    titulo: string;
    referenciaPrivada: string;
    inmuebleInfo: {
      titulo: string;
      descripcion: string;
      socio: {
        id: string;
        meteor_id?: string; // 🔑 CAMPO CLAVE para Meteor
        nombre: string;
        empresa?: string;
      };
    };
  };
}
```

## 🚀 Implementación Paso a Paso

### 1. Importación del Componente

```vue
<template>
  <!-- Modal para crear hilos de interés -->
  <NuevoHiloForm
    :show="showCrearHiloModal"
    :token="props.token"
    :post="inmuebleSeleccionadoParaHilo"
    @close="handleCloseCrearHilo"
    @hilo-created="handleHiloCreated"
    @abrir-chat="handleAbrirChatFromHilo"
    @error="handleHiloError"
  />
</template>

<script>
import NuevoHiloForm from "../muro-inmobiliario-social/NuevoHiloForm.vue";

export default {
  components: {
    NuevoHiloForm,
  },
  // ...
};
</script>
```

### 2. Props Requeridos

| Prop    | Tipo                       | Requerido | Descripción                      |
| ------- | -------------------------- | --------- | -------------------------------- |
| `show`  | `Boolean`                  | ✅        | Control de visibilidad del modal |
| `token` | `String`                   | ✅        | Token de autenticación DDP       |
| `post`  | `PostInmobiliario \| null` | ✅        | Datos del post/inmueble          |

### 3. Eventos Emitidos

| Evento         | Parámetros                 | Descripción                              |
| -------------- | -------------------------- | ---------------------------------------- |
| `close`        | -                          | Se emite al cerrar el modal              |
| `hilo-created` | `hiloRecienCreado: object` | Se emite cuando se crea exitosamente     |
| `abrir-chat`   | `hiloRecienCreado: object` | Se emite para abrir chat automáticamente |
| `error`        | `error: any`               | Se emite cuando ocurre un error          |

### 4. Variables Reactivas en el Componente Padre

```javascript
// Variables necesarias en el componente padre
const showCrearHiloModal = ref(false);
const inmuebleSeleccionadoParaHilo = ref(null);

// Funciones de manejo de eventos
const handleCloseCrearHilo = () => {
  showCrearHiloModal.value = false;
  inmuebleSeleccionadoParaHilo.value = null;
};

const handleHiloCreado = async (hiloRecienCreado) => {
  console.log("✅ Hilo creado:", hiloRecienCreado);
  // Cerrar modal de crear hilo
  handleCloseCrearHilo();
  // Abrir chat automáticamente
  hiloSeleccionado.value = hiloRecienCreado;
  showChatModal.value = true;
};

const handleHiloError = (error) => {
  console.error("❌ Error en el hilo:", error);
  showError("Ocurrió un error al crear el hilo de interés", "Error");
};
```

## 🔍 Lógica de Detección Dual: Posts vs Inmuebles

### 🎯 **Característica Clave:** Detección Automática de Tipo

El componente **detecta automáticamente** si el contenido es un **post normal** o un **inmueble** basándose en las propiedades del objeto `post`:

```javascript
const handleCrearHilo = async () => {
  // 🏠 DETECCIÓN: ¿Es un inmueble?
  if (props.post.esInmueble && props.post.inmuebleData) {
    // 🏠 FLUJO INMUEBLES: Usar hilosInteres.createFromInmueble
    const inmuebleData = {
      ...props.post.inmuebleData,
      titulo: nuevoHilo.titulo.trim(),
      mensajeInicial: nuevoHilo.mensajeInicial.trim(),
    };

    const result = await inmobiliarioService.call(
      "hilosInteres.createFromInmueble", // 🔑 Endpoint específico
      inmuebleData
    );
  } else {
    // 📝 FLUJO POSTS NORMALES: Usar hilosInteres.create
    const hiloData = {
      postId: normalizePostId(props.post),
      titulo: nuevoHilo.titulo.trim(),
      referenciaPrivada: nuevoHilo.referenciaPrivada.trim(),
      mensajeInicial: nuevoHilo.mensajeInicial.trim(),
    };

    const result = await inmobiliarioService.call(
      "hilosInteres.create", // 🔑 Endpoint genérico
      hiloData
    );
  }
};
```

## 🏠 Preparación de Datos para Inmuebles

### Desde el Componente Padre (ej. InmueblesSocios.vue)

```javascript
const abrirCrearHilo = (inmueble) => {
  // 🏠 Preparar datos específicos para inmuebles
  const inmuebleData = {
    inmuebleId: inmueble.id,
    titulo: `Interés en ${inmueble.titulo}`,
    referenciaPrivada: `Inmueble ${inmueble.key} - ${inmueble.socio.nombre}`,
    inmuebleInfo: {
      titulo: inmueble.titulo,
      descripcion: inmueble.descripcion,
      socio: {
        id: inmueble.socio.id,
        meteor_id: inmueble.socio.meteor_id, // 🔑 CAMPO CLAVE
        nombre: inmueble.socio.nombre,
        empresa: inmueble.socio.empresa,
      },
    },
  };

  // Para compatibilidad con NuevoHiloForm
  const postData = {
    _id: inmueble.id,
    id: inmueble.id,
    title: inmueble.titulo,
    authorCache: {
      firstName: inmueble.socio.nombre.split(" ")[0] || "",
      lastName: inmueble.socio.nombre.split(" ").slice(1).join(" ") || "",
      name: inmueble.socio.nombre,
    },
    // 🔑 CAMPOS CLAVE PARA DETECCIÓN
    esInmueble: true, // Flag de detección
    inmuebleData: inmuebleData, // Datos específicos
  };

  inmuebleSeleccionadoParaHilo.value = postData;
  showCrearHiloModal.value = true;
};
```

## 🔌 Métodos DDP Utilizados

### Conexión y Autenticación

```javascript
await inmobiliarioService.connectWithToken(props.token);
```

### Para Inmuebles

```javascript
const result = await inmobiliarioService.call(
  "hilosInteres.createFromInmueble",
  {
    inmuebleId: "...",
    titulo: "...",
    mensajeInicial: "...",
    referenciaPrivada: "...",
    inmuebleInfo: {
      titulo: "...",
      descripcion: "...",
      socio: {
        id: "...",
        meteor_id: "...", // 🔑 REQUERIDO
        nombre: "...",
        empresa: "...",
      },
    },
  }
);
```

### Para Posts Normales

```javascript
const result = await inmobiliarioService.call("hilosInteres.create", {
  postId: "...",
  titulo: "...",
  mensajeInicial: "...",
  referenciaPrivada: "...",
});
```

## 🛠️ Funciones Helper Integradas

### 1. Normalización de IDs

```javascript
const normalizePostId = (post) => {
  if (post._id && typeof post._id === "string") {
    return post._id;
  }
  if (post.id && typeof post.id === "string") {
    post._id = post.id; // Normalizar para consistencia
    return post.id;
  }
  return null;
};
```

### 2. Limpieza de Títulos

```javascript
const limpiarTituloHilo = (titulo) => {
  return titulo
    .replace(/\(\(/g, "") // Eliminar '(('
    .replace(/\)\)/g, "") // Eliminar '))'
    .replace(/\*/g, "") // Eliminar '*'
    .replace(/~/g, "") // Eliminar '~'
    .replace(/_/g, "") // Eliminar '_'
    .trim();
};
```

### 3. Nombres de Autores

```javascript
const getAuthorFullName = (authorCache) => {
  if (authorCache.firstName || authorCache.lastName) {
    return `${authorCache.firstName || ""} ${
      authorCache.lastName || ""
    }`.trim();
  }
  return authorCache.name || "Usuario";
};
```

### 4. Gestión de Ventanas Múltiples

```javascript
const isChatWindowOpen = (hiloId) => {
  const stored = localStorage.getItem("mulbin_chat_windows_open");
  const currentWindows = stored ? JSON.parse(stored) : [];
  return currentWindows.includes(hiloId);
};
```

## ⚠️ Manejo de Errores Específicos

### Error de Límite Máximo (3 hilos por publicación)

```javascript
const isMaxHilosError =
  err?.error === "max-hilos-reached" ||
  err?.message?.includes?.("máximo de 3 hilos") ||
  err?.reason?.includes?.("máximo de 3 hilos");

if (isMaxHilosError) {
  window.alert(
    "🚫 LÍMITE DE HILOS ALCANZADO\n\n" +
      "Has alcanzado el límite máximo de 3 hilos de interés por publicación.\n\n" +
      "Para crear un nuevo hilo, primero debes eliminar alguno de los hilos existentes.\n\n" +
      "💡 Sugerencias:\n" +
      "• Revisa tus hilos existentes\n" +
      "• Elimina conversaciones inactivas\n" +
      "• Consolida temas similares"
  );
}
```

### Otros Errores

```javascript
const errorMessage = err instanceof Error ? err.message : "Error desconocido";
error(`Error al crear hilo: ${errorMessage}`, "Error");
emit("error", err);
```

## 🎨 Características del UI

### Formulario Responsivo

- **Modal centrado** con animación de entrada suave
- **Campos requeridos**: Título (50 chars) y mensaje inicial (500 chars)
- **Contadores de caracteres** en tiempo real
- **Validación automática** con botón deshabilitado
- **Campo de referencia privada** (oculto por defecto)

### Estados Visuales

```vue
<button
  @click="handleCrearHilo"
  :disabled="
    !nuevoHilo.titulo.trim() || !nuevoHilo.mensajeInicial.trim() || isCreating
  "
  class="px-4 py-2 text-white rounded transition-colors bg-mulbin-600 hover:bg-mulbin-700 disabled:opacity-50"
>
  <span v-if="isCreating" class="flex items-center">
    <ion-icon name="reload-outline" class="mr-1 animate-spin"></ion-icon>
    Creando...
  </span>
  <span v-else>Crear hilo</span>
</button>
```

### Información del Contexto

```vue
<!-- Muestra información del post/inmueble -->
<div v-if="post" class="p-3 mb-4 bg-gray-50 rounded-lg">
  <p class="text-sm font-medium text-gray-800">
    {{ limpiarTituloHilo(post.title) }}
  </p>
  <p class="text-xs text-gray-600">
    Socio: {{ getAuthorFullName(post.authorCache) }}
  </p>
</div>
```

## 🔄 Flujo de Procesamiento del Hilo Creado

### Función Común para Ambos Tipos

```javascript
const procesarHiloCreado = (hiloRecienCreado) => {
  // 1. Resetear formulario
  Object.assign(nuevoHilo, {
    titulo: "",
    referenciaPrivada: "",
    mensajeInicial: "",
  });

  // 2. Emitir evento de creación
  emit("hilo-created", hiloRecienCreado);

  // 3. Verificar ventanas duplicadas
  if (isChatWindowOpen(hiloRecienCreado._id)) {
    warning("Esta conversación ya está abierta en otra ventana");
  } else {
    // 4. Emitir evento para abrir chat
    emit("abrir-chat", hiloRecienCreado);
  }

  // 5. Cerrar modal
  emit("close");
};
```

### Estructura del Objeto de Respuesta

#### Para Inmuebles:

```javascript
const hiloRecienCreado = {
  _id: result.hiloId,
  titulo: nuevoHilo.titulo.trim(),
  referenciaPrivada: inmuebleData.referenciaPrivada,
  postId: result.postId,
  postAuthorCache: props.post.authorCache,
  mensajesCount: 1,
  lastMessageAt: new Date().toISOString(),
  createdAt: new Date().toISOString(),
  postExternalId: result.postExternalId, // ID del inmueble
};
```

#### Para Posts Normales:

```javascript
const hiloRecienCreado = {
  _id: result.hiloId,
  titulo: nuevoHilo.titulo.trim(),
  referenciaPrivada: nuevoHilo.referenciaPrivada.trim(),
  postId: postId,
  postAuthorCache: props.post.authorCache,
  mensajesCount: 1,
  lastMessageAt: new Date().toISOString(),
  createdAt: new Date().toISOString(),
};
```

## 🧹 Limpieza Automática del Formulario

### Watch para Reseteo Automático

```javascript
watch(
  () => props.show,
  (newValue) => {
    if (!newValue) {
      // Resetear formulario cuando se cierra
      Object.assign(nuevoHilo, {
        titulo: "",
        referenciaPrivada: "",
        mensajeInicial: "",
      });
    }
  }
);
```

## 🔧 Problemas Comunes y Soluciones

### 1. Token No Disponible

```
❌ Error: Token is required
```

**Solución:** Verificar que el token se esté pasando correctamente como prop

### 2. Campo meteor_id Faltante

```
❌ Error: meteor_id is required for createFromInmueble
```

**Solución:** Verificar que el objeto inmueble tenga `socio.meteor_id`

### 3. Estructura de Datos Incorrecta

```
❌ Error: esInmueble is true but inmuebleData is missing
```

**Solución:** Verificar que al marcar `esInmueble: true` se incluya `inmuebleData`

### 4. ID de Post No Válido

```
❌ Error: ID de post no válido
```

**Solución:** Verificar que el post tenga campo `_id` o `id`

### 5. Límite de Hilos Alcanzado

```
❌ max-hilos-reached
```

**Solución:** El usuario debe eliminar hilos existentes (límite: 3 por publicación)

## ✅ Checklist de Implementación

### Preparación

- [ ] Servicios `inmobiliarioService` importados
- [ ] Token disponible en componente padre
- [ ] Tipo `PostInmobiliario` definido correctamente

### Para Inmuebles

- [ ] Campo `esInmueble: true` en el objeto post
- [ ] Campo `inmuebleData` con estructura completa
- [ ] Campo `meteor_id` del socio disponible
- [ ] Método `hilosInteres.createFromInmueble` accesible en Meteor

### Para Posts Normales

- [ ] Campo `_id` o `id` disponible en el post
- [ ] Campo `authorCache` con información del autor
- [ ] Método `hilosInteres.create` accesible en Meteor

### Manejo de Eventos

- [ ] Evento `hilo-created` conectado en componente padre
- [ ] Evento `abrir-chat` conectado para abrir `ChatHiloModal`
- [ ] Evento `error` con manejo de errores apropiado

### Testing

- [ ] Crear hilo desde post normal funciona
- [ ] Crear hilo desde inmueble funciona
- [ ] Validación de campos requeridos funciona
- [ ] Manejo de errores (límite, token, etc.) funciona
- [ ] Formulario se resetea al cerrar/crear

## 🔗 Integración con ChatHiloModal

### Flujo Completo: Crear → Chat

```javascript
// En el componente padre
const handleHiloCreated = async (hiloRecienCreado) => {
  // 1. Cerrar modal de crear hilo
  handleCloseCrearHilo();

  // 2. Verificar token si es necesario
  if (!props.token) {
    await initializeToken();
  }

  // 3. Abrir chat inmediatamente
  hiloSeleccionado.value = hiloRecienCreado;
  showChatModal.value = true;
};
```

## 🏗️ Consideraciones de Backend

### Endpoints Meteor Requeridos

#### hilosInteres.createFromInmueble

- **Ubicación:** `@MulbinComponents/app/imports/api/hilos-interes/methods.js` (líneas 1285-1539)
- **Parámetros requeridos:**
  ```javascript
  {
    inmuebleId: string,
    titulo: string,
    mensajeInicial: string,
    referenciaPrivada?: string,
    inmuebleInfo: {
      titulo: string,
      descripcion: string,
      socio: {
        id: string,
        meteor_id: string, // 🔑 CRÍTICO
        nombre: string,
        empresa?: string,
      }
    }
  }
  ```

#### hilosInteres.create

- **Parámetros requeridos:**
  ```javascript
  {
    postId: string,
    titulo: string,
    mensajeInicial: string,
    referenciaPrivada?: string,
  }
  ```

## 📊 Diagrama de Flujo

```
Usuario abre NuevoHiloForm
       ↓
Llenar título y mensaje
       ↓
Click en "Crear hilo"
       ↓
Validar campos requeridos
       ↓
Detectar tipo de contenido
       ↓
   ¿Es inmueble?
   /          \
 SI             NO
 ↓              ↓
createFromInmueble  →  create
       ↓              ↓
Preparar objeto hilo ←
       ↓
Emitir "hilo-created"
       ↓
Emitir "abrir-chat"
       ↓
Cerrar modal
       ↓
Abrir ChatHiloModal
```

## 🎯 Conclusión

`NuevoHiloForm.vue` es un componente **inteligente y versátil** que maneja automáticamente la creación de hilos para diferentes tipos de contenido. Su **detección dual** (posts vs inmuebles) y **manejo robusto de errores** lo hacen ideal para sistemas complejos de mensajería.

**Puntos críticos a recordar:**

1. **Detección automática** basada en `esInmueble` y `inmuebleData`
2. **Token requerido** para autenticación DDP
3. **Campo meteor_id** crítico para inmuebles
4. **Límite de 3 hilos** por publicación
5. **Integración automática** con ChatHiloModal
