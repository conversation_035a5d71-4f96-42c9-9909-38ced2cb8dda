<template>
  <div class="relative">
    <!-- Botón principal de hilos de interés -->
    <button
      @click="handleClick"
      :class="{
        'bg-green-100 hover:bg-green-200': esAutor,
        'bg-blue-100 hover:bg-blue-200': !esAutor,
      }"
      class="flex items-center px-3 py-1 text-sm font-medium text-gray-600 rounded-lg border border-gray-200 boton-hilos-interes"
    >
      <ion-icon
        :name="esAutor ? 'bulb-outline' : 'add-outline'"
        class="mr-1.5 text-base text-mulbin-600"
      ></ion-icon>
      <span class="hidden sm:inline">{{
        esAutor ? "Hilos de interés" : "Hilo de interés"
      }}</span>
      <span class="sm:hidden">{{ esAutor ? "Ver hilos" : "+ Hilo" }}</span>

      <!-- Badge de contador de mensajes no leídos -->
      <span
        v-if="unreadCount > 0"
        class="ml-1 px-1.5 py-0.5 text-xs font-bold text-white bg-red-500 rounded-full min-w-[18px] text-center animate-pulse"
        :title="`${unreadCount} mensaje${unreadCount > 1 ? 's' : ''} no leído${
          unreadCount > 1 ? 's' : ''
        }`"
      >
        {{ unreadCount > 99 ? "99+" : unreadCount }}
      </span>
    </button>

    <!-- Menú contextual de hilos (para usuarios no autores) -->
    <div
      v-if="!esAutor && mostrarMenu"
      class="absolute left-0 top-full z-30 mt-1 w-64 bg-white rounded-lg border border-gray-200 shadow-lg menu-desplegable-hilos"
    >
      <div class="py-1">
        <!-- Loading -->
        <div v-if="cargandoHilos" class="px-4 py-3 text-center">
          <div
            class="mx-auto w-4 h-4 rounded-full border-2 border-blue-500 animate-spin border-t-transparent"
          ></div>
          <p class="mt-1 text-xs text-gray-500">Cargando hilos...</p>
        </div>

        <!-- Contenido del menú -->
        <template v-else>
          <!-- Botón crear nuevo hilo -->
          <button
            @click="$emit('crear-hilo', post)"
            class="flex items-center px-4 py-2 w-full text-sm text-blue-700 hover:bg-blue-50"
          >
            <ion-icon
              name="add-circle-outline"
              class="mr-2 text-blue-600"
            ></ion-icon>
            Nuevo hilo
          </button>

          <!-- Separador si hay hilos -->
          <hr v-if="hilos && hilos.length > 0" class="my-1 border-gray-200" />

          <!-- Lista de hilos existentes -->
          <div v-if="hilos && hilos.length > 0">
            <div v-for="hilo in hilos" :key="hilo._id" class="relative">
              <button
                @click="$emit('abrir-chat', hilo)"
                class="flex justify-between items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
              >
                <div class="flex-1 text-left">
                  <p class="font-medium">{{ truncarTitulo(hilo.titulo) }}</p>
                  <p class="text-xs text-gray-500">
                    {{ hilo.referenciaPrivada }}
                  </p>
                </div>
                <!-- Badge de mensajes no leídos por hilo -->
                <span
                  v-if="
                    hilo.unreadMessagesCount && hilo.unreadMessagesCount > 0
                  "
                  class="px-1.5 py-0.5 text-xs font-bold text-white bg-red-500 rounded-full min-w-[18px] text-center"
                >
                  {{
                    hilo.unreadMessagesCount > 99
                      ? "99+"
                      : hilo.unreadMessagesCount
                  }}
                </span>
              </button>
            </div>
          </div>

          <!-- Mensaje si no hay hilos -->
          <div v-else class="px-4 py-3 text-center">
            <p class="text-xs text-gray-500">
              No tienes hilos para esta publicación
            </p>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted, onUnmounted } from "vue";

export default defineComponent({
  name: "BotonHilosInteres",
  props: {
    post: {
      type: Object,
      required: true,
    },
    esAutor: {
      type: Boolean,
      required: true,
    },
    unreadCount: {
      type: Number,
      default: 0,
    },
    hilos: {
      type: Array,
      default: () => [],
    },
    cargandoHilos: {
      type: Boolean,
      default: false,
    },
    menuAbierto: {
      type: String,
      default: null,
    },
  },
  emits: [
    "gestionar-hilo",
    "crear-hilo",
    "abrir-chat",
    "toggle-menu",
    "click-fuera",
  ],
  setup(props, { emit }) {
    const menuRef = ref(null);

    // Computed para determinar si mostrar el menú
    const mostrarMenu = computed(() => {
      if (props.esAutor) return false;
      const postId = getPostId(props.post);
      return props.menuAbierto === `hilos-${postId}`;
    });

    // Funciones auxiliares
    const getPostId = (post) => {
      return post._id || post.id || String(post.inmueble_id || "");
    };

    const truncarTitulo = (titulo) => {
      return titulo && titulo.length > 25
        ? titulo.substring(0, 25) + "..."
        : titulo;
    };

    // Manejar click en el botón principal
    const handleClick = () => {
      emit("gestionar-hilo", props.post);
    };

    // Manejar clicks fuera del menú
    const handleClickOutside = (event) => {
      if (
        menuRef.value &&
        !menuRef.value.contains(event.target) &&
        mostrarMenu.value
      ) {
        emit("click-fuera");
      }
    };

    // Lifecycle hooks
    onMounted(() => {
      document.addEventListener("click", handleClickOutside);
    });

    onUnmounted(() => {
      document.removeEventListener("click", handleClickOutside);
    });

    return {
      mostrarMenu,
      menuRef,
      handleClick,
      truncarTitulo,
      getPostId,
    };
  },
});
</script>

<style scoped>
/* Estilos específicos para el botón de hilos de interés */
.boton-hilos-interes {
  transition: all 0.2s ease;
}

.boton-hilos-interes:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Animaciones para el menú desplegable */
.menu-desplegable-hilos {
  animation: slideDown 0.15s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Efectos adicionales para mejor UX */
.menu-desplegable-hilos {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Estilos responsivos para el badge */
@media (max-width: 640px) {
  .animate-pulse {
    animation-duration: 1.5s;
  }
}
</style>
