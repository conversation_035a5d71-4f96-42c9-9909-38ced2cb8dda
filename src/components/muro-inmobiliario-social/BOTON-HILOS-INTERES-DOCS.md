# BotonHilosInteres - Componente Independiente

## Descripción

`BotonHilosInteres` es un componente Vue 3 completamente independiente y reutilizable que encapsula toda la funcionalidad relacionada con los hilos de interés en el muro inmobiliario social. Este componente fue extraído del componente principal `MuroInmobiliarioSocial.vue` para mejorar la modularidad y reutilización del código.

## Características

- ✅ **Completamente independiente**: No depende de lógica externa
- ✅ **Estilos incluidos**: Todos los estilos CSS están encapsulados
- ✅ **Responsive**: Se adapta a diferentes tamaños de pantalla
- ✅ **Accesible**: Incluye tooltips y aria-labels apropiados
- ✅ **Reutilizable**: Puede usarse en cualquier contexto con publicaciones
- ✅ **TypeScript friendly**: Tipado completo con Vue 3

## Props

### Requeridas

| Prop | Tipo | Descripción |
|------|------|-------------|
| `post` | `Object` | Objeto de la publicación inmobiliaria |
| `esAutor` | `Boolean` | Indica si el usuario actual es autor de la publicación |

### Opcionales

| Prop | Tipo | Por Defecto | Descripción |
|------|------|-------------|-------------|
| `unreadCount` | `Number` | `0` | Número de mensajes no leídos en todos los hilos |
| `hilos` | `Array` | `[]` | Array de hilos existentes para la publicación |
| `cargandoHilos` | `Boolean` | `false` | Estado de carga de hilos |
| `menuAbierto` | `String` | `null` | ID del menú actualmente abierto |

## Eventos

| Evento | Payload | Descripción |
|--------|---------|-------------|
| `gestionar-hilo` | `post` | Se emite cuando se hace clic en el botón principal |
| `crear-hilo` | `post` | Se emite cuando se solicita crear un nuevo hilo |
| `abrir-chat` | `hilo` | Se emite cuando se selecciona un hilo para abrir |
| `click-fuera` | - | Se emite cuando se hace clic fuera del menú |

## Uso Básico

```vue
<template>
  <BotonHilosInteres
    :post="publicacion"
    :es-autor="esAutorDelPost(publicacion)"
    :unread-count="contadorNoLeidos"
    :hilos="hilosDelPost"
    :cargando-hilos="cargando"
    :menu-abierto="menuActivo"
    @gestionar-hilo="manejarHilo"
    @crear-hilo="crearNuevoHilo"
    @abrir-chat="abrirChat"
    @click-fuera="cerrarMenu"
  />
</template>

<script>
import { BotonHilosInteres } from '@/components/muro-inmobiliario-social';

export default {
  components: {
    BotonHilosInteres
  },
  data() {
    return {
      publicacion: {
        _id: 'post123',
        title: 'Casa en venta',
        // ... otros campos
      },
      contadorNoLeidos: 5,
      hilosDelPost: [
        {
          _id: 'hilo1',
          titulo: 'Consulta sobre precio',
          referenciaPrivada: 'Ref: 001',
          unreadMessagesCount: 2
        }
      ],
      cargando: false,
      menuActivo: null
    }
  },
  methods: {
    manejarHilo(post) {
      console.log('Gestionar hilo para:', post.title);
    },
    crearNuevoHilo(post) {
      console.log('Crear hilo para:', post.title);
    },
    abrirChat(hilo) {
      console.log('Abrir chat:', hilo.titulo);
    },
    cerrarMenu() {
      this.menuActivo = null;
    }
  }
}
</script>
```

## Comportamiento por Tipo de Usuario

### Para Autores de Publicación

- **Botón verde** con texto "Hilos de interés"
- **Icono**: `bulb-outline` (bombilla)
- **Funcionalidad**: Al hacer clic, muestra modal con todos los hilos creados por otros usuarios
- **Contador**: Muestra total de mensajes no leídos de todos los hilos

### Para Usuarios No Autores

- **Botón azul** con texto "Hilo de interés"
- **Icono**: `add-outline` (más)
- **Funcionalidad**: Al hacer clic, muestra menú desplegable con:
  - Opción para crear nuevo hilo
  - Lista de hilos existentes del usuario
  - Contadores individuales por hilo

## Estructura del Objeto Post

```typescript
interface Post {
  _id?: string;
  id?: string;
  inmueble_id?: string | number;
  title: string;
  // ... otros campos
}
```

## Estructura del Objeto Hilo

```typescript
interface Hilo {
  _id: string;
  titulo: string;
  referenciaPrivada: string;
  unreadMessagesCount?: number;
  // ... otros campos
}
```

## Estilos Incluidos

El componente incluye sus propios estilos CSS scoped:

- **Botón principal**: Efectos hover con transformación y sombra
- **Menú desplegable**: Animación slideDown suave
- **Badges**: Diseño responsive para contadores
- **Estados**: Colores diferenciados por tipo de usuario

## Funciones Auxiliares Internas

- `getPostId(post)`: Extrae ID de la publicación de manera segura
- `truncarTitulo(titulo)`: Trunca títulos largos a 25 caracteres
- `handleClickOutside`: Maneja clics fuera del menú para cerrarlo

## Integración con el Sistema Principal

El componente está diseñado para integrarse perfectamente con:

- **inmobiliarioService**: Para llamadas API
- **hilosInteresService**: Para gestión de hilos
- **ddpService**: Para sincronización en tiempo real

## Personalización

### Cambiar Colores

```css
/* En tu componente padre o CSS global */
.boton-hilos-interes {
  --color-autor: #d1fae5; /* Verde para autores */
  --color-usuario: #dbeafe; /* Azul para usuarios */
}
```

### Modificar Textos

Los textos son completamente personalizables a través de las props del post y la lógica interna del componente.

## Dependencias

- **Vue 3**: Composition API
- **Ionicons**: Para iconografía
- **Tailwind CSS**: Para estilos base

## Consideraciones de Performance

- Uso de `computed` para propiedades reactivas
- Event listeners optimizados con cleanup
- Props reactivas para actualizaciones automáticas
- Lazy loading del menú desplegable

## Eventos del Ciclo de Vida

```javascript
onMounted(() => {
  // Registra listener global para clics fuera
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  // Limpia listeners para evitar memory leaks
  document.removeEventListener("click", handleClickOutside);
});
```

## Compatibilidad

- ✅ Vue 3.0+
- ✅ Navegadores modernos (ES6+)
- ✅ Dispositivos móviles y desktop
- ✅ Screen readers y herramientas de accesibilidad

## Ejemplo Avanzado

```vue
<template>
  <div class="publicaciones-container">
    <div 
      v-for="post in publicaciones" 
      :key="post._id"
      class="publicacion-card"
    >
      <!-- Contenido de la publicación -->
      <div class="publicacion-content">
        <h3>{{ post.title }}</h3>
        <p>{{ post.description }}</p>
      </div>
      
      <!-- Acciones de la publicación -->
      <div class="publicacion-actions">
        <BotonHilosInteres
          :post="post"
          :es-autor="post.authorId === currentUserId"
          :unread-count="getUnreadCount(post._id)"
          :hilos="getHilosForPost(post._id)"
          :cargando-hilos="loadingStates[post._id]"
          :menu-abierto="activeMenu"
          @gestionar-hilo="handleGestionarHilo"
          @crear-hilo="handleCrearHilo"
          @abrir-chat="handleAbrirChat"
          @click-fuera="handleClickFuera"
        />
        
        <!-- Otros botones de acción -->
        <button class="btn-comentar">Comentar</button>
        <button class="btn-compartir">Compartir</button>
      </div>
    </div>
  </div>
</template>
```

---

Este componente proporciona una solución completa y reutilizable para la gestión de hilos de interés, manteniendo toda la funcionalidad original mientras mejora la arquitectura del código. 