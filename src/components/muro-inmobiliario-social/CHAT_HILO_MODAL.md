# Documentación ChatHiloModal.vue

## 📋 Descripción General

`ChatHiloModal.vue` es un componente de chat en tiempo real completamente funcional que permite la comunicación entre usuarios en hilos de interés. Utiliza **Meteor DDP (Distributed Data Protocol)** para conexiones reactivas y sincronización de mensajes en tiempo real.

## 🎯 Casos de Uso

- **Chat de hilos de interés**: Comunicación entre interesados y propietarios de inmuebles
- **Mensajería en tiempo real**: Intercambio de mensajes con sincronización inmediata
- **Notificaciones de lectura**: Indicadores visuales de mensajes leídos
- **Gestión de múltiples ventanas**: Prevención de duplicación de chat

## ⚙️ Requisitos Técnicos

### Vue.js Version

- **Vue 3** con **Composition API**
- **TypeScript** support required
- **Reactive refs** y **computed properties**

### Dependencias Necesarias

```javascript
import inmobiliarioService from "../../services/inmobiliarioService";
import ddpService from "../../services/ddpService";
```

### Estructura de Servicios Requerida

- `inmobiliarioService`: Conexión DDP y métodos Meteor
- `ddpService`: Gestión de autenticación y estado de usuario

## 🚀 Implementación Paso a Paso

### 1. Importación del Componente

```vue
<template>
  <!-- Modal de chat para hilos de interés -->
  <ChatHiloModal
    :show="showChatModal"
    :hilo-seleccionado="hiloSeleccionado"
    :token="props.token"
    @close="cerrarChatModal"
    @error="handleChatError"
    @success="handleChatSuccess"
  />
</template>

<script>
import ChatHiloModal from "../muro-inmobiliario-social/ChatHiloModal.vue";

export default {
  components: {
    ChatHiloModal,
  },
  // ...
};
</script>
```

### 2. Props Requeridos

| Prop                | Tipo      | Requerido | Descripción                                      |
| ------------------- | --------- | --------- | ------------------------------------------------ |
| `show`              | `Boolean` | ✅        | Control de visibilidad del modal                 |
| `hilo-seleccionado` | `Object`  | ✅        | Datos del hilo de chat (debe tener `_id` o `id`) |
| `token`             | `String`  | ✅        | Token de autenticación DDP                       |

### 3. Eventos Emitidos

| Evento    | Parámetros        | Descripción                      |
| --------- | ----------------- | -------------------------------- |
| `close`   | -                 | Se emite al cerrar el modal      |
| `error`   | `message: string` | Se emite cuando ocurre un error  |
| `success` | `message: string` | Se emite en operaciones exitosas |

### 4. Variables Reactivas Necesarias en el Componente Padre

```javascript
// Variables reactivas en el componente padre
const showChatModal = ref(false);
const hiloSeleccionado = ref(null);

// Funciones de manejo
const cerrarChatModal = () => {
  showChatModal.value = false;
  hiloSeleccionado.value = null;
};

const handleChatError = (message) => {
  showError(message, "Error en el chat");
};

const handleChatSuccess = (message) => {
  success(message, "Éxito");
};
```

## 🔌 Conexión DDP y Token

### Obtención del Token

El token **DEBE** propagarse desde el componente padre siguiente este patrón:

```vue
<!-- BaseMultibolsaInmobiliaria.vue (Componente Base) -->
<template>
  <InmueblesSocios
    :token="props.token"
    <!-- otros props -->
  />
</template>

<!-- InmueblesSocios.vue (Componente Hijo) -->
<script>
export default {
  props: {
    token: {
      type: String,
      default: "",
    },
    // otros props...
  },

  setup(props, { emit }) {
    return {
      props, // 🔑 CRÍTICO: Exponer props para acceso en template
      // otros returns...
    };
  };
};
</script>

<template>
  <ChatHiloModal
    :token="props.token"  <!-- 🔑 Usar props.token -->
    <!-- otros props -->
  />
</template>
```

### ⚠️ Error Común en Vue 3

**PROBLEMA:** `Property "props" was accessed during render but is not defined`

**SOLUCIÓN:** En Vue 3 Composition API, las `props` NO están automáticamente disponibles en el template. DEBE exponerse en el return:

```javascript
// ❌ INCORRECTO
setup(props, { emit }) {
  return {
    // props NO expuesto
    otrosValores,
  };
}

// ✅ CORRECTO
setup(props, { emit }) {
  return {
    props, // 🔑 EXPONER props para template
    otrosValores,
  };
}
```

### Inicialización de Conexión DDP

El componente padre **DEBE** inicializar la conexión DDP:

```javascript
// En el componente padre (ej. InmueblesSocios.vue)
const initializeToken = async () => {
  try {
    if (props.token) {
      console.log("🔌 Inicializando conexión DDP...");
      await inmobiliarioService.connectWithToken(props.token);
      console.log("✅ Conexión DDP establecida correctamente");

      // Guardar en localStorage para persistencia
      localStorage.setItem("mulbin_token", props.token);
    }
  } catch (error) {
    console.error("❌ Error inicializando DDP:", error);
  }
};

// Llamar en onMounted
onMounted(async () => {
  await initializeToken();
  // otros inicializadores...
});
```

## 🏠 Estructura del Objeto Hilo

El objeto `hiloSeleccionado` debe tener la siguiente estructura:

```typescript
interface HiloSeleccionado {
  _id?: string; // ID principal del hilo
  id?: string; // ID alternativo
  titulo: string; // Título del hilo
  creatorId: string; // ID del creador
  creatorCache: {
    // Caché del creador
    firstName?: string;
    lastName?: string;
    name?: string;
    avatar?: string;
  };
  postAuthorCache: {
    // Caché del autor del post
    firstName?: string;
    lastName?: string;
    name?: string;
    avatar?: string;
  };
  referenciaPrivada?: string; // Referencia adicional
}
```

## 🔄 Flujo de Integración Completo

### 1. Crear Hilo desde Inmueble

```javascript
const abrirCrearHilo = (inmueble) => {
  const inmuebleData = {
    inmuebleId: inmueble.id,
    titulo: `Interés en ${inmueble.titulo}`,
    referenciaPrivada: `Inmueble ${inmueble.key} - ${inmueble.socio.nombre}`,
    inmuebleInfo: {
      titulo: inmueble.titulo,
      descripcion: inmueble.descripcion,
      socio: {
        id: inmueble.socio.id,
        meteor_id: inmueble.socio.meteor_id, // 🔑 CAMPO CLAVE
        nombre: inmueble.socio.nombre,
        empresa: inmueble.socio.empresa,
      },
    },
  };

  const postData = {
    _id: inmueble.id,
    id: inmueble.id,
    title: inmueble.titulo,
    authorCache: {
      firstName: inmueble.socio.nombre.split(" ")[0] || "",
      lastName: inmueble.socio.nombre.split(" ").slice(1).join(" ") || "",
      name: inmueble.socio.nombre,
    },
    inmuebleData: inmuebleData,
    esInmueble: true, // 🔑 Flag importante
  };

  inmuebleSeleccionadoParaHilo.value = postData;
  showCrearHiloModal.value = true;
};
```

### 2. Abrir Chat Después de Crear Hilo

```javascript
const handleHiloCreated = async (hiloRecienCreado) => {
  console.log("✅ Hilo creado:", hiloRecienCreado);

  // Cerrar modal de crear hilo
  handleCloseCrearHilo();

  // Verificar token
  if (!props.token) {
    console.warn("⚠️ Token no disponible");
    await initializeToken();
  }

  // 🔑 Abrir chat inmediatamente
  hiloSeleccionado.value = hiloRecienCreado;
  showChatModal.value = true;
};
```

## 🔌 Métodos DDP Utilizados

### Conexión y Autenticación

```javascript
await inmobiliarioService.connectWithToken(token);
```

### Suscripción a Mensajes

```javascript
await inmobiliarioService.subscribeToThreadMessages(hiloId);
```

### Obtener Mensajes

```javascript
const mensajes = inmobiliarioService.getThreadMessages(hiloId);
```

### Observador Reactivo

```javascript
const unsubscribe = inmobiliarioService.onThreadMessagesChange(
  hiloId,
  (mensajesActualizados) => {
    mensajesHilo.value = mensajesActualizados;
  }
);
```

### Enviar Mensaje

```javascript
await inmobiliarioService.call("hilosInteres.sendMessage", hiloId, mensaje);
```

### Marcar como Leído

```javascript
await inmobiliarioService.markMessagesAsRead(hiloId);
```

## 🎨 Características del UI

### Diseño Responsivo

- **Modal centrado** con altura fija de 85vh
- **Header fijo** con información del chat
- **Área de mensajes** con scroll automático
- **Input expandible** con contador de caracteres

### Indicadores Visuales

- 🔵 **Estado online** con indicador pulsante
- ✅ **Mensajes leídos** con check doble
- 🎨 **Mensajes propios vs recibidos** con colores diferentes
- 📅 **Agrupación por días** con divisores de fecha
- 🕐 **Timestamps inteligentes** (hoy, ayer, fecha completa)

### Interactividad

- **Enter para enviar** (Shift+Enter para nueva línea)
- **Auto-resize del textarea** hasta 120px máximo
- **Scroll automático** al recibir mensajes
- **Focus automático** al abrir y después de enviar
- **Límite de 500 caracteres** con indicador visual

## 🪟 Gestión de Múltiples Ventanas

### Prevención de Duplicación

```javascript
// Verificar si la ventana ya está abierta
if (isChatWindowOpen(hiloId)) {
  emit("error", "Esta conversación ya está abierta en otra ventana");
  return;
}

// Marcar ventana como abierta
addChatWindowMarker(hiloId);
```

### Limpieza Automática

```javascript
// Al cerrar
removeChatWindowMarker(hiloId);

// Al detectar cierre de ventana/pestaña
window.addEventListener("beforeunload", () => {
  removeChatWindowMarker(hiloId);
});
```

## 🔧 Solución de Problemas Comunes

### 1. Token No Disponible

```
❌ No hay hilo seleccionado o token para inicializar chat
```

**Solución:** Verificar que el token se esté pasando correctamente como prop

### 2. Props No Definido en Template

```
❌ Property "props" was accessed during render but is not defined
```

**Solución:** Agregar `props` al return del setup()

### 3. Hilo Sin ID

```
❌ ERROR CRÍTICO: No se pudo obtener ID del hilo
```

**Solución:** Verificar que el hilo tenga campo `_id` o `id`

### 4. Conexión DDP Fallida

```
❌ Error inicializando token y conexión DDP
```

**Solución:** Verificar token válido y servicios importados correctamente

## ✅ Checklist de Implementación

### Preparación

- [ ] Servicios `inmobiliarioService` y `ddpService` importados
- [ ] Token disponible en componente padre
- [ ] Props expuestas en return del setup()

### Variables Reactivas

- [ ] `showChatModal` definida
- [ ] `hiloSeleccionado` definida
- [ ] Funciones de manejo de eventos definidas

### Conexión DDP

- [ ] `initializeToken()` implementada
- [ ] Conexión DDP en `onMounted()`
- [ ] Token propagado correctamente

### Integración UI

- [ ] Componente `ChatHiloModal` importado
- [ ] Props correctamente bindeadas
- [ ] Eventos correctamente conectados

### Testing

- [ ] Token se inicializa correctamente
- [ ] Chat se abre después de crear hilo
- [ ] Mensajes se envían y reciben
- [ ] Indicadores de lectura funcionan
- [ ] Modal se cierra correctamente

## 📊 Diagrama de Flujo

```
Usuario crea hilo
       ↓
NuevoHiloForm envía a Meteor
       ↓
Evento handleHiloCreated
       ↓
Verificar token disponible
       ↓
Abrir ChatHiloModal
       ↓
Inicializar conexión DDP
       ↓
Suscribirse a mensajes
       ↓
Configurar observador reactivo
       ↓
Chat funcional en tiempo real
```

## 🎯 Conclusión

Esta documentación cubre todos los aspectos necesarios para implementar `ChatHiloModal.vue` correctamente. Siguiendo estos pasos y consideraciones, la integración debería funcionar a la primera sin problemas comunes de configuración.

**Puntos críticos a recordar:**

1. **Exponer props** en return del setup()
2. **Inicializar DDP** antes de usar chat
3. **Propagar token** correctamente
4. **Estructura correcta** del objeto hilo
5. **Gestión adecuada** de eventos y limpieza
