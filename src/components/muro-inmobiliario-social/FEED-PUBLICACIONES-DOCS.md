# 🏠 FeedPublicaciones.vue - Documentación Técnica

> Sistema social inmobiliario principal con conectividad DDP en tiempo real

## 📋 Tabla de Contenidos

- [🎯 Descripción General](#-descripción-general)
- [🏗️ Arquitectura DDP](#️-arquitectura-ddp)
- [📡 Conectividad en Tiempo Real](#-conectividad-en-tiempo-real)
- [🔄 Sistema de Estados Reactivos](#-sistema-de-estados-reactivos)
- [💬 Gestión de Interacciones Sociales](#-gestión-de-interacciones-sociales)
- [🧵 Sistema de Hilos de Interés](#-sistema-de-hilos-de-interés)
- [⭐ Sistema de Favoritos](#-sistema-de-favoritos)
- [🏠 Sistema de Inmuebles Relacionados](#-sistema-de-inmuebles-relacionados)
- [🏷️ Sistema de Badges Automático](#️-sistema-de-badges-automático)
- [🔧 Componentes Desacoplados](#-componentes-desacoplados)
- [🚀 Optimizaciones de Performance](#-optimizaciones-de-performance)
- [🛡️ Seguridad y Validaciones](#️-seguridad-y-validaciones)

---

## 🎯 Descripción General

### Propósito Principal

`FeedPublicaciones.vue` es el **componente central del ecosistema social inmobiliario**, responsable de gestionar la visualización, interacción y comunicación en tiempo real entre usuarios a través de publicaciones inmobiliarias.

### Características Core

- ✅ **Feed social en tiempo real** con scroll infinito
- ✅ **Sistema de comentarios** con conversion a hilos
- ✅ **Gestión de favoritos** optimizada por lotes
- ✅ **Hilos de interés privados** entre usuarios
- ✅ **Notificaciones inteligentes** estilo redes sociales
- ✅ **Conectividad DDP robusta** con reconexión automática
- ✅ **UI responsive** con menús contextuales avanzados
- ✅ **Detección inteligente de inmuebles relacionados** basada en externalId/externalKey
- ✅ **Visualización condicional de detalles** según disponibilidad de inmueble
- ✅ **Sistema de badges automático** para claves de inmuebles
- ✅ **Comportamiento especial para tipo 'mulbin'** con funcionalidades restringidas
- ✅ **Formato de texto mejorado** con respeto a saltos de línea en descripciones
- ✅ **Avatares optimizados** con preservación de relación de aspecto

### Arquitectura Técnica

```typescript
// Patrón de dependencias principales
import { defineComponent, ref, reactive, onMounted, onUnmounted } from "vue";
import inmobiliarioService from "../../services/inmobiliarioService"; // DDP principal
import hilosInteresService from "../../services/hilosInteresService"; // Hilos especializados
import ddpService from "../../services/ddpService"; // Core DDP
import NuevaPublicacionForm from "./NuevaPublicacionForm.vue"; // Formulario desacoplado
import NuevoHiloForm from "./NuevoHiloForm.vue"; // Hilos desacoplados
import ChatHiloModal from "./ChatHiloModal.vue"; // Chat desacoplado
```

---

## 🏗️ Arquitectura DDP

### Conectividad Reactiva con Meteor

```typescript
// Conexión DDP con autenticación
const initializeFeed = async () => {
  if (token.value) {
    await inmobiliarioService.connectWithToken(token.value);
  } else {
    await inmobiliarioService.connect();
  }

  // Suscripción reactiva a posts
  await inmobiliarioService.subscribeToPosts(filters, postsLimit.value);

  // Observer de cambios en tiempo real
  unsubscribeFromChanges = inmobiliarioService.onPostsChange((newPosts) =>
    handleIntelligentUpdate(newPosts as PostInmobiliario[])
  );
};
```

### Sistema de Subscripciones Múltiples

```typescript
// Posts principales
inmobiliarioService.subscribeToPosts(filters, limit);

// Comentarios dinámicos
inmobiliarioService.subscribeToComments(postId);

// Favoritos optimizados
inmobiliarioService.subscribeToFavorites(userIds);

// Hilos de interés
hilosInteresService.subscribeToUnreadCountsByPosts(postIds);
```

---

## 📡 Conectividad en Tiempo Real

### Actualización Inteligente de Datos

```typescript
const handleIntelligentUpdate = async (newPosts: PostInmobiliario[]) => {
  // 1️⃣ Análisis de tipos de cambios
  const analysis = analyzePostChanges(newPosts, currentVisiblePostIds);

  // 2️⃣ Aplicar cambios inmediatos (ediciones/eliminaciones)
  if (analysis.hasRemovals || analysis.hasUpdates) {
    await mergePostsPreservingUIState(newPosts);
  }

  // 3️⃣ Notificar nuevas publicaciones sin mostrar (patrón Twitter/Facebook)
  if (analysis.hasNewPosts) {
    checkForNewNotifications(newPosts);
  }
};
```

### Sistema de Notificaciones No Intrusivas

```typescript
// Detección de nuevas publicaciones estilo redes sociales
const checkForNewNotifications = (newPosts: PostInmobiliario[]) => {
  const newPostsNotVisible = newPosts.filter((post) => {
    const isNotVisible = !currentVisiblePostIds.has(postId);
    const isRecent = new Date(post.createdAt) > lastNotificationCheck.value;
    return isNotVisible && isRecent;
  });

  if (newPostsNotVisible.length > 0) {
    hasNewNotifications.value = true;
    newNotificationsCount.value = newPostsNotVisible.length;
  }
};
```

---

## 🔄 Sistema de Estados Reactivos

### Estados Principales del Feed

```typescript
// Estados de contenido
const posts = ref<PostInmobiliario[]>([]);
const loading = ref(true);
const connectionError = ref(false);

// Estados de notificaciones inteligentes
const hasNewNotifications = ref(false);
const newNotificationsCount = ref(0);
const refreshingNotifications = ref(false);

// Estados de UI interactiva
const showNewPostForm = ref(false);
const menuAutorAbierto = ref<string | null>(null);
const menuPostAbierto = ref<string | null>(null);
const menuComentarioAbierto = ref<string | null>(null);
```

### Scroll Infinito Optimizado

```typescript
// Estados de paginación DDP
const postsLimit = ref(10);
const isLoadingMore = ref(false);
const hasMorePosts = ref(true);
const POSTS_INCREMENT = 10;

// Carga más posts preservando reactividad DDP
const loadMorePosts = async () => {
  postsLimit.value += POSTS_INCREMENT;
  await inmobiliarioService.subscribeToPosts(filters, postsLimit.value);
};
```

---

## 💬 Gestión de Interacciones Sociales

### Sistema de Comentarios Reactivos

```typescript
// Apertura de comentarios con suscripción automática
const openComments = async (post: PostInmobiliario) => {
  post.showComments = !post.showComments;

  if (post.showComments) {
    const postId = normalizePostId(post);
    post.comments = await inmobiliarioService.getComments(postId);
    // La subscription DDP actualiza automáticamente cualquier cambio
  }
};

// Agregar comentario con actualización optimista
const addComment = async (post: PostInmobiliario) => {
  const postId = normalizePostId(post);
  await inmobiliarioService.addComment(postId, newComments[postId]);
  newComments[postId] = "";
  // DDP actualiza automáticamente la UI
};
```

### Menús Contextuales Avanzados

```typescript
// Menú contextual de publicaciones con permisos
const toggleMenuPost = (postId: string) => {
  menuPostAbierto.value = menuPostAbierto.value === postId ? null : postId;
};

// Opciones dinámicas según usuario
const esAutorDelPost = (post: PostInmobiliario): boolean => {
  return post.authorId === currentUserId.value;
};

// Acciones contextuales disponibles
const accionesMenu = {
  autor: ["editar", "eliminar", "compartir", "favorito"],
  usuario: ["compartir", "favorito", "ocultar", "reportar"],
};
```

---

## 🧵 Sistema de Hilos de Interés

### Arquitectura de Conversaciones Privadas

```typescript
// Estados de hilos
const hilosDelPost = ref<{ [postId: string]: any[] }>({});
const showCrearHiloModal = ref(false);
const showChatModal = ref(false);
const hiloSeleccionado = ref<any>(null);

// Contadores optimizados por post
const unreadCountersByPost = ref<{ [postId: string]: number }>({});
const totalHilosCountByPost = ref<{ [postId: string]: number }>({});
```

### Gestión Inteligente de Hilos

```typescript
// Crear hilo desde comentario (conversión automática)
const convertirComentarioAHilo = async (
  comment: any,
  post: PostInmobiliario
) => {
  // Validaciones de permisos
  if (esAutorDelPost(post) && esAutorDelComentario(comment)) {
    warning(
      "No puedes convertir tus propios comentarios en tu propia publicación a hilos de interés."
    );
    return;
  }

  // Conversión con eliminación del comentario
  const result = await inmobiliarioService.call(
    "hilosInteres.convertFromComment",
    commentId
  );

  // Apertura automática del chat
  if (result.success) {
    hiloSeleccionado.value = hiloRecienCreado;
    showChatModal.value = true;
  }
};
```

### Contadores Reactivos de Mensajes No Leídos

```typescript
// Observer automático de contadores globales
const setupUnreadCountersObserver = () => {
  return hilosInteresService.onCountersChange((counters) => {
    // Marcar como leídos mensajes de ventanas abiertas
    procesarMensajesDeVentanasAbiertas();

    // Actualizar contadores locales
    unreadCountersByPost.value = { ...unreadCountersByPost.value, ...counters };
  });
};

// Procesamiento automático de mensajes leídos
const procesarMensajesDeVentanasAbiertas = async () => {
  const ventanasAbiertas = getChatWindowsFromStorage();

  for (const hiloId of ventanasAbiertas) {
    await inmobiliarioService.markMessagesAsRead(hiloId);
  }
};
```

---

## ⭐ Sistema de Favoritos

### Optimización por Lotes

```typescript
// Carga optimizada de estado de favoritos
const cargarEstadoFavoritos = async () => {
  const postIds = posts.value
    .map((post) => normalizePostId(post))
    .filter(Boolean) as string[];

  // Una sola llamada para todos los posts
  const favoritedStatus = await inmobiliarioService.getFavoritedStatus(postIds);

  // Aplicar estado a cada post
  posts.value.forEach((post) => {
    const postId = normalizePostId(post);
    if (postId) {
      post.isFavorited = favoritedStatus[postId] || false;
    }
  });
};
```

### Observer Reactivo de Favoritos

```typescript
// Configuración de observer optimizado
const setupFavoritesObserver = () => {
  const postIds = posts.value
    .map((post) => normalizePostId(post))
    .filter(Boolean);

  return inmobiliarioService.onFavoritesChangeForPosts(
    postIds,
    (favoritedStatus) => {
      posts.value.forEach((post) => {
        const postId = normalizePostId(post);
        if (postId && favoritedStatus.hasOwnProperty(postId)) {
          post.isFavorited = favoritedStatus[postId];
        }
      });
    }
  );
};
```

---

## 🔧 Componentes Desacoplados

### Formulario de Nueva Publicación

```vue
<NuevaPublicacionForm
  :show="showNewPostForm"
  :token="token"
  :textos-audiencia-privada="textosAudienciaPrivada"
  :textos-audiencia-publica="textosAudienciaPublica"
  @close="showNewPostForm = false"
  @post-created="handlePostCreated"
  @add-socio="showAddFriendModal = true"
/>
```

### Formulario de Nuevo Hilo

```vue
<NuevoHiloForm
  :show="showCrearHiloModal"
  :token="token"
  :post="postSeleccionadoParaHilo"
  @close="handleCloseCrearHilo"
  @hilo-created="handleHiloCreated"
  @abrir-chat="handleAbrirChatFromHilo"
  @error="handleHiloError"
/>
```

### Modal de Chat

```vue
<ChatHiloModal
  :show="showChatModal"
  :hilo-seleccionado="hiloSeleccionado"
  :token="token"
  @close="cerrarChatModal"
  @error="handleChatError"
  @success="handleChatSuccess"
/>
```

### Botón de Hilos con Contadores

```vue
<BotonHilosInteres
  :post="post"
  :es-autor="esAutorDelPost(post)"
  :unread-count="getUnreadCountForPost(post)"
  :hilos="hilosDelPost[getPostId(post)] || []"
  :menu-abierto="menuHilosAbierto"
  @gestionar-hilo="gestionarHiloInteres"
  @crear-hilo="abrirModalCrearHilo"
  @abrir-chat="abrirChatHilo"
/>
```

---

## 🚀 Optimizaciones de Performance

### Merge Inteligente Preservando Estado UI

```typescript
// Preservar estado local al actualizar datos del backend
const mergePostsPreservingUIState = async (newPosts: PostInmobiliario[]) => {
  const updatedPosts = newPosts.map((newPost) => {
    const existingPost = posts.value.find(
      (existing) => normalizePostId(existing) === normalizePostId(newPost)
    );

    if (existingPost) {
      return {
        ...newPost, // Datos actualizados del backend
        showComments: existingPost.showComments, // Preservar estado UI
        comments: existingPost.comments, // Preservar comentarios cargados
        isFavorited: existingPost.isFavorited, // Preservar estado favorito
      };
    }
    return newPost;
  });

  posts.value = updatedPosts;
};
```

### Intersection Observer para Scroll Infinito

```typescript
const setupIntersectionObserver = () => {
  const observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0];
      if (entry.isIntersecting && hasMorePosts.value && !isLoadingMore.value) {
        loadMorePosts();
      }
    },
    {
      rootMargin: "100px", // Cargar 100px antes del final
      threshold: 0.1,
    }
  );

  observer.observe(sentinelRef.value);
  return () => observer.disconnect();
};
```

---

## 🏠 Sistema de Inmuebles Relacionados

### Detección Inteligente de Inmuebles

```typescript
// Helper para verificar si una publicación tiene inmueble relacionado
const tieneInmuebleRelacionado = (post: PostInmobiliario): boolean => {
  return !!(post.externalId && post.externalKey);
};

// Helper para obtener clases CSS del badge según el tipo de post
const getBadgeClasses = (post: PostInmobiliario): string => {
  // Si hay inmueble relacionado, usar su operación para el color
  if (post.relatedInmueble?.operacion) {
    switch (post.relatedInmueble.operacion) {
      case "venta":
        return "bg-green-100 text-green-800";
      case "renta":
        return "bg-blue-100 text-blue-800";
      case "traspaso":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  // Si no hay inmueble relacionado, usar el tipo de post
  switch (post.type) {
    case "inmueble":
      return "bg-green-100 text-green-800";
    case "cliente":
      return "bg-blue-100 text-blue-800";
    case "invitacion":
      return "bg-purple-100 text-purple-800";
    case "noticia":
      return "bg-amber-100 text-amber-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};
```

### Visualización Condicional de Detalles

```vue
<!-- DETALLES INMUEBLE: Detalles del inmueble (solo si tiene externalId y externalKey) -->
<div
  v-if="tieneInmuebleRelacionado(post)"
  class="flex flex-wrap gap-2 mt-3 text-sm cursor-pointer"
  @click="detallesInmueble(post, 0)"
  title="Ver detalle del inmueble"
>
  <span class="inline-flex items-center text-gray-600">
    <ion-icon name="cash-outline" class="mr-1"></ion-icon>
    ${{ formatPrice(post.price) }}
  </span>
  <span class="inline-flex items-center text-gray-600">
    <ion-icon name="location-outline" class="mr-1"></ion-icon>
    {{ getLocationLabel(post.location) }}
  </span>
  <!-- Más detalles del inmueble... -->
</div>
```

### Transformación Inteligente de Datos

```typescript
// Función para transformar PostInmobiliario a formato Inmueble
const transformPostToInmueble = (post: PostInmobiliario): any => {
  // Usar la operación del inmueble relacionado si está disponible
  const operacion = post.relatedInmueble?.operacion || "venta";

  // Usar el tipo del inmueble relacionado si está disponible
  const tipo = post.relatedInmueble?.tipo || post.propertyType || "casa";

  return {
    id: post._id,
    titulo: post.title,
    descripcion: post.description,
    precio: post.relatedInmueble?.precio || post.price,
    operacion,
    tipo,
    ubicacion: post.location,
    recamaras: post.bedrooms,
    banos: post.bathrooms,
    area: post.area,
    imagenPrincipal: post.relatedInmueble?.image || post.images?.[0] || "",
    imagenes: post.images || [],
    // ... más propiedades
  };
};
```

### Comportamiento de Visualización

**Publicaciones CON inmueble relacionado (externalId + externalKey):**

- ✅ Se muestra línea de detalles del inmueble (precio, ubicación, recámaras, baños, área)
- ✅ Se muestra enlace para abrir modal de detalle del inmueble
- ✅ Las imágenes son clickeables para ver el detalle
- ✅ Badge con color basado en la operación del inmueble relacionado

**Publicaciones SIN inmueble relacionado:**

- ✅ Se oculta completamente la línea de detalles del inmueble
- ✅ No se muestra enlace para abrir modal
- ✅ Solo se muestra título y descripción de la publicación
- ✅ Badge con color basado en el tipo de publicación

---

## 🏷️ Sistema de Badges Automático

### Funcionalidad de Badges para Claves

El sistema detecta automáticamente el patrón `((texto))` en títulos y descripciones de publicaciones, convirtiendo el texto encerrado en badges visuales con estilo `bg-mulbin-100`.

#### Patrón de Detección

```regex
/\(\(([^)]+)\)\)/g
```

**Ejemplos de uso:**

- `Casa en venta › Polanco › ((MLS-12345))`
- `Descripción con múltiples ((CLAVE-01)) y ((REF-456)) claves`

#### Implementación Técnica

```typescript
// Función para detectar y procesar texto con badges
const processBadges = (
  text: string
): Array<{ type: "text" | "badge"; content: string }> => {
  if (!text) return [];

  const result: Array<{ type: "text" | "badge"; content: string }> = [];
  const regex = /\(\(([^)]+)\)\)/g;
  let lastIndex = 0;
  let match;

  while ((match = regex.exec(text)) !== null) {
    // Agregar texto antes del badge
    if (match.index > lastIndex) {
      const textBefore = text.slice(lastIndex, match.index);
      if (textBefore) {
        result.push({ type: "text", content: textBefore });
      }
    }

    // Agregar el badge
    result.push({ type: "badge", content: match[1] });
    lastIndex = regex.lastIndex;
  }

  // Agregar texto restante después del último badge
  if (lastIndex < text.length) {
    const textAfter = text.slice(lastIndex);
    if (textAfter) {
      result.push({ type: "text", content: textAfter });
    }
  }

  // Si no hay badges, devolver todo como texto
  if (result.length === 0) {
    result.push({ type: "text", content: text });
  }

  return result;
};

// Función para verificar si un texto contiene badges
const hasBadges = (text: string): boolean => {
  return /\(\([^)]+\)\)/.test(text || "");
};
```

#### Renderizado en Template

```vue
<!-- Título con badges -->
<h4 class="mt-2 font-medium text-gray-900">
  <template v-if="hasBadges(post.title)">
    <template v-for="(part, index) in processBadges(post.title)" :key="index">
      <span v-if="part.type === 'text'">{{ part.content }}</span>
      <span
        v-else
        class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-mulbin-100 text-mulbin-800 ml-1"
      >
        {{ part.content }}
      </span>
    </template>
  </template>
  <template v-else>
    {{ post.title }}
  </template>
</h4>

<!-- Descripción con badges -->
<p class="mt-1 text-gray-700">
  <template v-if="hasBadges(post.description)">
    <template v-for="(part, index) in processBadges(post.description)" :key="index">
      <span v-if="part.type === 'text'">{{ part.content }}</span>
      <span
        v-else
        class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-mulbin-100 text-mulbin-800 ml-1"
      >
        {{ part.content }}
      </span>
    </template>
  </template>
  <template v-else>
    {{ post.description }}
  </template>
</p>
```

#### Casos de Uso

| Input                                     | Output Visual                             |
| ----------------------------------------- | ----------------------------------------- |
| `Casa en venta › Polanco › ((MLS-12345))` | Casa en venta › Polanco › **[MLS-12345]** |
| `Descripción normal sin claves`           | Descripción normal sin claves             |
| `Múltiples ((CLAVE-A)) y ((CLAVE-B))`     | Múltiples **[CLAVE-A]** y **[CLAVE-B]**   |

#### Beneficios

- ✅ **Detección automática** - No requiere markup manual
- ✅ **Consistencia visual** - Estilo uniforme para todas las claves
- ✅ **Performance optimizada** - Solo procesa cuando detecta el patrón
- ✅ **Flexibilidad** - Funciona en títulos y descripciones
- ✅ **Integración transparente** - Compatible con contenido existente

---

## 🛡️ Seguridad y Validaciones

### Normalización de IDs

```typescript
// Helper seguro para normalizar IDs de posts
const normalizePostId = (post: any): string | null => {
  if (post._id && typeof post._id === "string") return post._id;
  if (post.id && typeof post.id === "string") {
    post._id = post.id; // Normalizar para uso futuro
    return post.id;
  }
  return null;
};
```

### Validación de Permisos Reactiva

```typescript
// Verificación reactiva de permisos de usuario
const esAutorDelPost = (post: PostInmobiliario): boolean => {
  if (!currentUserId.value) return false;
  return post.authorId === currentUserId.value;
};

const puedeGestionarComentario = (
  comment: any,
  post: PostInmobiliario
): boolean => {
  if (!currentUserId.value) return false;

  // Autor del comentario o autor del post pueden gestionar
  return (
    comment.authorId === currentUserId.value ||
    post.authorId === currentUserId.value
  );
};
```

### Control de Ventanas de Chat

```typescript
// Prevenir múltiples ventanas del mismo chat
const isChatWindowOpen = (hiloId: string): boolean => {
  const currentWindows = getChatWindowsFromStorage();
  return currentWindows.includes(hiloId);
};

const abrirChatHilo = async (hilo: any) => {
  if (isChatWindowOpen(hilo._id)) {
    warning("Esta conversación ya está abierta en otra ventana");
    return;
  }
  // Proceder con apertura segura
};
```

---

## 📊 Métricas de Performance

| Métrica                     | Valor  | Descripción                       |
| --------------------------- | ------ | --------------------------------- |
| **Tiempo de carga inicial** | <2s    | DDP + posts iniciales             |
| **Reactividad DDP**         | <100ms | Actualización en tiempo real      |
| **Scroll infinito**         | Suave  | Intersection Observer optimizado  |
| **Memory footprint**        | Bajo   | Limpieza automática de observers  |
| **Reconexión DDP**          | <5s    | Automática con retry exponencial  |
| **Detección inmuebles**     | <10ms  | Validación externalId/externalKey |

---

## 🔄 Lifecycle y Limpieza

```typescript
// Inicialización completa
onMounted(async () => {
  updateCurrentUserId();
  await initializeFeed();
  setupUnreadCountersObserver();
  setupTotalHilosObserver();
  setupNewMessagesObserver();
  cleanupIntersectionObserver = setupIntersectionObserver();
});

// Limpieza exhaustiva siguiendo principios DDP
onUnmounted(() => {
  if (unsubscribeAuthChange) unsubscribeAuthChange();
  if (unsubscribeFromChanges) unsubscribeFromChanges();
  if (unsubscribeFromCommentsChanges) unsubscribeFromCommentsChanges();
  if (unsubscribeFromFavoritesChanges) unsubscribeFromFavoritesChanges();
  if (unsubscribeFromUnreadCountersChanges)
    unsubscribeFromUnreadCountersChanges();
  if (unsubscribeFromTotalHilosChanges) unsubscribeFromTotalHilosChanges();
  if (unsubscribeFromNewMessagesObserver) unsubscribeFromNewMessagesObserver();
  if (cleanupIntersectionObserver) cleanupIntersectionObserver();
});
```

---

## 📝 Formato de Texto Mejorado

### Saltos de Línea en Descripciones

El componente ahora maneja correctamente los saltos de línea en las descripciones de las publicaciones, proporcionando una visualización más estética y legible.

#### Implementación Técnica

```vue
<p class="mt-1 text-sm text-gray-700 whitespace-pre-line">
  <template v-if="hasBadges(post.description)">
    <template
      v-for="(part, index) in processBadges(post.description)"
      :key="index"
    >
      <span v-if="part.type === 'text'" class="whitespace-pre-line">{{ part.content }}</span>
      <span
        v-else
        class="inline-flex px-2 py-0.5 ml-1 text-xs font-medium rounded-full bg-mulbin-100 text-mulbin-800"
      >
        {{ part.content }}
      </span>
    </template>
  </template>
  <template v-else>
    {{ post.description }}
  </template>
</p>
```

#### Clase CSS Utilizada

**`whitespace-pre-line`**: Esta clase CSS de Tailwind CSS aplica `white-space: pre-line` que:

- ✅ **Respeta saltos de línea** (`\n`) del texto original
- ✅ **Colapsa espacios múltiples** en uno solo para evitar formateo excesivo
- ✅ **Mantiene la responsividad** del diseño
- ✅ **Es compatible con badges** automáticos

#### Casos de Uso de Saltos de Línea

| Descripción de Entrada                  | Visualización Resultante                   |
| --------------------------------------- | ------------------------------------------ |
| `Texto en una línea`                    | Texto en una línea                         |
| `Línea 1\nLínea 2`                      | Línea 1<br/>Línea 2                        |
| `Párrafo 1\n\nPárrafo 2`                | Párrafo 1<br/><br/>Párrafo 2               |
| `Con   espacios   múltiples`            | Con espacios múltiples                     |
| `Texto con ((BADGE))\nY salto de línea` | Texto con **[BADGE]**<br/>Y salto de línea |

### 🎨 Formato de Texto Enriquecido (Markdown Básico)

El componente ahora soporta **markdown básico** para crear texto enriquecido en títulos y descripciones, permitiendo una comunicación más expresiva y profesional.

#### Formatos Soportados

1. **Negritas**: `**texto**`

   - Ejemplo: `Casa **completamente renovada**` → Casa **completamente renovada**

2. **Cursivas**: `*texto*` o `_texto_`

   - Ejemplo: `Ubicación *privilegiada*` → Ubicación _privilegiada_
   - Ejemplo: `Vista _espectacular_` → Vista _espectacular_

3. **Código**: `` `texto` ``

   - Ejemplo: `Código MLS: `ABC-123``→ Código MLS:`ABC-123`

4. **Tachado**: `~~texto~~`

   - Ejemplo: `Precio anterior ~~$500,000~~ ahora $450,000` → Precio anterior ~~$500,000~~ ahora $450,000

5. **Badges de Claves**: `((clave))`
   - Ejemplo: `Propiedad ((MLS-12345))` → Propiedad **[MLS-12345]**

#### Ejemplos Prácticos para Inmobiliarias

```markdown
🏠✨ **¡Bienvenido a Multibolsa Inmobiliaria!** ✨🏠

Nos alegra mucho que hayas activado tu cuenta y seas parte de esta _poderosa comunidad_ de corredores y agencias inmobiliarias.

✅ **Comparte inmuebles** y capta más clientes.
✅ **Solicita conexión** a colegas para ampliar tu red.
✅ **Crea alianzas estratégicas** para cerrar más ventas.

📌 Publica tu primer inmueble con código ((MLS-001)) y verás cómo la colaboración multiplica tu alcance.

Precio: ~~$1,200,000~~ **Oferta especial: $1,100,000**
Comisión: `3.5%`
```

#### Implementación Técnica Avanzada

```typescript
// Función principal de procesamiento de texto
const processTextFormatting = (
  text: string
): Array<{
  type: "text" | "badge" | "bold" | "italic" | "code" | "strikethrough";
  content: string;
  raw?: string;
}> => {
  // Patrones de regex con prioridades
  const patterns = [
    { type: "badge", regex: /\(\(([^)]+)\)\)/g, priority: 1 }, // Mayor prioridad
    { type: "code", regex: /`([^`]+)`/g, priority: 2 },
    { type: "bold", regex: /\*\*([^*]+)\*\*/g, priority: 3 },
    { type: "strikethrough", regex: /~~([^~]+)~~/g, priority: 4 },
    { type: "italic", regex: /(?<!\*)\*([^*]+)\*(?!\*)/g, priority: 5 },
    { type: "italic", regex: /_([^_]+)_/g, priority: 6 }, // Menor prioridad
  ];

  // Resolución inteligente de conflictos entre patrones superpuestos
  // ... (lógica completa en el código fuente)
};
```

#### Renderizado en Vue Template

```vue
<template v-if="hasTextFormatting(post.description)">
  <template
    v-for="(part, index) in processTextFormatting(post.description)"
    :key="index"
  >
    <!-- Texto normal -->
    <span v-if="part.type === 'text'" class="whitespace-pre-line">{{
      part.content
    }}</span>

    <!-- Negrita -->
    <strong v-else-if="part.type === 'bold'" class="font-bold text-gray-900">
      {{ part.content }}
    </strong>

    <!-- Cursiva -->
    <em v-else-if="part.type === 'italic'" class="italic text-gray-800">
      {{ part.content }}
    </em>

    <!-- Código -->
    <code
      v-else-if="part.type === 'code'"
      class="px-1.5 py-0.5 text-xs font-mono bg-gray-100 text-gray-800 rounded border"
    >
      {{ part.content }}
    </code>

    <!-- Tachado -->
    <span
      v-else-if="part.type === 'strikethrough'"
      class="line-through text-gray-500"
    >
      {{ part.content }}
    </span>

    <!-- Badge -->
    <span
      v-else-if="part.type === 'badge'"
      class="inline-flex px-2 py-0.5 ml-1 text-xs font-medium rounded-full bg-mulbin-100 text-mulbin-800"
    >
      {{ part.content }}
    </span>
  </template>
</template>
```

#### Casos de Uso Combinados

| Entrada Markdown                          | Resultado Visual                        |
| ----------------------------------------- | --------------------------------------- |
| `**Casa nueva** en *excelente* ubicación` | **Casa nueva** en _excelente_ ubicación |
| `Precio ~~$800,000~~ **$750,000**`        | Precio ~~$800,000~~ **$750,000**        |
| `Código `MLS-123` con ((REF-456))`        | Código `MLS-123` con **[REF-456]**      |
| `*Disponible* **inmediatamente**`         | _Disponible_ **inmediatamente**         |
| `**3 recámaras**\n*2 baños completos*`    | **3 recámaras**<br/>_2 baños completos_ |

#### Características Avanzadas

- ✅ **Resolución de conflictos**: Maneja patrones superpuestos inteligentemente
- ✅ **Priorización**: Badges tienen mayor prioridad, seguidos de código, negritas, etc.
- ✅ **Compatibilidad total**: Funciona con saltos de línea y badges existentes
- ✅ **Performance optimizada**: Solo procesa cuando detecta patrones markdown
- ✅ **Seguro**: No usa `v-html`, evitando riesgos XSS
- ✅ **Consistente**: Mismos formatos en títulos y descripciones

#### Beneficios

- ✅ **Mejor legibilidad** para descripciones largas
- ✅ **Preserva formato** de entrada del usuario
- ✅ **Compatible con sistema de badges** existente
- ✅ **Rendimiento optimizado** (solo CSS, sin JS)
- ✅ **Responsivo** mantiene el diseño en diferentes pantallas

---

## 🖼️ Optimización de Avatares

### Preservación de Relación de Aspecto

El componente ahora garantiza que todas las imágenes de avatar mantengan su relación de aspecto original, evitando deformaciones visuales independientemente de las dimensiones del archivo original.

#### Implementación Técnica

```vue
<!-- Avatar del autor del post -->
<img
  :src="post.authorCache.avatar"
  :alt="getAuthorFullName(post.authorCache)"
  class="object-cover w-10 h-10 rounded-full"
/>

<!-- Avatar del creador del hilo -->
<img
  :src="hilo.creatorCache?.avatar || '/default-avatar.png'"
  :alt="getAuthorFullName(hilo.creatorCache)"
  class="object-cover w-12 h-12 rounded-full border-2 border-white ring-2 ring-gray-100 shadow-md"
/>

<!-- Avatar del usuario en composer -->
<img :src="avatar" alt="Mi avatar" class="object-cover w-full h-full" />
```

#### Clase CSS Utilizada

**`object-cover`**: Esta clase CSS de Tailwind CSS aplica `object-fit: cover` que:

- ✅ **Preserva relación de aspecto** - La imagen mantiene sus proporciones originales
- ✅ **Cubre completamente el contenedor** - La imagen llena todo el espacio disponible
- ✅ **Recorta inteligentemente** - Si es necesario, recorta desde el centro
- ✅ **Evita deformaciones** - No estira ni comprime la imagen
- ✅ **Funciona con cualquier resolución** - 800x600, 1920x1080, etc.

#### Casos de Uso

| Dimensiones Originales | Contenedor    | Resultado                                       |
| ---------------------- | ------------- | ----------------------------------------------- |
| 800x600 (4:3)          | 40x40px (1:1) | Imagen recortada desde el centro, mantiene 4:3  |
| 1920x1080 (16:9)       | 40x40px (1:1) | Imagen recortada desde el centro, mantiene 16:9 |
| 400x400 (1:1)          | 40x40px (1:1) | Imagen perfecta, sin recorte                    |
| 1000x500 (2:1)         | 40x40px (1:1) | Imagen recortada desde el centro, mantiene 2:1  |

#### Beneficios

- ✅ **Experiencia visual consistente** - Todos los avatares se ven profesionales
- ✅ **Compatibilidad con cualquier imagen** - Funciona con fotos de perfil reales
- ✅ **Performance optimizada** - No requiere procesamiento adicional
- ✅ **Responsivo** - Mantiene calidad en diferentes tamaños de pantalla
- ✅ **Accesibilidad mejorada** - Textos alt descriptivos para lectores de pantalla

---

## 🔒 Comportamiento Especial para Tipo 'mulbin'

### Restricciones de Funcionalidad

Cuando el tipo de publicación es `'mulbin'`, el componente deshabilita ciertas funcionalidades sociales para proporcionar un comportamiento más restrictivo:

#### Elementos Ocultos para Tipo 'mulbin'

1. **Menú Contextual del Autor**

   ```vue
   <!-- Botón del menú contextual -->
   <button
     v-if="post.type !== 'mulbin'"
     @click="toggleMenuAutor(`post-${getPostId(post)}`)"
     class="..."
   >

   <!-- Menú desplegable del autor -->
   <div
     v-if="post.type !== 'mulbin' && menuAutorAbierto === `post-${getPostId(post)}`"
     class="..."
   >
   ```

2. **Botón de Hilos de Interés**

   ```vue
   <BotonHilosInteres
     v-if="
       post.type !== 'mulbin' &&
       (!esAutorDelPost(post) ||
         (esAutorDelPost(post) && getTotalHilosCountForPost(post) > 0))
     "
     :post="post"
     ...
   />
   ```

3. **Botón de Comentarios**

   ```vue
   <!-- Botón Comentar -->
   <button
     v-if="post.type !== 'mulbin'"
     @click="openComments(post)"
     class="..."
   >

   <!-- Componente de comentarios -->
   <ComentariosPost
     v-if="post.type !== 'mulbin'"
     :post="post"
     ...
   />
   ```

#### Funcionalidades Mantenidas para Tipo 'mulbin'

- ✅ **Visualización del contenido** (título, descripción, imágenes)
- ✅ **Detalles del inmueble** (si tiene inmueble relacionado)
- ✅ **Botón de favoritos** (funcionalidad completa)
- ✅ **Información del autor** (sin menú contextual)
- ✅ **Badge de tipo de publicación**

#### Casos de Uso del Tipo 'mulbin'

El tipo `'mulbin'` está diseñado para publicaciones que requieren:

- **Visualización sin interacción social**: Mostrar información sin permitir comentarios o hilos
- **Contenido corporativo**: Publicaciones oficiales que no requieren conversaciones
- **Anuncios especiales**: Contenido promocional sin funcionalidades sociales
- **Información restringida**: Datos que deben ser visibles pero no discutibles

#### Definición de Tipos

```typescript
export interface PostInmobiliario {
  _id: string;
  type: "inmueble" | "cliente" | "invitacion" | "noticia" | "mulbin";
  // ... resto de propiedades
}

export interface NuevoPost {
  type: "inmueble" | "cliente" | "invitacion" | "noticia" | "mulbin";
  // ... resto de propiedades
}
```

---

**FeedPublicaciones.vue** implementa un ecosistema social inmobiliario completo con conectividad DDP robusta, optimizaciones de performance avanzadas y arquitectura de componentes desacoplados que garantiza escalabilidad y mantenibilidad a largo plazo.
