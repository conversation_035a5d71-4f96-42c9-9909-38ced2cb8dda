<template>
  <div class="bg-white rounded-lg shadow-md">
    <!-- Header con buscador -->
    <div class="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border-b">
      <!-- <PERSON>a de búsqueda de favoritos -->
      <div class="mb-4">
        <BuscadorInmuebles
          v-model="searchQuery"
          placeholder="Buscar en tus favoritos..."
          :input-classes="'py-2 pr-10 pl-10 w-full text-base rounded-lg border border-yellow-300 bg-white focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500'"
          :mostrar-contador="true"
          :total-resultados="favoritosFiltrados.length"
          :texto-resultados="
            favoritosFiltrados.length === 1 ? 'favorito' : 'favoritos'
          "
          @clear="limpiarBusqueda"
        />
      </div>

      <!-- Información de estadísticas -->
      <div class="flex justify-between items-center">
        <div>
          <p class="text-sm text-gray-600">
            <ion-icon name="star" class="mr-1 text-yellow-500"></ion-icon>
            {{ estadisticas.total }} inmuebles guardados
            <span v-if="estadisticas.fechaUltimoAgregado" class="ml-2">
              • Último agregado:
              {{ formatFechaRelativa(estadisticas.fechaUltimoAgregado) }}
            </span>
          </p>
        </div>

        <!-- Acciones rápidas -->
        <div class="flex items-center space-x-2">
          <button
            @click="recargarFavoritos"
            :disabled="loading"
            class="px-3 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50"
            title="Recargar favoritos"
          >
            <ion-icon
              name="refresh-outline"
              :class="loading ? 'animate-spin' : ''"
              class="visible"
            ></ion-icon>
          </button>

          <button
            @click="limpiarTodosFavoritos"
            :disabled="estadisticas.total === 0"
            class="px-3 py-2 text-sm font-medium text-red-600 bg-white rounded-md border border-red-300 hover:bg-red-50 disabled:opacity-50"
            title="Limpiar todos los favoritos"
          >
            <ion-icon name="trash-outline"></ion-icon>
          </button>
        </div>
      </div>

      <!-- Estadísticas por operación -->
      <div
        v-if="Object.keys(estadisticas.porOperacion).length > 0"
        class="flex flex-wrap gap-3 mt-3"
      >
        <div
          v-for="(count, operacion) in estadisticas.porOperacion"
          :key="operacion"
          class="flex items-center px-2 py-1 text-xs font-medium rounded-full"
          :class="getOperacionClass(operacion)"
        >
          <span class="capitalize">{{ operacion }}</span>
          <span
            class="px-1.5 py-0.5 ml-1 bg-white bg-opacity-50 rounded-full"
            >{{ count }}</span
          >
        </div>
      </div>
    </div>

    <!-- Indicador de carga -->
    <div v-if="loading" class="p-8 text-center text-gray-500">
      <div class="flex justify-center">
        <div
          class="w-8 h-8 rounded-full border-4 border-gray-200 animate-spin border-t-yellow-500"
        ></div>
      </div>
      <p class="mt-2">Cargando favoritos...</p>
    </div>

    <!-- Error de carga -->
    <div v-else-if="error" class="p-8 text-center text-red-500">
      <ion-icon name="alert-circle-outline" class="mb-2 text-5xl"></ion-icon>
      <p>{{ error }}</p>
      <button
        @click="recargarFavoritos"
        class="px-4 py-2 mt-4 text-white bg-yellow-600 rounded-lg hover:bg-yellow-700"
      >
        Reintentar
      </button>
    </div>

    <div v-else>
      <!-- Lista de favoritos -->
      <div v-if="favoritosFiltrados.length" class="divide-y divide-gray-100">
        <div
          v-for="inmueble in favoritosFiltrados"
          :key="inmueble.id"
          class="relative p-4 transition-colors cursor-pointer hover:bg-yellow-50"
          @click="verDetalleInmueble(inmueble)"
        >
          <!-- Badge de favorito en la esquina -->
          <div class="absolute right-2 top-11">
            <div
              class="flex items-center px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full"
            >
              <ion-icon name="star" class="mr-1"></ion-icon>
              Favorito
            </div>
          </div>

          <div
            class="flex flex-col space-y-3 lg:flex-row lg:space-y-0 lg:space-x-4"
          >
            <!-- Imagen principal -->
            <div class="flex-shrink-0">
              <img
                :src="
                  inmueble.imagenPrincipal || `${mulbinUrl}/images/no-image.png`
                "
                :alt="inmueble.titulo"
                class="object-cover w-full h-48 rounded-lg lg:w-64 lg:h-40"
              />
              <!-- Badge de tipo de operación -->
              <div class="relative -mt-8 ml-2">
                <span
                  :class="[
                    'inline-block px-2 py-1 text-xs font-bold text-white rounded',
                    inmueble.operacion === 'venta'
                      ? 'bg-green-600'
                      : inmueble.operacion === 'renta'
                      ? 'bg-blue-600'
                      : 'bg-orange-600',
                  ]"
                >
                  {{ inmueble.operacion?.toUpperCase() }}
                </span>
              </div>
            </div>

            <!-- Información del inmueble -->
            <div class="flex-1 min-w-0">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">
                    {{ decodeHtmlEntities(stripTags(inmueble.titulo)) }}
                  </h3>
                  <p class="mt-1 text-sm text-gray-600 line-clamp-2">
                    {{ decodeHtmlEntities(stripTags(inmueble.descripcion)) }}
                  </p>
                </div>

                <!-- Precio -->
                <div class="flex-shrink-0 ml-4 text-right">
                  <p class="text-xl font-bold text-green-600">
                    ${{ formatPrice(inmueble.precio) }}
                  </p>
                  <p
                    v-if="inmueble.operacion === 'renta'"
                    class="text-xs text-gray-500"
                  >
                    / mes
                  </p>
                </div>
              </div>

              <!-- Características del inmueble -->
              <div class="flex flex-wrap gap-4 mt-3 text-sm text-gray-600">
                <span v-if="inmueble.recamaras" class="flex items-center">
                  <ion-icon name="bed-outline" class="mr-1"></ion-icon>
                  {{ inmueble.recamaras }} recámaras
                </span>
                <span v-if="inmueble.banos" class="flex items-center">
                  <ion-icon name="water-outline" class="mr-1"></ion-icon>
                  {{ inmueble.banos }} baños
                </span>
                <span v-if="inmueble.area" class="flex items-center">
                  <ion-icon name="resize-outline" class="mr-1"></ion-icon>
                  {{ inmueble.area }} m²
                </span>
                <span class="flex items-center">
                  <ion-icon name="location-outline" class="mr-1"></ion-icon>
                  {{ inmueble.ubicacion }}
                </span>
              </div>

              <!-- Información del socio propietario -->
              <div
                class="flex justify-between items-center pt-3 mt-3 border-t border-gray-100"
              >
                <div class="flex items-center">
                  <img
                    :src="
                      inmueble.socio.avatar || `${mulbinUrl}/images/avatar.png`
                    "
                    :alt="inmueble.socio.nombre"
                    class="mr-2 w-8 h-8 rounded-full"
                  />
                  <div>
                    <p class="text-sm font-medium text-gray-900">
                      {{ inmueble.socio.nombre }}
                    </p>
                    <p class="text-xs text-gray-500">
                      {{ inmueble.socio.empresa }}
                    </p>
                  </div>
                </div>

                <!-- Acciones rápidas -->
                <div class="flex items-center space-x-2">
                  <button
                    @click.stop="contactarSocio(inmueble.socio, 'whatsapp')"
                    class="p-2 text-green-500 rounded-full hover:bg-green-50"
                    title="WhatsApp"
                  >
                    <ion-icon name="logo-whatsapp" class="text-lg"></ion-icon>
                  </button>
                  <button
                    @click.stop="contactarSocio(inmueble.socio, 'telefono')"
                    class="p-2 text-blue-500 rounded-full hover:bg-blue-50"
                    title="Teléfono"
                  >
                    <ion-icon name="call-outline" class="text-lg"></ion-icon>
                  </button>
                  <button
                    @click.stop="removerDeFavoritos(inmueble)"
                    class="p-2 text-red-500 rounded-full hover:bg-red-50"
                    title="Remover de favoritos"
                  >
                    <ion-icon
                      name="heart-dislike-outline"
                      class="text-lg"
                    ></ion-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Estado vacío -->
      <div
        v-if="!favoritosFiltrados.length"
        class="p-8 text-center text-gray-500"
      >
        <ion-icon
          name="star-outline"
          class="mb-2 text-5xl text-yellow-400"
        ></ion-icon>
        <div v-if="!loading && !error">
          <p class="text-lg font-medium">
            {{
              searchQuery
                ? "No se encontraron favoritos"
                : "No tienes inmuebles favoritos"
            }}
          </p>
          <p class="mt-2 text-sm">
            {{
              searchQuery
                ? "Intenta con otros términos de búsqueda o limpia el filtro"
                : "Marca inmuebles como favoritos desde el listado principal para verlos aquí"
            }}
          </p>
          <button
            v-if="!searchQuery"
            @click="irAExplorarInmuebles"
            class="px-4 py-2 mt-4 text-white bg-yellow-600 rounded-lg hover:bg-yellow-700"
          >
            Buscar inmuebles
          </button>
          <button
            v-else
            @click="limpiarBusqueda"
            class="px-4 py-2 mt-4 text-white bg-yellow-600 rounded-lg hover:bg-yellow-700"
          >
            Limpiar búsqueda
          </button>
        </div>
      </div>

      <!-- Mensaje de advertencia si algunos favoritos no están disponibles -->
      <div
        v-if="idsNoEncontrados.length > 0"
        class="p-4 m-4 bg-yellow-50 rounded-lg border border-yellow-200"
      >
        <div class="flex items-start">
          <ion-icon
            name="warning-outline"
            class="mt-0.5 mr-2 text-yellow-600"
          ></ion-icon>
          <div>
            <p class="text-sm font-medium text-yellow-800">
              Algunos favoritos ya no están disponibles
            </p>
            <p class="mt-1 text-xs text-yellow-700">
              {{ idsNoEncontrados.length }} inmuebles favoritos pueden haber
              sido eliminados o ya no estar publicados.
            </p>
            <button
              @click="limpiarFavoritosObsoletos"
              class="px-3 py-1 mt-2 text-xs font-medium text-yellow-700 bg-yellow-100 rounded hover:bg-yellow-200"
            >
              Limpiar favoritos obsoletos
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de detalle de inmueble -->
    <DetalleInmuebleModal
      v-model="showDetalleModal"
      :inmueble="inmuebleSeleccionado"
      @contactar="contactarSocio"
    />

    <!-- Modal global para confirmaciones -->
    <GlobalModal />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted } from "vue";
import axios from "axios";
import DetalleInmuebleModal from "./DetalleInmuebleModal.vue";
import GlobalModal from "../ui/GlobalModal.vue";
import BuscadorInmuebles from "../ui/BuscadorInmuebles.vue";
import favoritosService from "../../services/favoritosService";
import { useModal } from "../../composables/useModal";

interface Socio {
  id: string;
  nombre: string;
  empresa: string;
  avatar?: string;
  telefono?: string;
  whatsapp?: string;
  email?: string;
}

interface Inmueble {
  id: string;
  titulo: string;
  descripcion: string;
  precio: number;
  operacion: "venta" | "renta" | "traspaso";
  tipo: string;
  ubicacion: string;
  recamaras?: number;
  banos?: number;
  area?: number;
  imagenPrincipal?: string;
  imagenes: string[];
  socio: Socio;
  esFavorito: boolean;
  fechaCreacion: string;
}

export default defineComponent({
  name: "InmueblesFavoritos",

  components: {
    DetalleInmuebleModal,
    GlobalModal,
    BuscadorInmuebles,
  },

  emits: ["ir-a-filtros"],

  setup(_, { emit }) {
    const mulbinUrl = import.meta.env.VITE_MULBIN_URL;
    const { confirm: showConfirm, success: showSuccess } = useModal();

    const inmueblesFavoritos = ref<Inmueble[]>([]);
    const loading = ref(false);
    const error = ref<string | null>(null);
    const idsNoEncontrados = ref<string[]>([]);

    // Modal de detalle
    const showDetalleModal = ref(false);
    const inmuebleSeleccionado = ref<Inmueble | null>(null);

    // Búsqueda en favoritos
    const searchQuery = ref("");

    // 🔄 ESTADÍSTICAS DE FAVORITOS (REACTIVO)
    // Usar ref() en lugar de computed() porque los datos vienen de localStorage (externo)
    const estadisticas = ref(favoritosService.getEstadisticas());

    // 📡 SUSCRIPCIÓN A CAMBIOS EN FAVORITOS
    // Variable para almacenar la función de desuscripción
    let unsubscribeFavoritos: (() => void) | null = null;

    // Función para normalizar texto (eliminar acentos)
    const normalizeText = (text: string): string => {
      return text
        .normalize("NFD") // Normalizar con descomposición canónica
        .replace(/[\u0300-\u036f]/g, "") // Eliminar diacríticos
        .toLowerCase(); // Convertir a minúsculas
    };

    // Favoritos filtrados por búsqueda en tiempo real
    const favoritosFiltrados = computed(() => {
      // Normalizar el texto de búsqueda
      const normalizedQuery = normalizeText(searchQuery.value);

      // Si no hay búsqueda, mostrar todos los favoritos
      if (!normalizedQuery) {
        return inmueblesFavoritos.value;
      }

      // Filtrar favoritos por búsqueda de texto en tiempo real
      return inmueblesFavoritos.value.filter((inmueble) => {
        // Normalizar los textos del inmueble
        const normalizedTitulo = normalizeText(inmueble.titulo);
        const normalizedDescripcion = normalizeText(inmueble.descripcion);
        const normalizedUbicacion = normalizeText(inmueble.ubicacion);
        const normalizedTipo = normalizeText(inmueble.tipo);
        const normalizedSocioNombre = normalizeText(inmueble.socio.nombre);
        const normalizedSocioEmpresa = normalizeText(inmueble.socio.empresa);

        // Realizar la búsqueda en los textos normalizados
        return (
          normalizedTitulo.includes(normalizedQuery) ||
          normalizedDescripcion.includes(normalizedQuery) ||
          normalizedUbicacion.includes(normalizedQuery) ||
          normalizedTipo.includes(normalizedQuery) ||
          normalizedSocioNombre.includes(normalizedQuery) ||
          normalizedSocioEmpresa.includes(normalizedQuery)
        );
      });
    });

    // Función para cargar favoritos desde el backend
    const cargarFavoritos = async () => {
      loading.value = true;
      error.value = null;
      idsNoEncontrados.value = [];

      try {
        // Obtener IDs desde localStorage
        const favoritosIds = favoritosService.getFavoritosIds();

        if (favoritosIds.length === 0) {
          inmueblesFavoritos.value = [];
          return;
        }

        // Llamada al endpoint de favoritos
        const response = await axios.get(
          `/msi-v5/owner/inmuebles/socios/favoritos`,
          {
            params: {
              ids: favoritosIds.join(","),
            },
          }
        );

        if (response.data && response.data.statusCode === 200) {
          inmueblesFavoritos.value = response.data.data.inmuebles || [];
          idsNoEncontrados.value = response.data.data.ids_no_encontrados || [];

          console.log("⭐ Favoritos cargados exitosamente:", {
            total_solicitados: response.data.data.total_solicitados,
            total_encontrados: response.data.data.total,
            ids_no_encontrados: idsNoEncontrados.value,
          });
        } else {
          error.value = "Error al cargar los inmuebles favoritos";
        }
      } catch (err: any) {
        console.error("Error fetching favoritos:", err);

        if (err.response?.status === 403) {
          // Sesión perdida, redirigir al login
          window.location.href = "/";
          return;
        }

        if (err.response?.data?.data?.error) {
          error.value = err.response.data.data.error;
        } else {
          error.value =
            "No se pudieron cargar los inmuebles favoritos. Por favor intenta nuevamente.";
        }
      } finally {
        loading.value = false;
      }
    };

    // Recargar favoritos
    const recargarFavoritos = () => {
      cargarFavoritos();
    };

    // Ver detalle de inmueble
    const verDetalleInmueble = (inmueble: Inmueble) => {
      inmuebleSeleccionado.value = inmueble;
      showDetalleModal.value = true;
    };

    // Remover de favoritos
    const removerDeFavoritos = (inmueble: Inmueble) => {
      const exito = favoritosService.removerFavorito(inmueble.id);

      if (exito) {
        // Remover de la lista local
        inmueblesFavoritos.value = inmueblesFavoritos.value.filter(
          (item) => item.id !== inmueble.id
        );

        console.log(`🗑️ Inmueble ${inmueble.id} removido de favoritos`);

        // 🔄 NOTA: Las estadísticas se actualizan automáticamente
        // gracias al sistema de eventos en favoritosService.onChange()
      } else {
        console.warn("⚠️ No se pudo remover el inmueble de favoritos");
      }
    };

    // Limpiar todos los favoritos
    const limpiarTodosFavoritos = () => {
      showConfirm(
        "¿Estás seguro de que quieres limpiar todos los favoritos?",
        () => {
          favoritosService.limpiarFavoritos();
          inmueblesFavoritos.value = [];
          idsNoEncontrados.value = [];
          console.log("🧹 Todos los favoritos han sido limpiados");

          showSuccess(
            "Todos los favoritos han sido eliminados correctamente.",
            "¡Favoritos limpiados!"
          );
        },
        "Confirmar eliminación"
      );
    };

    // Limpiar solo favoritos obsoletos
    const limpiarFavoritosObsoletos = () => {
      const totalObsoletos = idsNoEncontrados.value.length;

      showConfirm(
        `¿Quieres eliminar ${totalObsoletos} inmueble${
          totalObsoletos > 1 ? "s" : ""
        } que ya no está${totalObsoletos > 1 ? "n" : ""} disponible${
          totalObsoletos > 1 ? "s" : ""
        }?`,
        () => {
          // Remover del localStorage los IDs que no se encontraron
          idsNoEncontrados.value.forEach((id) => {
            favoritosService.removerFavorito(id);
          });

          idsNoEncontrados.value = [];
          console.log("🧹 Favoritos obsoletos limpiados");

          showSuccess(
            `${totalObsoletos} inmueble${
              totalObsoletos > 1 ? "s" : ""
            } obsoleto${totalObsoletos > 1 ? "s" : ""} eliminado${
              totalObsoletos > 1 ? "s" : ""
            } de favoritos.`,
            "¡Favoritos actualizados!"
          );
        },
        "Limpiar favoritos obsoletos"
      );
    };

    // Contactar socio
    const contactarSocio = (socio: Socio, metodo: string) => {
      if (metodo === "whatsapp" && socio.whatsapp) {
        const mensaje = encodeURIComponent(
          `Hola ${socio.nombre}, me interesa uno de tus inmuebles.`
        );
        const url = `https://wa.me/${socio.whatsapp}?text=${mensaje}`;
        window.open(url, "_blank");
      } else if (metodo === "telefono" && socio.telefono) {
        window.open(`tel:${socio.telefono}`, "_self");
      } else if (metodo === "email" && socio.email) {
        const asunto = encodeURIComponent("Consulta sobre inmueble");
        const cuerpo = encodeURIComponent(
          `Hola ${socio.nombre},\n\nMe interesa obtener más información sobre uno de tus inmuebles.\n\nSaludos.`
        );
        window.open(
          `mailto:${socio.email}?subject=${asunto}&body=${cuerpo}`,
          "_self"
        );
      }
    };

    // Formatear precio
    const formatPrice = (price: number) => {
      return price.toLocaleString("es-MX");
    };

    // Formatear fecha relativa
    const formatFechaRelativa = (fecha: string) => {
      const ahora = new Date();
      const fechaFavorito = new Date(fecha);
      const diffMs = ahora.getTime() - fechaFavorito.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffDias === 0) return "hoy";
      if (diffDias === 1) return "ayer";
      if (diffDias < 7) return `hace ${diffDias} días`;
      if (diffDias < 30) return `hace ${Math.floor(diffDias / 7)} semanas`;
      return `hace ${Math.floor(diffDias / 30)} meses`;
    };

    // Obtener clase CSS para operación
    const getOperacionClass = (operacion: string) => {
      switch (operacion) {
        case "venta":
          return "text-green-700 bg-green-100";
        case "renta":
          return "text-blue-700 bg-blue-100";
        case "traspaso":
          return "text-orange-700 bg-orange-100";
        default:
          return "text-gray-700 bg-gray-100";
      }
    };

    // Limpiar etiquetas HTML de un string
    function stripTags(input: string) {
      return input ? input.replace(/<[^>]*>/g, "") : "";
    }

    // Decodificar entidades HTML
    function decodeHtmlEntities(input: string) {
      if (!input) return "";
      const txt = document.createElement("textarea");
      txt.innerHTML = input;
      return txt.value;
    }

    // Ir a explorar inmuebles (regresa al filtro inteligente)
    const irAExplorarInmuebles = () => {
      emit("ir-a-filtros");
    };

    // Limpiar búsqueda
    const limpiarBusqueda = () => {
      searchQuery.value = "";
    };

    // Cargar favoritos cuando el componente se monta
    onMounted(() => {
      cargarFavoritos();

      // 🆕 NUEVO: Suscribirse a cambios en favoritos para reactividad
      unsubscribeFavoritos = favoritosService.onChange(() => {
        // Actualizar estadísticas
        estadisticas.value = favoritosService.getEstadisticas();

        // 🔄 SINCRONIZAR LISTA: Manejar cambios en favoritos
        const favoritosActuales = favoritosService.getFavoritosIds();
        const inmueblesMostrados = inmueblesFavoritos.value.map((i) => i.id);

        // Detectar inmuebles removidos
        inmueblesFavoritos.value = inmueblesFavoritos.value.filter((inmueble) =>
          favoritosActuales.includes(inmueble.id)
        );

        // 🆕 Detectar inmuebles agregados desde el modal
        const inmuebleAgregado = favoritosActuales.find(
          (id) => !inmueblesMostrados.includes(id)
        );

        if (
          inmuebleAgregado &&
          inmuebleSeleccionado.value?.id === inmuebleAgregado
        ) {
          // El inmueble del modal fue agregado a favoritos, incluirlo en la lista
          const inmuebleParaAgregar = {
            ...inmuebleSeleccionado.value,
            esFavorito: true,
          };

          // Agregar al inicio de la lista
          inmueblesFavoritos.value.unshift(inmuebleParaAgregar);

          console.log(
            `➕ Inmueble ${inmuebleAgregado} agregado a la lista desde modal`
          );
        }

        console.log(
          "🔄 Lista de favoritos sincronizada después de cambio desde modal"
        );
      });
    });

    // Limpiar listeners al desmontar
    onUnmounted(() => {
      // 🆕 NUEVO: Desuscribirse de cambios en favoritos
      if (unsubscribeFavoritos) {
        unsubscribeFavoritos();
        unsubscribeFavoritos = null;
      }
    });

    return {
      inmueblesFavoritos,
      loading,
      error,
      idsNoEncontrados,
      mulbinUrl,
      estadisticas,
      showDetalleModal,
      inmuebleSeleccionado,
      // Búsqueda
      searchQuery,
      favoritosFiltrados,
      limpiarBusqueda,
      // Métodos
      cargarFavoritos,
      recargarFavoritos,
      verDetalleInmueble,
      removerDeFavoritos,
      limpiarTodosFavoritos,
      limpiarFavoritosObsoletos,
      contactarSocio,
      formatPrice,
      formatFechaRelativa,
      getOperacionClass,
      stripTags,
      decodeHtmlEntities,
      irAExplorarInmuebles,
    };
  },
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Transiciones suaves */
.transition-colors {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Efectos hover específicos para favoritos */
.hover\:bg-yellow-50:hover {
  background-color: #fefce8;
}

/* Animación del ícono de pulso */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
