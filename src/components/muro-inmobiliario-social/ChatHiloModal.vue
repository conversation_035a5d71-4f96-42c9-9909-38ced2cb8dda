<template>
  <!-- 🆕 NUEVO: Modal de chat del hilo (COMPONENTE DESACOPLADO) -->
  <div
    v-if="show && hiloSeleccionado"
    class="flex fixed inset-0 z-50 justify-center items-center p-4 bg-black bg-opacity-50"
  >
    <div
      class="overflow-hidden relative w-full max-w-lg bg-white rounded-xl shadow-2xl"
      style="height: 85vh; max-height: 700px"
    >
      <!-- Header del chat FIJO en la parte superior -->
      <div
        class="absolute top-0 right-0 left-0 z-10 p-4 text-white bg-gradient-to-r rounded-t-xl from-mulbin-600 to-mulbin-700"
      >
        <div class="flex justify-between items-center">
          <!-- Info del chat -->
          <div class="flex items-center space-x-3">
            <!-- Avatar del interlocutor -->
            <img
              :src="interlocutorInfo.avatar"
              :alt="interlocutorInfo.name"
              class="w-10 h-10 rounded-full ring-2 ring-white ring-opacity-30"
            />
            <div>
              <h3 class="font-semibold text-white truncate max-w-48">
                {{ hiloSeleccionado.titulo }}
              </h3>
              <p class="text-xs text-blue-100">
                {{ interlocutorInfo.description }}
              </p>
            </div>
          </div>

          <!-- Botones del header -->
          <div class="flex items-center space-x-2">
            <!-- Indicador de estado online -->
            <div class="w-2 h-2 bg-green-400 rounded-full"></div>

            <!-- Botón cerrar -->
            <button
              @click="cerrarChat"
              class="p-1 rounded-full transition-colors hover:bg-white hover:bg-opacity-20"
            >
              <ion-icon name="close-outline" class="text-lg"></ion-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Área de mensajes con scroll suave - ESPACIO DINÁMICO -->
      <div
        ref="chatContainer"
        class="overflow-y-auto absolute right-0 left-0 bg-gray-50 chat-messages-area"
        :style="{
          top: '80px',
          bottom: inputAreaHeight + 'px',
          paddingBottom: '1rem',
        }"
      >
        <!-- Loading indicator -->
        <div v-if="loading" class="flex justify-center items-center p-8">
          <div class="text-center">
            <div
              class="mx-auto w-8 h-8 rounded-full animate-spin border-3 border-mulbin-500 border-t-transparent"
            ></div>
            <p class="mt-3 text-sm text-gray-500">Cargando conversación...</p>
          </div>
        </div>

        <!-- Mensajes -->
        <div v-else class="p-4 space-y-2">
          <!-- Mensaje de bienvenida -->
          <div class="text-center">
            <div
              class="inline-block px-3 py-1 text-xs text-gray-600 bg-gray-200 rounded-full"
            >
              Conversación iniciada
            </div>
          </div>

          <!-- Lista de mensajes agrupados por día -->
          <template v-for="grupo in mensajesAgrupadosPorDia" :key="grupo.fecha">
            <!-- Divisor de fecha -->
            <div class="flex justify-center items-center my-4">
              <div class="flex-1 h-px bg-gray-300"></div>
              <div
                class="px-3 py-1 mx-4 text-xs font-medium text-gray-600 bg-gray-100 rounded-full border border-gray-200 shadow-sm"
              >
                {{ grupo.fechaFormateada }}
              </div>
              <div class="flex-1 h-px bg-gray-300"></div>
            </div>

            <!-- Mensajes del día -->
            <div
              v-for="mensaje in grupo.mensajes"
              :key="mensaje._id"
              :class="{
                'flex justify-end': mensaje.esMensajePropio,
                'flex justify-start': !mensaje.esMensajePropio,
              }"
              class="mb-2 animate-fade-in"
            >
              <div
                :class="{
                  'bg-mulbin-600 text-white shadow-lg': mensaje.esMensajePropio,
                  'bg-white text-gray-900 shadow-md border border-gray-100':
                    !mensaje.esMensajePropio,
                }"
                class="max-w-[75%] px-4 py-3 rounded-2xl relative"
                :style="{
                  'border-bottom-right-radius': mensaje.esMensajePropio
                    ? '6px'
                    : '20px',
                  'border-bottom-left-radius': !mensaje.esMensajePropio
                    ? '6px'
                    : '20px',
                }"
              >
                <!-- Contenido del mensaje -->
                <p class="text-sm leading-relaxed break-words">
                  {{ mensaje.mensaje }}
                </p>

                <!-- Timestamp -->
                <div class="flex justify-end items-center mt-2 space-x-1">
                  <p
                    :class="{
                      'text-blue-200': mensaje.esMensajePropio,
                      'text-gray-500': !mensaje.esMensajePropio,
                    }"
                    class="text-xs"
                  >
                    {{ formatearFechaChat(mensaje.createdAt) }}
                  </p>

                  <!-- Check de leído (solo para mensajes propios) -->
                  <ion-icon
                    v-if="mensaje.esMensajePropio"
                    :name="
                      esLeidoPorOtroUsuario(mensaje)
                        ? 'checkmark-done-outline'
                        : 'checkmark-outline'
                    "
                    :class="{
                      'text-blue-200': !esLeidoPorOtroUsuario(mensaje),
                      'text-blue-100': esLeidoPorOtroUsuario(mensaje),
                    }"
                    class="visible text-xs"
                  ></ion-icon>
                </div>
              </div>
            </div>
          </template>

          <!-- Indicador de escritura (futuro) -->
          <div v-if="false" class="flex justify-start">
            <div
              class="px-4 py-3 bg-white rounded-2xl border border-gray-100 shadow-md"
            >
              <div class="flex space-x-1">
                <div
                  class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                ></div>
                <div
                  class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 0.1s"
                ></div>
                <div
                  class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 0.2s"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Input FIJO en la parte inferior que crece hacia arriba -->
      <div
        ref="inputArea"
        class="absolute right-0 bottom-0 left-0 p-6 bg-white rounded-b-xl border-t border-gray-200 chat-input-area"
      >
        <div class="flex items-end space-x-4">
          <!-- Campo de texto expandible -->
          <div class="relative flex-1">
            <textarea
              ref="textareaRef"
              v-model="nuevoMensaje"
              placeholder="Escribe un mensaje..."
              class="px-4 py-3 pr-12 w-full rounded-2xl border border-gray-300 transition-all resize-none focus:border-mulbin-500 focus:ring-2 focus:ring-mulbin-200 focus:outline-none"
              rows="1"
              style="max-height: 120px; min-height: 44px"
              @keydown="handleKeydown"
              @input="autoResize"
              :disabled="enviandoMensaje"
            ></textarea>

            <!-- Contador de caracteres -->
            <div class="absolute bottom-1 right-3 mb-1 text-xs text-gray-400">
              {{ nuevoMensaje.length }}/500
            </div>
          </div>

          <!-- Botón enviar con animación -->
          <button
            @click="enviarMensaje"
            :disabled="
              !nuevoMensaje.trim() ||
              enviandoMensaje ||
              nuevoMensaje.length > 500
            "
            class="flex flex-shrink-0 justify-center items-center w-12 h-12 rounded-2xl transition-all transform"
            :class="{
              'bg-mulbin-600 hover:bg-mulbin-700 text-white hover:scale-105 shadow-lg':
                nuevoMensaje.trim() &&
                !enviandoMensaje &&
                nuevoMensaje.length <= 500,
              'bg-gray-200 text-gray-400 cursor-not-allowed':
                !nuevoMensaje.trim() ||
                enviandoMensaje ||
                nuevoMensaje.length > 500,
            }"
          >
            <ion-icon
              v-if="enviandoMensaje"
              name="reload-outline"
              class="text-lg animate-spin"
            ></ion-icon>
            <ion-icon v-else name="send" class="text-lg"></ion-icon>
          </button>
        </div>

        <!-- Indicador de límite de caracteres -->
        <div v-if="nuevoMensaje.length > 450" class="mt-2 text-center">
          <p
            :class="{
              'text-orange-500':
                nuevoMensaje.length > 450 && nuevoMensaje.length <= 500,
              'text-red-500': nuevoMensaje.length > 500,
            }"
            class="text-xs"
          >
            {{ nuevoMensaje.length }}/500 caracteres
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  onUnmounted,
  nextTick,
  watch,
  computed,
} from "vue";
import inmobiliarioService from "../../services/inmobiliarioService";
import ddpService from "../../services/ddpService";

export default defineComponent({
  name: "ChatHiloModal",

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    hiloSeleccionado: {
      type: Object,
      default: null,
    },
    token: {
      type: String,
      required: true,
    },
  },

  emits: ["close", "error", "success"],

  setup(props, { emit }) {
    // Estados del chat
    const loading = ref(false);
    const mensajesHilo = ref<any[]>([]);
    const nuevoMensaje = ref("");
    const enviandoMensaje = ref(false);
    const chatContainer = ref<HTMLElement | null>(null);
    const textareaRef = ref<HTMLTextAreaElement | null>(null);
    const inputArea = ref<HTMLElement | null>(null);
    const inputAreaHeight = ref(88); // Altura inicial del área de input

    // 🆕 NUEVO: Estado reactivo para userId actual
    const currentUserId = ref<string | null>(null);
    let unsubscribeAuthChange: (() => void) | null = null;

    // 🆕 NUEVO: Función para actualizar userId reactivo
    const updateCurrentUserId = () => {
      currentUserId.value = ddpService.getCurrentUserId();
    };

    // 🆕 NUEVO: Computed properties para optimizar template
    const mensajesConMetadata = computed(() => {
      return mensajesHilo.value.map((mensaje) => ({
        ...mensaje,
        esMensajePropio: mensaje.authorId === currentUserId.value,
      }));
    });

    // 🆕 NUEVO: Función para obtener fecha sin hora (para agrupación)
    const obtenerFechaSinHora = (fecha: string | Date): string => {
      const d = new Date(fecha);
      return d.toDateString(); // Formato: "Mon Dec 25 2023"
    };

    // 🆕 NUEVO: Función para formatear divisor de fecha (más legible que timestamp)
    const formatearDivisorFecha = (fecha: string | Date): string => {
      const d = new Date(fecha);
      const ahora = new Date();

      // Crear fechas sin componente de tiempo para comparación
      const fechaMensaje = new Date(d.getFullYear(), d.getMonth(), d.getDate());
      const fechaHoy = new Date(
        ahora.getFullYear(),
        ahora.getMonth(),
        ahora.getDate()
      );

      const diferenciaDias = Math.floor(
        (fechaHoy.getTime() - fechaMensaje.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Hoy
      if (diferenciaDias === 0) {
        return "Hoy";
      }

      // Ayer
      if (diferenciaDias === 1) {
        return "Ayer";
      }

      // Día de la semana si es de esta semana (últimos 6 días)
      if (diferenciaDias <= 6) {
        return d.toLocaleDateString("es-MX", { weekday: "long" });
      }

      // Fecha completa para mensajes más antiguos
      return d.toLocaleDateString("es-MX", {
        weekday: "long",
        day: "numeric",
        month: "long",
        year: diferenciaDias > 365 ? "numeric" : undefined, // Solo año si es de otro año
      });
    };

    // 🆕 NUEVO: Computed property para agrupar mensajes por día
    const mensajesAgrupadosPorDia = computed(() => {
      const mensajes = mensajesConMetadata.value;
      if (mensajes.length === 0) return [];

      const grupos: Array<{
        fecha: string;
        fechaFormateada: string;
        mensajes: any[];
      }> = [];

      let grupoActual: {
        fecha: string;
        fechaFormateada: string;
        mensajes: any[];
      } | null = null;

      mensajes.forEach((mensaje) => {
        const fechaSinHora = obtenerFechaSinHora(mensaje.createdAt);

        // Si no hay grupo actual o es un día diferente, crear nuevo grupo
        if (!grupoActual || grupoActual.fecha !== fechaSinHora) {
          grupoActual = {
            fecha: fechaSinHora,
            fechaFormateada: formatearDivisorFecha(mensaje.createdAt),
            mensajes: [],
          };
          grupos.push(grupoActual);
        }

        // Agregar mensaje al grupo actual
        grupoActual.mensajes.push(mensaje);
      });

      return grupos;
    });

    // 🆕 NUEVO: Computed para determinar el interlocutor
    const interlocutorInfo = computed(() => {
      if (!props.hiloSeleccionado || !currentUserId.value) {
        return {
          avatar: "/default-avatar.png",
          name: "Usuario",
          description: "Chat",
        };
      }

      const esCreador =
        props.hiloSeleccionado.creatorId === currentUserId.value;

      if (esCreador) {
        // Si soy el creador, el interlocutor es el autor del post
        const cache = props.hiloSeleccionado.postAuthorCache;
        const name = getAuthorFullName(cache);
        const referenciaPrivada = props.hiloSeleccionado.referenciaPrivada;

        return {
          avatar: cache?.avatar || "/default-avatar.png",
          name,
          description: referenciaPrivada
            ? `Hilo con ${name} › ${referenciaPrivada}`
            : `Hilo con ${name}`,
        };
      } else {
        // Si no soy el creador, el interlocutor es el creador del hilo
        const cache = props.hiloSeleccionado.creatorCache;
        const name = getAuthorFullName(cache);

        return {
          avatar: cache?.avatar || "/default-avatar.png",
          name,
          description: `Hilo con ${name}`,
        };
      }
    });

    // Control de subscriptions reactivas
    let unsubscribeFromChatMessages: (() => void) | null = null;
    const chatMessageSubscriptionActive = ref(false);

    // 🆕 NUEVO: Constantes para localStorage
    const CHAT_WINDOWS_KEY = "mulbin_chat_windows_open";

    // 🆕 NUEVO: Métodos para gestionar marcadores en localStorage
    const getChatWindowsFromStorage = (): string[] => {
      try {
        const stored = localStorage.getItem(CHAT_WINDOWS_KEY);
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        console.warn("⚠️ Error leyendo chat windows del localStorage:", error);
        return [];
      }
    };

    const saveChatWindowsToStorage = (chatWindows: string[]): void => {
      try {
        localStorage.setItem(CHAT_WINDOWS_KEY, JSON.stringify(chatWindows));
        console.log("💾 Chat windows guardados en localStorage:", chatWindows);
      } catch (error) {
        console.error(
          "❌ Error guardando chat windows en localStorage:",
          error
        );
      }
    };

    const addChatWindowMarker = (hiloId: string): void => {
      if (!hiloId) return;

      const currentWindows = getChatWindowsFromStorage();
      if (!currentWindows.includes(hiloId)) {
        currentWindows.push(hiloId);
        saveChatWindowsToStorage(currentWindows);
        console.log(`✅ Marcador agregado para hilo ${hiloId}`);
      }
    };

    const removeChatWindowMarker = (hiloId: string): void => {
      if (!hiloId) return;

      const currentWindows = getChatWindowsFromStorage();
      const filteredWindows = currentWindows.filter((id) => id !== hiloId);

      if (filteredWindows.length !== currentWindows.length) {
        saveChatWindowsToStorage(filteredWindows);
        console.log(`🗑️ Marcador removido para hilo ${hiloId}`);
      }
    };

    const isChatWindowOpen = (hiloId: string): boolean => {
      if (!hiloId) return false;

      const currentWindows = getChatWindowsFromStorage();
      const isOpen = currentWindows.includes(hiloId);
      console.log(
        `🔍 Verificando si ventana está abierta para hilo ${hiloId}: ${isOpen}`
      );
      return isOpen;
    };

    // 🔧 MODIFICADO: Limpiar marcadores huérfanos al inicializar
    const cleanupOrphanedMarkers = (): void => {
      try {
        const currentWindows = getChatWindowsFromStorage();
        if (currentWindows.length > 0) {
          console.log(
            `🧹 Limpiando ${currentWindows.length} marcadores huérfanos al inicializar`
          );
          saveChatWindowsToStorage([]);
        }
      } catch (error) {
        console.warn("⚠️ Error limpiando marcadores huérfanos:", error);
      }
    };

    // 🆕 Calcular altura dinámica del área de input
    const calcularAlturaInput = () => {
      if (inputArea.value) {
        const altura = inputArea.value.offsetHeight;
        inputAreaHeight.value = altura;
      }
    };

    // 🔧 HELPER: Obtener ID del hilo de forma robusta
    const getHiloId = (hilo: any): string | null => {
      if (!hilo) return null;

      // Intentar diferentes campos posibles
      const id = hilo._id || hilo.id || hilo.hiloId;

      if (!id) {
        console.error("❌ No se pudo obtener ID del hilo:", hilo);
        return null;
      }

      return String(id);
    };

    // 🆕 Métodos principales del chat

    // 🔧 MODIFICADO: Inicializar chat con marcador en localStorage
    const inicializarChat = async () => {
      console.log("🔍 DEBUG: props.hiloSeleccionado =", props.hiloSeleccionado);
      console.log(
        "🔍 DEBUG: props.hiloSeleccionado._id =",
        props.hiloSeleccionado?._id
      );
      console.log("🔍 DEBUG: props.token =", props.token);
      console.log("🔍 DEBUG: props.token length =", props.token?.length);

      if (!props.hiloSeleccionado || !props.token) {
        console.error(
          "❌ No hay hilo seleccionado o token para inicializar chat"
        );
        console.error("❌ DEBUG: hiloSeleccionado exists =", !!props.hiloSeleccionado);
        console.error("❌ DEBUG: token exists =", !!props.token);
        return;
      }

      try {
        loading.value = true;

        const hiloId = getHiloId(props.hiloSeleccionado);

        if (!hiloId) {
          console.error("❌ ERROR CRÍTICO: No se pudo obtener ID del hilo");
          emit("error", "Error: El hilo no tiene identificador válido");
          return;
        }

        // 🆕 NUEVO: Verificar si la ventana ya está abierta
        if (isChatWindowOpen(hiloId)) {
          console.warn(
            `⚠️ La ventana de chat para el hilo ${hiloId} ya está abierta`
          );
          emit("error", "Esta conversación ya está abierta en otra ventana");
          return;
        }

        // 🆕 NUEVO: Agregar marcador de ventana abierta
        addChatWindowMarker(hiloId);

        console.log(
          `💬 Inicializando chat REACTIVO para hilo: ${props.hiloSeleccionado.titulo}`
        );

        // 🔧 SIGUIENDO PRINCIPIOS METEOR-DDP-REACTIVIDAD-GUIA.md
        await inmobiliarioService.connectWithToken(props.token);

        // 1. Suscribirse REACTIVAMENTE a mensajes del hilo (UNA SOLA VEZ)
        console.log("🔍 DEBUG: Intentando suscribirse con hiloId:", hiloId);
        console.log("🔍 DEBUG: Hilo completo:", props.hiloSeleccionado);

        await inmobiliarioService.subscribeToThreadMessages(hiloId);
        chatMessageSubscriptionActive.value = true;

        // 2. Obtener estado inicial (UNA SOLA VEZ)
        const mensajesIniciales = inmobiliarioService.getThreadMessages(hiloId);
        mensajesHilo.value = mensajesIniciales;

        // 3. 🆕 MARCAR MENSAJES COMO LEÍDOS al abrir el chat
        try {
          await inmobiliarioService.markMessagesAsRead(hiloId);
          console.log(
            `✅ Mensajes marcados como leídos para hilo: ${props.hiloSeleccionado.titulo}`
          );
        } catch (readError) {
          console.warn("⚠️ Error marcando mensajes como leídos:", readError);
          // No bloquear la inicialización por este error
        }

        // 4. Configurar OBSERVADOR REACTIVO para cambios futuros
        unsubscribeFromChatMessages =
          inmobiliarioService.onThreadMessagesChange(
            hiloId,
            (mensajesActualizados) => {
              mensajesHilo.value = mensajesActualizados;
              setTimeout(scrollToBottom, 100);
            }
          );

        // 5. Scroll automático al final después de renderizado
        await nextTick();
        setTimeout(() => {
          calcularAlturaInput();
          scrollToBottom();

          // 🎯 FOCUS automático al cargar el chat
          if (textareaRef.value) {
            textareaRef.value.focus();
          }
        }, 100);

        console.log(
          `✅ Chat inicializado REACTIVAMENTE con ${mensajesIniciales.length} mensajes y marcador en localStorage`
        );
      } catch (error) {
        console.error("❌ Error inicializando chat:", error);

        // 🆕 NUEVO: Remover marcador si hay error en la inicialización
        if (props.hiloSeleccionado?._id) {
          removeChatWindowMarker(props.hiloSeleccionado._id);
        }

        emit("error", "Error al inicializar el chat");
      } finally {
        loading.value = false;
      }
    };

    // Enviar mensaje SIGUIENDO PRINCIPIOS REACTIVOS
    const enviarMensaje = async () => {
      if (!props.hiloSeleccionado || !nuevoMensaje.value.trim()) {
        return;
      }

      try {
        enviandoMensaje.value = true;

        await inmobiliarioService.connectWithToken(props.token);

        // ✅ SOLO enviar mensaje - la subscription se encarga del resto
        const hiloId = getHiloId(props.hiloSeleccionado);
        if (!hiloId) {
          throw new Error("No se pudo obtener ID del hilo");
        }

        await inmobiliarioService.call(
          "hilosInteres.sendMessage",
          hiloId,
          nuevoMensaje.value.trim()
        );

        // 🔧 RESETEAR altura del textarea ANTES de limpiar contenido
        if (textareaRef.value) {
          textareaRef.value.style.height = "44px"; // Min height original
        }

        // Limpiar mensaje
        nuevoMensaje.value = "";

        // 🔄 Recalcular altura del input después del reset
        setTimeout(calcularAlturaInput, 50);

        // 🎯 FOCUS automático en el input después de enviar
        setTimeout(() => {
          if (textareaRef.value) {
            textareaRef.value.focus();
          }
        }, 100);

        // 🚀 Los mensajes se actualizan automáticamente via observer reactivo
        console.log("💬 Mensaje enviado - actualizando via reactividad...");
      } catch (error) {
        console.error("❌ Error enviando mensaje:", error);
        emit("error", "Error al enviar mensaje");
      } finally {
        enviandoMensaje.value = false;
      }
    };

    // 🔧 MODIFICADO: Cerrar chat con limpieza de marcador en localStorage
    const cerrarChat = () => {
      console.log("🚪 Cerrando chat - limpiando recursos...");

      // 🆕 NUEVO: Remover marcador de localStorage ANTES de limpiar recursos
      const hiloId = getHiloId(props.hiloSeleccionado);
      if (hiloId) {
        removeChatWindowMarker(hiloId);
      }

      // 🧹 LIMPIAR subscription reactiva del chat SIGUIENDO GUÍA METEOR
      limpiarRecursos();

      // Emitir evento de cierre al componente padre
      emit("close");
    };

    // 🧹 Limpiar recursos del chat
    const limpiarRecursos = () => {
      if (unsubscribeFromChatMessages) {
        unsubscribeFromChatMessages();
        unsubscribeFromChatMessages = null;
        console.log("✅ Subscription de mensajes de chat limpiada");
      }

      // 🧹 Limpiar estados del chat
      mensajesHilo.value = [];
      nuevoMensaje.value = "";
      chatMessageSubscriptionActive.value = false;
      inputAreaHeight.value = 88; // Reset altura

      // 🔧 Resetear altura del textarea si existe
      if (textareaRef.value) {
        textareaRef.value.style.height = "44px";
      }

      console.log("✅ Recursos del chat limpiados correctamente");
    };

    // 🆕 Métodos de utilidad

    // Scroll automático al final del chat
    const scrollToBottom = () => {
      if (chatContainer.value) {
        chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
      }
    };

    // Helper para obtener nombre completo del autor
    const getAuthorFullName = (authorCache: any) => {
      if (!authorCache) return "Usuario";
      if (authorCache.firstName || authorCache.lastName) {
        return `${authorCache.firstName || ""} ${
          authorCache.lastName || ""
        }`.trim();
      }
      return authorCache.name || "Usuario";
    };

    // Formatear fecha para chat (como WhatsApp/Telegram)
    const formatearFechaChat = (fecha: string | Date): string => {
      const d = new Date(fecha);
      const ahora = new Date();

      // Crear fechas sin componente de tiempo para comparación
      const fechaMensaje = new Date(d.getFullYear(), d.getMonth(), d.getDate());
      const fechaHoy = new Date(
        ahora.getFullYear(),
        ahora.getMonth(),
        ahora.getDate()
      );
      const fechaAyer = new Date(fechaHoy);
      fechaAyer.setDate(fechaAyer.getDate() - 1);

      const diferenciaDias = Math.floor(
        (fechaHoy.getTime() - fechaMensaje.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Solo hora si es de hoy
      if (diferenciaDias === 0) {
        return d.toLocaleTimeString("es-MX", {
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      // "Ayer" + hora si es de ayer
      if (diferenciaDias === 1) {
        const hora = d.toLocaleTimeString("es-MX", {
          hour: "2-digit",
          minute: "2-digit",
        });
        return `Ayer ${hora}`;
      }

      // Día de la semana + hora si es de esta semana (últimos 6 días)
      if (diferenciaDias <= 6) {
        const nombreDia = d.toLocaleDateString("es-MX", {
          weekday: "short",
        });
        const hora = d.toLocaleTimeString("es-MX", {
          hour: "2-digit",
          minute: "2-digit",
        });
        return `${nombreDia} ${hora}`;
      }

      // Fecha completa + hora para mensajes más antiguos
      const fechaFormateada = d.toLocaleDateString("es-MX", {
        day: "2-digit",
        month: "short",
        year: diferenciaDias > 365 ? "numeric" : undefined, // Solo año si es de otro año
      });
      const hora = d.toLocaleTimeString("es-MX", {
        hour: "2-digit",
        minute: "2-digit",
      });

      return `${fechaFormateada} ${hora}`;
    };

    // Manejar teclas en textarea del chat
    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();
        enviarMensaje();
      }
    };

    // Auto-resize del textarea CON recálculo de altura
    const autoResize = (event: Event) => {
      const textarea = event.target as HTMLTextAreaElement;
      textarea.style.height = "auto";
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px";

      // 🔄 Recalcular altura del área de input
      setTimeout(calcularAlturaInput, 10);
    };

    // ✅ Verificar si un mensaje ha sido leído por el otro usuario (OPTIMIZADO)
    const esLeidoPorOtroUsuario = (mensaje: any): boolean => {
      if (!mensaje.readBy || !Array.isArray(mensaje.readBy)) {
        return false;
      }

      if (!currentUserId.value || mensaje.authorId !== currentUserId.value) {
        return false; // Solo verificar para mensajes propios
      }

      // Buscar si hay algún userId en readBy que no sea el autor del mensaje
      return mensaje.readBy.some(
        (userId: string) => userId !== currentUserId.value
      );
    };

    // 🔄 Watcher para cambios en props
    watch(
      () => props.show,
      (newShow) => {
        if (newShow && props.hiloSeleccionado) {
          inicializarChat();
        } else if (!newShow) {
          // 🆕 NUEVO: Remover marcador al ocultar modal
          const hiloId = getHiloId(props.hiloSeleccionado);
          if (hiloId) {
            removeChatWindowMarker(hiloId);
          }
          limpiarRecursos();
        }
      }
    );

    // 🔧 MODIFICADO: Lifecycle hooks con sistema reactivo de autenticación
    onMounted(() => {
      // 🆕 NUEVO: Configurar sistema reactivo de autenticación
      updateCurrentUserId(); // Estado inicial
      unsubscribeAuthChange = ddpService.onAuthChange(() => {
        updateCurrentUserId(); // Actualizar cuando cambie la autenticación
      });

      // 🆕 NUEVO: Limpiar marcadores huérfanos al montar el componente
      cleanupOrphanedMarkers();

      // Inicializar si el modal ya está visible
      if (props.show && props.hiloSeleccionado) {
        inicializarChat();
      }

      // 🆕 NUEVO: Agregar listener para cierre de ventana/pestaña
      window.addEventListener("beforeunload", handleBeforeUnload);
    });

    // 🔧 MODIFICADO: Limpiar al desmontar con sistema reactivo
    onUnmounted(() => {
      console.log("🧹 Limpiando recursos del componente ChatHiloModal...");

      // 🆕 NUEVO: Limpiar listener de autenticación
      if (unsubscribeAuthChange) {
        unsubscribeAuthChange();
      }

      // 🆕 NUEVO: Asegurar que se remueva el marcador al desmontar
      const hiloId = getHiloId(props.hiloSeleccionado);
      if (hiloId) {
        removeChatWindowMarker(hiloId);
      }

      limpiarRecursos();

      // 🆕 NUEVO: Remover listener para cierre de ventana/pestaña
      window.removeEventListener("beforeunload", handleBeforeUnload);

      console.log("✅ Recursos del ChatHiloModal limpiados correctamente");
    });

    // 🆕 NUEVO: Listener para detectar cierre de ventana/pestaña
    const handleBeforeUnload = () => {
      const hiloId = getHiloId(props.hiloSeleccionado);
      if (hiloId) {
        removeChatWindowMarker(hiloId);
      }
    };

    return {
      // Estados
      loading,
      mensajesHilo,
      nuevoMensaje,
      enviandoMensaje,
      chatContainer,
      textareaRef,
      inputArea,
      inputAreaHeight,
      chatMessageSubscriptionActive,

      // Métodos principales
      inicializarChat,
      enviarMensaje,
      cerrarChat,
      calcularAlturaInput,
      getHiloId,

      // 🆕 NUEVO: Métodos de localStorage
      getChatWindowsFromStorage,
      addChatWindowMarker,
      removeChatWindowMarker,
      isChatWindowOpen,
      cleanupOrphanedMarkers,

      // Métodos de utilidad
      scrollToBottom,
      getAuthorFullName,
      formatearFechaChat,
      handleKeydown,
      autoResize,
      esLeidoPorOtroUsuario,

      // 🆕 NUEVO: Computed properties
      mensajesConMetadata,
      mensajesAgrupadosPorDia,
      interlocutorInfo,
    };
  },
});
</script>

<style scoped>
/* 🆕 NUEVO: Layout absoluto para el chat */
.chat-messages-area {
  /* El área de mensajes se ajusta dinámicamente entre header e input */
  transition: bottom 0.2s ease;
}

.chat-input-area {
  /* El input está fijo en el bottom y puede crecer hacia arriba */
  min-height: 88px; /* Altura mínima del área de input */
}

/* 🆕 NUEVO: Animaciones para el chat mejorado */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* 🆕 NUEVO: Scroll suave para el chat */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* 🆕 NUEVO: Transiciones suaves para botones */
button {
  transition: all 0.2s ease;
}

button:active {
  transform: scale(0.98);
}

/* 🆕 NUEVO: Estilo para textarea expandible */
textarea {
  transition: height 0.2s ease;
}

/* 🆕 NUEVO: Estilo para el indicador de escritura */
@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out;
}

/* 🆕 NUEVO: Gradiente para el header del chat */
.bg-gradient-to-r {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

/* 🆕 NUEVO: Efecto de borde para los avatares */
.ring-2 {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

/* 🆕 NUEVO: Estilo para mensajes con forma de burbuja */
.rounded-2xl {
  border-radius: 1rem;
}

/* 🆕 NUEVO: Sombras mejoradas */
.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 🆕 NUEVO: Estilo para el indicador online */
.bg-green-400 {
  background-color: #34d399;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 🆕 NUEVO: Efectos especiales para el scroll del chat */
.bg-gray-50 {
  background: linear-gradient(to bottom, #f9fafb 0%, #f3f4f6 100%);
}

/* 🆕 NUEVO: Estilos para los checks de leído */
.text-blue-100,
.text-blue-200 {
  transition: color 0.3s ease;
}

/* 🆕 NUEVO: Hover effects para elementos interactivos */
.hover\:scale-105:hover {
  transform: scale(1.05);
}

.transition-all {
  transition: all 0.2s ease-in-out;
}

/* 🆕 NUEVO: Estilo para el contador de caracteres */
.text-orange-500 {
  color: #f97316;
}

.text-red-500 {
  color: #ef4444;
}

/* 🆕 NUEVO: Border animations */
.border-3 {
  border-width: 3px;
}

/* 🆕 NUEVO: Flex-shrink para evitar que el botón se encoja */
.flex-shrink-0 {
  flex-shrink: 0;
}
</style>
