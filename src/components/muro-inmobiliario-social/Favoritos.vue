<template>
  <div class="overflow-visible bg-white rounded-lg shadow-md">
    <!-- Header de Favoritos -->
    <div class="p-4 text-white bg-gradient-to-r from-pink-600 to-purple-800">
      <div class="flex items-center justify-between">
        <h2 class="flex items-center text-xl font-bold">
          <ion-icon name="heart" class="mr-2 text-2xl"></ion-icon>
          <span>Mis Favoritos</span>
          <span v-if="totalFavorites > 0" class="ml-2 text-sm opacity-75">
            ({{ totalFavorites }})
          </span>
        </h2>

        <div class="flex space-x-3">
          <!-- Filtros -->
          <button
            @click="showFilters = !showFilters"
            class="flex items-center px-3 py-1 text-sm font-medium text-white rounded-full bg-pink-600"
          >
            <ion-icon name="filter-outline" class="mr-1"></ion-icon>
            <span class="hidden sm:block">Filtrar</span>
          </button>

          <!-- Limpiar favoritos obsoletos -->
          <button
            @click="limpiarFavoritosObsoletos"
            class="flex items-center px-3 py-1 text-sm font-medium text-white rounded-full bg-pink-600"
            :disabled="limpiando"
          >
            <ion-icon
              :name="limpiando ? 'reload-outline' : 'trash-outline'"
              :class="{ 'animate-spin': limpiando }"
              class="mr-1"
            ></ion-icon>
            <span class="hidden sm:block">Limpiar</span>
          </button>
        </div>
      </div>

      <!-- Filtros desplegables -->
      <div
        v-if="showFilters"
        class="p-3 mt-4 rounded-lg bg-white bg-opacity-20"
      >
        <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
          <div>
            <label class="block mb-1 text-xs text-pink-100"
              >Tipo de propiedad</label
            >
            <select
              v-model="filtros.type"
              class="w-full px-2 py-1 text-sm text-gray-800 bg-white rounded"
            >
              <option value="">Todos</option>
              <option value="venta">Venta</option>
              <option value="renta">Renta</option>
              <option value="socio">Busco socio</option>
              <option value="intercambio">Intercambio</option>
            </select>
          </div>
          <div>
            <label class="block mb-1 text-xs text-pink-100">Ubicación</label>
            <select
              v-model="filtros.location"
              class="w-full px-2 py-1 text-sm text-gray-800 bg-white rounded"
            >
              <option value="">Todas</option>
              <option value="norte">Zona Norte</option>
              <option value="sur">Zona Sur</option>
              <option value="este">Zona Este</option>
              <option value="oeste">Zona Oeste</option>
              <option value="centro">Centro</option>
            </select>
          </div>
          <div class="flex items-end">
            <button
              @click="aplicarFiltros"
              class="w-full px-3 py-1 text-sm font-medium text-pink-700 bg-white rounded"
            >
              Aplicar filtros
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading indicator -->
    <div v-if="loading" class="p-10 text-center">
      <div
        class="w-8 h-8 mx-auto border-4 border-pink-500 rounded-full animate-spin border-t-transparent"
      ></div>
      <p class="mt-2 text-gray-500">Cargando favoritos...</p>
    </div>

    <!-- Lista de favoritos -->
    <div v-else-if="favoritos.length > 0" class="divide-y divide-gray-100">
      <div v-for="favorito in favoritos" :key="favorito._id" class="p-4">
        <div class="flex items-start">
          <!-- Avatar del autor -->
          <img
            :src="favorito.post.authorCache.avatar"
            :alt="getAuthorFullName(favorito.post.authorCache)"
            class="w-10 h-10 mr-3 rounded-full"
          />

          <div class="flex-1">
            <div class="flex items-start justify-between">
              <div>
                <!-- Información del autor -->
                <h3 class="font-semibold text-gray-900">
                  {{ getAuthorFullName(favorito.post.authorCache) }}
                </h3>
                <p class="text-xs text-gray-500">
                  Guardado el {{ formatDate(favorito.createdAt) }}
                </p>
              </div>

              <!-- Badge tipo de publicación y botón remover -->
              <div class="flex items-center space-x-2">
                <span
                  :class="[
                    'px-2 py-1 text-xs rounded-full font-medium',
                    favorito.post.type === 'venta'
                      ? 'bg-green-100 text-green-800'
                      : favorito.post.type === 'renta'
                      ? 'bg-blue-100 text-blue-800'
                      : favorito.post.type === 'socio'
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-amber-100 text-amber-800',
                  ]"
                >
                  {{ getTypeLabel(favorito.post.type) }}
                </span>

                <!-- Botón remover de favoritos -->
                <button
                  @click="removerDeFavoritos(favorito.postId)"
                  class="flex items-center justify-center w-6 h-6 text-pink-500 rounded-full hover:text-pink-700 hover:bg-pink-50"
                  title="Remover de favoritos"
                >
                  <ion-icon name="heart" class="text-sm"></ion-icon>
                </button>
              </div>
            </div>

            <!-- Contenido del post -->
            <h4 class="mt-2 font-medium text-gray-900">
              {{ favorito.post.title }}
            </h4>
            <p class="mt-1 text-gray-700">{{ favorito.post.description }}</p>

            <!-- Detalles de la propiedad -->
            <div class="flex flex-wrap gap-2 mt-3 text-sm">
              <span class="inline-flex items-center text-gray-600">
                <ion-icon name="cash-outline" class="mr-1"></ion-icon>
                ${{ formatPrice(favorito.post.price) }}
              </span>
              <span class="inline-flex items-center text-gray-600">
                <ion-icon name="location-outline" class="mr-1"></ion-icon>
                {{ getLocationLabel(favorito.post.location) }}
              </span>
              <span
                v-if="favorito.post.bedrooms"
                class="inline-flex items-center text-gray-600"
              >
                <ion-icon name="bed-outline" class="mr-1"></ion-icon>
                {{ favorito.post.bedrooms }} recámaras
              </span>
              <span
                v-if="favorito.post.bathrooms"
                class="inline-flex items-center text-gray-600"
              >
                <ion-icon name="water-outline" class="mr-1"></ion-icon>
                {{ favorito.post.bathrooms }} baños
              </span>
              <span
                v-if="favorito.post.area"
                class="inline-flex items-center text-gray-600"
              >
                <ion-icon name="resize-outline" class="mr-1"></ion-icon>
                {{ favorito.post.area }} m²
              </span>
            </div>

            <!-- Imágenes (si las hay) -->
            <div
              v-if="favorito.post.images && favorito.post.images.length"
              class="mt-3"
            >
              <div class="grid grid-cols-2 gap-2">
                <img
                  v-for="(image, index) in favorito.post.images.slice(0, 4)"
                  :key="index"
                  :src="image"
                  :alt="`Imagen ${index + 1} de ${favorito.post.title}`"
                  class="object-cover w-full h-32 rounded cursor-pointer"
                  @click="abrirGaleria(favorito.post, index)"
                />
              </div>
              <p
                v-if="favorito.post.images.length > 4"
                class="mt-1 text-xs text-right text-pink-600"
              >
                +{{ favorito.post.images.length - 4 }} imágenes más
              </p>
            </div>

            <!-- Acciones -->
            <div class="flex pt-2 mt-3 text-gray-600 border-t border-gray-100">
              <button
                @click="verPostCompleto(favorito.post)"
                class="flex items-center mr-4 text-pink-600 hover:text-pink-700"
              >
                <ion-icon name="open-outline" class="mr-1"></ion-icon>
                Ver completo
              </button>
              <button
                @click="contactarAutor(favorito.post.authorCache)"
                class="flex items-center mr-4 text-blue-600 hover:text-blue-700"
              >
                <ion-icon name="chatbubble-outline" class="mr-1"></ion-icon>
                Contactar
              </button>
              <button
                @click="compartirPost(favorito.post)"
                class="flex items-center text-gray-600 hover:text-gray-700"
              >
                <ion-icon name="share-social-outline" class="mr-1"></ion-icon>
                Compartir
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estado vacío -->
    <div v-else class="p-10 text-center">
      <ion-icon name="heart-outline" class="text-5xl text-gray-300"></ion-icon>
      <p class="mt-2 text-gray-500">No tienes favoritos guardados</p>
      <p class="text-sm text-gray-400">
        Marca publicaciones como favoritas para verlas aquí
      </p>
    </div>

    <!-- Paginación -->
    <div
      v-if="favoritos.length > 0 && totalPages > 1"
      class="flex items-center justify-between p-4 border-t border-gray-100"
    >
      <p class="text-sm text-gray-500">
        Página {{ currentPage }} de {{ totalPages }} ({{
          totalFavorites
        }}
        favoritos)
      </p>
      <div class="flex space-x-2">
        <button
          @click="paginaAnterior"
          class="px-3 py-1 text-gray-600 border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
          :disabled="currentPage === 1"
        >
          Anterior
        </button>
        <button
          @click="paginaSiguiente"
          class="px-3 py-1 text-gray-600 border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
          :disabled="currentPage === totalPages"
        >
          Siguiente
        </button>
      </div>
    </div>

    <!-- Mensaje de resultado -->
    <div
      v-if="mensajeAccion"
      class="p-3 m-4 rounded"
      :class="
        mensajeExito ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      "
    >
      {{ mensajeAccion }}
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  reactive,
  onMounted,
  onUnmounted,
  computed,
} from "vue";
import inmobiliarioService from "../../services/inmobiliarioService";
import type { FavoritoPost, PostInmobiliario } from "../../types/inmobiliario";

export default defineComponent({
  name: "Favoritos",

  setup() {
    // Estados
    const loading = ref(true);
    const showFilters = ref(false);
    const limpiando = ref(false);
    const favoritos = ref<FavoritoPost[]>([]);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const totalFavorites = ref(0);
    const mensajeAccion = ref("");
    const mensajeExito = ref(true);
    let unsubscribeFromFavorites: (() => void) | null = null;

    // Filtros
    const filtros = reactive({
      type: "",
      location: "",
    });

    // Computed
    const limit = computed(() => 10);

    // Métodos de formateo
    const formatDate = (date: string | Date) => {
      const d = new Date(date);
      return d.toLocaleDateString("es-MX", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    };

    const formatPrice = (price: number) => {
      return price.toLocaleString("es-MX");
    };

    const getTypeLabel = (type: string) => {
      switch (type) {
        case "venta":
          return "Venta";
        case "renta":
          return "Renta";
        case "socio":
          return "Busco socio";
        case "intercambio":
          return "Intercambio";
        default:
          return type;
      }
    };

    const getLocationLabel = (location: string) => {
      switch (location) {
        case "norte":
          return "Zona Norte";
        case "sur":
          return "Zona Sur";
        case "este":
          return "Zona Este";
        case "oeste":
          return "Zona Oeste";
        case "centro":
          return "Centro";
        default:
          return location;
      }
    };

    const getAuthorFullName = (authorCache: any) => {
      if (authorCache.firstName || authorCache.lastName) {
        return `${authorCache.firstName || ""} ${
          authorCache.lastName || ""
        }`.trim();
      }
      return authorCache.name || "Usuario";
    };

    // Cargar favoritos
    const cargarFavoritos = async () => {
      try {
        loading.value = true;

        const options = {
          page: currentPage.value,
          limit: limit.value,
          filters: {
            ...(filtros.type && { type: filtros.type }),
            ...(filtros.location && { location: filtros.location }),
          },
        };

        console.log("⭐ Cargando favoritos con opciones:", options);
        const response = await inmobiliarioService.getFavorites(options);

        favoritos.value = response.favorites;
        totalFavorites.value = response.totalCount;
        currentPage.value = response.currentPage;
        totalPages.value = response.totalPages;

        console.log("✅ Favoritos cargados:", {
          count: favoritos.value.length,
          total: totalFavorites.value,
          page: currentPage.value,
        });
      } catch (error) {
        console.error("❌ Error al cargar favoritos:", error);
        mensajeAccion.value = "Error al cargar favoritos";
        mensajeExito.value = false;
        favoritos.value = [];
      } finally {
        loading.value = false;
      }
    };

    // Aplicar filtros
    const aplicarFiltros = async () => {
      currentPage.value = 1;
      await cargarFavoritos();
    };

    // Paginación
    const paginaAnterior = async () => {
      if (currentPage.value > 1) {
        currentPage.value--;
        await cargarFavoritos();
      }
    };

    const paginaSiguiente = async () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
        await cargarFavoritos();
      }
    };

    // Remover de favoritos
    const removerDeFavoritos = async (postId: string) => {
      try {
        console.log("⭐ Removiendo de favoritos:", postId);
        await inmobiliarioService.toggleFavorite(postId);

        // Actualización optimista
        favoritos.value = favoritos.value.filter(
          (fav) => fav.postId !== postId
        );
        totalFavorites.value = Math.max(0, totalFavorites.value - 1);

        mensajeAccion.value = "Removido de favoritos exitosamente";
        mensajeExito.value = true;

        // Limpiar mensaje después de 3 segundos
        setTimeout(() => {
          mensajeAccion.value = "";
        }, 3000);
      } catch (error) {
        console.error("❌ Error al remover de favoritos:", error);
        mensajeAccion.value = "Error al remover de favoritos";
        mensajeExito.value = false;

        // Recargar favoritos en caso de error
        await cargarFavoritos();
      }
    };

    // Limpiar favoritos obsoletos
    const limpiarFavoritosObsoletos = async () => {
      try {
        limpiando.value = true;
        const result = await inmobiliarioService.cleanupFavorites();

        if (result.cleanedCount > 0) {
          mensajeAccion.value = `${result.cleanedCount} favoritos obsoletos eliminados`;
          mensajeExito.value = true;
          await cargarFavoritos(); // Recargar lista
        } else {
          mensajeAccion.value = "No se encontraron favoritos obsoletos";
          mensajeExito.value = true;
        }

        // Limpiar mensaje después de 3 segundos
        setTimeout(() => {
          mensajeAccion.value = "";
        }, 3000);
      } catch (error) {
        console.error("❌ Error al limpiar favoritos:", error);
        mensajeAccion.value = "Error al limpiar favoritos obsoletos";
        mensajeExito.value = false;
      } finally {
        limpiando.value = false;
      }
    };

    // Acciones de posts
    const verPostCompleto = (post: PostInmobiliario) => {
      // TODO: Implementar navegación al post completo
      console.log("Ver post completo:", post.title);
    };

    const contactarAutor = (autor: any) => {
      // TODO: Implementar contacto directo
      console.log("Contactar autor:", getAuthorFullName(autor));
    };

    const compartirPost = (post: PostInmobiliario) => {
      // TODO: Implementar compartir
      console.log("Compartir post:", post.title);
    };

    const abrirGaleria = (post: PostInmobiliario, index: number) => {
      // TODO: Implementar galería de imágenes
      console.log("Abrir galería:", post.title, "imagen", index);
    };

    // Configurar observer de favoritos
    const configurarObserver = () => {
      if (!unsubscribeFromFavorites) {
        unsubscribeFromFavorites = inmobiliarioService.onFavoritesChange(
          (newFavorites) => {
            console.log(
              "🔄 Favoritos actualizados via observer:",
              newFavorites.length
            );
            // Recargar favoritos cuando hay cambios
            cargarFavoritos();
          }
        );
      }
    };

    // Inicializar
    onMounted(async () => {
      await cargarFavoritos();
      configurarObserver();
    });

    // Limpiar al desmontar
    onUnmounted(() => {
      if (unsubscribeFromFavorites) {
        unsubscribeFromFavorites();
      }
    });

    return {
      // Estados
      loading,
      showFilters,
      limpiando,
      favoritos,
      currentPage,
      totalPages,
      totalFavorites,
      mensajeAccion,
      mensajeExito,
      filtros,

      // Computed
      limit,

      // Métodos
      formatDate,
      formatPrice,
      getTypeLabel,
      getLocationLabel,
      getAuthorFullName,
      cargarFavoritos,
      aplicarFiltros,
      paginaAnterior,
      paginaSiguiente,
      removerDeFavoritos,
      limpiarFavoritosObsoletos,
      verPostCompleto,
      contactarAutor,
      compartirPost,
      abrirGaleria,
    };
  },
});
</script>
