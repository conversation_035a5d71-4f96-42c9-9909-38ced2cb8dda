<template>
  <div class="overflow-visible bg-white rounded-lg shadow-md">
    <!-- Mensaje de error de conexión -->
    <div v-if="connectionError" class="p-4 text-white bg-red-600">
      <div class="flex items-center">
        <ion-icon name="alert-circle" class="mr-2 text-2xl"></ion-icon>
        <div>
          <h3 class="font-bold">Error de conexión</h3>
          <p>
            No se pudo conectar con el servidor. {{ connectionErrorMessage }}
          </p>
        </div>
      </div>
      <button
        @click="retryConnection"
        class="px-4 py-2 mt-2 font-medium text-red-600 bg-white rounded"
      >
        Reintentar
      </button>
    </div>

    <!-- Modal para nueva publicación -->
    <NuevaPublicacionModal
      :show="showNewPostForm && !showMisSocios"
      :token="token"
      :avatar="avatar"
      :textos-audiencia-privada="textosAudienciaPrivada"
      :textos-audiencia-publica="textosAudienciaPublica"
      @close="showNewPostForm = false"
      @post-created="handlePostCreated"
      @add-socio="showAddFriendModal = true"
    />

    <!-- Componente Favoritos -->
    <div v-if="showFavoritos" class="border-b border-gray-200">
      <Favoritos />
    </div>

    <!-- Barra de nuevas notificaciones -->
    <div
      v-if="
        !showMisSocios &&
        !showInmuebles &&
        !showFavoritos &&
        !showFeedHilos &&
        hasNewNotifications
      "
      class="overflow-hidden relative bg-gradient-to-r from-blue-500 border-b border-gray-200 to-mulbin-600"
    >
      <div class="absolute inset-0 bg-white opacity-10"></div>
      <div class="relative px-4 py-3">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-3">
            <div
              class="flex justify-center items-center w-8 h-8 bg-white bg-opacity-20 rounded-full"
            >
              <ion-icon
                name="notifications"
                class="text-lg text-white animate-pulse"
              ></ion-icon>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-white">
                {{
                  newNotificationsCount === 1
                    ? "Nueva publicación disponible"
                    : `${newNotificationsCount} nuevas publicaciones disponibles`
                }}
              </p>
              <p class="text-xs text-blue-100">
                Haz clic en "Ver nuevas" para mostrarlas en tu feed
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="loadNewNotifications"
              :disabled="refreshingNotifications"
              class="flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 bg-white rounded-full transition-colors hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ion-icon
                :name="
                  refreshingNotifications ? 'reload-outline' : 'refresh-outline'
                "
                class="mr-1"
                :class="{ 'animate-spin': refreshingNotifications }"
              ></ion-icon>
              {{ refreshingNotifications ? "Cargando..." : "Ver nuevas" }}
            </button>
            <button
              @click="dismissNotifications"
              class="flex justify-center items-center w-8 h-8 text-white rounded-full transition-colors hover:bg-white hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            >
              <ion-icon name="close-outline" class="text-lg"></ion-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Barra de progreso animada -->
      <div class="absolute right-0 bottom-0 left-0 h-1 bg-white bg-opacity-20">
        <div
          class="h-full bg-white bg-opacity-40 animate-pulse"
          :style="{
            width: `${Math.min((newNotificationsCount / 10) * 100, 100)}%`,
          }"
        ></div>
      </div>
    </div>

    <!-- Feed principal -->
    <div>
      <!-- Barra de herramientas del feed -->
      <div class="px-4 py-2">
        <div class="flex justify-center items-center">
          <div v-if="false" class="hidden">
            <h3 class="flex items-center text-lg font-semibold text-gray-900">
              <ion-icon
                name="newspaper-outline"
                class="mr-2 text-mulbin-600"
              ></ion-icon>
              <span class="hidden xs:block">Feed de Publicaciones</span>
              <!-- 🆕 NUEVO: Botón Refresh Feed -->
              <button
                @click="refreshFeed"
                :disabled="refreshingFeed"
                class="flex items-center px-2 py-1 ml-2 text-sm font-medium text-white bg-gray-300 rounded-full transition-colors hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
                :title="refreshingFeed ? 'Recargando feed...' : 'Recargar feed'"
              >
                <ion-icon
                  :name="refreshingFeed ? 'reload-outline' : 'refresh-outline'"
                  class="visible mr-1"
                  :class="{ 'animate-spin': refreshingFeed }"
                ></ion-icon>
                <span class="hidden sm:block">
                  {{ refreshingFeed ? "Recargando..." : "" }}
                </span>
              </button>
            </h3>
            <p class="text-sm text-gray-600">
              Todas las publicaciones de tus socios
            </p>
          </div>
          <!-- Composer estilo red social que actúa como botón -->
          <div
            @click="showNewPostForm = true"
            class="flex items-center p-3 transition-all hover:scale-[1.01]"
          >
            <!-- Avatar del usuario -->
            <div
              class="flex overflow-hidden flex-shrink-0 justify-center items-center mr-3 w-10 h-10 rounded-full cursor-pointer"
            >
              <img
                :src="avatar"
                alt="Mi avatar"
                class="object-cover w-full h-full"
              />
            </div>

            <!-- Área de "input" falso -->
            <div
              class="flex-1 px-4 py-2 text-sm text-gray-500 bg-gray-50 rounded-full border border-gray-200 transition-colors"
            >
              ¿Qué quieres publicar hoy?
            </div>

            <!-- Botones de acción rápida -->
            <div class="flex items-center ml-3 space-x-2">
              <div class="hidden items-center space-x-1 sm:flex">
                <div
                  class="flex items-center px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded-full cursor-pointer"
                >
                  <ion-icon name="home" class="mr-1 text-mulbin-600"></ion-icon>
                  <span>Inmueble</span>
                </div>
                <div
                  class="flex items-center px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded-full cursor-pointer"
                >
                  <ion-icon name="people" class="mr-1 text-blue-600"></ion-icon>
                  <span>Audiencia</span>
                </div>
              </div>

              <!-- Botón visual de publicar -->
              <button
                class="px-4 py-2 text-sm font-medium text-white rounded-full transition-colors bg-mulbin-600 hover:bg-mulbin-700"
              >
                Publicar
              </button>
            </div>
          </div>
        </div>

        <!-- Separador elegante -->
        <div class="h-2 bg-gradient-to-b from-gray-50 to-transparent"></div>
      </div>

      <!-- Loading indicator -->
      <div v-if="loading" class="p-10 text-center">
        <div
          class="mx-auto w-8 h-8 rounded-full border-4 border-blue-500 animate-spin border-t-transparent"
        ></div>
        <p class="mt-2 text-gray-500">Cargando publicaciones...</p>
      </div>

      <!-- Feed principal -->
      <div v-else class="divide-y divide-gray-100">
        <div v-for="post in posts" :key="getPostId(post)" class="p-4">
          <!-- Cabecera del post -->
          <div class="flex items-start">
            <!-- Avatar y menú contextual del autor -->
            <div class="relative mr-3">
              <img
                :src="post.authorCache.avatar"
                :alt="getAuthorFullName(post.authorCache)"
                class="object-cover w-10 h-10 rounded-full"
              />

              <!-- Botón del menú contextual -->
              <button
                v-if="post.type !== 'mulbin'"
                @click="toggleMenuAutor(`post-${getPostId(post)}`)"
                class="flex absolute -right-1 -bottom-3 justify-center items-center w-5 h-5 text-xs text-white rounded-full bg-mulbin-600 hover:bg-mulbin-700 boton-menu-autor"
              >
                <ion-icon
                  name="chevron-down-outline"
                  :class="{
                    'rotate-180':
                      menuAutorAbierto === `post-${getPostId(post)}`,
                  }"
                  class="visible text-xs transition-transform duration-200"
                ></ion-icon>
              </button>

              <!-- Menú desplegable del autor -->
              <div
                v-if="
                  post.type !== 'mulbin' &&
                  menuAutorAbierto === `post-${getPostId(post)}`
                "
                class="absolute left-0 top-full z-10 mt-1 w-48 bg-white rounded-lg border border-gray-200 shadow-lg menu-desplegable-autor"
              >
                <div class="py-1">
                  <!-- Ver su catálogo -->
                  <button
                    @click="verCatalogoAutor(post.authorCache)"
                    class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon name="open-outline" class="mr-2"></ion-icon>
                    Ver su catálogo
                  </button>
                  <!-- Ver publicaciones -->
                  <button
                    @click="verPublicacionesAutor(post.authorCache)"
                    class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon
                      name="document-text-outline"
                      class="mr-2"
                    ></ion-icon>
                    Ver publicaciones
                  </button>
                  <hr class="my-1 border border-indigo-300" />
                  <!-- Teléfono -->
                  <button
                    @click="abrirTelefonoAutor(post.authorCache)"
                    class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon
                      name="call-outline"
                      class="mr-2 text-blue-600"
                    ></ion-icon>
                    Teléfono
                  </button>
                  <!-- WhatsApp -->
                  <button
                    @click="abrirWhatsAppAutor(post.authorCache)"
                    class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon
                      name="logo-whatsapp"
                      class="mr-2 text-green-600"
                    ></ion-icon>
                    WhatsApp
                  </button>
                  <!-- Telegram -->
                  <button
                    @click="abrirTelegramAutor(post.authorCache)"
                    class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon
                      name="paper-plane-outline"
                      class="mr-2 text-blue-500"
                    ></ion-icon>
                    Telegram
                  </button>
                  <!-- Email -->
                  <button
                    @click="abrirEmailAutor(post.authorCache)"
                    class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <ion-icon
                      name="mail-outline"
                      class="mr-2 text-gray-600"
                    ></ion-icon>
                    Email
                  </button>
                </div>
              </div>
            </div>

            <div class="flex-1">
              <div class="flex justify-between items-start">
                <div>
                  <h3 class="font-semibold text-gray-900">
                    {{ getAuthorFullName(post.authorCache) }}
                  </h3>
                  <p class="text-xs text-gray-500">
                    {{ formatDate(post.createdAt) }}
                  </p>
                </div>

                <!-- Contenedor para badge y menú contextual -->
                <div class="flex relative items-center space-x-2">
                  <!-- Badge tipo de publicación -->
                  <span
                    :class="[
                      'px-2 py-1 text-xs rounded-full font-medium',
                      getBadgeClasses(post),
                    ]"
                  >
                    {{ getTypeLabel(post.type) }}
                  </span>

                  <!-- 🆕 NUEVO: Botón del menú contextual de la publicación -->
                  <button
                    @click="toggleMenuPost(`post-${getPostId(post)}`)"
                    class="flex relative justify-center items-center w-6 h-6 text-gray-400 rounded-full hover:text-gray-600 hover:bg-gray-100 boton-menu-post"
                  >
                    <ion-icon
                      name="ellipsis-vertical"
                      class="text-sm"
                    ></ion-icon>
                  </button>

                  <!-- 🆕 NUEVO: Menú desplegable de la publicación -->
                  <div
                    v-if="menuPostAbierto === `post-${getPostId(post)}`"
                    class="absolute right-0 top-full z-20 mt-1 w-56 bg-white rounded-lg border border-gray-200 shadow-lg menu-desplegable-post"
                  >
                    <div class="py-1">
                      <!-- Opciones para el AUTOR de la publicación -->
                      <template v-if="esAutorDelPost(post)">
                        <!-- Header indicativo para el autor -->
                        <div
                          class="px-4 py-2 text-xs font-medium text-gray-500 bg-blue-50"
                        >
                          <ion-icon
                            name="person-outline"
                            class="mr-1"
                          ></ion-icon>
                          Tu publicación
                        </div>

                        <!-- Editar publicación -->
                        <button
                          v-if="false"
                          @click="editarPublicacion(post)"
                          class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                        >
                          <ion-icon
                            name="create-outline"
                            class="mr-2 text-blue-600"
                          ></ion-icon>
                          Editar publicación
                        </button>

                        <!-- Marcar como favorito -->
                        <button
                          @click="marcarComoFavorito(post)"
                          class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                        >
                          <ion-icon
                            :name="post.isFavorited ? 'heart' : 'heart-outline'"
                            class="mr-2 text-pink-600"
                          ></ion-icon>
                          {{
                            post.isFavorited
                              ? "Remover de favoritos"
                              : "Guardar en favoritos"
                          }}
                        </button>

                        <hr class="my-1 border-gray-200" />

                        <!-- Eliminar publicación -->
                        <button
                          @click="eliminarPublicacion(post)"
                          class="flex items-center px-4 py-2 w-full text-sm text-red-700 hover:bg-red-50"
                        >
                          <ion-icon
                            name="trash-outline"
                            class="mr-2 text-red-600"
                          ></ion-icon>
                          Eliminar publicación
                        </button>
                      </template>

                      <!-- Opciones GENERALES para todos los usuarios -->
                      <!-- Compartir enlace -->
                      <button
                        v-if="false"
                        @click="compartirEnlacePublicacion(post)"
                        class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <ion-icon
                          name="link-outline"
                          class="mr-2 text-blue-600"
                        ></ion-icon>
                        Copiar enlace
                      </button>

                      <!-- Opciones para OTROS usuarios (no autores) -->
                      <template v-if="!esAutorDelPost(post)">
                        <!-- Marcar como favorito (solo para no autores) -->
                        <button
                          @click="marcarComoFavorito(post)"
                          class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                        >
                          <ion-icon
                            :name="post.isFavorited ? 'heart' : 'heart-outline'"
                            class="mr-2 text-pink-600"
                          ></ion-icon>
                          {{
                            post.isFavorited
                              ? "Remover de favoritos"
                              : "Guardar en favoritos"
                          }}
                        </button>

                        <hr class="my-1 border-gray-200" />

                        <!-- Ocultar publicación -->
                        <button
                          @click="ocultarPublicacion(post)"
                          class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                        >
                          <ion-icon
                            name="eye-off-outline"
                            class="mr-2 text-gray-600"
                          ></ion-icon>
                          Ocultar publicación
                        </button>

                        <!-- Reportar publicación -->
                        <button
                          v-if="false"
                          @click="reportarPublicacion(post)"
                          class="flex items-center px-4 py-2 w-full text-sm text-orange-700 hover:bg-orange-50"
                        >
                          <ion-icon
                            name="flag-outline"
                            class="mr-2 text-orange-600"
                          ></ion-icon>
                          Reportar publicación
                        </button>
                      </template>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Contenido del post -->
              <h4 class="mt-2 font-medium text-gray-900">
                <template v-if="hasTextFormatting(post.title)">
                  <template
                    v-for="(part, index) in processTextFormatting(post.title)"
                    :key="index"
                  >
                    <!-- Texto normal -->
                    <span v-if="part.type === 'text'">{{ part.content }}</span>

                    <!-- Badge -->
                    <span
                      v-else-if="part.type === 'badge'"
                      class="inline-flex px-2 py-0.5 ml-1 text-sm font-semibold rounded-md bg-mulbin-50 text-mulbin-800"
                    >
                      {{ part.content }}
                    </span>

                    <!-- Negrita (en títulos ya es negrita, pero enfatizar más) -->
                    <span
                      v-else-if="part.type === 'bold'"
                      class="font-black text-gray-900"
                    >
                      {{ part.content }}
                    </span>

                    <!-- Cursiva -->
                    <em
                      v-else-if="part.type === 'italic'"
                      class="italic font-medium text-gray-800"
                    >
                      {{ part.content }}
                    </em>

                    <!-- Código -->
                    <code
                      v-else-if="part.type === 'code'"
                      class="px-1.5 py-0.5 font-mono text-sm text-gray-800 bg-gray-100 rounded border"
                    >
                      {{ part.content }}
                    </code>

                    <!-- Tachado -->
                    <span
                      v-else-if="part.type === 'strikethrough'"
                      class="font-normal text-gray-500 line-through"
                    >
                      {{ part.content }}
                    </span>
                  </template>
                </template>
                <template v-else>
                  {{ post.title }}
                </template>
              </h4>
              <p class="mt-1 text-sm text-gray-700 whitespace-pre-line">
                <template v-if="hasTextFormatting(post.description)">
                  <template
                    v-for="(part, index) in processTextFormatting(
                      post.description
                    )"
                    :key="index"
                  >
                    <!-- Texto normal -->
                    <span
                      v-if="part.type === 'text'"
                      class="whitespace-pre-line"
                      >{{ part.content }}</span
                    >

                    <!-- Badge -->
                    <span
                      v-else-if="part.type === 'badge'"
                      class="inline-flex px-2 py-0.5 ml-1 text-xs font-medium rounded-full bg-mulbin-100 text-mulbin-800"
                    >
                      {{ part.content }}
                    </span>

                    <!-- Negrita -->
                    <strong
                      v-else-if="part.type === 'bold'"
                      class="font-bold text-gray-900"
                    >
                      {{ part.content }}
                    </strong>

                    <!-- Cursiva -->
                    <em
                      v-else-if="part.type === 'italic'"
                      class="italic text-gray-800"
                    >
                      {{ part.content }}
                    </em>

                    <!-- Código -->
                    <code
                      v-else-if="part.type === 'code'"
                      class="px-1.5 py-0.5 font-mono text-xs text-gray-800 bg-gray-100 rounded border"
                    >
                      {{ part.content }}
                    </code>

                    <!-- Tachado -->
                    <span
                      v-else-if="part.type === 'strikethrough'"
                      class="text-gray-500 line-through"
                    >
                      {{ part.content }}
                    </span>
                  </template>
                </template>
                <template v-else>
                  {{ post.description }}
                </template>
              </p>

              <!-- DETALLES INMUEBLE: Detalles del inmueble (solo si tiene externalId y externalKey) -->
              <div
                v-if="tieneInmuebleRelacionado(post)"
                class="flex flex-wrap gap-2 mt-3 text-sm cursor-pointer"
                @click="detallesInmueble(post, 0)"
                title="Ver detalle del inmueble"
              >
                <span class="inline-flex items-center text-gray-600">
                  <ion-icon name="cash-outline" class="mr-1"></ion-icon>
                  ${{ formatPrice(post.relatedInmueble?.precio || post.price) }}
                </span>
                <span class="inline-flex items-center text-gray-600">
                  <ion-icon name="location-outline" class="mr-1"></ion-icon>
                  {{ getLocationLabel(post.location) }}
                </span>
                <span
                  v-if="post.bedrooms"
                  class="inline-flex items-center text-gray-600"
                >
                  <ion-icon name="bed-outline" class="mr-1"></ion-icon>
                  {{ post.bedrooms }} recámaras
                </span>
                <span
                  v-if="post.bathrooms"
                  class="inline-flex items-center text-gray-600"
                >
                  <ion-icon name="water-outline" class="mr-1"></ion-icon>
                  {{ post.bathrooms }} baños
                </span>
                <span
                  v-if="post.area"
                  class="inline-flex items-center text-gray-600"
                >
                  <ion-icon name="resize-outline" class="mr-1"></ion-icon>
                  {{ post.area }} m²
                </span>
                <span class="inline-flex items-center text-gray-600">
                  <ion-icon name="open-outline" class="mr-1"></ion-icon>
                </span>
              </div>

              <!-- DETALLES INMUEBLE: Imágenes del post o del inmueble relacionado -->
              <div
                v-if="
                  (post.images && post.images.length) ||
                  (post.relatedInmueble?.imagenes &&
                    post.relatedInmueble.imagenes.length) ||
                  post.relatedInmueble?.image
                "
                class="mt-3"
                @click="detallesInmueble(post, 0)"
                title="Ver detalle del inmueble"
              >
                <div class="grid grid-cols-2 gap-2">
                  <!-- Prioridad 1: Mostrar imágenes del post si existen -->
                  <template v-if="post.images && post.images.length">
                    <img
                      v-for="(image, index) in post.images.slice(0, 2)"
                      :key="index"
                      :src="image"
                      :alt="`Imagen ${index + 1} de ${post.title}`"
                      class="object-cover w-full h-32 rounded cursor-pointer"
                    />
                  </template>
                  <!-- Prioridad 2: Mostrar imágenes del inmueble relacionado si existen -->
                  <template
                    v-else-if="
                      post.relatedInmueble?.imagenes &&
                      post.relatedInmueble.imagenes.length
                    "
                  >
                    <img
                      v-for="(
                        image, index
                      ) in post.relatedInmueble.imagenes.slice(0, 2)"
                      :key="index"
                      :src="image"
                      :alt="`Imagen ${index + 1} de ${post.title}`"
                      class="object-cover w-full h-32 rounded cursor-pointer"
                    />
                  </template>
                  <!-- Prioridad 3: Mostrar imagen principal del inmueble relacionado como fallback -->
                  <template v-else-if="post.relatedInmueble?.image">
                    <img
                      :src="post.relatedInmueble.image"
                      :alt="`Imagen de ${post.title}`"
                      class="object-cover w-full h-32 rounded cursor-pointer"
                    />
                  </template>
                </div>
                <!-- Contador de imágenes: usar post.images o relatedInmueble.imagenes según el caso -->
                <p
                  v-if="
                    (post.images && post.images.length > 2) ||
                    (post.relatedInmueble?.imagenes &&
                      post.relatedInmueble.imagenes.length > 2)
                  "
                  class="mt-1 text-xs text-right cursor-pointer text-mulbin-600"
                >
                  <template v-if="post.images && post.images.length > 2">
                    +{{ post.images.length - 2 }} imágenes más
                  </template>
                  <template
                    v-else-if="
                      post.relatedInmueble?.imagenes &&
                      post.relatedInmueble.imagenes.length > 2
                    "
                  >
                    +{{ post.relatedInmueble.imagenes.length - 2 }} imágenes más
                  </template>
                </p>
              </div>

              <!-- Acciones del post -->
              <div class="flex justify-between items-center pt-3">
                <!-- Grupo de acciones principales -->
                <div class="flex items-center space-x-1">
                  <!-- 🆕 NUEVO: Botón Hilo de interés -->
                  <!-- 🔍 CONDICIÓN: Solo visible para autores si hay hilos, siempre visible para no autores -->
                  <!-- Botón Hilo de Interés -->
                  <BotonHilosInteres
                    v-if="
                      post.type !== 'mulbin' &&
                      (!esAutorDelPost(post) ||
                        (esAutorDelPost(post) &&
                          getTotalHilosCountForPost(post) > 0))
                    "
                    :post="post"
                    :es-autor="esAutorDelPost(post)"
                    :unread-count="getUnreadCountForPost(post)"
                    :hilos="hilosDelPost[getPostId(post)] || []"
                    :cargando-hilos="loadingHilos[getPostId(post)] || false"
                    :menu-abierto="menuHilosAbierto"
                    @gestionar-hilo="gestionarHiloInteres"
                    @crear-hilo="abrirModalCrearHilo"
                    @abrir-chat="abrirChatHilo"
                    @click-fuera="cerrarMenuHilosAlClickFuera"
                  />

                  <!-- Botón Comentar -->
                  <button
                    v-if="post.type !== 'mulbin'"
                    @click="openComments(post)"
                    class="flex items-center px-3 py-1 text-sm font-medium text-gray-600 rounded-lg border border-gray-200 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-200"
                    :class="{
                      'bg-blue-50 text-blue-700 border-blue-200':
                        post.showComments,
                    }"
                  >
                    <ion-icon
                      name="chatbubble-outline"
                      class="mr-1.5 text-base"
                    ></ion-icon>
                    <span class="hidden sm:inline">Comentarios</span>
                    <span class="sm:hidden">{{ post.commentsCount }}</span>
                    <span
                      class="hidden sm:inline ml-1 px-1.5 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full min-w-[20px] text-center"
                    >
                      {{ post.commentsCount }}
                    </span>
                  </button>
                </div>

                <!-- Grupo de acciones secundarias -->
                <div class="flex items-center space-x-1">
                  <!-- Botón Favorito -->
                  <button
                    @click="marcarComoFavorito(post)"
                    class="flex items-center px-3 py-1 text-sm font-medium text-gray-600 rounded-lg border border-gray-200 hover:bg-pink-50 hover:text-pink-700 hover:border-pink-200"
                    :class="{
                      'bg-pink-50 text-pink-700 border-pink-200':
                        post.isFavorited,
                    }"
                  >
                    <ion-icon
                      :name="post.isFavorited ? 'heart' : 'heart-outline'"
                      class="visible mr-1.5 text-base"
                      :class="{
                        'text-pink-600': post.isFavorited,
                        'hover:text-pink-600': !post.isFavorited,
                      }"
                    ></ion-icon>
                    <span
                      class="px-1.5 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full min-w-[20px] text-center"
                    >
                      {{ post.favorites || 0 }}
                    </span>
                  </button>
                </div>
              </div>

              <!-- COMMENTS POST: Componente desacoplado de comentarios -->
              <ComentariosPost
                v-if="post.type !== 'mulbin'"
                :post="post"
                :show-comments="post.showComments"
                :token="token"
                :current-user-id="currentUserId"
                @comments-updated="handleCommentsUpdated"
                @comment-added="handleCommentAdded"
                @comment-deleted="handleCommentDeleted"
                @comment-converted="handleCommentConverted"
                @error="handleCommentError"
              />
            </div>
          </div>
        </div>

        <!-- Empty state -->
        <div v-if="posts.length === 0" class="p-10 text-center">
          <ion-icon
            name="search-outline"
            class="text-5xl text-gray-300"
          ></ion-icon>
          <p class="mt-2 text-gray-500">No se encontraron publicaciones</p>
          <p class="mb-4 text-sm text-gray-400">
            Es posible que necesites recargar para recuperar el contenido más
            reciente
          </p>

          <!-- Botón de recarga -->
          <button
            @click="initializeFeed"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 mb-4 text-sm font-medium text-white bg-blue-600 rounded-lg transition-colors duration-200 hover:bg-blue-700 disabled:bg-blue-400"
          >
            <ion-icon
              :name="loading ? 'refresh-outline' : 'reload-outline'"
              :class="loading ? 'animate-spin' : ''"
              class="mr-2"
            ></ion-icon>
            {{ loading ? "Recargando..." : "Recargar contenido" }}
          </button>

          <!-- Mensaje de resultado -->
          <div
            v-if="mensajeAccion"
            class="p-3 mt-4 rounded"
            :class="
              mensajeExito
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            "
          >
            {{ mensajeAccion }}
          </div>
        </div>
        <!-- Botones de administración -->
        <div class="hidden justify-center my-4 space-x-3">
          <button
            @click="generarDatosEjemplo"
            class="px-4 py-2 text-sm text-white bg-green-600 rounded hover:bg-green-700"
            :disabled="generandoDatos"
          >
            <span v-if="generandoDatos">
              <ion-icon
                name="reload-outline"
                class="mr-1 animate-spin"
              ></ion-icon>
              Generando...
            </span>
            <span v-else>
              <ion-icon name="add-circle-outline" class="mr-1"></ion-icon>
              Generar datos de ejemplo
            </span>
          </button>
          <button
            @click="limpiarDatosEjemplo"
            class="px-4 py-2 text-sm text-white bg-red-600 rounded hover:bg-red-700"
            :disabled="generandoDatos"
          >
            <ion-icon name="trash-outline" class="mr-1"></ion-icon>
            Limpiar datos
          </button>
        </div>
      </div>

      <!-- SCROLL INFINITO: Sentinel element para intersection observer -->
      <div
        v-if="posts.length > 0 && hasMorePosts"
        ref="sentinelRef"
        class="flex justify-center py-8 text-center"
      >
        <div
          v-if="isLoadingMore"
          class="flex items-center space-x-2 text-gray-500"
        >
          <div
            class="w-5 h-5 rounded-full border-2 border-blue-500 animate-spin border-t-transparent"
          ></div>
          <span class="text-sm"> Cargando más publicaciones... </span>
        </div>
        <div v-else class="text-sm text-gray-400">
          Desliza hacia abajo para ver más publicaciones
        </div>
      </div>

      <!-- SCROLL INFINITO: Mensaje cuando no hay más posts -->
      <div
        v-if="posts.length > 0 && !hasMorePosts && !isLoadingMore"
        class="py-8 text-center text-gray-500"
      >
        <ion-icon
          name="checkmark-circle-outline"
          class="text-2xl text-green-500"
        ></ion-icon>
        <p class="mt-2 text-sm">¡Has visto todas las publicaciones!</p>
        <p class="text-xs text-gray-400">
          {{ posts.length }} publicaciones en total
        </p>
      </div>
    </div>

    <!-- HILOS DE INTERÉS: Componente desacoplado para crear hilo de interés -->
    <NuevoHiloForm
      :show="showCrearHiloModal"
      :token="token"
      :post="postSeleccionadoParaHilo"
      @close="handleCloseCrearHilo"
      @hilo-created="handleHiloCreated"
      @abrir-chat="handleAbrirChatFromHilo"
      @error="handleHiloError"
    />

    <!-- HILOS DE INTERÉS: Componente de chat desacoplado -->
    <ChatHiloModal
      :show="showChatModal"
      :hilo-seleccionado="hiloSeleccionado"
      :token="token"
      @close="cerrarChatModal"
      @error="handleChatError"
      @success="handleChatSuccess"
    />

    <!-- HILOS DE INTERÉS: Modal optimizado de hilos para autores (UX mejorada) -->
    <div
      v-if="showHilosModal && postSeleccionado"
      class="flex fixed inset-0 z-50 justify-center items-end p-4 bg-black bg-opacity-50 backdrop-blur-sm sm:items-center"
    >
      <div
        class="overflow-hidden relative w-full bg-white rounded-t-2xl shadow-2xl sm:rounded-2xl animate-slideUp sm:animate-slideIn"
        :class="{
          'max-w-lg max-h-[85vh]':
            hilosDelPost[getPostId(postSeleccionado)]?.length <= 3,
          'max-w-2xl max-h-[90vh]':
            hilosDelPost[getPostId(postSeleccionado)]?.length > 3,
        }"
      >
        <!-- Header mejorado con gradiente -->
        <div
          class="relative p-4 text-white bg-gradient-to-r to-indigo-700 from-mulbin-600"
        >
          <!-- Patrón de fondo sutil -->
          <div class="absolute inset-0 opacity-10">
            <div
              class="absolute inset-0 bg-gradient-to-br from-transparent via-white to-transparent opacity-20"
            ></div>
          </div>

          <div class="flex relative justify-between items-start">
            <div class="flex-1 pr-4">
              <div class="flex items-center mb-1 space-x-2">
                <ion-icon
                  name="chatbubbles"
                  class="text-xl text-blue-200"
                ></ion-icon>
                <h3 class="text-lg font-bold">Hilos de Interés</h3>
                <span
                  v-if="hilosDelPost[getPostId(postSeleccionado)]?.length > 0"
                  class="px-2 py-0.5 text-xs font-medium bg-white bg-opacity-20 rounded-full"
                >
                  {{ hilosDelPost[getPostId(postSeleccionado)].length }}
                </span>
              </div>
              <p class="text-sm leading-snug text-blue-100 line-clamp-2">
                {{ postSeleccionado?.title || "Publicación sin título" }}
              </p>
              <div
                class="flex items-center mt-2 space-x-4 text-xs text-blue-200"
              >
                <span class="flex items-center">
                  <ion-icon name="home-outline" class="mr-1"></ion-icon>
                  {{ getLocationLabel(postSeleccionado?.location) }}
                </span>
                <span class="flex items-center">
                  <ion-icon name="cash-outline" class="mr-1"></ion-icon>
                  ${{
                    formatPrice(
                      postSeleccionado?.relatedInmueble?.precio ||
                        postSeleccionado?.price ||
                        0
                    )
                  }}
                </span>
              </div>
            </div>

            <button
              @click="showHilosModal = false"
              class="flex justify-center items-center w-8 h-8 rounded-full transition-colors hover:bg-white hover:bg-opacity-20"
            >
              <ion-icon name="close-outline" class="text-xl"></ion-icon>
            </button>
          </div>
        </div>

        <!-- Contenido del modal con scroll optimizado -->
        <div
          class="flex overflow-hidden flex-col"
          style="max-height: calc(90vh - 140px)"
        >
          <!-- Loading state mejorado -->
          <div
            v-if="loading"
            class="flex flex-col justify-center items-center py-12"
          >
            <div class="relative">
              <div
                class="w-12 h-12 rounded-full border-4 border-blue-200 animate-spin border-t-mulbin-600"
              ></div>
              <div
                class="absolute inset-0 w-12 h-12 rounded-full border-4 border-transparent animate-pulse border-r-blue-300"
              ></div>
            </div>
            <p class="mt-4 text-sm font-medium text-gray-600">
              Cargando conversaciones...
            </p>
            <p class="mt-1 text-xs text-gray-500">
              Esto solo tomará un momento
            </p>
          </div>

          <!-- Lista de hilos mejorada -->
          <div
            v-else-if="hilosDelPost[getPostId(postSeleccionado)]?.length > 0"
            class="overflow-y-auto divide-y divide-gray-100"
          >
            <div
              v-for="hilo in hilosDelPost[getPostId(postSeleccionado)]"
              :key="hilo._id"
              class="relative p-4 border-l-4 border-transparent transition-all cursor-pointer group hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:border-mulbin-500"
              @click="abrirChatDesdeModal(hilo)"
              :class="{
                'bg-gradient-to-r from-red-50 to-orange-50 border-l-red-400':
                  hilo.unreadMessagesCount > 0,
              }"
            >
              <!-- Contenido principal del hilo -->
              <div class="flex items-start space-x-4">
                <!-- Avatar mejorado con indicadores -->
                <div class="relative flex-shrink-0">
                  <img
                    :src="hilo.creatorCache?.avatar || '/default-avatar.png'"
                    :alt="getAuthorFullName(hilo.creatorCache)"
                    class="object-cover w-12 h-12 rounded-full border-2 border-white ring-2 ring-gray-100 shadow-md"
                  />
                  <!-- Indicador de actividad reciente -->
                  <div
                    v-if="esActivoReciente(hilo)"
                    class="absolute -right-1 -bottom-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm"
                    title="Activo recientemente"
                  ></div>
                  <!-- Badge de prioridad para mensajes no leídos -->
                  <div
                    v-if="hilo.unreadMessagesCount > 0"
                    class="flex absolute -top-1 -right-1 justify-center items-center w-5 h-5 bg-red-500 rounded-full border-2 border-white shadow-lg"
                  >
                    <span class="text-xs font-bold text-white">!</span>
                  </div>
                </div>

                <!-- Información del hilo -->
                <div class="flex-1 min-w-0">
                  <!-- Línea superior: Título y badge de no leídos -->
                  <div class="flex justify-between items-start mb-1">
                    <h4
                      class="text-sm font-semibold leading-tight text-gray-900 transition-colors group-hover:text-mulbin-700"
                    >
                      {{ hilo.titulo }}
                    </h4>
                    <div class="flex items-center ml-3 space-x-2">
                      <!-- Badge de mensajes no leídos (más prominente) -->
                      <span
                        v-if="hilo.unreadMessagesCount > 0"
                        class="px-2 py-1 text-xs font-bold text-white bg-gradient-to-r from-red-500 to-red-600 rounded-full shadow-lg animate-pulse min-w-[24px] text-center"
                        :title="`${hilo.unreadMessagesCount} mensaje${
                          hilo.unreadMessagesCount > 1 ? 's' : ''
                        } sin leer`"
                      >
                        {{
                          hilo.unreadMessagesCount > 99
                            ? "99+"
                            : hilo.unreadMessagesCount
                        }}
                      </span>
                      <!-- Fecha -->
                      <span class="text-xs text-gray-500 whitespace-nowrap">
                        {{
                          formatearFechaCorta(
                            hilo.lastMessageAt || hilo.createdAt
                          )
                        }}
                      </span>
                    </div>
                  </div>

                  <!-- Información del creador -->
                  <p class="mb-2 text-xs font-medium text-gray-600">
                    <ion-icon name="person-outline" class="mr-1"></ion-icon>
                    {{ getAuthorFullName(hilo.creatorCache) }}
                  </p>

                  <!-- Referencia privada si existe -->
                  <p
                    v-if="hilo.referenciaPrivada"
                    class="px-2 py-1 mb-2 text-xs italic text-gray-500 bg-gray-50 rounded"
                  >
                    <ion-icon name="bookmark-outline" class="mr-1"></ion-icon>
                    {{ hilo.referenciaPrivada }}
                  </p>

                  <!-- Stats del hilo con iconos mejorados -->
                  <div
                    class="flex items-center space-x-4 text-xs text-gray-500"
                  >
                    <span class="flex items-center">
                      <ion-icon
                        name="chatbubble-ellipses-outline"
                        class="mr-1 text-blue-500"
                      ></ion-icon>
                      <span class="font-medium">{{
                        hilo.mensajesCount || 0
                      }}</span>
                      <span class="ml-1"
                        >mensaje{{
                          (hilo.mensajesCount || 0) !== 1 ? "s" : ""
                        }}</span
                      >
                    </span>
                    <span class="flex items-center">
                      <ion-icon
                        name="time-outline"
                        class="mr-1 text-green-500"
                      ></ion-icon>
                      <span>{{
                        formatearTiempoTranscurrido(
                          hilo.lastMessageAt || hilo.createdAt
                        )
                      }}</span>
                    </span>
                  </div>
                </div>

                <!-- Indicador de acción -->
                <div
                  class="flex-shrink-0 transition-all duration-200 group-hover:translate-x-1"
                >
                  <div
                    class="flex justify-center items-center w-8 h-8 bg-gray-100 rounded-full transition-colors group-hover:bg-mulbin-100"
                  >
                    <ion-icon
                      name="chevron-forward-outline"
                      class="text-gray-400 transition-colors group-hover:text-mulbin-600"
                    ></ion-icon>
                  </div>
                </div>
              </div>

              <!-- Barra de progreso visual para mensajes no leídos -->
              <div
                v-if="hilo.unreadMessagesCount > 0"
                class="absolute right-0 bottom-0 left-0 h-1 bg-gradient-to-r from-red-400 to-orange-400 opacity-60"
              ></div>
            </div>
          </div>

          <!-- Estado vacío mejorado -->
          <div
            v-else
            class="flex flex-col justify-center items-center px-6 py-16"
          >
            <div class="relative mb-6">
              <!-- Círculo de fondo con gradiente -->
              <div
                class="flex justify-center items-center w-24 h-24 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full"
              >
                <ion-icon
                  name="chatbubbles-outline"
                  class="text-4xl text-gray-400"
                ></ion-icon>
              </div>
              <!-- Indicador de pulso -->
              <div
                class="absolute inset-0 w-24 h-24 bg-blue-200 rounded-full opacity-20 animate-ping"
              ></div>
            </div>

            <h4 class="mb-2 text-lg font-semibold text-gray-900">
              Sin conversaciones
            </h4>
            <p
              class="max-w-sm text-sm leading-relaxed text-center text-gray-500"
            >
              Aún no hay hilos de interés para esta publicación. Los socios
              interesados podrán crear conversaciones privadas contigo.
            </p>

            <!-- Consejos útiles -->
            <div
              class="p-4 mt-6 max-w-sm bg-blue-50 rounded-lg border border-blue-200"
            >
              <div class="flex items-start space-x-2">
                <ion-icon
                  name="bulb-outline"
                  class="mt-0.5 text-blue-600"
                ></ion-icon>
                <div>
                  <p class="mb-1 text-xs font-medium text-blue-800">
                    💡 Consejo
                  </p>
                  <p class="text-xs text-blue-700">
                    Los hilos aparecerán aquí cuando los socios interesados
                    inicien conversaciones sobre tu publicación.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer mejorado con estadísticas -->
        <div
          v-if="hilosDelPost[getPostId(postSeleccionado)]?.length > 0"
          class="px-4 py-3 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200"
        >
          <div class="flex justify-between items-center text-xs">
            <div class="flex items-center space-x-4 text-gray-600">
              <span class="flex items-center">
                <ion-icon name="people-outline" class="mr-1"></ion-icon>
                {{ hilosDelPost[getPostId(postSeleccionado)].length }}
                conversación{{
                  hilosDelPost[getPostId(postSeleccionado)].length !== 1
                    ? "es"
                    : ""
                }}
              </span>
              <span
                v-if="getTotalUnreadMessagesForPost(postSeleccionado) > 0"
                class="flex items-center font-medium text-red-600"
              >
                <ion-icon name="notifications-outline" class="mr-1"></ion-icon>
                {{ getTotalUnreadMessagesForPost(postSeleccionado) }} sin leer
              </span>
            </div>
            <p class="text-gray-500">Toca para abrir conversación</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Global -->
    <GlobalModal />

    <!-- 🆕 NUEVO: Modal de detalle de inmueble -->
    <DetalleInmuebleModal
      v-model="showDetalleInmuebleModal"
      :inmueble="inmuebleSeleccionado"
      @contactar="contactarSocioDesdeModal"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  reactive,
  onMounted,
  onUnmounted,
  toRefs,
} from "vue";
import inmobiliarioService from "../../services/inmobiliarioService";
import hilosInteresService from "../../services/hilosInteresService";
import ddpService from "../../services/ddpService";
import type { PostInmobiliario, Filtros } from "../../types/inmobiliario";
import MisSocios from "./MisSocios.vue";
import InmueblesSocios from "../inmuebles-multibolsa/InmueblesSocios.vue";
import Favoritos from "./Favoritos.vue";
import FeedHilos from "./FeedHilos.vue"; // 🆕 NUEVO: Componente Feed de Hilos
import NuevaPublicacionModal from "./NuevaPublicacionModal.vue"; // 🆕 NUEVO: Modal para nueva publicación
import SelectorSociosWrapper from "../selector-socios-wrapper/SelectorSociosWrapper.vue";
import ChatHiloModal from "./ChatHiloModal.vue";
import BotonHilosInteres from "./BotonHilosInteres.vue";
import NuevoHiloForm from "./NuevoHiloForm.vue";
import ComentariosPost from "./ComentariosPost.vue";
import { GlobalModal, useModal, Tooltip } from "../ui";
import FiltroInmueblesInteligente from "../inmuebles-multibolsa/FiltroInmueblesInteligente.vue";
import DetalleInmuebleModal from "./DetalleInmuebleModal.vue";

export default defineComponent({
  name: "MuroInmobiliarioSocial",

  components: {
    MisSocios,
    InmueblesSocios,
    Favoritos,
    FeedHilos, // 🆕 NUEVO: Componente Feed de Hilos
    NuevaPublicacionModal, // 🆕 NUEVO: Modal para nueva publicación
    SelectorSociosWrapper,
    ChatHiloModal,
    BotonHilosInteres,
    NuevoHiloForm, // 🆕 NUEVO: Componente desacoplado para crear hilos
    ComentariosPost, // 🆕 NUEVO: Componente desacoplado para comentarios
    GlobalModal,
    Tooltip,
    FiltroInmueblesInteligente,
    DetalleInmuebleModal,
  },

  props: {
    token: {
      type: String,
      default: "",
    },
    avatar: {
      type: String,
      default: "",
    },
    textosAudienciaPrivada: {
      type: Object,
      required: true,
    },
    textosAudienciaPublica: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    // Composables
    const { alert, confirm, success, error, warning } = useModal();

    // 🆕 NUEVO: Estado reactivo para userId actual (OPTIMIZACIÓN PERFORMANCE)
    const currentUserId = ref<string | null>(null);
    let unsubscribeAuthChange: (() => void) | null = null;

    // 🆕 NUEVO: Función para actualizar userId reactivo
    const updateCurrentUserId = () => {
      currentUserId.value = ddpService.getCurrentUserId();
    };

    // Estados
    const loading = ref(true);
    const showFilter = ref(false);
    const showNewPostForm = ref(false);
    const showMisSocios = ref(false);
    const showInmuebles = ref(false);
    const showFavoritos = ref(false);
    const showFeedHilos = ref(false); // 🆕 NUEVO: Estado para Feed de Hilos
    const showAddFriendModal = ref(false);

    // 🚨 NUEVO: Sistema de respaldo para detectar limpieza accidental del feed
    const lastValidPostsCount = ref(0);
    const feedBackup = ref<PostInmobiliario[]>([]);

    // 🆕 NUEVO: Estados para sistema de filtros inteligentes de inmuebles
    const mostrarListadoInmuebles = ref(false);
    const filtrosAplicados = ref({ ubicacion: "", operacion: "" });
    const totalInmueblesEncontrados = ref(0);
    const mostrandoFavoritosEnInmuebles = ref(false); // 🆕 NUEVO: Control para ocultar barra de filtros

    // 🆕 NUEVO: Estados para modal de detalle de inmueble
    const showDetalleInmuebleModal = ref(false);
    const inmuebleSeleccionado = ref<any>(null);

    // 🆕 SCROLL INFINITO: Estados optimizados siguiendo principios DDP
    const postsLimit = ref(5); // Límite inicial de posts
    const isLoadingMore = ref(false);
    const hasMorePosts = ref(true);
    const isInitialLoad = ref(true);
    const POSTS_INCREMENT = 5; // Incremento al hacer scroll

    // 🔧 FIX SCROLL INFINITO: Flag para diferenciar posts cargados por scroll vs reactividad natural
    const isLoadingByScrollInfinito = ref(false);

    const posts = ref<PostInmobiliario[]>([]);
    const newComments = reactive<Record<string, string>>({});
    let unsubscribeFromChanges: (() => void) | null = null;
    let unsubscribeFromCommentsChanges: (() => void) | null = null;
    let unsubscribeFromFavoritesChanges: (() => void) | null = null; // 🚀 NUEVO: Observer de favoritos
    const { token } = toRefs(props);

    // Control de errores de conexión
    const connectionError = ref(false);
    const connectionErrorMessage = ref("");

    // 🆕 NUEVO: Control de estado de conexión DDP
    const connectionStatus = ref({
      connected: false,
      authenticated: false,
      reconnecting: false,
    });

    // Control de generación de datos
    const generandoDatos = ref(false);
    const mensajeAccion = ref("");
    const mensajeExito = ref(true);

    // 🆕 NUEVO: Estado específico para refresh manual
    const refreshingNotifications = ref(false);

    // 🆕 NUEVO: Estado para refresh del feed completo
    const refreshingFeed = ref(false);

    // Control de notificaciones nuevas
    const hasNewNotifications = ref(false);
    const newNotificationsCount = ref(0);
    const lastNotificationCheck = ref(new Date());

    // 🔧 REMOVIDO: Estados para selector de socios movidos a NuevaPublicacionForm

    // 🆕 NUEVO: Estados para textos de audiencia
    const textosAudienciaPrivada = ref(props.textosAudienciaPrivada);
    const textosAudienciaPublica = ref(props.textosAudienciaPublica);

    // 🆕 NUEVO: Estados para menú contextual de autores de posts
    const menuAutorAbierto = ref<string | null>(null);

    // 🆕 NUEVO: Estados para menú contextual de PUBLICACIONES
    const menuPostAbierto = ref<string | null>(null);

    // 🆕 NUEVO: Estados para menú contextual de COMENTARIOS
    const menuComentarioAbierto = ref<string | null>(null);

    // 🆕 NUEVO: Estados para conversión de comentarios a hilos
    const convirtiendoComentario = ref(false);

    // 🆕 NUEVO: Estados para modal de lead
    // 🔧 REMOVIDO: Estados del modal de lead eliminados

    // Filtros
    const filters = reactive<Filtros>({
      type: "",
      location: "",
      maxPrice: null,
    });

    // 🔧 REMOVIDO: newPost movido a NuevaPublicacionForm

    // 🆕 SCROLL INFINITO: Elemento sentinel para intersection observer
    const sentinelRef = ref<HTMLElement | null>(null);

    // 🔧 REMOVIDO: filteredFriends movido a NuevaPublicacionForm

    // Reintento de conexión
    const retryConnection = async () => {
      connectionError.value = false;
      connectionErrorMessage.value = "";
      await initializeFeed();
    };

    // 🆕 NUEVO: Monitor de estado de conexión DDP
    const monitorConnectionStatus = () => {
      console.log("🔌 Iniciando monitor de conexión DDP...");

      // Verificar estado cada 30 segundos
      const checkInterval = setInterval(async () => {
        try {
          const status = await inmobiliarioService.checkAndRepairConnection();

          connectionStatus.value = {
            connected: status,
            authenticated: false, // Se actualizará correctamente en versiones futuras
            reconnecting: false,
          };

          // Si detectamos problemas, intentar reparar
          if (!status) {
            console.warn(
              "⚠️ Problema de conexión detectado, intentando reparar..."
            );
            connectionStatus.value.reconnecting = true;

            try {
              await inmobiliarioService.checkAndRepairConnection();
              // DDP mantendrá los datos sincronizados automáticamente
              console.log("✅ Conexión reparada exitosamente");
            } catch (error) {
              console.error("❌ Error al reparar conexión:", error);
            } finally {
              connectionStatus.value.reconnecting = false;
            }
          }
        } catch (error) {
          console.error("❌ Error monitoreando conexión:", error);
        }
      }, 30000); // Verificar cada 30 segundos

      // Limpiar interval al desmontar
      onUnmounted(() => {
        clearInterval(checkInterval);
      });
    };

    // Métodos
    const toggleFilter = () => {
      showFilter.value = !showFilter.value;
    };

    // Toggle para Mis Socios
    const toggleMisSocios = () => {
      showMisSocios.value = !showMisSocios.value;

      // Si estamos mostrando Mis Socios, ocultamos otros paneles
      if (showMisSocios.value) {
        showInmuebles.value = false;
        showFavoritos.value = false;
        showFeedHilos.value = false; // 🆕 NUEVO: Ocultar Feed de Hilos
        showFilter.value = false;
        showNewPostForm.value = false;
      }
    };

    // Toggle para Inmuebles
    const toggleInmuebles = () => {
      showInmuebles.value = !showInmuebles.value;

      // Si estamos mostrando Inmuebles, ocultamos otros paneles
      if (showInmuebles.value) {
        showMisSocios.value = false;
        showFavoritos.value = false;
        showFeedHilos.value = false; // 🆕 NUEVO: Ocultar Feed de Hilos
        showFilter.value = false;
        showNewPostForm.value = false;
      }
    };

    // Toggle para Favoritos
    const toggleFavoritos = () => {
      showFavoritos.value = !showFavoritos.value;

      // Si estamos mostrando Favoritos, ocultamos otros paneles
      if (showFavoritos.value) {
        showMisSocios.value = false;
        showInmuebles.value = false;
        showFeedHilos.value = false; // 🆕 NUEVO: Ocultar Feed de Hilos
        showFilter.value = false;
        showNewPostForm.value = false;
      }
    };

    // 🆕 NUEVO: Toggle para Feed de Hilos
    const toggleFeedHilos = () => {
      showFeedHilos.value = !showFeedHilos.value;

      // Si estamos mostrando Feed de Hilos, ocultamos otros paneles
      if (showFeedHilos.value) {
        showMisSocios.value = false;
        showInmuebles.value = false;
        showFavoritos.value = false;
        showFilter.value = false;
        showNewPostForm.value = false;
      }
    };

    // 🆕 NUEVO: Métodos para sistema de filtros inteligentes de inmuebles
    const handleCargarInmuebles = (filtros: {
      ubicacion: string;
      operacion: string;
    }) => {
      filtrosAplicados.value = { ...filtros };
      mostrarListadoInmuebles.value = true;
      console.log("🚀 Cargando inmuebles con filtros:", filtros);
    };

    const volverAFiltros = () => {
      mostrarListadoInmuebles.value = false;
      filtrosAplicados.value = { ubicacion: "", operacion: "" };
      totalInmueblesEncontrados.value = 0;
      console.log("🔙 Volviendo al selector de filtros");
    };

    const irAMisSocios = () => {
      showInmuebles.value = false;
      showMisSocios.value = true;
      console.log("👥 Navegando a Mis Socios");
    };

    // 🆕 NUEVO: Variable para controlar activación automática de favoritos
    const activarFavoritosInmediatamente = ref(false);

    // 🆕 NUEVO: Función para ir a favoritos desde filtro inteligente
    const irAFavoritos = () => {
      // Activar vista de inmuebles Y activar favoritos directamente
      showInmuebles.value = true;
      showMisSocios.value = false;
      showFavoritos.value = false;
      showFeedHilos.value = false;
      showFilter.value = false;
      showNewPostForm.value = false;

      // Asegurarse de que se muestre el listado de inmuebles para poder acceder a favoritos
      mostrarListadoInmuebles.value = true;

      // 🎯 NUEVO: Señal para que Inmuebles active favoritos automáticamente
      activarFavoritosInmediatamente.value = true;

      console.log(
        "⭐ Navegando directamente a favoritos desde filtro inteligente"
      );
    };

    const handleTotalUpdated = (total: number) => {
      totalInmueblesEncontrados.value = total;
    };

    // 🆕 NUEVO: Manejar cambio de vista de favoritos en Inmuebles
    const handleFavoritosChanged = (mostrandoFavoritos: boolean) => {
      mostrandoFavoritosEnInmuebles.value = mostrandoFavoritos;
      console.log(
        `🔄 Vista de favoritos en inmuebles: ${
          mostrandoFavoritos ? "activada" : "desactivada"
        }`
      );
    };

    // 🆕 NUEVO: Resetear prop cuando se activan favoritos
    const handleFavoritosActivados = () => {
      activarFavoritosInmediatamente.value = false;
      console.log("✅ Favoritos activados, reseteando prop");
    };

    const obtenerLabelOperacion = (operacion: string): string => {
      const labels: Record<string, string> = {
        venta: "En Venta",
        renta: "En Renta",
        traspaso: "En Traspaso",
      };
      return labels[operacion] || operacion;
    };

    const formatDate = (date: string | Date) => {
      const d = new Date(date);
      return d.toLocaleDateString("es-MX", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    };

    const formatPrice = (price: number) => {
      return price.toLocaleString("es-MX");
    };

    // 🆕 NUEVO: Función avanzada para procesar texto con markdown y badges
    const processTextFormatting = (
      text: string
    ): Array<{
      type: "text" | "badge" | "bold" | "italic" | "code" | "strikethrough";
      content: string;
      raw?: string; // Texto original sin procesar (para casos especiales)
    }> => {
      if (!text) return [];

      const result: Array<{
        type: "text" | "badge" | "bold" | "italic" | "code" | "strikethrough";
        content: string;
        raw?: string;
      }> = [];

      // Expresiones regulares para diferentes formatos (orden de prioridad)
      const patterns = [
        { type: "badge" as const, regex: /\(\(([^)]+)\)\)/g, priority: 1 },
        { type: "code" as const, regex: /`([^`]+)`/g, priority: 2 },
        { type: "bold" as const, regex: /\*\*([^*]+)\*\*/g, priority: 3 },
        { type: "strikethrough" as const, regex: /~~([^~]+)~~/g, priority: 4 },
        {
          type: "italic" as const,
          regex: /(?<!\*)\*([^*]+)\*(?!\*)/g,
          priority: 5,
        }, // Evita conflicto con bold
        { type: "italic" as const, regex: /_([^_]+)_/g, priority: 6 },
      ];

      // Encontrar todas las coincidencias con su posición
      const matches: Array<{
        type: "text" | "badge" | "bold" | "italic" | "code" | "strikethrough";
        content: string;
        start: number;
        end: number;
        raw: string;
        priority: number;
      }> = [];

      patterns.forEach((pattern) => {
        let match;
        pattern.regex.lastIndex = 0; // Reset regex
        while ((match = pattern.regex.exec(text)) !== null) {
          matches.push({
            type: pattern.type,
            content: match[1], // Contenido capturado (sin marcadores)
            start: match.index,
            end: match.index + match[0].length,
            raw: match[0], // Texto completo con marcadores
            priority: pattern.priority,
          });
        }
      });

      // Ordenar por posición y prioridad (menor prioridad = mayor importancia)
      matches.sort((a, b) => {
        if (a.start !== b.start) return a.start - b.start;
        return a.priority - b.priority;
      });

      // Resolver conflictos (patrones que se superponen)
      const resolvedMatches = [];
      for (let i = 0; i < matches.length; i++) {
        const current = matches[i];
        let isValid = true;

        // Verificar si se superpone con matches ya aceptados
        for (const resolved of resolvedMatches) {
          if (
            (current.start >= resolved.start && current.start < resolved.end) ||
            (current.end > resolved.start && current.end <= resolved.end) ||
            (current.start <= resolved.start && current.end >= resolved.end)
          ) {
            // Hay superposición, mantener el de mayor prioridad (menor número)
            if (current.priority >= resolved.priority) {
              isValid = false;
              break;
            }
          }
        }

        if (isValid) {
          resolvedMatches.push(current);
        }
      }

      // Reordenar por posición
      resolvedMatches.sort((a, b) => a.start - b.start);

      // Construir resultado final
      let lastIndex = 0;

      resolvedMatches.forEach((match) => {
        // Agregar texto antes del formato
        if (match.start > lastIndex) {
          const textBefore = text.slice(lastIndex, match.start);
          if (textBefore) {
            result.push({ type: "text", content: textBefore });
          }
        }

        // Agregar el elemento formateado
        result.push({
          type: match.type,
          content: match.content,
          raw: match.raw,
        });
        lastIndex = match.end;
      });

      // Agregar texto restante
      if (lastIndex < text.length) {
        const textAfter = text.slice(lastIndex);
        if (textAfter) {
          result.push({ type: "text", content: textAfter });
        }
      }

      // Si no hay formatos, devolver todo como texto
      if (result.length === 0) {
        result.push({ type: "text", content: text });
      }

      return result;
    };

    // 🔧 DEPRECATED: Función legacy para badges (mantenida para compatibilidad)
    const processBadges = (
      text: string
    ): Array<{ type: "text" | "badge"; content: string }> => {
      // Usar la nueva función pero filtrar solo badges y texto
      return processTextFormatting(text).map((item) => ({
        type: item.type === "badge" ? "badge" : ("text" as "text" | "badge"),
        content: item.content,
      }));
    };

    // 🆕 NUEVO: Función para verificar si un texto contiene formatos (badges, markdown, etc.)
    const hasTextFormatting = (text: string): boolean => {
      if (!text) return false;

      // Verificar badges, markdown y otros formatos
      return (
        /\(\([^)]+\)\)/.test(text) || // Badges ((texto))
        /\*\*[^*]+\*\*/.test(text) || // Negritas **texto**
        /(?<!\*)\*[^*]+\*(?!\*)/.test(text) || // Cursiva *texto*
        /_[^_]+_/.test(text) || // Cursiva _texto_
        /`[^`]+`/.test(text) || // Código `texto`
        /~~[^~]+~~/.test(text) // Tachado ~~texto~~
      );
    };

    // 🔧 DEPRECATED: Función legacy para verificar badges (mantenida para compatibilidad)
    const hasBadges = (text: string): boolean => {
      return /\(\([^)]+\)\)/.test(text || "");
    };

    const getTypeLabel = (type: string) => {
      // Devolver el tipo, pero solo con la primer letra en mayúscula
      return type.charAt(0).toUpperCase() + type.slice(1);
    };

    const getLocationLabel = (location: string) => {
      switch (location) {
        case "norte":
          return "Zona Norte";
        case "sur":
          return "Zona Sur";
        case "este":
          return "Zona Este";
        case "oeste":
          return "Zona Oeste";
        case "centro":
          return "Centro";
        default:
          return location;
      }
    };

    // 🆕 REFACTORIZADO: Aplicar filtros usando reactividad DDP
    const applyFilters = async () => {
      console.log("🔍 Aplicando filtros...");
      postsLimit.value = POSTS_INCREMENT; // Reiniciar límite
      hasMorePosts.value = true;

      // Re-suscribirse con nuevos filtros (DDP manejará la actualización)
      await inmobiliarioService.subscribeToPosts(filters, postsLimit.value);
    };

    // 🆕 SCROLL INFINITO: Cargar más posts cuando se hace scroll
    const loadMorePosts = async () => {
      if (isLoadingMore.value || !hasMorePosts.value) {
        return; // Evitar cargas múltiples
      }

      try {
        isLoadingMore.value = true;
        isLoadingByScrollInfinito.value = true; // 🔧 FIX: Marcar que estamos cargando por scroll infinito
        console.log(
          `📦 Cargando más posts por SCROLL INFINITO, límite actual: ${
            postsLimit.value
          } -> ${postsLimit.value + POSTS_INCREMENT}`
        );

        const previousCount = posts.value.length;
        postsLimit.value += POSTS_INCREMENT; // Incrementar límite

        // 🚀 OPTIMIZACIÓN DDP: Re-suscribirse con el nuevo límite
        // DDP automáticamente enviará solo los posts adicionales
        await inmobiliarioService.subscribeToPosts(filters, postsLimit.value);

        // Esperar un poco para que DDP procese la suscripción
        await new Promise((resolve) => setTimeout(resolve, 500)); // 🔧 FIX: Incrementar timeout

        const newCount = posts.value.length;

        // Si no hay nuevos posts, hemos llegado al final
        if (newCount <= previousCount) {
          hasMorePosts.value = false;
          console.log("📍 No hay más posts disponibles");
        } else {
          console.log(
            `✅ ${
              newCount - previousCount
            } posts adicionales cargados por SCROLL INFINITO`
          );
        }
      } catch (error) {
        console.error("❌ Error cargando más posts:", error);
      } finally {
        isLoadingMore.value = false;
        // 🔧 FIX: Resetear flag después de un delay para permitir procesamiento
        setTimeout(() => {
          isLoadingByScrollInfinito.value = false;
        }, 1000);
      }
    };

    // Helper para normalizar IDs de posts
    const normalizePostId = (post: any): string | null => {
      if (post._id && typeof post._id === "string") {
        return post._id;
      }
      if (post.id && typeof post.id === "string") {
        post._id = post.id; // Normalizar para uso futuro
        return post.id;
      }
      return null;
    };

    // 🔧 REMOVIDO: toggleInterested eliminado

    const openComments = async (post: PostInmobiliario) => {
      post.showComments = !post.showComments;

      // Si se abren los comentarios, suscribirse y configurar reactividad
      if (post.showComments) {
        try {
          const postId = normalizePostId(post);
          if (!postId) {
            console.error("Post ID no válido:", post);
            post.comments = [];
            return;
          }

          // ✅ Cargar comentarios iniciales via subscription
          post.comments = await inmobiliarioService.getComments(postId);

          // 🚀 La subscription ya está activa y actualizará automáticamente
          // cualquier cambio (nuevos comentarios, eliminaciones, etc.)
        } catch (error) {
          console.error("Error al cargar comentarios:", error);
          post.comments = [];
        }
      }
    };

    const addComment = async (post: PostInmobiliario) => {
      // Usar el ID normalizado para el key de newComments
      const postId = normalizePostId(post);
      if (!postId || !newComments[postId]) return;

      try {
        // ✅ Solo agregar el comentario - la subscription se encarga del resto
        await inmobiliarioService.addComment(postId, newComments[postId]);
        newComments[postId] = "";
        // 🚀 Los comentarios se actualizan automáticamente via subscription DDP
        // 🚀 post.showComments permanece true automáticamente
      } catch (error) {
        console.error("Error al agregar comentario:", error);
      }
    };

    // 🆕 NUEVO: Función para transformar PostInmobiliario a formato Inmueble
    const transformPostToInmueble = (post: PostInmobiliario): any => {
      // Usar la operación del inmueble relacionado si está disponible, sino asumir "venta"
      const operacion = post.relatedInmueble?.operacion || "venta";

      // Usar el tipo del inmueble relacionado si está disponible, sino usar propertyType
      const tipo = post.relatedInmueble?.tipo || post.propertyType || "casa";

      return {
        id: post._id,
        titulo: post.title,
        descripcion: post.description,
        precio: post.relatedInmueble?.precio || post.price,
        operacion,
        tipo,
        ubicacion: post.location,
        recamaras: post.bedrooms,
        banos: post.bathrooms,
        area: post.area,
        imagenPrincipal: post.relatedInmueble?.image || post.images?.[0] || "",
        imagenes: post.relatedInmueble?.imagenes || post.images || [],
        socio: {
          id: post.authorId,
          nombre: getAuthorFullName(post.authorCache),
          empresa: post.authorCache?.company || "Sin empresa",
          avatar: post.authorCache?.avatar,
          telefono: (post.authorCache as any)?.phone,
          whatsapp: (post.authorCache as any)?.whatsapp,
          email: (post.authorCache as any)?.email,
        },
        esFavorito: post.isFavorited || false,
        fechaCreacion: post.createdAt,
      };
    };

    // DETALLES INMUEBLE: Función para abrir detalle de inmueble desde galería
    const detallesInmueble = (post: PostInmobiliario, _index: number) => {
      // Transformar el post a formato inmueble
      const inmueble = transformPostToInmueble(post);

      // Abrir modal de detalle
      inmuebleSeleccionado.value = inmueble;
      showDetalleInmuebleModal.value = true;

      console.log(`🏠 Abriendo detalle de inmueble: ${post.title}`);
    };

    // 🆕 NUEVO: Función para contactar socio desde modal
    const contactarSocioDesdeModal = (socio: any, metodo: string) => {
      if (metodo === "whatsapp" && socio.whatsapp) {
        const mensaje = encodeURIComponent(
          `Hola ${socio.nombre}, me interesa tu inmueble.`
        );
        const url = `https://wa.me/${socio.whatsapp}?text=${mensaje}`;
        window.open(url, "_blank");
      } else if (metodo === "telefono" && socio.telefono) {
        window.open(`tel:${socio.telefono}`, "_self");
      } else if (metodo === "email" && socio.email) {
        const asunto = encodeURIComponent("Consulta sobre inmueble");
        const cuerpo = encodeURIComponent(
          `Hola ${socio.nombre},\n\nMe interesa obtener más información sobre tu inmueble.\n\nSaludos.`
        );
        window.open(
          `mailto:${socio.email}?subject=${asunto}&body=${cuerpo}`,
          "_self"
        );
      }
    };

    // 🆕 NUEVO: Manejador para cuando se crea un post desde el formulario
    const handlePostCreated = async () => {
      showNewPostForm.value = false;
      // DDP actualizará automáticamente el feed
      success("Publicación creada exitosamente", "¡Éxito!");
    };

    // 🚀 REFACTORIZADO: Función de inicialización única (NO refresh repetitivo)
    const initializeFeed = async () => {
      // Solo mostrar loading en inicialización real
      loading.value = true;
      connectionError.value = false;
      connectionErrorMessage.value = "";

      try {
        console.log("🚀 Inicializando feed del muro (UNA SOLA VEZ)...");

        // 1️⃣ CONECTAR al servicio DDP con autenticación si tenemos token
        if (token.value) {
          await inmobiliarioService.connectWithToken(token.value);
        } else {
          await inmobiliarioService.connect();
        }

        // 2️⃣ SUSCRIBIRSE una sola vez (DDP mantendrá los datos sincronizados)
        console.log(
          `📡 Suscribiéndose a ${postsLimit.value} posts con filtros:`,
          filters
        );
        await inmobiliarioService.subscribeToPosts(filters, postsLimit.value);

        // 3️⃣ ESTADO INICIAL: Obtener posts del cache DDP una sola vez
        const initialPosts = inmobiliarioService.getPosts(
          false,
          filters
        ) as PostInmobiliario[];

        // Filtrar posts con ID válido
        const validPosts = initialPosts.filter((post) => {
          const normalizedId = normalizePostId(post);
          if (!normalizedId) {
            console.warn(
              "⚠️ Filtrando post sin ID válido en inicialización:",
              post
            );
            return false;
          }
          return true;
        });

        // 4️⃣ ASIGNAR estado inicial (solo una vez)
        posts.value = validPosts;
        console.log(`📊 ${posts.value.length} posts iniciales cargados`);

        // 5️⃣ CONFIGURAR REACTIVIDAD (solo una vez)
        if (!unsubscribeFromChanges) {
          console.log(
            "👂 Configurando observer reactivo de posts (UNA SOLA VEZ)..."
          );
          unsubscribeFromChanges = inmobiliarioService.onPostsChange(
            (newPosts) => {
              console.log(
                `🔄 DDP onChange: ${newPosts.length} posts recibidos`
              );

              // 🔍 DEBUG: Mostrar IDs de posts recibidos vs posts actuales
              const newPostIds = newPosts
                .map((p) => normalizePostId(p))
                .filter(Boolean);
              const currentPostIds = posts.value
                .map((p) => normalizePostId(p))
                .filter(Boolean);

              console.log("🔍 DEBUG onChange:", {
                nuevosIds: newPostIds,
                actualesIds: currentPostIds,
                posiblesEliminaciones: currentPostIds.filter(
                  (id) => !newPostIds.includes(id)
                ),
              });

              // 🚨 PROTECCIÓN CRÍTICA MEJORADA: Procesar arrays vacíos para permitir eliminaciones
              if (newPosts.length === 0 && posts.value.length > 0) {
                console.log(
                  `🔍 Observer DDP recibió array vacío. Procesando como posible eliminación de ${posts.value.length} posts...`
                );
                // NO hacer return aquí - dejar que handleIntelligentUpdate procese la eliminación
              }

              handleIntelligentUpdate(newPosts as PostInmobiliario[]);
            }
          );
        }

        // 6️⃣ CONFIGURAR OBSERVERS AUXILIARES (solo una vez)
        if (!unsubscribeFromCommentsChanges) {
          unsubscribeFromCommentsChanges = setupCommentsObserver();
        }
        if (!unsubscribeFromFavoritesChanges) {
          unsubscribeFromFavoritesChanges = setupFavoritesObserver();
        }

        // 7️⃣ CARGAR ESTADOS AUXILIARES
        await cargarEstadoFavoritos();
        await updateCountersForCurrentPosts();

        console.log("✅ Feed inicializado correctamente con reactividad DDP");
      } catch (error) {
        console.error("❌ Error al inicializar feed:", error);
        connectionError.value = true;
        connectionErrorMessage.value =
          error instanceof Error ? error.message : "Error inesperado";

        // Fallback: datos de prueba
        try {
          posts.value = generarPostsPrueba();
          hasMorePosts.value = false;
        } catch (e) {
          console.error("❌ Error al generar datos de prueba:", e);
        }
      } finally {
        loading.value = false;
        isInitialLoad.value = false;
      }
    };

    // ✅ Helper para obtener nombre completo del autor
    const getAuthorFullName = (authorCache: any) => {
      if (authorCache.firstName || authorCache.lastName) {
        return `${authorCache.firstName || ""} ${
          authorCache.lastName || ""
        }`.trim();
      }
      // Compatibilidad hacia atrás con estructura antigua
      return authorCache.name || "Usuario";
    };

    // Función para generar datos de prueba en caso de error
    const generarPostsPrueba = () => {
      const tipos = ["venta", "renta", "socio", "intercambio"];
      const ubicaciones = ["norte", "sur", "este", "oeste", "centro"];

      return Array.from({ length: 5 }, (_, i) => ({
        _id: `mock-${i}`,
        type: tipos[i % tipos.length] as any,
        title: `Propiedad de prueba #${i + 1}`,
        description:
          "Datos generados localmente. No se pudo conectar al servidor.",
        price: 1000000 * (i + 1),
        location: ubicaciones[i % ubicaciones.length],
        createdAt: new Date().toISOString(),

        // ✅ ESTRUCTURA CONSOLIDADA
        authorId: "local-user",
        authorCache: {
          firstName: "Usuario",
          lastName: "Local",
          avatar: "https://randomuser.me/api/portraits/lego/1.jpg",
          company: "Sistema Local",
          verified: false,
        },

        images: [],
        commentsCount: 0,
        active: true,
        isPublic: true,
      }));
    };

    // Método para generar datos de ejemplo
    const generarDatosEjemplo = async () => {
      try {
        generandoDatos.value = true;
        mensajeAccion.value = "Generando datos de ejemplo...";
        mensajeExito.value = true;

        // Conectar al servicio DDP con autenticación si tenemos token
        if (token.value) {
          await inmobiliarioService.connectWithToken(token.value);
        } else {
          await inmobiliarioService.connect();
        }

        // Llamar al método del servidor para generar datos
        const resultado = await inmobiliarioService.call(
          "demo.generatePostsData",
          15
        );

        mensajeAccion.value = resultado.message;
        mensajeExito.value = resultado.success;

        // Refrescar datos
        await loadMorePosts();
      } catch (error) {
        console.error("Error al generar datos:", error);
        mensajeAccion.value =
          error instanceof Error
            ? error.message
            : "Error al generar datos de ejemplo";
        mensajeExito.value = false;
      } finally {
        generandoDatos.value = false;
      }
    };

    // Método para limpiar datos de ejemplo
    const limpiarDatosEjemplo = async () => {
      await confirm(
        "¿Estás seguro de querer eliminar todos los datos?",
        async () => {
          await limpiarDatosConfirmado();
        },
        "Confirmar eliminación"
      );
    };

    const limpiarDatosConfirmado = async () => {
      try {
        generandoDatos.value = true;
        mensajeAccion.value = "Eliminando datos...";
        mensajeExito.value = true;

        // Conectar al servicio DDP con autenticación si tenemos token
        if (token.value) {
          await inmobiliarioService.connectWithToken(token.value);
        } else {
          await inmobiliarioService.connect();
        }

        // Llamar al método del servidor para limpiar datos
        const resultado = await inmobiliarioService.call("demo.clearAllData");

        mensajeAccion.value = resultado.message;
        mensajeExito.value = resultado.success;

        // Refrescar datos
        await loadMorePosts();
      } catch (error) {
        console.error("Error al limpiar datos:", error);
        mensajeAccion.value =
          error instanceof Error
            ? error.message
            : "Error al limpiar datos de ejemplo";
        mensajeExito.value = false;
      } finally {
        generandoDatos.value = false;
      }
    };

    // Helper para obtener el ID de un post (para usar en template)
    const getPostId = (post: any): string => {
      return normalizePostId(post) || "unknown";
    };

    // 🆕 NUEVO: Helper para verificar si una publicación tiene inmueble relacionado
    const tieneInmuebleRelacionado = (post: PostInmobiliario): boolean => {
      // Verificar el nuevo campo relatedInmueble (desde NuevaPublicacionForm)
      if (post.relatedInmueble && post.relatedInmueble.id) {
        return true;
      }
      // Mantener compatibilidad con campos legacy (externalId/externalKey)
      return !!(post.externalId && post.externalKey);
    };

    // 🆕 NUEVO: Helper para obtener clases CSS del badge según el tipo de post
    const getBadgeClasses = (post: PostInmobiliario): string => {
      // Si hay inmueble relacionado, usar su operación para el color
      if (post.relatedInmueble?.operacion) {
        switch (post.relatedInmueble.operacion) {
          case "venta":
            return "bg-green-100 text-green-800";
          case "renta":
            return "bg-blue-100 text-blue-800";
          case "traspaso":
            return "bg-orange-100 text-orange-800";
          default:
            return "bg-gray-100 text-gray-800";
        }
      }

      // Si no hay inmueble relacionado, usar el tipo de post
      switch (post.type) {
        case "inmueble":
          return "bg-green-100 text-green-800";
        case "cliente":
          return "bg-blue-100 text-blue-800";
        case "invitacion":
          return "bg-purple-100 text-purple-800";
        case "noticia":
          return "bg-amber-100 text-amber-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
    };

    // 🎯 REACTIVIDAD INTELIGENTE: Diferenciar entre nuevas publicaciones y otros cambios
    const handleIntelligentUpdate = async (newPosts: PostInmobiliario[]) => {
      try {
        // 🚨 PROTECCIÓN CRÍTICA MEJORADA: Solo bloquear si NO es una eliminación legítima
        if (newPosts.length === 0 && posts.value.length > 0) {
          // Si tenemos posts y recibimos array vacío, podría ser una eliminación legítima
          // Verificar si es una situación donde todos los posts fueron eliminados
          console.log(
            `🔍 Array vacío recibido. Verificando si es eliminación legítima...`
          );

          // En lugar de bloquear, procesar como array vacío para permitir eliminaciones
          // La lógica de analyzePostChanges detectará correctamente las eliminaciones
          console.log(
            `✅ Procesando array vacío como posible eliminación de ${posts.value.length} posts`
          );
        }

        // 🔧 FIX SCROLL INFINITO: Si estamos cargando por scroll infinito, agregar posts directamente
        if (isLoadingByScrollInfinito.value) {
          console.log(
            `🚀 SCROLL INFINITO ACTIVO: Agregando ${newPosts.length} posts directamente sin filtros`
          );
          await mergePostsPreservingUIState(newPosts);
          return; // Salir - no aplicar lógica de notificaciones
        }

        // 1️⃣ Obtener IDs de posts actualmente visibles en el feed
        const currentVisiblePostIds = new Set(
          posts.value
            .map((post) => normalizePostId(post))
            .filter((id): id is string => Boolean(id))
        );

        // 2️⃣ Analizar qué tipo de cambios hay
        const analysis = analyzePostChanges(newPosts, currentVisiblePostIds);

        console.log(`🔍 Análisis de cambios:`, analysis);

        // 3️⃣ 🔧 FIX: Procesar eliminaciones y actualizaciones
        if (analysis.hasRemovals || analysis.hasUpdates) {
          if (analysis.hasRemovals) {
            console.log(
              `🗑️ Procesando ${
                analysis.removedCount
              } eliminaciones: ${analysis.removedPostIds.join(", ")}`
            );

            // ✅ ELIMINAR posts que ya no están en newPosts
            posts.value = posts.value.filter((post) => {
              const postId = normalizePostId(post);
              if (!postId) return true; // Mantener posts sin ID válido

              // Mantener solo si NO está en la lista de eliminados
              return !analysis.removedPostIds.includes(postId);
            });

            console.log(
              `✅ Eliminaciones aplicadas: ${posts.value.length} posts restantes`
            );
          }

          if (analysis.hasUpdates) {
            // ✅ ACTUALIZAR posts existentes
            const postsToUpdate = newPosts.filter((post) => {
              const postId = normalizePostId(post);
              if (!postId) return false;

              // Incluir solo posts que necesitan actualización y están visibles
              return (
                currentVisiblePostIds.has(postId) &&
                analysis.updatedPosts.some(
                  (updatedPost) => normalizePostId(updatedPost) === postId
                )
              );
            });

            if (postsToUpdate.length > 0) {
              console.log(
                `🔄 Aplicando ${postsToUpdate.length} actualizaciones`
              );
              await mergePostsPreservingUIState(postsToUpdate);
            }
          }
        }

        // 4️⃣ Para nuevas publicaciones, solo notificar sin mostrar (SOLO cuando NO es scroll infinito)
        if (analysis.hasNewPosts) {
          console.log(
            `🔔 ${analysis.newPostsCount} nuevas publicaciones detectadas (pendientes de mostrar)`
          );
          checkForNewNotifications(newPosts);
        }

        // 5️⃣ Si no hay nuevas publicaciones, actualizar timestamp
        if (
          !analysis.hasNewPosts &&
          (analysis.hasRemovals || analysis.hasUpdates)
        ) {
          lastNotificationCheck.value = new Date();
        }
      } catch (error) {
        console.error("❌ Error en actualización inteligente:", error);
        // Fallback: aplicar todos los cambios
        await mergePostsPreservingUIState(newPosts);
      }
    };

    // 🔍 FUNCIÓN AUXILIAR: Analizar tipos de cambios en los posts
    const analyzePostChanges = (
      newPosts: PostInmobiliario[],
      currentVisibleIds: Set<string>
    ) => {
      const newPostIds = new Set(
        newPosts
          .map((post) => normalizePostId(post))
          .filter((id): id is string => Boolean(id))
      );

      // Detectar eliminaciones (posts que estaban visibles pero ya no están en newPosts)
      const removedPosts = Array.from(currentVisibleIds).filter(
        (id) => !newPostIds.has(id)
      );

      console.log("🔍 DEBUG analyzePostChanges:", {
        currentVisibleIds: Array.from(currentVisibleIds),
        newPostIds: Array.from(newPostIds),
        removedPosts: removedPosts,
        removedCount: removedPosts.length,
      });

      // Detectar nuevas publicaciones (posts en newPosts que no están visibles)
      const newPosts_filtered = newPosts.filter((post) => {
        const postId = normalizePostId(post);
        if (!postId) return false;

        const isNew = !currentVisibleIds.has(postId);
        const isRecent = new Date(post.createdAt) > lastNotificationCheck.value;

        return isNew && isRecent;
      });

      // Detectar actualizaciones (posts que están en ambos lados, comparar por fecha de modificación)
      const updatedPosts = newPosts.filter((post) => {
        const postId = normalizePostId(post);
        if (!postId || !currentVisibleIds.has(postId)) return false;

        // Buscar el post actual en el feed visible
        const currentPost = posts.value.find(
          (p) => normalizePostId(p) === postId
        );
        if (!currentPost) return false;

        // 🔧 MEJORADO: Detectar cambios específicos importantes
        const hasContentChanges =
          post.title !== currentPost.title ||
          post.description !== currentPost.description ||
          post.price !== currentPost.price ||
          post.location !== currentPost.location ||
          (post.images?.length || 0) !== (currentPost.images?.length || 0);

        // 🔧 NUEVO: Detectar cambios en contadores (comentarios, favoritos, etc.)
        const hasCounterChanges =
          post.commentsCount !== currentPost.commentsCount ||
          (post.favorites || 0) !== (currentPost.favorites || 0);

        // 🔧 NUEVO: Solo usar fechas para cambios de contenido, no contadores
        const newModifiedAt = new Date(post.updatedAt || post.createdAt);
        const currentModifiedAt = new Date(
          currentPost.updatedAt || currentPost.createdAt
        );
        const hasTimestampChanges = newModifiedAt > currentModifiedAt;

        // ✅ Considerar actualización si:
        // 1. Hay cambios de contenido con timestamp más reciente
        // 2. O hay cambios en contadores (independiente del timestamp)
        return (hasContentChanges && hasTimestampChanges) || hasCounterChanges;
      });

      return {
        hasRemovals: removedPosts.length > 0,
        hasNewPosts: newPosts_filtered.length > 0,
        hasUpdates: updatedPosts.length > 0,
        removedCount: removedPosts.length,
        newPostsCount: newPosts_filtered.length,
        updatedCount: updatedPosts.length,
        removedPostIds: removedPosts,
        newPosts: newPosts_filtered,
        updatedPosts: updatedPosts,
      };
    };

    // 🎯 COMPORTAMIENTO COMO TWITTER/FACEBOOK: Detectar nuevas publicaciones sin mostrarlas
    const checkForNewNotifications = (newPosts: PostInmobiliario[]) => {
      try {
        // 1️⃣ Obtener IDs de posts actualmente visibles en el feed
        const currentVisiblePostIds = new Set(
          posts.value
            .map((post) => normalizePostId(post))
            .filter((id): id is string => Boolean(id))
        );

        // 2️⃣ Encontrar posts que están en el cache DDP pero NO en el feed visible
        const newPostsNotVisible = newPosts.filter((post) => {
          const postId = normalizePostId(post);
          if (!postId) return false;

          // Solo considerar posts que NO están actualmente visibles
          const isNotVisible = !currentVisiblePostIds.has(postId);

          // Y que son más recientes que la última verificación
          const postDate = new Date(post.createdAt);
          const isRecent = postDate > lastNotificationCheck.value;

          return isNotVisible && isRecent;
        });

        console.log(`🔍 Verificación de nuevas publicaciones:`, {
          totalEnCache: newPosts.length,
          visiblesEnFeed: currentVisiblePostIds.size,
          nuevasNoVisibles: newPostsNotVisible.length,
          ultimaVerificacion: lastNotificationCheck.value.toISOString(),
        });

        // 3️⃣ Si hay posts nuevos no visibles, mostrar barra de notificaciones
        if (newPostsNotVisible.length > 0) {
          hasNewNotifications.value = true;
          newNotificationsCount.value = newPostsNotVisible.length;

          console.log(
            `🔔 ${newPostsNotVisible.length} nuevas publicaciones detectadas (pendientes de mostrar)`
          );
        }
      } catch (error) {
        console.error("❌ Error verificando nuevas notificaciones:", error);
      }
    };

    // 🚀 OPTIMIZADO: Botón de refresh que usa datos ya disponibles (SIN llamadas redundantes)
    const loadNewNotifications = async () => {
      if (refreshingNotifications.value) return; // Evitar múltiples refreshes simultáneos

      console.log(
        "🚀 Integrando nuevas publicaciones usando datos ya disponibles (SIN websocket)..."
      );
      refreshingNotifications.value = true;

      try {
        // 🎯 OPTIMIZACIÓN MÁXIMA: Los datos YA ESTÁN en el cache DDP local
        // NO necesitamos hacer llamadas async - es instantáneo
        console.log(
          "📊 Obteniendo posts del cache DDP local (0ms latencia)..."
        );

        const allPostsInCache = inmobiliarioService.getPosts(
          false,
          filters
        ) as PostInmobiliario[];

        // 🔧 FILTRAR posts válidos (operación local, no async)
        const validPosts = allPostsInCache.filter((post: PostInmobiliario) => {
          const normalizedId = normalizePostId(post);
          if (!normalizedId) {
            console.warn("⚠️ Post sin ID válido:", post);
            return false;
          }
          return true;
        });

        console.log(
          `🚀 Integrando ${validPosts.length} posts desde cache local (instantáneo)`
        );

        // 🚨 PROTECCIÓN CRÍTICA: No hacer merge si validPosts está vacío
        if (validPosts.length === 0 && posts.value.length > 0) {
          console.warn(
            `⚠️ PROTECCIÓN: loadNewNotifications - validPosts vacío. Manteniendo ${posts.value.length} posts existentes.`
          );
        } else {
          // 🎯 MERGE DIRECTO: Los datos ya están actualizados por DDP
          await mergePostsPreservingUIState(validPosts as PostInmobiliario[]);
        }

        // 🔄 ACTUALIZAR timestamp y ocultar notificaciones
        lastNotificationCheck.value = new Date();
        hasNewNotifications.value = false;
        newNotificationsCount.value = 0;

        // ✅ OPTIMIZACIÓN: Solo cargar estados si realmente es necesario
        // Los favoritos y contadores ya deberían estar actualizados por los observers
        const needsStateReload = posts.value.some(
          (post: PostInmobiliario) =>
            post.isFavorited === undefined || post.commentsCount === undefined
        );

        if (needsStateReload) {
          console.log(
            "🔄 Recargando estados auxiliares (solo si es necesario)..."
          );
          await cargarEstadoFavoritos();
          await updateCountersForCurrentPosts();
        } else {
          console.log("✅ Estados auxiliares ya actualizados por observers");
        }

        console.log(
          "✅ Feed actualizado instantáneamente usando cache DDP local"
        );
      } catch (integrationError) {
        console.error("❌ Error integrando publicaciones:", integrationError);

        // Fallback: solo ocultar notificaciones sin reinicializar
        hasNewNotifications.value = false;
        newNotificationsCount.value = 0;
        error("Error al actualizar el feed", "Error");
      } finally {
        refreshingNotifications.value = false;
      }
    };

    const dismissNotifications = () => {
      hasNewNotifications.value = false;
      newNotificationsCount.value = 0;
      lastNotificationCheck.value = new Date();
    };

    // 🆕 MEJORADO: Hard refresh del feed siguiendo principios DDP (como F5/CMD+R)
    const refreshFeed = async () => {
      if (refreshingFeed.value) return; // Evitar múltiples refreshes simultáneos

      console.log(
        "🔄 Iniciando HARD REFRESH del feed (como F5/CMD+R pero siguiendo principios DDP)..."
      );
      refreshingFeed.value = true;

      try {
        // 🧹 PASO 1: LIMPIAR COMPLETAMENTE EL ESTADO LOCAL (Hard Reset)
        console.log("🧹 Limpiando estado local completamente...");

        // Resetear arrays y contadores
        posts.value = [];
        unreadCountersByPost.value = {};
        totalHilosCountByPost.value = {};
        hilosDelPost.value = {};

        // Resetear estados de UI
        hasNewNotifications.value = false;
        newNotificationsCount.value = 0;
        lastNotificationCheck.value = new Date();

        // Resetear menús abiertos
        menuAutorAbierto.value = null;
        menuPostAbierto.value = null;
        menuComentarioAbierto.value = null;
        menuHilosAbierto.value = null;

        // Resetear estados de carga
        loading.value = true;
        connectionError.value = false;
        connectionErrorMessage.value = "";

        // Resetear scroll infinito
        postsLimit.value = 5; // Volver al límite inicial
        isLoadingMore.value = false;
        hasMorePosts.value = true;
        isInitialLoad.value = true;

        // Resetear sistema de respaldo
        lastValidPostsCount.value = 0;
        feedBackup.value = [];

        // 🔌 PASO 2: DESCONECTAR Y LIMPIAR OBSERVERS EXISTENTES (Hard Reset)
        console.log("🔌 Desconectando y limpiando observers existentes...");

        // Limpiar observers de posts
        if (unsubscribeFromChanges) {
          unsubscribeFromChanges();
          unsubscribeFromChanges = null;
        }

        // Limpiar observers de comentarios
        if (unsubscribeFromCommentsChanges) {
          unsubscribeFromCommentsChanges();
          unsubscribeFromCommentsChanges = null;
        }

        // Limpiar observers de favoritos
        if (unsubscribeFromFavoritesChanges) {
          unsubscribeFromFavoritesChanges();
          unsubscribeFromFavoritesChanges = null;
        }

        // Limpiar observers de contadores
        if (unsubscribeFromUnreadCountersChanges) {
          unsubscribeFromUnreadCountersChanges();
          unsubscribeFromUnreadCountersChanges = null;
        }

        // Limpiar observers de hilos
        if (unsubscribeFromTotalHilosChanges) {
          unsubscribeFromTotalHilosChanges();
          unsubscribeFromTotalHilosChanges = null;
        }

        // Limpiar observer de mensajes nuevos
        if (unsubscribeFromNewMessagesObserver) {
          unsubscribeFromNewMessagesObserver();
          unsubscribeFromNewMessagesObserver = null;
        }

        // ⏱️ PASO 3: PAUSA BREVE PARA ASEGURAR LIMPIEZA COMPLETA
        console.log("⏱️ Pausa breve para asegurar limpieza de observers...");
        await new Promise((resolve) => setTimeout(resolve, 300));

        // 🚀 PASO 4: RECONECTAR Y RE-INICIALIZAR COMPLETAMENTE (como initializeFeed)
        console.log("🚀 Reconectando y re-inicializando completamente...");

        // Re-conectar al servicio DDP con autenticación
        if (token.value) {
          await inmobiliarioService.connectWithToken(token.value);
        } else {
          await inmobiliarioService.connect();
        }

        // Re-suscribirse a posts con filtros actuales
        console.log(
          `📡 Re-suscribiéndose a ${postsLimit.value} posts con filtros:`,
          filters
        );
        await inmobiliarioService.subscribeToPosts(filters, postsLimit.value);

        // Obtener estado inicial fresco del cache DDP
        const initialPosts = inmobiliarioService.getPosts(
          false,
          filters
        ) as PostInmobiliario[];

        // Filtrar posts con ID válido
        const validPosts = initialPosts.filter((post) => {
          const normalizedId = normalizePostId(post);
          if (!normalizedId) {
            console.warn(
              "⚠️ Filtrando post sin ID válido en hard refresh:",
              post
            );
            return false;
          }
          return true;
        });

        // Asignar estado inicial fresco
        posts.value = validPosts;
        console.log(
          `📊 ${posts.value.length} posts iniciales cargados tras hard refresh`
        );

        // 🔄 PASO 5: RE-CONFIGURAR TODOS LOS OBSERVERS (Hard Reset)
        console.log("🔄 Re-configurando todos los observers...");

        // Re-configurar observer de posts
        unsubscribeFromChanges = inmobiliarioService.onPostsChange(
          (newPosts) => {
            console.log(`🔄 DDP onChange: ${newPosts.length} posts recibidos`);

            // Protección crítica
            if (newPosts.length === 0 && posts.value.length > 0) {
              console.warn(
                `⚠️ PROTECCIÓN: Observer DDP recibió array vacío. Ignorando para preservar ${posts.value.length} posts existentes.`
              );
              return;
            }

            handleIntelligentUpdate(newPosts as PostInmobiliario[]);
          }
        );

        // Re-configurar observers auxiliares
        unsubscribeFromCommentsChanges = setupCommentsObserver();
        unsubscribeFromFavoritesChanges = setupFavoritesObserver();
        setupUnreadCountersObserver();
        setupTotalHilosObserver();
        unsubscribeFromNewMessagesObserver = setupNewMessagesObserver();

        // 🔄 PASO 6: RECARGAR TODOS LOS ESTADOS AUXILIARES
        console.log("🔄 Recargando todos los estados auxiliares...");
        await cargarEstadoFavoritos();
        await updateCountersForCurrentPosts();

        // 🆕 PASO 7: RE-CONFIGURAR SCROLL INFINITO (Intersection Observer)
        console.log("🔄 Re-configurando scroll infinito tras hard refresh...");

        // Limpiar observer anterior si existe
        if (cleanupIntersectionObserver) {
          cleanupIntersectionObserver();
          cleanupIntersectionObserver = null;
        }

        // Re-configurar intersection observer con un breve delay para asegurar que el DOM esté listo
        setTimeout(() => {
          cleanupIntersectionObserver = setupIntersectionObserver();
          if (cleanupIntersectionObserver) {
            console.log(
              "✅ Scroll infinito re-configurado exitosamente tras hard refresh"
            );
          } else {
            console.warn(
              "⚠️ No se pudo re-configurar scroll infinito - sentinelRef no disponible"
            );
          }
        }, 500); // Delay ligeramente mayor para asegurar que el DOM se haya actualizado

        console.log(
          "✅ HARD REFRESH completado exitosamente - Feed reinicializado desde cero siguiendo principios DDP"
        );
      } catch (error) {
        console.error("❌ Error en hard refresh del feed:", error);

        // Mostrar error al usuario
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Error al actualizar el feed";
        warning(
          `Error al refrescar completamente: ${errorMessage}`,
          "❌ Error de Hard Refresh"
        );

        // Fallback: intentar inicialización normal
        try {
          console.log("🔄 Intentando inicialización normal como fallback...");
          await initializeFeed();
        } catch (fallbackError) {
          console.error(
            "❌ Error incluso en fallback de inicialización:",
            fallbackError
          );

          // Último recurso: datos de prueba
          try {
            posts.value = generarPostsPrueba();
            hasMorePosts.value = false;
            console.log("🔄 Datos de prueba cargados como último recurso");
          } catch (lastResortError) {
            console.error(
              "❌ Error incluso en último recurso:",
              lastResortError
            );
          }
        }
      } finally {
        loading.value = false;
        isInitialLoad.value = false;
        refreshingFeed.value = false;
      }
    };

    // 🆕 NUEVO: Funciones para menú contextual de autores
    const cerrarMenuAutorAlClickFuera = (event: Event) => {
      const target = event.target as HTMLElement;
      const menuElement = target.closest(".menu-desplegable-autor");
      const botonElement = target.closest(".boton-menu-autor");

      if (!menuElement && !botonElement) {
        menuAutorAbierto.value = null;
      }
    };

    const toggleMenuAutor = (autorId: string) => {
      menuAutorAbierto.value =
        menuAutorAbierto.value === autorId ? null : autorId;
    };

    const verCatalogoAutor = (autor: any) => {
      menuAutorAbierto.value = null;
      console.log("Ver catálogo de:", getAuthorFullName(autor));
      // Aquí implementarías la lógica para ver el catálogo del autor
    };

    const verPublicacionesAutor = (autor: any) => {
      menuAutorAbierto.value = null;
      console.log("Ver publicaciones de:", getAuthorFullName(autor));
      // Aquí implementarías la lógica para ver las publicaciones del autor
    };

    const abrirTelefonoAutor = (autor: any) => {
      menuAutorAbierto.value = null;
      if (autor.telefono) {
        const url = `tel:${autor.telefono}`;
        window.open(url, "_self");
      } else {
        warning(
          "Este usuario no tiene número de teléfono registrado",
          "Teléfono no disponible"
        );
      }
    };

    const abrirWhatsAppAutor = (autor: any) => {
      menuAutorAbierto.value = null;
      if (autor.wa) {
        const mensaje = encodeURIComponent(
          `Hola ${getAuthorFullName(autor)}, espero te encuentres bien.`
        );
        const url = `https://wa.me/${autor.wa.replace(
          /[^0-9]/g,
          ""
        )}?text=${mensaje}`;
        window.open(url, "_blank");
      } else {
        warning(
          "Este usuario no tiene número de WhatsApp registrado",
          "WhatsApp no disponible"
        );
      }
    };

    const abrirTelegramAutor = (autor: any) => {
      menuAutorAbierto.value = null;
      if (autor.telegram) {
        const url = `https://t.me/${autor.telegram.replace(/[^0-9]/g, "")}`;
        window.open(url, "_blank");
      } else {
        warning(
          "Este usuario no tiene número de Telegram registrado",
          "Telegram no disponible"
        );
      }
    };

    const abrirEmailAutor = (autor: any) => {
      menuAutorAbierto.value = null;
      if (autor.email) {
        const asunto = encodeURIComponent(`Contacto desde Mulbin`);
        const cuerpo = encodeURIComponent(
          `Hola ${getAuthorFullName(
            autor
          )},\n\nEspero te encuentres bien.\n\nSaludos.`
        );
        const url = `mailto:${autor.email}?subject=${asunto}&body=${cuerpo}`;
        window.open(url, "_self");
      } else {
        warning(
          "Este usuario no tiene email registrado",
          "Email no disponible"
        );
      }
    };

    // 🆕 NUEVO: Funciones para menú contextual de PUBLICACIONES
    const cerrarMenuPostAlClickFuera = (event: Event) => {
      const target = event.target as HTMLElement;
      const menuElement = target.closest(".menu-desplegable-post");
      const botonElement = target.closest(".boton-menu-post");

      if (!menuElement && !botonElement) {
        menuPostAbierto.value = null;
      }
    };

    // 🆕 NUEVO: Toggle para menú de publicación
    const toggleMenuPost = (postId: string) => {
      // Cerrar menú de autor si está abierto
      menuAutorAbierto.value = null;

      menuPostAbierto.value = menuPostAbierto.value === postId ? null : postId;
    };

    // 🆕 NUEVO: Funciones del menú contextual de PUBLICACIONES
    const editarPublicacion = async (post: PostInmobiliario) => {
      menuPostAbierto.value = null;
      console.log("Editar publicación:", post.title);

      // TODO: Implementar modal de edición
      alert(
        `Función de editar publicación "${post.title}" pendiente de implementar`,
        "Función en desarrollo"
      );
    };

    const eliminarPublicacion = async (post: PostInmobiliario) => {
      menuPostAbierto.value = null;

      // Elimino del título '((' y '))'
      const title = post.title.replace(/\(\(/g, "").replace(/\)\)/g, "");

      confirm(
        `¿Estás seguro de que quieres eliminar permanentemente la publicación?<br/><strong>${title}</strong>`,
        async () => {
          await eliminarPublicacionConfirmada(post);
        },
        "Eliminar publicación"
      );
    };

    // Función auxiliar para eliminar publicación después de confirmación
    const eliminarPublicacionConfirmada = async (post: PostInmobiliario) => {
      try {
        const postId = normalizePostId(post);
        if (!postId) {
          throw new Error("ID de publicación no válido");
        }

        // Llamar al método del servicio
        await inmobiliarioService.removePost(postId);

        // Actualizar la UI optimísticamente
        const postIndex = posts.value.findIndex(
          (p) => normalizePostId(p) === postId
        );
        if (postIndex !== -1) {
          posts.value.splice(postIndex, 1);
        }

        console.log("✅ Publicación eliminada exitosamente");

        // Mostrar mensaje de éxito
        mensajeAccion.value = "Publicación eliminada exitosamente";
        mensajeExito.value = true;

        // Limpiar mensaje después de 3 segundos
        setTimeout(() => {
          mensajeAccion.value = "";
        }, 3000);
      } catch (error) {
        console.error("❌ Error al eliminar publicación:", error);

        mensajeAccion.value =
          error instanceof Error
            ? `Error: ${error.message}`
            : "Error al eliminar la publicación";
        mensajeExito.value = false;

        // Limpiar mensaje después de 5 segundos
        setTimeout(() => {
          mensajeAccion.value = "";
        }, 5000);
      }
    };

    const reportarPublicacion = (post: PostInmobiliario) => {
      menuPostAbierto.value = null;
      console.log("Reportar publicación:", post.title);

      // TODO: Implementar sistema de reportes
      alert(
        `Función de reportar publicación "${post.title}" pendiente de implementar`,
        "Función en desarrollo"
      );
    };

    const compartirEnlacePublicacion = async (post: PostInmobiliario) => {
      menuPostAbierto.value = null;

      try {
        const postId = normalizePostId(post);
        const url = `${window.location.origin}/post/${postId}`;

        // Intentar usar la API del Clipboard
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(url);
          success("Enlace copiado al portapapeles", "¡Listo!");
        } else {
          // Fallback para contextos no seguros
          const textArea = document.createElement("textarea");
          textArea.value = url;
          textArea.style.position = "fixed";
          textArea.style.left = "-999999px";
          textArea.style.top = "-999999px";
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();

          try {
            document.execCommand("copy");
            success("Enlace copiado al portapapeles", "¡Listo!");
          } catch (err) {
            console.error("Error copiando al portapapeles:", err);
            // Mostrar el URL en un prompt para que el usuario lo copie manualmente
            prompt("Copia este enlace:", url);
          } finally {
            document.body.removeChild(textArea);
          }
        }
      } catch (err) {
        console.error("Error compartiendo enlace:", err);
        error("Error al copiar el enlace", "Error");
      }
    };

    const ocultarPublicacion = async (post: PostInmobiliario) => {
      menuPostAbierto.value = null;

      try {
        const postId = normalizePostId(post);
        if (!postId) {
          warning("ID de publicación no válido", "Error");
          return;
        }

        // Verificar que no sea el autor del post
        if (esAutorDelPost(post)) {
          warning(
            "No puedes ocultar tus propias publicaciones",
            "Acción no permitida"
          );
          return;
        }

        // Elimino del título '((' y '))'
        const title = post.title.replace(/\(\(/g, "").replace(/\)\)/g, "");

        // 🎯 NUEVA LÓGICA: Ahora se pueden ocultar tanto publicaciones públicas como privadas
        // Confirmar la acción
        await confirm(
          `¿Estás seguro de que quieres ocultar esta publicación?<br/><strong>${title}</strong><br/><br/>Esta publicación dejará de aparecer en tu feed. Podrás volver a verla desde tu configuración de publicaciones ocultas.`,
          async () => {
            await ocultarPublicacionConfirmada(post, postId);
          },
          "Ocultar publicación"
        );
      } catch (error) {
        console.error("❌ Error preparando ocultación de publicación:", error);
        warning("Error inesperado al preparar la ocultación", "Error");
      }
    };

    // 🆕 NUEVO: Función auxiliar para ocultar publicación después de confirmación
    const ocultarPublicacionConfirmada = async (
      post: PostInmobiliario,
      postId: string
    ) => {
      try {
        console.log(`👁️‍🗨️ Ocultando publicación: "${post.title}"`);

        // Llamar al servicio para ocultar la publicación
        const result = await inmobiliarioService.hideForUser(postId);

        // Actualización optimista de la UI - remover el post del feed local
        const postIndex = posts.value.findIndex(
          (p) => normalizePostId(p) === postId
        );
        if (postIndex !== -1) {
          posts.value.splice(postIndex, 1);
          console.log(
            `✅ Post removido del feed local. Quedan ${posts.value.length} posts`
          );
        }

        // Cerrar comentarios si estaban abiertos
        if (post.showComments) {
          post.showComments = false;
        }

        // Cerrar cualquier menú abierto
        menuPostAbierto.value = null;
        menuAutorAbierto.value = null;

        console.log("✅ Publicación ocultada exitosamente:", result);

        // Mostrar mensaje de éxito
        success(
          `Publicación "${post.title}" ocultada exitosamente.\n\nEsta publicación ya no aparecerá en tu feed. Puedes restaurarla desde tu configuración de publicaciones ocultas.`,
          "¡Publicación ocultada!"
        );

        // 🔄 OPCIONAL: Si quedan muy pocos posts visibles, intentar cargar más
        if (
          posts.value.length < 3 &&
          hasMorePosts.value &&
          !isLoadingMore.value
        ) {
          console.log(
            "📦 Pocos posts visibles, cargando más automáticamente..."
          );
          setTimeout(() => {
            loadMorePosts();
          }, 500);
        }
      } catch (error) {
        console.error("❌ Error al ocultar publicación:", error);

        let errorMessage = "Error al ocultar la publicación";

        if (error instanceof Error) {
          errorMessage = error.message;
        }

        warning(errorMessage, "Error al ocultar");
      }
    };

    const marcarComoFavorito = async (post: PostInmobiliario) => {
      menuPostAbierto.value = null;

      try {
        const postId = normalizePostId(post);
        if (!postId) {
          warning("ID de publicación no válido", "Error");
          return;
        }

        console.log("⭐ Alternando favorito para post:", post.title);
        const result = await inmobiliarioService.toggleFavorite(postId);

        // Actualizar estado local del post
        post.isFavorited = result.favorited;
        post.favorites = result.favoritesCount;

        // Sin alertas - solo feedback visual directo con el corazón
        console.log(
          result.favorited
            ? "✅ Agregado a favoritos"
            : "✅ Removido de favoritos"
        );
      } catch (err) {
        console.error("❌ Error al alternar favorito:", err);
        error("Error al actualizar favoritos", "Error");
      }
    };

    // 🔒 Función helper para verificar si el usuario actual es el autor del post
    // 🚀 OPTIMIZADO: Verificar si el usuario actual es el autor del post (REACTIVO)
    const esAutorDelPost = (post: PostInmobiliario): boolean => {
      try {
        if (!currentUserId.value) {
          return false; // Sin usuario autenticado, sin permisos
        }

        // Verificar si el usuario actual es el autor del post
        return post.authorId === currentUserId.value;
      } catch (error) {
        console.error("❌ Error al verificar permisos del post:", error);
        return false; // En caso de error, denegar permisos por seguridad
      }
    };

    // 🆕 NUEVO: Helper para normalizar IDs de comentarios
    const normalizeCommentId = (comment: any): string | null => {
      if (comment._id && typeof comment._id === "string") {
        return comment._id;
      }
      if (comment.id && typeof comment.id === "string") {
        comment._id = comment.id; // Normalizar para uso futuro
        return comment.id;
      }
      return null;
    };

    // 🆕 NUEVO: Helper para obtener ID de comentario (para usar en template)
    const getCommentId = (comment: any): string => {
      return normalizeCommentId(comment) || "unknown";
    };

    // 🚀 OPTIMIZADO: Verificar si el usuario actual es el autor del comentario (REACTIVO)
    const esAutorDelComentario = (comment: any): boolean => {
      try {
        if (!currentUserId.value) {
          return false;
        }
        return comment.authorId === currentUserId.value;
      } catch (error) {
        console.error("❌ Error al verificar permisos del comentario:", error);
        return false;
      }
    };

    // 🆕 NUEVO: Verificar si el usuario puede gestionar el comentario (autor del comentario O autor del post)
    const puedeGestionarComentario = (
      comment: any,
      post: PostInmobiliario
    ): boolean => {
      try {
        if (!currentUserId.value) {
          return false;
        }

        // El autor del comentario siempre puede gestionarlo
        if (comment.authorId === currentUserId.value) {
          return true;
        }

        // El autor del post puede gestionar cualquier comentario en su publicación
        if (post.authorId === currentUserId.value) {
          return true;
        }

        return false;
      } catch (error) {
        console.error(
          "❌ Error al verificar permisos de gestión del comentario:",
          error
        );
        return false;
      }
    };

    // 🆕 NUEVO: Toggle menú contextual de comentario
    const toggleMenuComentario = (commentId: string) => {
      // Cerrar otros menús
      menuAutorAbierto.value = null;
      menuPostAbierto.value = null;

      menuComentarioAbierto.value =
        menuComentarioAbierto.value === commentId ? null : commentId;
    };

    // 🆕 NUEVO: Eliminar comentario
    const eliminarComentario = async (comment: any, post: PostInmobiliario) => {
      menuComentarioAbierto.value = null;

      await confirm(
        `¿Estás seguro de que quieres eliminar este comentario?\n\n"${comment.text}"\n\nEsta acción no se puede deshacer.`,
        () => eliminarComentarioConfirmado(comment, post),
        "Eliminar comentario"
      );
    };

    // 🆕 NUEVO: Función auxiliar para eliminar comentario después de confirmación
    const eliminarComentarioConfirmado = async (
      comment: any,
      post: PostInmobiliario
    ) => {
      try {
        const commentId = normalizeCommentId(comment);
        if (!commentId) {
          throw new Error("ID de comentario no válido");
        }

        // Llamar al método del servicio
        await inmobiliarioService.removeComment(commentId);

        // Actualización optimista de la UI
        if (post.comments) {
          const commentIndex = post.comments.findIndex(
            (c) => normalizeCommentId(c) === commentId
          );
          if (commentIndex !== -1) {
            post.comments.splice(commentIndex, 1);
            post.commentsCount = Math.max(0, post.commentsCount - 1);
          }
        }

        console.log("✅ Comentario eliminado exitosamente");

        // Mostrar mensaje de éxito
        success("Comentario eliminado exitosamente", "¡Listo!");
      } catch (error) {
        console.error("❌ Error al eliminar comentario:", error);

        const errorMessage =
          error instanceof Error
            ? `Error: ${error.message}`
            : "Error al eliminar el comentario";

        warning(errorMessage, "Error al eliminar");
      }
    };

    // 🆕 NUEVO: Helper para verificar si el comentario es del autor del post
    const esComentarioDelAutorDelPost = (
      comment: any,
      post: PostInmobiliario
    ): boolean => {
      try {
        return comment.authorId === post.authorId;
      } catch (error) {
        console.error(
          "❌ Error al verificar si comentario es del autor del post:",
          error
        );
        return false;
      }
    };

    // 🆕 NUEVO: Convertir comentario a hilo de interés
    const convertirComentarioAHilo = async (
      comment: any,
      post: PostInmobiliario
    ) => {
      // Cerrar menú
      menuComentarioAbierto.value = null;

      // Validaciones previas
      if (!currentUserId.value) {
        warning(
          "Debes iniciar sesión para convertir comentarios a hilos",
          "Sin autorización"
        );
        return;
      }

      // NUEVA LÓGICA: Permitir conversión excepto si el mismo usuario es autor del post Y del comentario
      if (esAutorDelPost(post) && esAutorDelComentario(comment)) {
        warning(
          "No puedes convertir tus propios comentarios en tu propia publicación a hilos de interés.",
          "Acción no permitida"
        );
        return;
      }

      // Verificar que el usuario tiene permisos (autor del comentario O autor del post)
      if (!esAutorDelComentario(comment) && !esAutorDelPost(post)) {
        warning(
          "Solo el autor del comentario o el autor de la publicación pueden convertir comentarios a hilos de interés",
          "Sin autorización"
        );
        return;
      }

      const commentId = normalizeCommentId(comment);
      if (!commentId) {
        warning("Error: ID de comentario no válido", "Error");
        return;
      }

      // Confirmación del usuario
      await confirm(
        `¿Convertir este comentario en un hilo de interés?\n\n"${comment.text}"\n\nEl comentario se eliminará y se creará un hilo privado entre tú y el autor de la publicación.\n\n💬 El hilo se abrirá automáticamente para continuar la conversación.`,
        () => convertirComentarioConfirmado(commentId, post),
        "Convertir a Hilo"
      );
    };

    // 🆕 NUEVO: Función auxiliar para convertir después de confirmación
    const convertirComentarioConfirmado = async (
      commentId: string,
      post: PostInmobiliario
    ) => {
      try {
        convirtiendoComentario.value = true;

        console.log(
          `🔄 Convirtiendo comentario ${commentId} a hilo de interés...`
        );

        // Conectar con el servicio usando el token
        await inmobiliarioService.connectWithToken(props.token);

        // Llamar al método del backend
        const result = await inmobiliarioService.call(
          "hilosInteres.convertFromComment",
          commentId
        );

        if (result.success) {
          // Actualización optimista de la UI - eliminar el comentario
          if (post.comments) {
            const commentIndex = post.comments.findIndex(
              (c) => normalizeCommentId(c) === commentId
            );
            if (commentIndex !== -1) {
              post.comments.splice(commentIndex, 1);
              post.commentsCount = Math.max(0, post.commentsCount - 1);
            }
          }

          console.log(
            `✅ Comentario convertido exitosamente a hilo: ${result.hiloId}`
          );

          // 🚀 NUEVA FUNCIONALIDAD: Preparar objeto del hilo para abrir el chat automáticamente
          const hiloRecienCreado = {
            _id: result.hiloId,
            titulo: result.titulo,
            referenciaPrivada: result.referenciaPrivada || "",
            postId: normalizePostId(post),
            creatorId: currentUserId.value, // Usuario actual es el creador
            postAuthorCache: post.authorCache,
            creatorCache: {
              // Cache del usuario actual (creador del hilo)
              firstName: "", // Se llenará automáticamente por el backend
              lastName: "",
              avatar: "/default-avatar.png", // Fallback
            },
            mensajesCount: 1,
            lastMessageAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
          };

          // 🆕 MEJORADO: Abrir automáticamente el chat del hilo recién creado por conversión
          console.log(
            "💬 Abriendo chat automáticamente para hilo creado por conversión:",
            hiloRecienCreado.titulo
          );

          // Verificar si la ventana ya está abierta (por seguridad)
          if (isChatWindowOpen(hiloRecienCreado._id)) {
            warning(
              "Esta conversación ya está abierta en otra ventana",
              "Conversación abierta"
            );
          } else {
            // Abrir el chat modal con el hilo recién creado
            hiloSeleccionado.value = hiloRecienCreado;
            showChatModal.value = true;

            console.log(
              "✅ Chat abierto automáticamente tras conversión de comentario"
            );
          }

          // Mostrar mensaje de éxito más directo (ya que se abre el chat)
          success(
            `Comentario convertido a hilo de interés exitosamente.\n\nTítulo: "${result.titulo}"\n\n¡El hilo se ha abierto para continuar la conversación!`,
            "¡Hilo Creado!"
          );

          // 🔄 OPCIONAL: Refrescar datos para asegurar sincronización
          setTimeout(async () => {
            try {
              await updateCountersForCurrentPosts();
            } catch (error) {
              console.warn(
                "⚠️ Error actualizando contadores tras conversión:",
                error
              );
            }
          }, 1000);
        } else {
          throw new Error(result.message || "Error al convertir comentario");
        }
      } catch (error) {
        console.error("❌ Error al convertir comentario a hilo:", error);

        let errorMessage = "Error al convertir el comentario a hilo de interés";

        // Verificar si es el error de límite máximo de hilos
        const isMaxHilosError =
          (error as any)?.error === "max-hilos-reached" ||
          (error as any)?.message?.includes?.("máximo de 3 hilos") ||
          (error as any)?.reason?.includes?.("máximo de 3 hilos");

        if (isMaxHilosError) {
          window.alert(
            "🚫 LÍMITE DE HILOS ALCANZADO\n\n" +
              "Has alcanzado el límite máximo de 3 hilos de interés por publicación.\n\n" +
              "Para crear un nuevo hilo, primero debes eliminar alguno de los hilos existentes.\n\n" +
              "💡 Sugerencias:\n" +
              "• Revisa tus hilos existentes\n" +
              "• Elimina conversaciones inactivas\n" +
              "• Consolida temas similares"
          );
          return; // Salir sin mostrar warning adicional
        }

        if (error instanceof Error) {
          if (error.message.includes("invalid-conversion")) {
            errorMessage =
              "No se puede convertir comentarios del autor de la publicación a hilos de interés.";
          } else if (error.message.includes("not-authorized")) {
            errorMessage =
              "Solo puedes convertir tus propios comentarios a hilos de interés.";
          } else if (error.message.includes("hilo-exists")) {
            errorMessage = error.message; // Ya contiene el título
          } else {
            errorMessage = `Error: ${error.message}`;
          }
        }

        warning(errorMessage, "Error de Conversión");
      } finally {
        convirtiendoComentario.value = false;
      }
    };

    // 🆕 NUEVO: Event listener para cerrar menú de comentario al hacer clic fuera
    const cerrarMenuComentarioAlClickFuera = (event: Event) => {
      const target = event.target as HTMLElement;
      const menuElement = target.closest(".menu-desplegable-comentario");
      const botonElement = target.closest(".boton-menu-comentario");

      if (!menuElement && !botonElement) {
        menuComentarioAbierto.value = null;
      }
    };

    // 🚀 OPTIMIZADO: Cargar estado de favoritos por lotes
    const cargarEstadoFavoritos = async () => {
      try {
        if (posts.value.length === 0) return;

        console.log(
          "⭐ Cargando estado de favoritos optimizado para",
          posts.value.length,
          "posts..."
        );

        // Obtener IDs de todos los posts
        const postIds = posts.value
          .map((post) => normalizePostId(post))
          .filter(Boolean) as string[];

        if (postIds.length === 0) return;

        // 🚀 OPTIMIZACIÓN: Una sola llamada para todos los posts
        const favoritedStatus = await inmobiliarioService.getFavoritedStatus(
          postIds
        );

        // Aplicar estado a cada post
        posts.value.forEach((post) => {
          const postId = normalizePostId(post);
          if (postId) {
            post.isFavorited = favoritedStatus[postId] || false;
          }
        });

        console.log("✅ Estado de favoritos cargado optimizado:", {
          currentPosts: posts.value.length,
          favoritedPosts: posts.value.filter((p) => p.isFavorited).length,
        });
      } catch (error) {
        console.error("❌ Error al cargar estado de favoritos:", error);
        // Fallback: marcar todos como no favoritos
        posts.value.forEach((post) => {
          post.isFavorited = false;
        });
      }
    };

    // 🔧 NUEVO: Merge inteligente que preserva estado local de UI
    const mergePostsPreservingUIState = async (
      newPosts: PostInmobiliario[]
    ) => {
      console.log(
        `🔄 Iniciando merge de ${newPosts.length} posts nuevos con ${
          posts.value.length
        } posts existentes${
          isLoadingByScrollInfinito.value ? " (SCROLL INFINITO ACTIVO)" : ""
        }`
      );

      // 🚨 PROTECCIÓN CRÍTICA: No limpiar el feed si newPosts está vacío (EXCEPTO en scroll infinito)
      if (
        newPosts.length === 0 &&
        posts.value.length > 0 &&
        !isLoadingByScrollInfinito.value
      ) {
        console.warn(
          `⚠️ PROTECCIÓN ACTIVADA: Se intentó limpiar el feed con array vacío. Manteniendo ${posts.value.length} posts existentes.`
        );
        return; // Salir sin modificar posts.value
      }

      // 🚨 PROTECCIÓN ADICIONAL: Verificar que los posts tienen IDs válidos
      const validNewPosts = newPosts.filter((post) => {
        const postId = normalizePostId(post);
        if (!postId) {
          console.warn("⚠️ Post sin ID válido filtrado en merge:", post);
          return false;
        }
        return true;
      });

      if (
        validNewPosts.length === 0 &&
        posts.value.length > 0 &&
        !isLoadingByScrollInfinito.value
      ) {
        console.warn(
          `⚠️ PROTECCIÓN ACTIVADA: Todos los posts nuevos tienen IDs inválidos. Manteniendo ${posts.value.length} posts existentes.`
        );
        return; // Salir sin modificar posts.value
      }

      const updatedPosts = validNewPosts.map((newPost) => {
        // Buscar el post existente por ID
        const existingPost = posts.value.find(
          (existing) => normalizePostId(existing) === normalizePostId(newPost)
        );

        if (existingPost) {
          // 🔧 PRESERVAR estado local de UI
          return {
            ...newPost, // Datos actualizados del backend
            showComments: existingPost.showComments, // Preservar si está abierto
            comments: existingPost.comments, // Preservar comentarios cargados
            isFavorited: existingPost.isFavorited, // Preservar estado de favorito
          };
        } else {
          // Post nuevo, usar datos del backend
          return newPost;
        }
      });

      // 🚨 PROTECCIÓN FINAL: Solo actualizar si tenemos posts válidos (EXCEPTO scroll infinito)
      if (updatedPosts.length > 0 || isLoadingByScrollInfinito.value) {
        // 🚨 SISTEMA DE RESPALDO: Guardar backup antes de actualizar
        if (posts.value.length > 0) {
          feedBackup.value = [...posts.value];
          lastValidPostsCount.value = posts.value.length;
        }

        // Actualizar el array completo
        posts.value = updatedPosts;
        console.log(
          `✅ Merge completado: ${posts.value.length} posts en array reactivo${
            isLoadingByScrollInfinito.value ? " (SCROLL INFINITO)" : ""
          }`
        );

        // 🚨 DETECCIÓN DE LIMPIEZA ACCIDENTAL: Verificar si el feed se vació drásticamente (EXCEPTO scroll infinito)
        if (
          lastValidPostsCount.value > 5 &&
          posts.value.length === 0 &&
          !isLoadingByScrollInfinito.value
        ) {
          console.error(
            `🚨 FEED LIMPIADO ACCIDENTALMENTE: Se tenían ${lastValidPostsCount.value} posts y ahora hay 0. Restaurando backup...`
          );
          posts.value = feedBackup.value;
          warning(
            "Se detectó un problema que limpió el feed accidentalmente. Los posts han sido restaurados.",
            "Feed Restaurado"
          );
        }
      } else {
        console.warn(
          `⚠️ PROTECCIÓN ACTIVADA: updatedPosts está vacío. Manteniendo ${posts.value.length} posts existentes.`
        );
      }

      // 🚀 OPTIMIZACIÓN: Si hay posts nuevos, cargar su estado de favoritos
      const newPostIds = newPosts
        .filter(
          (newPost) =>
            !posts.value.some(
              (existing) =>
                normalizePostId(existing) === normalizePostId(newPost)
            )
        )
        .map((post) => normalizePostId(post))
        .filter(Boolean) as string[];

      if (newPostIds.length > 0) {
        try {
          const favoritedStatus = await inmobiliarioService.getFavoritedStatus(
            newPostIds
          );

          // Aplicar estado de favoritos a posts nuevos
          posts.value.forEach((post) => {
            const postId = normalizePostId(post);
            if (postId && newPostIds.includes(postId)) {
              post.isFavorited = favoritedStatus[postId] || false;
            }
          });

          console.log("🔄 Estado de favoritos cargado para posts nuevos:", {
            newPosts: newPostIds.length,
            favoritedNewPosts:
              Object.values(favoritedStatus).filter(Boolean).length,
          });
        } catch (error) {
          console.warn("⚠️ Error cargando favoritos para posts nuevos:", error);
        }
      }

      console.log("🔄 Posts actualizados preservando estado UI:", {
        total: updatedPosts.length,
        conComentariosAbiertos: updatedPosts.filter((p) => p.showComments)
          .length,
        conFavoritos: updatedPosts.filter((p) => p.isFavorited).length,
      });
    };

    // 🆕 NUEVO: Configurar observer de comentarios para actualización automática
    const setupCommentsObserver = () => {
      console.log("🔄 Configurando observer de comentarios...");

      const commentsCollection = ddpService.collection("comentariosPost");
      const observer = commentsCollection.onChange(() => {
        console.log("💬 Cambio detectado en comentarios");

        // Actualizar comentarios en posts que tienen showComments: true
        posts.value.forEach(async (post) => {
          if (post.showComments) {
            const postId = normalizePostId(post);
            if (postId) {
              try {
                // 🚀 Obtener comentarios actualizados para este post
                const updatedComments = commentsCollection
                  .filter((c: any) => c.postId === postId && c.active !== false)
                  .fetch()
                  .sort(
                    (a: any, b: any) =>
                      new Date(a.createdAt).getTime() -
                      new Date(b.createdAt).getTime()
                  );

                // 🔄 Actualizar comentarios y contador
                post.comments = updatedComments;
                post.commentsCount = updatedComments.length;

                console.log(
                  `💬 Comentarios actualizados para post ${postId}: ${updatedComments.length} comentarios`
                );
              } catch (error) {
                console.error(
                  `❌ Error actualizando comentarios para post ${postId}:`,
                  error
                );
              }
            }
          }
        });
      });

      // Retornar función de limpieza
      return () => {
        if (observer && typeof observer.stop === "function") {
          observer.stop();
        }
      };
    };

    // 🚀 NUEVO: Configurar observer optimizado de favoritos para reactividad completa
    const setupFavoritesObserver = () => {
      try {
        if (posts.value.length === 0) return () => {};

        // Obtener IDs de todos los posts
        const postIds = posts.value
          .map((post) => normalizePostId(post))
          .filter(Boolean) as string[];

        if (postIds.length === 0) return () => {};

        console.log(
          "🔄 Configurando observer optimizado de favoritos para",
          postIds.length,
          "posts..."
        );

        // Configurar observer usando el método optimizado del servicio
        return inmobiliarioService.onFavoritesChangeForPosts(
          postIds,
          (favoritedStatus) => {
            // Actualizar estado isFavorited en todos los posts
            posts.value.forEach((post) => {
              const postId = normalizePostId(post);
              if (postId && favoritedStatus.hasOwnProperty(postId)) {
                post.isFavorited = favoritedStatus[postId];
              }
            });

            console.log("🔄 Estado de favoritos actualizado reactivamente:", {
              currentPosts: posts.value.length,
              favoritedPosts: posts.value.filter((p) => p.isFavorited).length,
            });
          }
        );
      } catch (error) {
        console.error("❌ Error configurando observer de favoritos:", error);
        return () => {};
      }
    };

    // 🆕 NUEVO: Estados para hilos de interés
    const menuHilosAbierto = ref<string | null>(null);
    const hilosDelPost = ref<{ [postId: string]: any[] }>({});
    const loadingHilos = ref<{ [postId: string]: boolean }>({});
    const loadingHilosModal = ref(false); // 🔧 OPTIMIZADO: Loading específico para modal de hilos
    const showCrearHiloModal = ref(false);
    const showChatModal = ref(false);
    const showHilosModal = ref(false);

    const hiloSeleccionado = ref<any>(null);
    const postSeleccionado = ref<any>(null);
    const postSeleccionadoParaHilo = ref<any>(null);

    // 🆕 ESTADOS OPTIMIZADOS: Contadores globales de mensajes no leídos por post
    const unreadCountersByPost = ref<{ [postId: string]: number }>({});
    const loadingUnreadCounters = ref(false);
    let unsubscribeFromUnreadCountersChanges: (() => void) | null = null;

    // 🆕 NUEVO: Estados para conteo total de hilos por post (leídos + no leídos)
    const totalHilosCountByPost = ref<{ [postId: string]: number }>({});
    const loadingTotalHilosCount = ref(false);
    let unsubscribeFromTotalHilosChanges: (() => void) | null = null;

    // 🆕 NUEVO: Constantes y métodos para localStorage de ventanas de chat
    const CHAT_WINDOWS_KEY = "mulbin_chat_windows_open";

    const getChatWindowsFromStorage = (): string[] => {
      try {
        const stored = localStorage.getItem(CHAT_WINDOWS_KEY);
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        console.warn("⚠️ Error leyendo chat windows del localStorage:", error);
        return [];
      }
    };

    const isChatWindowOpen = (hiloId: string): boolean => {
      if (!hiloId) return false;

      const currentWindows = getChatWindowsFromStorage();
      return currentWindows.includes(hiloId);
    };

    // 🆕 FUNCIÓN OPTIMIZADA: Obtener contador de mensajes no leídos para un post específico
    const getUnreadCountForPost = (post: PostInmobiliario): number => {
      const postId = normalizePostId(post);
      if (!postId) return 0;

      return unreadCountersByPost.value[postId] || 0;
    };

    // 🆕 NUEVO: Obtener conteo total de hilos para un post específico (para autores)
    const getTotalHilosCountForPost = (post: PostInmobiliario): number => {
      const postId = normalizePostId(post);
      if (!postId) return 0;

      return totalHilosCountByPost.value[postId] || 0;
    };

    // 🆕 FUNCIÓN OPTIMIZADA: Cargar contadores iniciales para posts visibles
    const loadUnreadCountersForPosts = async (posts: PostInmobiliario[]) => {
      try {
        if (posts.length === 0) return;

        loadingUnreadCounters.value = true;
        console.log(`📊 Cargando contadores para ${posts.length} posts...`);

        // Obtener IDs de posts válidos
        const postIds = posts
          .map((post) => normalizePostId(post))
          .filter((id) => id !== null) as string[];

        if (postIds.length === 0) return;

        // Suscribirse a contadores globales
        await hilosInteresService.subscribeToUnreadCountsByPosts(postIds);

        // Obtener contadores optimizados
        const counters = await hilosInteresService.getUnreadCountsByPosts({
          postIds,
          forceRecalculate: false,
        });

        // Actualizar estado reactivo
        unreadCountersByPost.value = {
          ...unreadCountersByPost.value,
          ...counters,
        };

        console.log(
          `✅ Contadores cargados para ${Object.keys(counters).length} posts:`,
          counters
        );
      } catch (error) {
        console.error("❌ Error cargando contadores iniciales:", error);
      } finally {
        loadingUnreadCounters.value = false;
      }
    };

    // 🆕 FUNCIÓN OPTIMIZADA: Configurar observer reactivo para contadores globales
    const setupUnreadCountersObserver = () => {
      try {
        console.log("👂 Configurando observer de contadores globales...");

        // Configurar observer reactivo
        const unsubscribe = hilosInteresService.onCountersChange((counters) => {
          // 🆕 NUEVO: Verificar y marcar como leídos los mensajes de ventanas abiertas
          procesarMensajesDeVentanasAbiertas();

          // Actualizar estado local
          unreadCountersByPost.value = {
            ...unreadCountersByPost.value,
            ...counters,
          };

          console.log("🔄 Contadores actualizados automáticamente:", counters);
        });

        // Guardar referencia para limpieza
        unsubscribeFromUnreadCountersChanges = unsubscribe;

        console.log("✅ Observer de contadores configurado");
      } catch (error) {
        console.error("❌ Error configurando observer de contadores:", error);
      }
    };

    // 🔧 OPTIMIZADO: Procesar mensajes siguiendo principios DDP - Solo marcar como leídos sin verificaciones redundantes
    const procesarMensajesDeVentanasAbiertas = async () => {
      try {
        const ventanasAbiertas = getChatWindowsFromStorage();

        if (ventanasAbiertas.length === 0) {
          return; // No hay ventanas abiertas
        }

        console.log(
          `📖 Marcando como leídos mensajes de ${ventanasAbiertas.length} ventanas abiertas (siguiendo principios DDP)...`
        );

        // ✅ SIGUIENDO GUÍA DDP: Solo marcar como leídos, DDP maneja la actualización automática
        for (const hiloId of ventanasAbiertas) {
          try {
            // 🚀 NO verificar manualmente - DDP ya sabe si hay mensajes no leídos
            // Solo marcar como leídos si la ventana está abierta
            await inmobiliarioService.markMessagesAsRead(hiloId);

            console.log(
              `✅ Mensajes marcados como leídos para hilo abierto: ${hiloId} (DDP actualizará contadores automáticamente)`
            );
          } catch (error) {
            console.warn(
              `⚠️ Error marcando mensajes como leídos para hilo ${hiloId}:`,
              error
            );
            // Continuar con el siguiente hilo sin detener el proceso
          }
        }
      } catch (error) {
        console.error(
          "❌ Error procesando mensajes de ventanas abiertas:",
          error
        );
      }
    };

    // 🔧 OPTIMIZADO: Observer de mensajes nuevos siguiendo principios DDP reactivos
    const setupNewMessagesObserver = () => {
      try {
        console.log(
          "👂 Configurando observer reactivo de mensajes nuevos (siguiendo principios DDP)..."
        );

        const mensajesCollection = ddpService.collection("mensajesHilo");
        const observer = mensajesCollection.onChange((change: any) => {
          if (change.type === "added") {
            console.log("📨 Nuevo mensaje detectado via DDP:", change.doc);

            const nuevoMensaje = change.doc;
            const hiloId = nuevoMensaje.hiloId;

            // ✅ SIGUIENDO GUÍA DDP: Solo actuar si la ventana está abierta
            if (hiloId && isChatWindowOpen(hiloId)) {
              console.log(
                `📖 Ventana abierta detectada para hilo ${hiloId}, marcando como leído (DDP se encarga del resto)...`
              );

              // 🚀 OPTIMIZADO: Marcar como leído inmediatamente - DDP maneja la propagación
              setTimeout(async () => {
                try {
                  // Solo marcar como leído - DDP actualiza contadores automáticamente
                  await inmobiliarioService.markMessagesAsRead(hiloId);
                  console.log(
                    `✅ Mensaje automáticamente marcado como leído - DDP propagará cambios reactivamente`
                  );
                } catch (error) {
                  console.warn(`⚠️ Error marcando mensaje como leído:`, error);
                }
              }, 100); // Delay mínimo para asegurar procesamiento DDP
            }
          }
        });

        // Retornar función de limpieza DDP
        return () => {
          if (observer && typeof observer.stop === "function") {
            observer.stop();
            console.log(
              "🧹 Observer de mensajes nuevos limpiado correctamente"
            );
          }
        };
      } catch (error) {
        console.error(
          "❌ Error configurando observer de mensajes nuevos:",
          error
        );
        return () => {};
      }
    };

    // Variable para almacenar la función de limpieza del observer de mensajes nuevos
    let unsubscribeFromNewMessagesObserver: (() => void) | null = null;

    // 🆕 FUNCIÓN OPTIMIZADA: Actualizar contadores cuando cambian los posts
    const updateCountersForCurrentPosts = async () => {
      if (posts.value.length > 0) {
        await loadUnreadCountersForPosts(posts.value);
        await loadTotalHilosCountForPosts(posts.value);
      }
    };

    // 🆕 MEJORADO: Cargar conteo total de hilos para posts visibles (siguiendo principios DDP)
    const loadTotalHilosCountForPosts = async (posts: PostInmobiliario[]) => {
      try {
        if (posts.length === 0) return;

        loadingTotalHilosCount.value = true;
        console.log(
          `📊 Cargando conteo total de hilos para ${posts.length} posts...`
        );

        // Obtener IDs de posts válidos
        const postIds = posts
          .map((post) => normalizePostId(post))
          .filter((id) => id !== null) as string[];

        if (postIds.length === 0) return;

        // 🔌 Conectar al servicio DDP si no está conectado
        await inmobiliarioService.connectWithToken(token.value);

        // 🚀 OPTIMIZACIÓN: Primero intentar usar contadores ya disponibles
        const countersCollection = ddpService.collection("unreadCountsByPost");
        const availableCounters = countersCollection.fetch();
        const totalCounts: { [key: string]: number } = {};
        const missingPostIds: string[] = [];

        // Extraer conteos de contadores disponibles
        postIds.forEach((postId) => {
          const counter = availableCounters.find(
            (c: any) => c.postId === postId
          );
          if (counter && typeof counter.hilosCount === "number") {
            totalCounts[postId] = counter.hilosCount;
            console.log(
              `✅ Conteo obtenido de contadores para ${postId}: ${counter.hilosCount}`
            );
          } else {
            missingPostIds.push(postId);
          }
        });

        // 📊 Para posts sin contadores disponibles, hacer llamada individual
        if (missingPostIds.length > 0) {
          console.log(
            `📞 Obteniendo conteos faltantes para ${missingPostIds.length} posts...`
          );

          for (const postId of missingPostIds) {
            try {
              const hilos = await inmobiliarioService.call(
                "hilosInteres.getByPost",
                postId
              );
              totalCounts[postId] = hilos.length;
            } catch (error) {
              console.warn(
                `⚠️ Error obteniendo hilos para post ${postId}:`,
                error
              );
              totalCounts[postId] = 0;
            }
          }
        }

        // 🔄 Actualizar estado reactivo
        totalHilosCountByPost.value = {
          ...totalHilosCountByPost.value,
          ...totalCounts,
        };

        console.log(
          `✅ Conteo total de hilos cargado para ${
            Object.keys(totalCounts).length
          } posts:`,
          totalCounts
        );
      } catch (error) {
        console.error("❌ Error cargando conteo total de hilos:", error);
        // En caso de error, marcar como 0 para evitar problemas de UI
        const postIds = posts
          .map((post) => normalizePostId(post))
          .filter(Boolean);
        const fallbackCounts: { [key: string]: number } = {};
        postIds.forEach((postId) => {
          if (postId) fallbackCounts[postId] = 0;
        });
        totalHilosCountByPost.value = {
          ...totalHilosCountByPost.value,
          ...fallbackCounts,
        };
      } finally {
        loadingTotalHilosCount.value = false;
      }
    };

    // 🆕 MEJORADO: Configurar observer reactivo para conteo total de hilos (siguiendo principios DDP)
    const setupTotalHilosObserver = () => {
      try {
        console.log("👂 Configurando observer de conteo total de hilos...");

        // 🔌 Observer 1: Cambios en la colección de hilos
        const hilosCollection = ddpService.collection("hilosInteres");
        const hilosObserver = hilosCollection.onChange(() => {
          console.log("🔄 Cambio detectado en hilos de interés");

          // 📊 Recalcular conteos para posts actuales cuando hay cambios
          if (posts.value.length > 0) {
            setTimeout(async () => {
              await loadTotalHilosCountForPosts(posts.value);
            }, 100);
          }
        });

        // 🆕 NUEVO: Observer 2: Cambios en contadores (para detectar nuevos hilos)
        const countersCollection = ddpService.collection("unreadCountsByPost");
        const countersObserver = countersCollection.onChange(() => {
          console.log("🔄 Cambio detectado en contadores de hilos");

          // 📊 Extraer conteos directamente de los contadores optimizados
          const countersData = countersCollection.fetch();
          const newTotalCounts: { [key: string]: number } = {};

          countersData.forEach((counter: any) => {
            if (counter.postId && typeof counter.hilosCount === "number") {
              newTotalCounts[counter.postId] = counter.hilosCount;
            }
          });

          // 🔄 Actualizar estado reactivo si hay cambios
          if (Object.keys(newTotalCounts).length > 0) {
            totalHilosCountByPost.value = {
              ...totalHilosCountByPost.value,
              ...newTotalCounts,
            };

            console.log(
              "✅ Conteos totales actualizados desde contadores:",
              newTotalCounts
            );
          }
        });

        // Guardar referencia para limpieza
        unsubscribeFromTotalHilosChanges = () => {
          if (hilosObserver && typeof hilosObserver.stop === "function") {
            hilosObserver.stop();
          }
          if (countersObserver && typeof countersObserver.stop === "function") {
            countersObserver.stop();
          }
        };

        console.log(
          "✅ Observer de conteo total de hilos configurado con doble observación"
        );
      } catch (error) {
        console.error(
          "❌ Error configurando observer de conteo total de hilos:",
          error
        );
      }
    };

    // 🔧 REMOVIDO: Estados del chat movidos a ChatHiloModal.vue

    // 🆕 NUEVO: Función para gestionar hilos de interés
    const gestionarHiloInteres = async (post: PostInmobiliario) => {
      const esAutor = esAutorDelPost(post);
      const postId = normalizePostId(post);

      if (!postId) {
        warning("ID de post no válido", "Error");
        return;
      }

      if (esAutor) {
        // Si es el autor, mostrar modal con hilos de interés de otros usuarios
        await mostrarHilosDelPost(post);
      } else {
        // Si no es el autor, mostrar menú contextual con sus hilos + opción crear
        toggleMenuHilos(postId);
      }
    };

    // 🆕 NUEVO: Mostrar modal de hilos para autores del post
    const mostrarHilosDelPost = async (post: PostInmobiliario) => {
      try {
        const postId = normalizePostId(post);
        if (!postId) return;

        loadingHilosModal.value = true; // 🔧 OPTIMIZADO: Loading específico del modal
        postSeleccionado.value = post;

        // Conectar y obtener hilos
        await inmobiliarioService.connectWithToken(token.value);
        const hilos = await inmobiliarioService.call(
          "hilosInteres.getByPost",
          postId
        );

        // ✅ ACTUALIZAR contadores de mensajes no leídos (ya incluidos en el backend)
        // Los hilos ya vienen con unreadMessagesCount del backend, pero verificamos
        if (hilos.length > 0) {
          const hiloIds = hilos.map((h: any) => h._id);
          const contadores = await inmobiliarioService.getUnreadCounts(hiloIds);

          // Actualizar cada hilo con su contador real de no leídos
          hilos.forEach((hilo: any) => {
            hilo.unreadMessagesCount = contadores[hilo._id] || 0;
          });
        }

        console.log(
          `👀 ${hilos.length} hilos encontrados para el post ${post.title} con contadores actualizados`
        );

        // Guardar hilos y mostrar modal
        hilosDelPost.value[postId] = hilos;
        showHilosModal.value = true;
      } catch (err) {
        console.error("❌ Error obteniendo hilos del post:", err);
        error("Error al cargar hilos de interés", "Error");
      } finally {
        loadingHilosModal.value = false; // 🔧 OPTIMIZADO: Loading específico del modal
      }
    };

    // 🆕 NUEVO: Toggle menú contextual de hilos (para usuarios no autores)
    const toggleMenuHilos = async (postId: string) => {
      const menuKey = `hilos-${postId}`;

      if (menuHilosAbierto.value === menuKey) {
        menuHilosAbierto.value = null;
        return;
      }

      try {
        menuHilosAbierto.value = menuKey;
        loadingHilos.value[postId] = true;

        // Conectar y obtener hilos del usuario para este post
        await inmobiliarioService.connectWithToken(token.value);
        const hilos = await inmobiliarioService.call(
          "hilosInteres.getByUser",
          postId
        );

        // ✅ ACTUALIZAR contadores de mensajes no leídos
        if (hilos.length > 0) {
          const hiloIds = hilos.map((h: any) => h._id);
          const contadores = await inmobiliarioService.getUnreadCounts(hiloIds);

          // Actualizar cada hilo con su contador real de no leídos
          hilos.forEach((hilo: any) => {
            hilo.unreadMessagesCount = contadores[hilo._id] || 0;
          });
        }

        hilosDelPost.value[postId] = hilos;
        console.log(
          `📋 ${hilos.length} hilos del usuario encontrados para post ${postId} con contadores actualizados`
        );
      } catch (err) {
        console.error("❌ Error obteniendo hilos del usuario:", err);
        hilosDelPost.value[postId] = [];
      } finally {
        loadingHilos.value[postId] = false;
      }
    };

    // 🆕 NUEVO: Abrir modal para crear hilo
    const abrirModalCrearHilo = (post: PostInmobiliario) => {
      postSeleccionadoParaHilo.value = post;
      menuHilosAbierto.value = null;
      showCrearHiloModal.value = true;
    };

    // 🆕 NUEVO: Handlers para el componente desacoplado NuevoHiloForm
    const handleCloseCrearHilo = () => {
      showCrearHiloModal.value = false;
      postSeleccionadoParaHilo.value = null;
    };

    const handleHiloCreated = async (hiloRecienCreado: any) => {
      console.log(
        "✅ Hilo creado recibido en FeedPublicaciones:",
        hiloRecienCreado
      );

      // Actualizar contadores tras un breve delay
      setTimeout(async () => {
        try {
          await updateCountersForCurrentPosts();
        } catch (error) {
          console.warn(
            "⚠️ Error actualizando contadores tras crear hilo:",
            error
          );
        }
      }, 1000);
    };

    const handleAbrirChatFromHilo = (hiloRecienCreado: any) => {
      console.log(
        "💬 Abriendo chat para hilo recién creado:",
        hiloRecienCreado
      );

      // Asignar el hilo y abrir chat modal
      hiloSeleccionado.value = hiloRecienCreado;
      showChatModal.value = true;
    };

    const handleHiloError = (error: any) => {
      console.error("❌ Error en creación de hilo:", error);
      // El componente NuevoHiloForm ya maneja la visualización del error
    };

    // 🔧 MODIFICADO: Abrir chat de un hilo con verificación de localStorage
    const abrirChatHilo = async (hilo: any) => {
      // 🆕 NUEVO: Verificar si la ventana ya está abierta
      if (isChatWindowOpen(hilo._id)) {
        warning(
          "Esta conversación ya está abierta en otra ventana",
          "Conversación abierta"
        );
        return;
      }

      hiloSeleccionado.value = hilo;
      menuHilosAbierto.value = null;
      showChatModal.value = true;
      console.log(`💬 Chat abierto para hilo: ${hilo.titulo}`);
    };

    // 🔧 REMOVIDO: Funciones del chat movidas a ChatHiloModal.vue

    // 🆕 NUEVO: Handlers para eventos del chat desacoplado
    const handleChatError = (message: string) => {
      error(message, "Error de chat");
    };

    const handleChatSuccess = (message: string) => {
      success(message, "Chat");
    };

    // 🆕 NUEVO: Cerrar menú de hilos al hacer clic fuera
    const cerrarMenuHilosAlClickFuera = (event: Event) => {
      const target = event.target as HTMLElement;
      const menuElement = target.closest(".menu-desplegable-hilos");
      const botonElement = target.closest(".boton-hilos-interes");

      if (!menuElement && !botonElement) {
        menuHilosAbierto.value = null;
      }
    };

    // 🆕 NUEVO: Truncar título para mostrar en menú
    const truncarTitulo = (titulo: string, maxLength: number = 20): string => {
      if (!titulo) return "";
      return titulo.length > maxLength
        ? titulo.substring(0, maxLength) + "..."
        : titulo;
    };

    // 🆕 NUEVO: Formatear fecha para chat (como WhatsApp/Telegram)
    const formatearFechaChat = (fecha: string | Date): string => {
      const d = new Date(fecha);
      const ahora = new Date();

      // Crear fechas sin componente de tiempo para comparación
      const fechaMensaje = new Date(d.getFullYear(), d.getMonth(), d.getDate());
      const fechaHoy = new Date(
        ahora.getFullYear(),
        ahora.getMonth(),
        ahora.getDate()
      );
      const fechaAyer = new Date(fechaHoy);
      fechaAyer.setDate(fechaAyer.getDate() - 1);

      const diferenciaDias = Math.floor(
        (fechaHoy.getTime() - fechaMensaje.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Solo hora si es de hoy
      if (diferenciaDias === 0) {
        return d.toLocaleTimeString("es-MX", {
          hour: "2-digit",
          minute: "2-digit",
        });
      }

      // "Ayer" + hora si es de ayer
      if (diferenciaDias === 1) {
        const hora = d.toLocaleTimeString("es-MX", {
          hour: "2-digit",
          minute: "2-digit",
        });
        return `Ayer ${hora}`;
      }

      // Día de la semana + hora si es de esta semana (últimos 6 días)
      if (diferenciaDias <= 6) {
        const nombreDia = d.toLocaleDateString("es-MX", {
          weekday: "short",
        });
        const hora = d.toLocaleTimeString("es-MX", {
          hour: "2-digit",
          minute: "2-digit",
        });
        return `${nombreDia} ${hora}`;
      }

      // Fecha completa + hora para mensajes más antiguos
      const fechaFormateada = d.toLocaleDateString("es-MX", {
        day: "2-digit",
        month: "short",
        year: diferenciaDias > 365 ? "numeric" : undefined, // Solo año si es de otro año
      });
      const hora = d.toLocaleTimeString("es-MX", {
        hour: "2-digit",
        minute: "2-digit",
      });

      return `${fechaFormateada} ${hora}`;
    };

    // 🆕 NUEVO: Formatear fecha corta para hilos
    const formatearFechaCorta = (fecha: string | Date): string => {
      const d = new Date(fecha);
      const now = new Date();
      const diffMs = now.getTime() - d.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        return d.toLocaleTimeString("es-MX", {
          hour: "2-digit",
          minute: "2-digit",
        });
      } else if (diffDays === 1) {
        return "Ayer";
      } else if (diffDays < 7) {
        return `${diffDays}d`;
      } else {
        return d.toLocaleDateString("es-MX", {
          day: "2-digit",
          month: "short",
        });
      }
    };

    // 🔧 MODIFICADO: Abrir chat desde modal con verificación de localStorage
    const abrirChatDesdeModal = async (hilo: any) => {
      // 🆕 NUEVO: Verificar si la ventana ya está abierta
      if (isChatWindowOpen(hilo._id)) {
        showHilosModal.value = false;
        warning(
          "Esta conversación ya está abierta en otra ventana",
          "Conversación abierta"
        );
        return;
      }

      showHilosModal.value = false;
      hiloSeleccionado.value = hilo;
      showChatModal.value = true;
      console.log(`💬 Chat abierto desde modal para hilo: ${hilo.titulo}`);
    };

    // 🆕 NUEVO: Cerrar modal de chat (ahora solo maneja el estado del modal)
    const cerrarChatModal = async () => {
      console.log("🚪 Cerrando chat modal...");

      // Guardar referencia al hilo antes de limpiarlo
      const hiloAnterior = hiloSeleccionado.value;

      showChatModal.value = false;
      hiloSeleccionado.value = null;

      // ✅ ACTUALIZAR contadores de mensajes no leídos después de cerrar el chat
      if (hiloAnterior && hiloAnterior.postId) {
        try {
          const postId = hiloAnterior.postId;

          // Si hay hilos cargados para este post, actualizar contadores
          if (
            hilosDelPost.value[postId] &&
            hilosDelPost.value[postId].length > 0
          ) {
            const hiloIds = hilosDelPost.value[postId].map((h: any) => h._id);
            const contadores = await inmobiliarioService.getUnreadCounts(
              hiloIds
            );

            // Actualizar contadores en los hilos cargados
            hilosDelPost.value[postId].forEach((hilo: any) => {
              hilo.unreadMessagesCount = contadores[hilo._id] || 0;
            });

            console.log(
              `✅ Contadores actualizados después de cerrar chat para post ${postId}`
            );
          }
        } catch (error) {
          console.warn(
            "⚠️ Error actualizando contadores después de cerrar chat:",
            error
          );
        }
      }
    };

    // 🔍 DEBUGGING: Función para diagnosticar autenticación (temporal)
    const debugAuth = () => {
      console.log("=== 🔍 DIAGNÓSTICO DE AUTENTICACIÓN ===");
      console.log(
        "Token prop:",
        token.value ? "***" + token.value.slice(-4) : null
      );
      console.log("Connection status:", ddpService.getConnectionStatus());
      console.log("Current userId:", currentUserId.value);
      console.log("Is authenticated:", ddpService.isAuthenticatedUser());

      // Hacer disponible globalmente para debugging
      (window as any).debugAuth = debugAuth;
      (window as any).ddpService = ddpService;
    };

    // 🆕 NUEVO: Funciones auxiliares para el modal mejorado
    const esActivoReciente = (hilo: any): boolean => {
      if (!hilo.lastMessageAt) return false;

      const now = new Date();
      const lastMessage = new Date(hilo.lastMessageAt);
      const diffMs = now.getTime() - lastMessage.getTime();
      const diffHours = diffMs / (1000 * 60 * 60);

      // Consideramos activo reciente si el último mensaje fue en las últimas 24 horas
      return diffHours <= 24;
    };

    const formatearTiempoTranscurrido = (fecha: string | Date): string => {
      const d = new Date(fecha);
      const now = new Date();
      const diffMs = now.getTime() - d.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffMinutes < 1) {
        return "Ahora";
      } else if (diffMinutes < 60) {
        return `${diffMinutes}min`;
      } else if (diffHours < 24) {
        return `${diffHours}h`;
      } else if (diffDays < 7) {
        return `${diffDays}d`;
      } else {
        return formatearFechaCorta(fecha);
      }
    };

    const getTotalUnreadMessagesForPost = (post: PostInmobiliario): number => {
      const postId = normalizePostId(post);
      if (!postId || !hilosDelPost.value[postId]) {
        return 0;
      }

      return hilosDelPost.value[postId].reduce((total: number, hilo: any) => {
        return total + (hilo.unreadMessagesCount || 0);
      }, 0);
    };

    // 🆕 SCROLL INFINITO: Configurar intersection observer
    const setupIntersectionObserver = (): (() => void) | null => {
      if (!sentinelRef.value) return null;

      const observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (
            entry.isIntersecting &&
            hasMorePosts.value &&
            !isLoadingMore.value
          ) {
            console.log("👁️ Sentinel visible, cargando más posts...");
            loadMorePosts();
          }
        },
        {
          root: null, // viewport
          rootMargin: "100px", // Cargar 100px antes de llegar al final
          threshold: 0.1,
        }
      );

      // Type assertion para resolver el problema de tipos
      const sentinelElement = sentinelRef.value as unknown as Element;
      observer.observe(sentinelElement);

      // Retornar función de limpieza
      return () => {
        observer.disconnect();
      };
    };

    let cleanupIntersectionObserver: (() => void) | null = null;

    // Inicializar
    onMounted(async () => {
      // 🚀 NUEVO: Configurar sistema reactivo de autenticación
      updateCurrentUserId(); // Estado inicial
      unsubscribeAuthChange = ddpService.onAuthChange(() => {
        updateCurrentUserId(); // Actualizar cuando cambie la autenticación
      });

      // Iniciar monitor de conexión
      // monitorConnectionStatus();

      await initializeFeed();

      // 🆕 OPTIMIZADO: Configurar observer de contadores globales
      setupUnreadCountersObserver();

      // 🆕 NUEVO: Configurar observer de conteo total de hilos (siguiendo principios DDP)
      setupTotalHilosObserver();

      // 🆕 NUEVO: Configurar observer de mensajes nuevos para marcar como leídos automáticamente
      unsubscribeFromNewMessagesObserver = setupNewMessagesObserver();

      // 🆕 OPTIMIZADO: Cargar contadores iniciales para posts visibles
      await updateCountersForCurrentPosts();

      // 🔍 Configurar debugging
      debugAuth();

      // 🆕 SCROLL INFINITO: Configurar intersection observer después de cargar posts iniciales
      setTimeout(() => {
        cleanupIntersectionObserver = setupIntersectionObserver();
      }, 1000); // Delay para asegurar que el DOM esté listo

      // Agregar event listener para cerrar menú de autor al hacer clic fuera
      document.addEventListener("click", cerrarMenuAutorAlClickFuera);

      // 🆕 NUEVO: Event listener para cerrar menú de post al hacer clic fuera
      document.addEventListener("click", cerrarMenuPostAlClickFuera);

      // 🆕 NUEVO: Event listener para cerrar menú de comentario al hacer clic fuera
      document.addEventListener("click", cerrarMenuComentarioAlClickFuera);

      // 🆕 NUEVO: Event listener para cerrar menú de hilos al hacer clic fuera
      document.addEventListener("click", cerrarMenuHilosAlClickFuera);
    });

    // Limpiar al desmontar SIGUIENDO PRINCIPIOS METEOR-DDP-REACTIVIDAD-GUIA.md
    onUnmounted(() => {
      console.log("🧹 Limpiando recursos del componente...");

      // 🚀 NUEVO: Limpiar listener de autenticación
      if (unsubscribeAuthChange) {
        unsubscribeAuthChange();
      }

      // Limpiar subscription de posts
      if (unsubscribeFromChanges) {
        unsubscribeFromChanges();
      }

      // Limpiar observer de comentarios
      if (unsubscribeFromCommentsChanges) {
        unsubscribeFromCommentsChanges();
      }

      // 🚀 NUEVO: Limpiar observer optimizado de favoritos
      if (unsubscribeFromFavoritesChanges) {
        unsubscribeFromFavoritesChanges();
      }

      // 🆕 OPTIMIZADO: Limpiar observer de contadores globales
      if (unsubscribeFromUnreadCountersChanges) {
        unsubscribeFromUnreadCountersChanges();
      }

      // 🆕 NUEVO: Limpiar observer de conteo total de hilos
      if (unsubscribeFromTotalHilosChanges) {
        unsubscribeFromTotalHilosChanges();
      }

      // 🆕 NUEVO: Limpiar observer de mensajes nuevos
      if (unsubscribeFromNewMessagesObserver) {
        unsubscribeFromNewMessagesObserver();
      }

      // 🆕 SCROLL INFINITO: Limpiar intersection observer
      if (cleanupIntersectionObserver) {
        cleanupIntersectionObserver();
      }

      // 🔧 REMOVIDO: Limpieza del chat movida a ChatHiloModal

      // Limpiar event listeners
      document.removeEventListener("click", cerrarMenuAutorAlClickFuera);
      document.removeEventListener("click", cerrarMenuPostAlClickFuera);
      document.removeEventListener("click", cerrarMenuComentarioAlClickFuera);
      document.removeEventListener("click", cerrarMenuHilosAlClickFuera);

      console.log(
        "✅ Todos los recursos del componente limpiados correctamente"
      );
    });

    // 🆕 NUEVO: Funciones auxiliares para el modal mejorado

    // 🆕 NUEVO: Handlers para el componente desacoplado ComentariosPost
    const handleCommentsUpdated = (updatedComments: any[]) => {
      console.log("📝 Comentarios actualizados:", updatedComments);
      // Opcional: Actualizar estados locales si es necesario
    };

    const handleCommentAdded = (newComment: any) => {
      console.log("➕ Nuevo comentario agregado:", newComment);
      // Opcional: Mostrar notificación de éxito
    };

    const handleCommentDeleted = (deletedCommentId: string) => {
      console.log("🗑️ Comentario eliminado:", deletedCommentId);
      // Opcional: Mostrar notificación de eliminación
    };

    const handleCommentConverted = (convertedComment: any) => {
      console.log("🔄 Comentario convertido a hilo:", convertedComment);
      // Opcional: Actualizar contadores de hilos
    };

    const handleCommentError = (error: any) => {
      console.error("❌ Error en comentarios:", error);
      // Opcional: Mostrar notificación de error
    };

    return {
      textosAudienciaPrivada,
      textosAudienciaPublica,

      // Estados
      loading,
      showFilter,
      showNewPostForm,
      showMisSocios,
      showInmuebles,
      showFavoritos,
      showFeedHilos, // 🆕 NUEVO: Estado para Feed de Hilos
      showAddFriendModal,
      refreshingNotifications, // 🆕 NUEVO: Estado específico para refresh manual

      // 🆕 NUEVO: Estados para sistema de filtros inteligentes de inmuebles
      mostrarListadoInmuebles,
      filtrosAplicados,
      totalInmueblesEncontrados,
      mostrandoFavoritosEnInmuebles,
      activarFavoritosInmediatamente,

      // 🆕 SCROLL INFINITO: Estados optimizados
      postsLimit,
      isLoadingMore,
      hasMorePosts,
      isInitialLoad,
      sentinelRef,

      posts,
      newComments,
      filters,
      connectionError,
      connectionErrorMessage,
      generandoDatos,
      mensajeAccion,
      mensajeExito,
      token: props.token,
      avatar: props.avatar,
      hasNewNotifications,
      newNotificationsCount,

      // Métodos
      toggleFilter,
      toggleMisSocios,
      toggleInmuebles,
      toggleFavoritos,
      toggleFeedHilos, // 🆕 NUEVO: Toggle para Feed de Hilos

      // 🆕 NUEVO: Métodos para sistema de filtros inteligentes de inmuebles
      handleCargarInmuebles,
      volverAFiltros,
      irAMisSocios,
      irAFavoritos,
      handleTotalUpdated,
      handleFavoritosChanged,
      handleFavoritosActivados,
      obtenerLabelOperacion,

      formatDate,
      formatPrice,
      processBadges,
      hasBadges,
      processTextFormatting, // 🆕 NUEVO: Función para procesar markdown y badges
      hasTextFormatting, // 🆕 NUEVO: Función para verificar formatos
      getTypeLabel,
      getLocationLabel,
      applyFilters,
      loadMorePosts, // 🆕 SCROLL INFINITO: Reemplaza prevPage/nextPage
      openComments,
      addComment,
      detallesInmueble,
      handlePostCreated,
      initializeFeed,

      // 🆕 NUEVO: Estados y métodos para modal de detalle de inmueble
      showDetalleInmuebleModal,
      inmuebleSeleccionado,
      contactarSocioDesdeModal,
      retryConnection,
      monitorConnectionStatus,
      generarPostsPrueba,
      generarDatosEjemplo,
      limpiarDatosEjemplo,
      getPostId,
      tieneInmuebleRelacionado, // 🆕 NUEVO: Helper para verificar inmueble relacionado
      getBadgeClasses, // 🆕 NUEVO: Helper para clases CSS del badge
      loadNewNotifications,
      dismissNotifications,
      refreshFeed, // 🆕 NUEVO: Función de refresh del feed
      refreshingFeed, // 🆕 NUEVO: Estado de refresh del feed
      getAuthorFullName,

      // 🆕 Funciones del menú contextual de autores
      menuAutorAbierto,
      toggleMenuAutor,
      verCatalogoAutor,
      verPublicacionesAutor,
      abrirTelefonoAutor,
      abrirWhatsAppAutor,
      abrirTelegramAutor,
      abrirEmailAutor,

      // 🆕 NUEVO: Funciones del menú contextual de PUBLICACIONES
      menuPostAbierto,
      toggleMenuPost,
      editarPublicacion,
      eliminarPublicacion,
      eliminarPublicacionConfirmada,
      reportarPublicacion,
      compartirEnlacePublicacion,
      ocultarPublicacion,
      ocultarPublicacionConfirmada, // 🆕 NUEVO: Función auxiliar para ocultar
      marcarComoFavorito,
      esAutorDelPost,

      // 🆕 NUEVO: Funciones del menú contextual de COMENTARIOS
      menuComentarioAbierto,
      getCommentId,
      esAutorDelComentario,
      esComentarioDelAutorDelPost,
      puedeGestionarComentario,
      toggleMenuComentario,
      eliminarComentario,
      convertirComentarioAHilo,
      convirtiendoComentario,

      // 🔧 REMOVIDO: Referencias del modal de lead eliminadas

      // 🆕 NUEVO: Función para gestionar hilos de interés
      gestionarHiloInteres,

      // 🆕 NUEVO: Estados y funciones para hilos de interés
      menuHilosAbierto,
      hilosDelPost,
      loadingHilos,
      showCrearHiloModal,
      showChatModal,
      showHilosModal,

      hiloSeleccionado,
      postSeleccionado,
      postSeleccionadoParaHilo,

      // 🔧 REMOVIDO: Estados del chat movidos a ChatHiloModal

      // 🆕 NUEVO: Funciones para hilos de interés
      mostrarHilosDelPost,
      toggleMenuHilos,
      abrirModalCrearHilo,

      // 🆕 NUEVO: Handlers para el componente desacoplado NuevoHiloForm
      handleCloseCrearHilo,
      handleHiloCreated,
      handleAbrirChatFromHilo,
      handleHiloError,

      abrirChatHilo,
      abrirChatDesdeModal,
      cerrarMenuHilosAlClickFuera,
      truncarTitulo,
      formatearFechaChat,
      formatearFechaCorta,
      cerrarChatModal,

      // 🔧 REMOVIDO: Funciones del chat movidas a ChatHiloModal
      handleChatError,
      handleChatSuccess,

      // 🚀 OPTIMIZADO: Estado reactivo del usuario actual
      currentUserId,

      // 🆕 OPTIMIZADO: Estados y funciones para contadores de hilos
      unreadCountersByPost,
      loadingUnreadCounters,
      getUnreadCountForPost,

      // 🆕 NUEVO: Estados y funciones para conteo total de hilos
      totalHilosCountByPost,
      loadingTotalHilosCount,
      getTotalHilosCountForPost,

      // 🆕 NUEVO: Funciones de localStorage para ventanas de chat
      getChatWindowsFromStorage,
      isChatWindowOpen,

      // 🆕 NUEVO: Funciones auxiliares para el modal mejorado
      esActivoReciente,
      formatearTiempoTranscurrido,
      getTotalUnreadMessagesForPost,

      // 🚨 NUEVO: Sistema de respaldo para detectar limpieza accidental
      lastValidPostsCount,
      feedBackup,

      // 🆕 NUEVO: Handlers para el componente desacoplado ComentariosPost
      handleCommentsUpdated,
      handleCommentAdded,
      handleCommentDeleted,
      handleCommentConverted,
      handleCommentError,
    };
  },
});
</script>

<style scoped>
.boton-menu-autor {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.relative:hover .boton-menu-autor {
  opacity: 1;
}

.menu-desplegable-autor {
  animation: slideDown 0.15s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🆕 NUEVO: Animaciones para menú contextual de posts */
.menu-desplegable-post {
  animation: slideDown 0.15s ease-out;
}

.boton-menu-post {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.relative:hover .boton-menu-post {
  opacity: 1;
}

/* 🆕 NUEVO: Animaciones para menú contextual de comentarios */
.menu-desplegable-comentario {
  animation: slideDown 0.15s ease-out;
}

.boton-menu-comentario {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.relative:hover .boton-menu-comentario {
  opacity: 1;
}

/* 🔧 REMOVIDO: Estilos para hilos de interés movidos a BotonHilosInteres.vue */

/* 🔧 REMOVIDO: Estilos del chat movidos a ChatHiloModal.vue */

/* 🆕 NUEVO: Efectos de hover para los hilos */
.group:hover .opacity-0 {
  opacity: 1;
}

/* 🆕 NUEVO: Animaciones para el modal mejorado */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-slideUp {
  animation: slideUp 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.animate-slideIn {
  animation: slideIn 0.25s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 🆕 NUEVO: Utility class para truncar texto de múltiples líneas */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
