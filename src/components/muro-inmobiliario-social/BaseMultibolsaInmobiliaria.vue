<template>
  <div class="overflow-visible bg-white rounded-lg shadow-md">
    <!-- <PERSON><PERSON> <PERSON> la Multibolsa -->
    <div class="p-4 text-white bg-gradient-to-r to-indigo-800 from-mulbin-600">
      <div class="flex justify-between items-center">
        <!-- <PERSON><PERSON><PERSON><PERSON> según la vista activa -->
        <h2 class="flex items-center text-xl font-bold">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 150 150"
            fill="currentColor"
            preserveAspectRatio="xMidYMid meet"
            class="mr-2 w-6 h-6"
          >
            <path
              d="M60.7 145.3c-0.4-0.3-0.7-3.4-0.7-6.8 0-5.2 0.4-6.6 2.8-9.5 2.7-3.2 3.2-3.4 11.3-4 8.1-0.5 8.6-0.7 15.2-5.5l6.7-5v-5.8c0-5.4 0.3-6.2 3.4-9.3 3.3-3.3 3.7-3.4 11-3.4 13 0 16.9 3.7 16.4 15.8l-0.3 6.7-15.5 0.5-15.5 0.5-4.5 3.5c-2.5 1.9-4.7 3.6-4.9 3.7-0.2 0.1 0.4 1 1.4 1.9 0.9 0.9 2.1 2.8 2.6 4.1 1.3 3.4 1.1 10.7-0.3 12.1-1.3 1.3-27.9 1.8-29.1 0.5z"
            ></path>
            <path
              d="M16.6 129.4c-0.3-0.9-0.6-5.8-0.6-11 0-8.8-0.1-9.4-2.1-9.4-3.7 0-1.6-2.8 10-13.5 8.9-8.1 11.8-10.2 13-9.5 0.9 0.5 6.6 5.7 12.6 11.4 8.5 8.2 10.7 10.7 9.6 11.5-0.8 0.5-2 0.7-2.8 0.4-1-0.4-1.3 1.7-1.3 10.6v11.1h-18.9c-16.3 0-19-0.2-19.5-1.6z"
            ></path>
            <path
              d="M69.8 119c-2.5-2.6-2.9-3.6-2.4-6.2 0.3-1.8 1.3-3.9 2.1-4.8 1.1-1.3 1.5-4.3 1.5-12.4 0-9.3-0.2-10.7-1.7-11.3-3.4-1.2-5.3-3.7-6-7.5-0.5-3.7-1.3-4.4-10.7-10.6l-10.1-6.7-11.5-0.5-11.5-0.5-0.3-7c-0.4-12 4.2-16.9 16.2-17 8.1 0 11.4 1.5 14.6 6.8 2.2 3.5 2.8 15.5 1 17.3-0.8 0.8 1.3 2.6 6.6 6.2 7.4 4.8 7.8 5 10.1 3.5 1.6-1 3.6-1.4 6.3-1 3.6 0.5 4.2 0.2 8.9-4.8l5.1-5.3v-14c0-13.5-0.1-14-2-13.5-3.2 0.8-2.5-2.4 1.2-5.5 1.8-1.5 7.7-6.6 13.1-11.5 5.5-4.8 10.6-8.7 11.3-8.7 2 0 28.5 23.1 28.1 24.4-0.2 0.6-1.4 1.1-2.6 1-2.1-0.1-2.1 0.3-2.1 14.2 0 17 0.4 16.4-12.4 16.4h-8.6v8.3c0 7.6 0.2 8.6 2.5 10.7 5 4.7 1.6 14-5 14-3 0-8.5-4.9-8.5-7.5 0-2.7 2.9-7.3 5-8 1.8-0.6 2-1.5 2-9.1v-8.4h-9.3l-9.3 0-5.3 5.4c-4.6 4.6-5.3 5.9-4.8 8.3 0.7 3.7-1.6 8.9-4.2 9.8-2 0.6-2.1 1.4-2.1 11.1 0 9.1 0.2 10.5 1.8 11.1 7.2 2.6 8.5 11.5 2.1 14.8-4.1 2.2-5.7 1.9-9.1-1.5z"
            ></path>
            <path
              d="M104 41v-10.4c0-4.6 1.5-7.1 5-7.1 3.5 0 5 2.5 5 7.1v10.4h-10z"
            ></path>
            <path
              d="M32.3 30.5c-3.3-1.4-4.3-3.2-4.3-7.6 0-6.3 8.6-9.4 13-4.7 5.8 6.2-0.8 15.6-8.7 12.3z"
            ></path>
          </svg>
          <span class="hidden xs:inline-block">Multibolsa</span>
          <span class="hidden sm:inline-block">&nbsp;Inmobiliaria</span>
        </h2>

        <!-- Botones de navegación -->
        <div class="flex space-x-3">
          <NotificationButton
            icon="newspaper-outline"
            label="Feed"
            :is-active="vistaActiva === 'feed'"
            @click="cambiarVista('feed')"
          />

          <NotificationButton
            icon="search-outline"
            label="Inmuebles"
            :is-active="vistaActiva === 'filtro'"
            @click="cambiarVista('filtro')"
          />

          <NotificationButton
            icon="chatbubbles-outline"
            label="Hilos"
            :notification-count="notificacionesHilos"
            :is-active="vistaActiva === 'hilos'"
            @click="cambiarVista('hilos')"
          />

          <NotificationButton
            icon="people-outline"
            label="Mis Socios"
            :is-active="vistaActiva === 'socios'"
            @click="cambiarVista('socios')"
          />
        </div>
      </div>
    </div>

    <!-- Contenido principal -->
    <div>
      <!-- Vista de Inmuebles (Filtro + Listado) -->
      <div v-if="vistaActiva === 'filtro'" class="border-b border-gray-200">
        <!-- Filtro Inteligente (Bypass) -->
        <FiltroInmueblesInteligente
          v-if="!mostrarListadoInmuebles && !mostrarListadoOwner"
          @cargarInmuebles="handleCargarInmuebles"
          @irASocios="irAMisSocios"
          @verFavoritos="handleVerFavoritos"
        />

        <div v-if="mostrarListadoInmuebles || mostrarListadoOwner">
          <!-- Header con filtros aplicados y botón de regreso -->
          <div
            v-if="!mostrandoFavoritosEnInmuebles && !mostrarListadoOwner"
            class="p-4 text-white bg-gradient-to-r from-blue-500 to-indigo-600"
          >
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <button
                  @click="volverAFiltros"
                  class="flex items-center px-3 py-1 mr-4 text-sm bg-white bg-opacity-20 rounded-lg transition-colors hover:bg-opacity-30"
                >
                  <ion-icon name="arrow-back" class="mr-1"></ion-icon>
                  Cambiar Filtros
                </button>
                <div>
                  <h3 class="text-lg font-semibold">Inmuebles Filtrados</h3>
                  <p class="text-sm text-blue-100">
                    {{ filtrosAplicados.ubicacion || "Todas las ciudades" }} •
                    {{
                      obtenerLabelOperacion(filtrosAplicados.operacion) ||
                      "Todas las operaciones"
                    }}
                  </p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm text-blue-100">Resultados encontrados</p>
                <p class="text-xl font-bold">{{ totalInmueblesEncontrados }}</p>
              </div>
            </div>
          </div>

          <!-- Componente Owner o Socios según selección -->
          <div v-if="mostrarListadoOwner">
            <div class="p-4 bg-gray-50 border-b">
              <button
                @click="volverAFiltros"
                class="flex items-center px-3 py-1 mr-4 text-sm text-gray-700 bg-white rounded-lg border border-gray-200 transition-colors hover:bg-gray-100"
              >
                <ion-icon name="arrow-back" class="mr-1"></ion-icon>
                Volver a seleccionar filtros
              </button>
            </div>
            <InmueblesOwner />
          </div>
          <div v-else>
            <InmueblesSocios
              :token="props.token"
              :filtros-iniciales="filtrosAplicados"
              :activar-favoritos-inmediatamente="activarFavoritosInmediatamente"
              @total-updated="handleTotalUpdated"
              @ir-a-filtros="volverAFiltros"
              @favoritos-changed="handleFavoritosChanged"
              @favoritos-activados="handleFavoritosActivados"
            />
          </div>
        </div>
      </div>

      <!-- Componente Mis Socios -->
      <div v-if="vistaActiva === 'socios'" class="border-b border-gray-200">
        <MisSocios />
      </div>

      <!-- Componente Feed de Hilos -->
      <div v-if="vistaActiva === 'hilos'" class="border-b border-gray-200">
        <FeedHilos :token="props.token" />
      </div>

      <!-- 🆕 NUEVO: Componente Feed de Publicaciones -->
      <div v-if="vistaActiva === 'feed'" class="border-b border-gray-200">
        <FeedPublicaciones
          :token="props.token"
          :textos-audiencia-privada="textosAudienciaPrivada"
          :textos-audiencia-publica="textosAudienciaPublica"
          :avatar="props.avatar"
        />
      </div>
    </div>

    <!-- Modal global para alertas y confirmaciones -->
    <GlobalModal />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import FiltroInmueblesInteligente from "../inmuebles-multibolsa/FiltroInmueblesInteligente.vue";
import MisSocios from "./MisSocios.vue";
import FeedHilos from "./FeedHilos.vue";
import InmueblesSocios from "../inmuebles-multibolsa/InmueblesSocios.vue";
import InmueblesOwner from "../inmuebles-multibolsa/InmueblesOwner.vue";
import FeedPublicaciones from "./FeedPublicaciones.vue";
import GlobalModal from "../ui/GlobalModal.vue";
import NotificationButton from "../ui/NotificationButton.vue";
import ddpService from "../../services/ddpService";

// Props
interface Props {
  token?: string;
  avatar?: string;
  textosAudienciaPrivada?: {
    titulo: string;
    descripcion: string;
  };
  textosAudienciaPublica?: {
    titulo: string;
    descripcion: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  token: "",
  avatar: "",
  textosAudienciaPrivada: () => ({
    titulo: "Solo a socios determinados",
    descripcion:
      "Solo tus socios seleccionados verán esta publicación en su muro inmobiliario",
  }),
  textosAudienciaPublica: () => ({
    titulo: "Todos mis socios",
    descripcion:
      "Todos tus socios verán esta publicación en su muro inmobiliario",
  }),
});

// Estados reactivos
const vistaActiva = ref<"filtro" | "socios" | "hilos" | "feed">("feed");

// Estado para contadores globales de hilos
const totalNotificacionesHilos = ref(0);
const loadingHilosCount = ref(false);

// Computed para el total de notificaciones de hilos
const notificacionesHilos = computed(() => {
  return totalNotificacionesHilos.value;
});

// Estados para sistema de filtros inteligentes de inmuebles
const mostrarListadoInmuebles = ref(false);
const mostrarListadoOwner = ref(false);
const filtrosAplicados = ref({
  ubicacion: "",
  operacion: "",
  tipos: [] as number[],
});
const totalInmueblesEncontrados = ref(0);
const mostrandoFavoritosEnInmuebles = ref(false);

// Variable para controlar activación automática de favoritos
const activarFavoritosInmediatamente = ref(false);

// 🆕 NUEVO: Props para textos de audiencia con valores por defecto
const textosAudienciaPrivada = ref(props.textosAudienciaPrivada);
const textosAudienciaPublica = ref(props.textosAudienciaPublica);

// Métodos
const cambiarVista = (vista: "filtro" | "socios" | "hilos" | "feed") => {
  vistaActiva.value = vista;
};

// Métodos para sistema de filtros inteligentes de inmuebles
const handleCargarInmuebles = (payload: any) => {
  // Soporta dos flujos: filtros de socios o listado del owner
  if (payload && payload.ownerOnly) {
    mostrarListadoOwner.value = true;
    mostrarListadoInmuebles.value = false;
    console.log("👤 Mostrando solo mis inmuebles (owner)");
    return;
  }

  const filtros = payload as {
    ubicacion: string;
    operacion: string;
    tipos?: number[]; // Array de IDs de tipos
  };
  filtrosAplicados.value = { ...filtros, tipos: filtros.tipos || [] };
  mostrarListadoOwner.value = false;
  mostrarListadoInmuebles.value = true;
  console.log("🚀 Cargando inmuebles con filtros:", filtros);
};

const volverAFiltros = () => {
  mostrarListadoInmuebles.value = false;
  mostrarListadoOwner.value = false;
  filtrosAplicados.value = { ubicacion: "", operacion: "", tipos: [] };
  totalInmueblesEncontrados.value = 0;
  console.log("🔙 Volviendo al selector de filtros");
};

const irAMisSocios = () => {
  cambiarVista("socios");
  console.log("👥 Navegando a Mis Socios");
};

// Función para ir a favoritos desde filtro inteligente
const handleVerFavoritos = () => {
  // Asegurarse de que se muestre el listado de inmuebles para poder acceder a favoritos
  mostrarListadoInmuebles.value = true;

  // Señal para que Inmuebles active favoritos automáticamente
  activarFavoritosInmediatamente.value = true;

  console.log("⭐ Navegando directamente a favoritos desde filtro inteligente");
};

const handleTotalUpdated = (total: number) => {
  totalInmueblesEncontrados.value = total;
};

// Manejar cambio de vista de favoritos en Inmuebles
const handleFavoritosChanged = (mostrandoFavoritos: boolean) => {
  mostrandoFavoritosEnInmuebles.value = mostrandoFavoritos;
  console.log(
    `🔄 Vista de favoritos en inmuebles: ${
      mostrandoFavoritos ? "activada" : "desactivada"
    }`
  );
};

// Resetear prop cuando se activan favoritos
const handleFavoritosActivados = () => {
  activarFavoritosInmediatamente.value = false;
  console.log("✅ Favoritos activados, reseteando prop");
};

const obtenerLabelOperacion = (operacion: string): string => {
  const labels: Record<string, string> = {
    venta: "En Venta",
    renta: "En Renta",
    traspaso: "En Traspaso",
  };
  return labels[operacion] || operacion;
};

// Función para cargar contadores globales de hilos
const loadHilosGlobalesCount = async () => {
  try {
    loadingHilosCount.value = true;

    // Llamar al método para obtener total global desde unreadCountsByPost
    const response = (await ddpService.call(
      "hilosInteres.getAllUnreadCounts"
    )) as { total: number; breakdown: { [id: string]: number } };
    
    totalNotificacionesHilos.value = response.total;

    console.log(
      `📊 Total global de notificaciones cargado: ${response.total} mensajes sin leer`,
      response.breakdown
    );
  } catch (error) {
    console.error("Error cargando contadores globales de hilos:", error);
    totalNotificacionesHilos.value = 0;
  } finally {
    loadingHilosCount.value = false;
  }
};

// Inicializar al montar el componente y configurar refresco periódico
onMounted(() => {
  loadHilosGlobalesCount();

  // Recargar contadores cada 30 segundos para mantener actualizados
  const intervalId = setInterval(() => {
    if (!loadingHilosCount.value) {
      loadHilosGlobalesCount();
    }
  }, 30000);

  // Limpiar intervalo al desmontar
  onUnmounted(() => {
    clearInterval(intervalId);
  });
});
</script>

<style scoped>
/* Estilos adicionales si son necesarios */
.transition-colors {
  transition: background-color 0.2s ease, color 0.2s ease;
}
</style>
