# 👥 SelectorSocios - Documentación Técnica

> Componente avanzado para selección de socios con soporte para contratos y configuración de audiencia

## 📋 Tabla de Contenidos

- [🎯 Descripción General](#-descripción-general)
- [🏗️ Arquitectura del Componente](#️-arquitectura-del-componente)
- [🧩 Props y Configuración](#-props-y-configuración)
- [🔧 Funcionalidades Principales](#-funcionalidades-principales)
- [🎨 Interfaz de Usuario](#-interfaz-de-usuario)
- [📡 Integración con Backend](#-integración-con-backend)
- [🔄 Cambios Recientes](#-cambios-recientes)
- [💡 Ejemplos de Uso](#-ejemplos-de-uso)

---

## 🎯 Descripción General

### ¿Qué es SelectorSocios?

El **SelectorSocios** es un componente Vue.js especializado para la selección de socios en el contexto de publicaciones inmobiliarias. Permite configurar la audiencia de una publicación (pública o privada) y seleccionar socios específicos cuando se requiere una audiencia limitada.

### Características Principales

- ✅ **Toggle público/privado** con textos personalizables
- ✅ **Búsqueda avanzada** por nombre, empresa, ubicación y etiquetas
- ✅ **Soporte para etiquetas** con colores y filtrado
- ✅ **Selección múltiple** con indicadores visuales
- ✅ **Soporte para contratos** además de IDs de socios
- ✅ **Campos de formulario** compatibles con sistemas legacy
- ✅ **Responsive design** optimizado para móvil y desktop
- ✅ **Interacción selectiva** - Solo el área de selección es clickeable

---

## 🏗️ Arquitectura del Componente

### Estructura de Archivos

```
src/components/selector-socios-wrapper/
├── SelectorSocios.vue           # 🎯 Componente principal
├── SelectorSociosWrapper.vue    # 🔄 Wrapper para adaptación de datos
├── index.js                     # 📦 Entry point y auto-inicialización
└── SELECTOR-SOCIOS-DOCS.md      # 📖 Esta documentación
```

### Flujo de Datos

```mermaid
graph TD
    A[InmuebleBolsaInmobiliaria] --> B[SelectorSociosWrapper]
    B --> C[SelectorSocios]
    C --> D[API Backend]
    D --> E[Datos de Socios]
    E --> C
    C --> F[Formulario HTML]
    F --> G[Backend PHP/Meteor]
```

---

## 🧩 Props y Configuración

### Props Principales

```typescript
interface Props {
  // Control de visibilidad
  showAddButton: boolean; // Mostrar botón "Agregar socio"

  // Valores iniciales
  initialIsPublic: boolean; // Estado inicial público/privado
  initialSelectedSocios: string[]; // Socios preseleccionados

  // Configuración de formulario
  publicFieldName: string; // Nombre del campo público (default: "es_publico")
  selectedFieldName: string; // Nombre del campo socios (default: "socios_seleccionados")
  contractFieldName: string; // Nombre del campo contratos (default: "contratos_seleccionados")
  checkboxName: string; // Nombre checkboxes socios (default: "socio_ids[]")
  contractCheckboxName: string; // Nombre checkboxes contratos (default: "contrato_ids[]")

  // Textos personalizables
  textosAudienciaPublica: {
    titulo: string;
    descripcion: string;
  };
  textosAudienciaPrivada: {
    titulo: string;
    descripcion: string;
  };

  // Configuración de carga
  autoLoad: boolean; // Cargar socios automáticamente
  externalSocios: Socio[]; // Datos externos de socios
}
```

### Eventos Emitidos

```typescript
interface Events {
  "update:isPublic": boolean; // Cambio en toggle público/privado
  "update:selectedSocios": string[]; // Cambio en socios seleccionados
  "add-socio": void; // Solicitud para agregar nuevo socio
  "selection-change": {
    // Cambio completo en selección
    isPublic: boolean;
    selectedSocios: string[];
  };
}
```

---

## 🔧 Funcionalidades Principales

### 1. **Toggle Público/Privado**

```vue
<label class="inline-flex relative items-center cursor-pointer">
  <input type="checkbox" v-model="isPublic" class="sr-only peer">
  <div class="w-11 h-6 bg-gray-200 rounded-full peer-checked:bg-indigo-500 peer"></div>
  <span class="ml-3 text-sm font-medium text-gray-700">Público</span>
</label>
```

### 2. **Búsqueda Avanzada**

- **Por nombre**: Búsqueda insensible a acentos
- **Por empresa**: Incluye nombre de la empresa
- **Por ubicación**: Búsqueda en campo de ubicación
- **Por etiquetas**: Búsqueda en nombres de etiquetas asignadas

```typescript
const filteredSocios = computed(() => {
  const normalizedQuery = normalizeText(searchQuery.value);

  return availableSocios.value.filter((socio) => {
    const normalizedNombre = normalizeText(socio.nombre);
    const normalizedEmpresa = normalizeText(socio.empresa);
    const normalizedUbicacion = normalizeText(socio.ubicacion);

    let matchesEtiquetas = false;
    if (socio.etiquetas && socio.etiquetas.length > 0) {
      matchesEtiquetas = socio.etiquetas.some((etiqueta) =>
        normalizeText(etiqueta.nombre).includes(normalizedQuery)
      );
    }

    return (
      normalizedNombre.includes(normalizedQuery) ||
      normalizedEmpresa.includes(normalizedQuery) ||
      normalizedUbicacion.includes(normalizedQuery) ||
      matchesEtiquetas
    );
  });
});
```

### 3. **Selección por Etiquetas**

Permite seleccionar todos los socios que tengan una etiqueta específica:

```typescript
const selectByTag = (etiqueta: Etiqueta) => {
  const sociosConEtiqueta = availableSocios.value
    .filter((socio) => socio.etiquetas?.some((tag) => tag.id === etiqueta.id))
    .map((socio) => socio.id);

  selectedSocios.value = [...sociosConEtiqueta];
};
```

### 4. **Soporte para Contratos**

El componente maneja tanto IDs de socios como contratos:

```typescript
// Computed que mapea socios seleccionados a sus contratos
const selectedContracts = computed(() => {
  return selectedSocios.value.map((socioId) => {
    const socio = availableSocios.value.find((s) => s.id === socioId);
    return socio ? socio.contrato : socioId;
  });
});
```

---

## 🎨 Interfaz de Usuario

### Estados Visuales

| Estado           | Descripción                     | Clases CSS                                |
| ---------------- | ------------------------------- | ----------------------------------------- |
| **Seleccionado** | Socio marcado como seleccionado | `bg-blue-50 border-l-4 border-mulbin-600` |
| **Hover**        | Efecto al pasar el mouse        | `hover:bg-gray-50`                        |
| **Loading**      | Estado de carga                 | `animate-spin text-mulbin-600`            |
| **Vacío**        | Sin socios disponibles          | `text-gray-500`                           |

### Responsive Design

```css
/* Mobile First */
.socio-item-selector {
  @apply flex flex-col p-3 space-y-3;
}

/* Desktop */
@media (min-width: 640px) {
  .socio-item-selector {
    @apply flex-row items-center justify-between space-y-0 p-4;
  }
}
```

### Iconografía

- **👤 Avatar**: Imagen del socio o placeholder
- **✅ Verificado**: Badge para socios verificados
- **🏷️ Etiquetas**: Tags con colores personalizados
- **🔍 Búsqueda**: Icono de lupa en campo de búsqueda
- **❌ Limpiar**: Icono para limpiar búsqueda

---

## 📡 Integración con Backend

### Endpoint Principal

```http
GET /msi-v5/owner/socios
```

**Respuesta esperada:**

```json
{
  "statusCode": 200,
  "data": {
    "socios": [
      {
        "id": "123",
        "contrato": "ABC123",
        "nombre": "Juan Pérez",
        "empresa": "Inmobiliaria XYZ",
        "ubicacion": "Madrid, España",
        "avatar": "https://...",
        "email": "<EMAIL>",
        "telefono": "+34123456789",
        "verified": true,
        "tipo": "directo",
        "tags": ["1", "3"]
      }
    ],
    "tags": [
      {
        "id": 1,
        "tag": "VIP",
        "style": "background-color: #EDE9FE; border: 1px solid #7C3AED;",
        "description": "Cliente VIP"
      }
    ]
  }
}
```

### Campos de Formulario Generados

```html
<!-- Campos principales -->
<input type="hidden" name="es_publico" value="Si|No" />
<input type="hidden" name="socios_seleccionados" value="123,456,789" />
<input
  type="hidden"
  name="contratos_seleccionados"
  value="ABC123,DEF456,GHI789"
/>

<!-- Arrays individuales -->
<input type="checkbox" name="socio_ids[]" value="123" checked />
<input type="checkbox" name="socio_ids[]" value="456" checked />
<input type="checkbox" name="contrato_ids[]" value="ABC123" checked />
<input type="checkbox" name="contrato_ids[]" value="DEF456" checked />
```

---

## 🔄 Cambios Recientes

### Enero 2024 - Soporte para Contratos y Fix de Layout

#### ✅ **Nuevas Funcionalidades**

1. **Soporte para Contratos**

   - Agregadas props `contractFieldName` y `contractCheckboxName`
   - Computed `selectedContracts` que mapea IDs a contratos
   - Campos ocultos adicionales para contratos

2. **Textos Personalizables**

   - Props `textosAudienciaPublica` y `textosAudienciaPrivada`
   - Flexibilidad para diferentes contextos de uso

3. **Clase Específica para Targeting**
   - Agregada clase `socio-item-selector` para identificación precisa
   - Mejora en la especificidad de eventos

---

## 💡 Ejemplos de Uso

### Uso Básico

```vue
<template>
  <SelectorSocios
    :initial-is-public="false"
    :initial-selected-socios="['123', '456']"
    @update:isPublic="handlePublicChange"
    @update:selectedSocios="handleSociosChange"
  />
</template>

<script>
import SelectorSocios from "./SelectorSocios.vue";

export default {
  components: { SelectorSocios },
  methods: {
    handlePublicChange(isPublic) {
      console.log("Público:", isPublic);
    },
    handleSociosChange(socios) {
      console.log("Socios seleccionados:", socios);
    },
  },
};
</script>
```

### Uso con Datos Externos

```vue
<template>
  <SelectorSociosWrapper
    :is-public="isPublic"
    :selected-socios="selectedSocios"
    :available-friends="sociosData"
    @update:isPublic="isPublic = $event"
    @update:selectedSocios="selectedSocios = $event"
  />
</template>
```

### Uso con Textos Personalizados

```vue
<template>
  <SelectorSocios
    :textos-audiencia-publica="{
      titulo: 'Publicar a todos los socios',
      descripcion: 'El inmueble se publicará en la Multibolsa Inmobiliaria',
    }"
    :textos-audiencia-privada="{
      titulo: 'Publicar solo a socios determinados',
      descripcion: 'Solo tus socios seleccionados recibirán la publicación',
    }"
  />
</template>
```

### Integración en Formulario HTML

```html
<!-- Container para auto-inicialización -->
<div
  data-selector-socios
  data-initial-public="false"
  data-selected-socios="123,456"
></div>

<!-- Script de inicialización -->
<script src="dist/assets/selectorSocios.js"></script>
```

---

## 🔧 Desarrollo y Mantenimiento

### Scripts de Build

```bash
# Desarrollo
npm run build

# Producción
npm run prod

# Testing
npm run preview
```

### Testing del Componente

```bash
# Abrir panel de testing
open http://localhost:4173/panel.html

# Verificar en consola
console.log('SelectorSocios loaded:', window.SelectorSocios);
```

### Debugging

```javascript
// Verificar estado del componente
const component = document.querySelector(
  "[data-selector-socios]"
).__vueParentComponent;
console.log("Estado actual:", component.setupState);

// Verificar datos de socios
console.log("Socios disponibles:", component.setupState.availableSocios);
console.log("Socios seleccionados:", component.setupState.selectedSocios);
```

---

## 📚 Referencias

- [README Principal](../../../README.md)
- [MuroInmobiliarioSocial Docs](../muro-inmobiliario-social/MURO-INMOBILIARIO-SOCIAL-DOCS.md)
- [InmuebleBolsaInmobiliaria Docs](../inmueble-bolsa-inmobiliaria/INMUEBLE-BOLSA-DOCS.md)
- [API Backend](../../../../Meteor/MulbinComponents/app/API-REST-External-Backends.md)
