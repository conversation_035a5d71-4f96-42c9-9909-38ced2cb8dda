import { createApp } from "vue";
import Alpine from "alpinejs";
import mask from "@alpinejs/mask";
import SelectorSociosWrapper from "./SelectorSociosWrapper.vue";
import ddpService from "../../services/ddpService";
import { createDDPWithPlugins, ws } from "../../services/simpleddpWrapper";

// Registrar el plugin x-mask
Alpine.plugin(mask);

// Inicializar Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Exponer una instancia global para pruebas
window.createTestMeteorConnection = () => {
  const opts = {
    endpoint:
      import.meta.env.VITE_METEOR_URL || "ws://localhost:3000/websocket",
    SocketConstructor: ws,
    reconnectInterval: 5000,
  };

  return createDDPWithPlugins(opts);
};

// Inicializar DDP de manera explícita para evitar problemas de carga
try {
  // Exponemos el servicio y creamos una instancia adicional para diagnóstico
  window.ddpService = ddpService;
  // window.testDDP = window.createTestMeteorConnection();

  console.log("Servicio DDP inicializado correctamente");
  // console.log("Instancia de prueba creada:", window.testDDP);
} catch (error) {
  console.error("Error inicializando servicio DDP:", error);
}

// Inicializar Vue cuando el DOM esté cargado
document.addEventListener("DOMContentLoaded", () => {
  // Cargar Ionicons si no están ya cargados
  if (!document.querySelector('script[src*="ionicons"]')) {
    const moduleScript = document.createElement("script");
    moduleScript.type = "module";
    moduleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js";
    document.body.appendChild(moduleScript);

    const noModuleScript = document.createElement("script");
    noModuleScript.noModule = true;
    noModuleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js";
    document.body.appendChild(noModuleScript);
  }

  const targetElement = document.querySelector(
    "[data-selector-socios-wrapper]"
  );

  if (targetElement) {
    // Obtener el token del atributo data-token
    const token = targetElement.getAttribute("data-token") || "";

    // 🆕 Leer otros atributos data-* y convertirlos a las props correctas
    const isPublicAttr = targetElement.getAttribute("data-is-public");
    const selectedSociosAttr = targetElement.getAttribute(
      "data-selected-socios"
    );
    const availableFriendsAttr = targetElement.getAttribute(
      "data-available-friends"
    );

    // 🆕 Convertir strings a tipos correctos
    let isPublic = true; // default
    let selectedSocios = []; // default
    let availableFriends = []; // default

    // Procesar is-public
    if (isPublicAttr !== null) {
      isPublic =
        isPublicAttr === "true" ||
        isPublicAttr === "1" ||
        isPublicAttr === "Si";
    }

    // Procesar selected-socios (puede ser string separado por comas o JSON)
    if (selectedSociosAttr && selectedSociosAttr.trim()) {
      try {
        // Intentar parsear como JSON primero
        selectedSocios = JSON.parse(selectedSociosAttr);
      } catch (e) {
        // Si no es JSON, tratarlo como string separado por comas
        selectedSocios = selectedSociosAttr
          .split(",")
          .map((s) => s.trim())
          .filter(Boolean);
      }
    }

    // Procesar available-friends (esperamos JSON)
    if (availableFriendsAttr && availableFriendsAttr.trim()) {
      try {
        availableFriends = JSON.parse(availableFriendsAttr);
      } catch (e) {
        console.warn("Error parsing available-friends JSON:", e);
        availableFriends = [];
      }
    }

    console.log("📋 Props para SelectorSociosWrapper:", {
      token: token ? "***" : "(vacío)",
      isPublic,
      selectedSocios,
      availableFriends: availableFriends.length + " socios",
    });

    // Crear y montar la aplicación Vue con todas las props
    const app = createApp(SelectorSociosWrapper, {
      token: token,
      isPublic: isPublic,
      selectedSocios: selectedSocios,
      availableFriends: availableFriends,
    });

    // Proporcionar el servicio DDP a toda la aplicación
    app.provide("ddpService", ddpService);

    app.mount(targetElement);

    console.log("✅ Componente SelectorSociosWrapper montado correctamente");
  } else {
    console.error(
      "❌ No se encontró el elemento [data-selector-socios-wrapper]"
    );
  }
});
