<template>
  <SelectorSocios
    :initial-is-public="isPublic"
    :initial-selected-socios="selectedSocios"
    :external-socios="adaptedSocios"
    :auto-load="false"
    :textos-audiencia-privada="textosAudienciaPrivada"
    :textos-audiencia-publica="textosAudienciaPublica"
    @update:isPublic="$emit('update:isPublic', $event)"
    @update:selectedSocios="$emit('update:selectedSocios', $event)"
    @add-socio="$emit('add-socio')"
  />
</template>

<script lang="ts">
import { defineComponent, computed } from "vue";
import SelectorSocios from "./SelectorSocios.vue";

export default defineComponent({
  name: "SelectorSociosWrapper",

  components: {
    SelectorSocios,
  },

  props: {
    isPublic: {
      type: Boolean,
      required: true,
    },
    selectedSocios: {
      type: Array as () => string[],
      required: true,
    },
    availableFriends: {
      type: Array as () => any[],
      required: true,
    },
    textosAudienciaPrivada: {
      type: Object,
      required: true,
    },
    textosAudienciaPublica: {
      type: Object,
      required: true,
    },
  },

  emits: ["update:isPublic", "update:selectedSocios", "add-socio"],

  setup(props) {
    // Adaptar la estructura de datos de MuroInmobiliarioSocial a la esperada por SelectorSocios
    const adaptedSocios = computed(() => {
      const result = props.availableFriends.map((friend) => ({
        id: friend._id || friend.id,
        contrato: friend.contrato || friend._id || friend.id,
        nombre: friend.name || friend.nombre,
        empresa: friend.company || friend.empresa,
        ubicacion: friend.location || friend.ubicacion || "",
        avatar: friend.avatar,
        email: friend.email,
        telefono: friend.phone || friend.telefono,
        wa: friend.wa,
        telegram: friend.telegram,
        tipo: "directo" as const,
        verified: friend.verified || false,
        etiquetas: friend.etiquetas || [],
      }));

      return result;
    });

    console.log(props.availableFriends);

    return {
      adaptedSocios,
    };
  },
});
</script>
