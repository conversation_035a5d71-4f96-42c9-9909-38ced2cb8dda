<template>
  <div class="selector-socios-container">
    <!-- Toggle para publicación pública/privada -->
    <div
      class="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200"
    >
      <div class="flex justify-between items-center mb-3">
        <div class="flex items-center space-x-3">
          <div
            class="flex justify-center items-center w-10 h-10 bg-white rounded-full shadow-sm"
          >
            <ion-icon
              :name="isPublic ? 'globe-outline' : 'people-outline'"
              class="visible text-lg"
              :class="isPublic ? 'text-blue-600' : 'text-mulbin-600'"
            ></ion-icon>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-800">
              {{
                isPublic
                  ? textosAudienciaPublica.titulo
                  : textosAudienciaPrivada.titulo
              }}
            </p>
            <p class="text-xs text-gray-600">
              {{
                isPublic
                  ? textosAudienciaPublica.descripcion
                  : textosAudienciaPrivada.descripcion
              }}
            </p>
          </div>
        </div>

        <!-- Toggle Switch Elegante -->
        <label class="inline-flex relative items-center cursor-pointer">
          <input
            type="checkbox"
            v-model="isPublic"
            class="sr-only peer"
            @change="onPublicToggle"
          />
          <div
            class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-500"
          ></div>
          <span class="ml-3 text-sm font-medium text-gray-700">Todos</span>
        </label>
      </div>

      <!-- Selector de socios (solo visible cuando no es público) -->
      <div v-if="!isPublic" class="space-y-3">
        <div class="flex justify-between items-center">
          <h4 class="text-sm font-medium text-gray-800">
            Seleccionar socios destinatarios
          </h4>
          <span
            class="px-2 py-1 text-xs font-medium rounded-full text-mulbin-600 bg-mulbin-100"
          >
            {{ selectedSocios.length }} seleccionado{{
              selectedSocios.length !== 1 ? "s" : ""
            }}
          </span>
        </div>

        <!-- 🔍 Buscador de socios -->
        <div v-if="availableSocios.length > 0" class="relative">
          <div
            class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none"
          >
            <ion-icon name="search-outline" class="text-gray-400"></ion-icon>
          </div>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Buscar socio por nombre, empresa o etiqueta..."
            class="py-2 pr-4 pl-10 w-full text-sm rounded-lg border border-gray-300 transition-colors focus:ring-2 focus:ring-mulbin-500 focus:border-mulbin-500"
          />
          <div
            v-if="searchQuery.trim()"
            class="flex absolute inset-y-0 right-0 items-center pr-3"
          >
            <button
              @click="searchQuery = ''"
              class="text-gray-400 transition-colors hover:text-gray-600"
              title="Limpiar búsqueda"
            >
              <ion-icon name="close-circle-outline"></ion-icon>
            </button>
          </div>
        </div>

        <div
          class="overflow-y-auto max-h-64 bg-white rounded-lg border border-gray-200 shadow-sm"
        >
          <div v-if="loading" class="p-4 text-center text-gray-500">
            <div class="flex justify-center items-center space-x-2">
              <ion-icon
                name="reload-outline"
                class="animate-spin text-mulbin-600"
              ></ion-icon>
              <span class="text-sm">Cargando socios...</span>
            </div>
          </div>

          <div v-else-if="availableSocios.length === 0" class="p-4 text-center">
            <ion-icon
              name="people-outline"
              class="mb-2 text-3xl text-gray-300"
            ></ion-icon>
            <p class="text-sm text-gray-500">No tienes socios agregados aún</p>
            <button
              v-if="showAddButton"
              @click="$emit('add-socio')"
              class="mt-2 text-xs font-medium text-mulbin-600 hover:text-mulbin-700"
            >
              Agregar tu primer socio
            </button>
          </div>

          <!-- Mensaje cuando no hay resultados de búsqueda -->
          <div v-else-if="filteredSocios.length === 0" class="p-4 text-center">
            <ion-icon
              name="search-outline"
              class="mb-2 text-3xl text-gray-300"
            ></ion-icon>
            <p class="text-sm text-gray-500">
              No se encontraron socios con "{{ searchQuery }}"
            </p>
            <button
              @click="searchQuery = ''"
              class="mt-2 text-xs font-medium text-mulbin-600 hover:text-mulbin-700"
            >
              Limpiar búsqueda
            </button>
          </div>

          <div v-else class="divide-y divide-gray-100">
            <div
              v-for="socio in filteredSocios"
              :key="socio.id"
              class="flex flex-col p-3 space-y-3 transition-colors duration-150 socio-item-selector sm:p-4 hover:bg-gray-50 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
              :class="{
                'bg-blue-50 border-l-4 border-mulbin-600':
                  selectedSocios.includes(socio.id),
              }"
            >
              <!-- Área de selección a la izquierda -->
              <div
                class="flex items-center mr-3 cursor-pointer sm:mr-4"
                @click="toggleSocioSelection(socio.id)"
              >
                <div
                  v-if="selectedSocios.includes(socio.id)"
                  class="flex justify-center items-center w-8 h-8 rounded-full bg-mulbin-600"
                >
                  <ion-icon
                    name="checkmark"
                    class="text-sm text-white"
                  ></ion-icon>
                </div>
                <div
                  v-else
                  class="flex justify-center items-center w-8 h-8 rounded-full border-2 border-gray-300 hover:border-mulbin-400"
                >
                  <div class="w-3 h-3 bg-transparent rounded-full"></div>
                </div>
              </div>

              <div class="flex flex-1 items-center">
                <!-- Avatar del socio -->
                <img
                  v-if="socio.avatar"
                  :src="socio.avatar"
                  :alt="socio.nombre"
                  class="object-cover flex-shrink-0 mr-3 w-10 h-10 rounded-full sm:w-12 sm:h-12 sm:mr-4"
                />
                <!-- Avatar predeterminado -->
                <div
                  v-else
                  class="flex flex-shrink-0 justify-center items-center mr-3 w-10 h-10 text-gray-400 bg-gray-200 rounded-full sm:w-12 sm:h-12 sm:mr-4"
                >
                  <img
                    :src="`${mulbinUrl}/images/avatar.png`"
                    alt="Avatar"
                    class="w-6 h-6 sm:w-8 sm:h-8"
                  />
                </div>

                <div class="flex-1 min-w-0">
                  <!-- Nombre y badges -->
                  <h3
                    class="flex flex-wrap items-center text-sm font-medium text-gray-900 sm:text-base"
                  >
                    {{ socio.nombre }}

                    <!-- Badge de verificación -->
                    <span
                      v-if="socio.verified"
                      class="px-1.5 py-0.5 ml-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full sm:ml-2 sm:px-2"
                    >
                      Verificado
                    </span>

                    <!-- Etiquetas personalizadas del socio (solo visualización) -->
                    <template v-if="socio.etiquetas && socio.etiquetas.length">
                      <span
                        v-for="etiqueta in socio.etiquetas.slice(0, 3)"
                        :key="etiqueta.id"
                        class="inline-flex items-center px-2 py-0.5 ml-1 text-xs rounded-md sm:ml-2"
                        :style="{
                          backgroundColor: etiqueta.backgroundColor,
                          color: etiqueta.color,
                          border: `1px solid ${etiqueta.color}20`,
                        }"
                        :title="etiqueta.descripcion || etiqueta.nombre"
                      >
                        {{ etiqueta.nombre }}
                      </span>
                      <span
                        v-if="socio.etiquetas.length > 3"
                        class="ml-1 text-xs text-gray-500 sm:ml-2"
                        :title="`${
                          socio.etiquetas.length - 3
                        } etiquetas más: ${socio.etiquetas
                          .slice(3)
                          .map((e) => e.nombre)
                          .join(', ')}`"
                      >
                        +{{ socio.etiquetas.length - 3 }}
                      </span>
                    </template>
                  </h3>

                  <!-- Información adicional -->
                  <div
                    class="flex flex-col mt-1 space-y-1 text-xs text-gray-500 sm:flex-row sm:items-center sm:text-sm sm:space-y-0"
                  >
                    <span
                      v-if="socio.empresa"
                      class="flex items-center sm:mr-3"
                    >
                      <ion-icon
                        name="briefcase-outline"
                        class="mr-1 text-sm"
                      ></ion-icon>
                      <span class="truncate">{{ socio.empresa }}</span>
                    </span>
                    <span v-if="socio.ubicacion" class="flex items-center">
                      <ion-icon
                        name="location-outline"
                        class="mr-1 text-sm"
                      ></ion-icon>
                      <span class="truncate">{{ socio.ubicacion }}</span>
                    </span>
                  </div>
                </div>
              </div>

              <!-- Checkbox oculto para funcionalidad -->
              <input
                type="checkbox"
                :value="socio.id"
                v-model="selectedSocios"
                class="sr-only"
                :name="checkboxName"
              />
              <!-- Checkbox oculto para contrato -->
              <input
                v-if="selectedSocios.includes(socio.id)"
                type="checkbox"
                :value="socio.contrato"
                checked
                class="sr-only"
                :name="contractCheckboxName"
              />
            </div>
          </div>
        </div>

        <!-- Acciones rápidas -->
        <div class="flex justify-between items-center pt-2">
          <div class="flex flex-wrap gap-2 items-center">
            <button
              @click="selectedSocios = filteredSocios.map((f) => f.id)"
              class="text-xs font-medium text-mulbin-600 hover:text-mulbin-700"
              v-if="
                filteredSocios.length > 0 &&
                selectedSocios.length < filteredSocios.length
              "
            >
              Seleccionar
              {{ searchQuery.trim() ? "filtrados" : "todos" }}
            </button>

            <!-- Botón para seleccionar por etiqueta exacta -->
            <button
              v-if="matchingTag"
              @click="selectByTag(matchingTag)"
              class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md transition-colors hover:opacity-80"
              :style="{
                backgroundColor: matchingTag.backgroundColor,
                color: matchingTag.color,
                border: `1px solid ${matchingTag.color}20`,
              }"
              :title="`Seleccionar todos los socios con la etiqueta '${matchingTag.nombre}'`"
              type="button"
            >
              <ion-icon name="pricetag-outline" class="mr-1 text-xs"></ion-icon>
              {{ matchingTag.nombre }}
            </button>

            <!-- Botón para seleccionar por ciudad exacta -->
            <button
              v-if="matchingCity"
              @click="selectByCity(matchingCity)"
              class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded-md border border-gray-300 transition-colors hover:bg-gray-200"
              :title="`Seleccionar todos los socios de '${matchingCity}'`"
              type="button"
            >
              <ion-icon name="location-outline" class="mr-1 text-xs"></ion-icon>
              {{ matchingCity }}
            </button>

            <button
              @click="selectedSocios = []"
              class="text-xs font-medium text-gray-500 hover:text-gray-700"
              v-if="selectedSocios.length > 0"
            >
              Limpiar selección
            </button>
          </div>

          <p v-if="selectedSocios.length > 0" class="text-xs text-gray-600">
            {{ selectedSocios.length }} de {{ availableSocios.length }} socios
            <span
              v-if="
                searchQuery.trim() &&
                filteredSocios.length < availableSocios.length
              "
            >
              ({{ filteredSocios.length }} mostrados)
            </span>
          </p>
        </div>
      </div>
    </div>

    <!-- Campos ocultos para compatibilidad con formularios tradicionales -->
    <input
      type="hidden"
      :name="publicFieldName"
      :value="isPublic ? 'Si' : 'No'"
    />
    <input
      v-if="isPublic"
      type="hidden"
      :name="selectedFieldName"
      :value="selectedSocios.join(',')"
    />
    <input
      v-if="isPublic"
      type="hidden"
      :name="contractFieldName"
      :value="selectedContracts.join(',')"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, watch } from "vue";
import axios from "axios";

interface Etiqueta {
  id: string;
  nombre: string;
  color: string;
  backgroundColor: string;
  descripcion?: string;
}

interface Socio {
  id: string;
  contrato: string;
  nombre: string;
  empresa: string;
  ubicacion: string;
  avatar: string;
  email?: string;
  telefono?: string;
  wa?: string;
  telegram?: string;
  tipo: "directo" | "indirecto" | "pendiente" | "porAutorizar";
  desde?: string;
  bolsas?: Record<string, { nombre: string; descripcion: string }>;
  etiquetas?: Etiqueta[];
  verified?: boolean;
}

export default defineComponent({
  name: "SelectorSocios",

  props: {
    // Control de visibilidad y comportamiento
    showAddButton: {
      type: Boolean,
      default: true,
    },
    // Valores iniciales
    initialIsPublic: {
      type: Boolean,
      default: true,
    },
    initialSelectedSocios: {
      type: Array as () => string[],
      default: () => [],
    },
    // Nombres de campos para formularios tradicionales
    publicFieldName: {
      type: String,
      default: "es_publico",
    },
    selectedFieldName: {
      type: String,
      default: "socios_seleccionados",
    },
    checkboxName: {
      type: String,
      default: "socio_ids[]",
    },
    // Nuevos campos para contratos
    contractFieldName: {
      type: String,
      default: "contratos_seleccionados",
    },
    contractCheckboxName: {
      type: String,
      default: "contrato_ids[]",
    },
    // Configuración de carga automática
    autoLoad: {
      type: Boolean,
      default: true,
    },
    // Datos externos (para casos donde no se puede cargar automáticamente)
    externalSocios: {
      type: Array as () => Socio[],
      default: () => [],
    },
    textosAudienciaPrivada: {
      type: Object,
      required: true,
    },
    textosAudienciaPublica: {
      type: Object,
      required: true,
    },
  },

  emits: [
    "update:isPublic",
    "update:selectedSocios",
    "add-socio",
    "selection-change",
  ],

  setup(props, { emit }) {
    const mulbinUrl = import.meta.env.VITE_MULBIN_URL;

    // Estados reactivos
    const isPublic = ref(props.initialIsPublic);
    const selectedSocios = ref<string[]>([...props.initialSelectedSocios]);
    const availableSocios = ref<Socio[]>([]);
    const searchQuery = ref("");
    const loading = ref(false);
    const error = ref<string | null>(null);

    // Computed para obtener los contratos seleccionados basándose en los socios seleccionados
    const selectedContracts = computed(() => {
      return selectedSocios.value.map((socioId) => {
        const socio = availableSocios.value.find((s) => s.id === socioId);
        return socio ? socio.contrato : socioId; // Fallback al ID si no se encuentra el contrato
      });
    });

    // Función para normalizar texto (eliminar acentos)
    const normalizeText = (text: string): string => {
      return text
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .toLowerCase();
    };

    // Socios filtrados por búsqueda
    const filteredSocios = computed(() => {
      const normalizedQuery = normalizeText(searchQuery.value);

      return availableSocios.value.filter((socio) => {
        const normalizedNombre = normalizeText(socio.nombre);
        const normalizedEmpresa = normalizeText(socio.empresa);
        const normalizedUbicacion = normalizeText(socio.ubicacion);

        let matchesEtiquetas = false;
        if (socio.etiquetas && socio.etiquetas.length > 0) {
          matchesEtiquetas = socio.etiquetas.some((etiqueta) =>
            normalizeText(etiqueta.nombre).includes(normalizedQuery)
          );
        }

        return (
          normalizedNombre.includes(normalizedQuery) ||
          normalizedEmpresa.includes(normalizedQuery) ||
          normalizedUbicacion.includes(normalizedQuery) ||
          matchesEtiquetas
        );
      });
    });

    // Detectar si la búsqueda coincide exactamente con alguna etiqueta
    const matchingTag = computed(() => {
      if (!searchQuery.value.trim()) return null;

      const normalizedQuery = normalizeText(searchQuery.value.trim());

      // Buscar en todas las etiquetas de todos los socios
      const allTags = new Map<string, Etiqueta>();
      availableSocios.value.forEach((socio) => {
        if (socio.etiquetas) {
          socio.etiquetas.forEach((etiqueta) => {
            if (!allTags.has(etiqueta.id)) {
              allTags.set(etiqueta.id, etiqueta);
            }
          });
        }
      });

      // Buscar coincidencia exacta
      for (const etiqueta of allTags.values()) {
        if (normalizeText(etiqueta.nombre) === normalizedQuery) {
          return etiqueta;
        }
      }

      return null;
    });

    // Detectar si la búsqueda coincide exactamente con alguna ciudad
    const matchingCity = computed(() => {
      if (!searchQuery.value.trim()) return null;

      const normalizedQuery = normalizeText(searchQuery.value.trim());

      // Buscar en todas las ciudades de todos los socios
      const allCities = new Set<string>();
      availableSocios.value.forEach((socio) => {
        if (socio.ubicacion && socio.ubicacion.trim()) {
          allCities.add(socio.ubicacion.trim());
        }
      });

      // Buscar coincidencia exacta
      for (const ciudad of allCities) {
        if (normalizeText(ciudad) === normalizedQuery) {
          return ciudad;
        }
      }

      return null;
    });

    // Función para cargar socios desde la API
    const fetchSocios = async () => {
      if (!props.autoLoad) return;

      loading.value = true;
      error.value = null;

      try {
        const response = await axios.get("/msi-v5/owner/socios");

        if (response.data && response.data.statusCode === 200) {
          // Procesar tags del backend
          const tagsBackend = response.data.data.tags || [];
          const etiquetasDisponibles = tagsBackend.map((tag: any) => {
            let color = "#7C3AED";
            let backgroundColor = "#EDE9FE";
            if (tag.style) {
              const bgMatch = tag.style.match(/background-color:\s*([^;]+);?/);
              const borderMatch = tag.style.match(
                /border:\s*1px solid ([^;]+);?/
              );
              if (bgMatch) backgroundColor = bgMatch[1].trim();
              if (borderMatch) color = borderMatch[1].trim();
            }
            return {
              id: tag.id.toString(),
              nombre: tag.tag,
              color,
              backgroundColor,
              descripcion: tag.description || undefined,
            };
          });

          // Procesar socios y asignar objetos de etiqueta
          availableSocios.value = (response.data.data.socios || [])
            .filter((socio: any) => socio.tipo === "directo") // Solo socios directos
            .map((socio: any) => {
              const etiquetas = (socio.tags || [])
                .map((tagId: any) =>
                  etiquetasDisponibles.find(
                    (e: Etiqueta) => e.id == tagId.toString()
                  )
                )
                .filter(Boolean);
              return {
                ...socio,
                etiquetas,
                verified: socio.verified || false,
              };
            });
        } else {
          error.value = "Error al cargar los datos de socios";
        }
      } catch (err: any) {
        console.error("Error fetching socios:", err);
        error.value =
          "No se pudieron cargar los socios. Por favor intenta nuevamente.";
      } finally {
        loading.value = false;
      }
    };

    // Función para manejar cambio en el toggle público/privado
    const onPublicToggle = () => {
      emit("update:isPublic", isPublic.value);
      emit("selection-change", {
        isPublic: isPublic.value,
        selectedSocios: selectedSocios.value,
      });
    };

    // Función para seleccionar todos los socios que tengan exactamente una etiqueta específica
    const selectByTag = (etiqueta: Etiqueta) => {
      const sociosConEtiqueta = availableSocios.value
        .filter(
          (socio) =>
            socio.etiquetas &&
            socio.etiquetas.some((tag) => tag.id === etiqueta.id)
        )
        .map((socio) => socio.id);

      selectedSocios.value = [...sociosConEtiqueta];
    };

    // Función para seleccionar todos los socios de una ciudad específica
    const selectByCity = (ciudad: string) => {
      const sociosDeCiudad = availableSocios.value
        .filter((socio) => socio.ubicacion && socio.ubicacion.trim() === ciudad)
        .map((socio) => socio.id);

      selectedSocios.value = [...sociosDeCiudad];
    };

    // Función para alternar la selección de un socio específico
    const toggleSocioSelection = (socioId: string) => {
      const index = selectedSocios.value.indexOf(socioId);
      if (index > -1) {
        // Si está seleccionado, lo quitamos
        selectedSocios.value.splice(index, 1);
      } else {
        // Si no está seleccionado, lo agregamos
        selectedSocios.value.push(socioId);
      }
    };

    // Watchers para emitir cambios
    watch(
      selectedSocios,
      (newValue) => {
        emit("update:selectedSocios", newValue);
        emit("selection-change", {
          isPublic: isPublic.value,
          selectedSocios: newValue,
        });
      },
      { deep: true }
    );

    // Watcher para datos externos
    watch(
      () => props.externalSocios,
      (newSocios) => {
        if (newSocios.length > 0) {
          availableSocios.value = newSocios;
        }
      },
      { immediate: true }
    );

    // Métodos públicos para control externo
    const setSocios = (socios: Socio[]) => {
      availableSocios.value = socios;
    };

    const setSelection = (socioIds: string[]) => {
      selectedSocios.value = [...socioIds];
    };

    const setPublic = (isPublicValue: boolean) => {
      isPublic.value = isPublicValue;
    };

    const getSelection = () => ({
      isPublic: isPublic.value,
      selectedSocios: selectedSocios.value,
    });

    // Cargar socios al montar si está habilitado
    onMounted(() => {
      if (props.autoLoad && props.externalSocios.length === 0) {
        fetchSocios();
      }
    });

    return {
      // Estados
      isPublic,
      selectedSocios,
      availableSocios,
      searchQuery,
      loading,
      error,
      mulbinUrl,

      // Computadas
      filteredSocios,
      matchingTag,
      matchingCity,
      selectedContracts,
      textosAudienciaPrivada: props.textosAudienciaPrivada || {
        titulo: "Publicar solo a socios determinados",
        descripcion:
          "Solo tus socios seleccionados verán este inmueble en su muro inmobiliario",
      },
      textosAudienciaPublica: props.textosAudienciaPublica || {
        titulo: "Publicar a todos los socios",
        descripcion: "Publicación en el Feed de todos tus socios",
      },

      // Métodos
      onPublicToggle,
      fetchSocios,
      selectByTag,
      selectByCity,
      toggleSocioSelection,

      // Métodos públicos
      setSocios,
      setSelection,
      setPublic,
      getSelection,
    };
  },
});
</script>

<style scoped>
/* Estilos para compatibilidad con sistemas legacy */
.selector-socios-container input[type="hidden"] {
  display: none;
}
</style>
