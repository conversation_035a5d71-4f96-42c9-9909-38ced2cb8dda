import { createApp } from "vue";
import Alpine from "alpinejs";
import mask from "@alpinejs/mask";
import InmuebleBolsaInmobiliaria from "./InmuebleBolsaInmobiliaria.vue";
import ddpService from "../../services/ddpService";

// Registrar el plugin x-mask
Alpine.plugin(mask);

// Inicializar Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Inicializar Vue cuando el DOM esté cargado
document.addEventListener("DOMContentLoaded", () => {
  // Cargar Ionicons si no están ya cargados
  if (!document.querySelector('script[src*="ionicons"]')) {
    const moduleScript = document.createElement("script");
    moduleScript.type = "module";
    moduleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js";
    document.body.appendChild(moduleScript);

    const noModuleScript = document.createElement("script");
    noModuleScript.noModule = true;
    noModuleScript.src =
      "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js";
    document.body.appendChild(noModuleScript);
  }

  const targetElement = document.querySelector(
    "[data-inmueble-bolsa-inmobiliaria]"
  );

  if (targetElement) {
    const textosAudienciaPrivada = {
      titulo:
        targetElement.getAttribute("data-titulo-audiencia-privada") ||
        "Publicar solo a socios determinados",
      descripcion:
        targetElement.getAttribute("data-descripcion-audiencia-privada") ||
        "Tu publicación solo aparecerá en el Feed de los socios que selecciones",
    };
    const textosAudienciaPublica = {
      titulo:
        targetElement.getAttribute("data-titulo-audiencia-publica") ||
        "Publicar a todos los socios",
      descripcion:
        targetElement.getAttribute("data-descripcion-audiencia-publica") ||
        "Tu publicación aparecerá en el Feed de todos tus socios",
    };
    // 🆕 CONFIGURACIÓN DE MULTIBOLSA DESDE BASE DE DATOS
    const initialActivarMultibolsa =
      targetElement.getAttribute("data-activar-multibolsa") === "true";
    const initialIsPublic =
      targetElement.getAttribute("data-is-public") === "true";
    const initialSolicitarPublicacionWebsites =
      targetElement.getAttribute("data-solicitar-publicacion-websites") ===
      "true";

    // 🆕 CONTROL DE DUPLICACIÓN DE PUBLICACIONES
    const yaPublicadoEnFeed =
      targetElement.getAttribute("data-ya-publicado-feed") === "true";
    const fechaPublicacionFeed =
      targetElement.getAttribute("data-fecha-publicacion-feed") || null;
    const fechaPublicacionFormateada =
      targetElement.getAttribute("data-fecha-publicacion-formateada") || null;

    // Parsear socios seleccionados desde string
    let initialSelectedSocios = [];
    const sociosString = targetElement.getAttribute("data-selected-socios");
    if (sociosString && sociosString.trim() !== "") {
      try {
        // Si es JSON array
        if (sociosString.startsWith("[") && sociosString.endsWith("]")) {
          initialSelectedSocios = JSON.parse(sociosString);
        }
        // Si es comma-separated string
        else {
          initialSelectedSocios = sociosString
            .split(",")
            .map((id) => id.trim())
            .filter((id) => id !== "");
        }
      } catch (e) {
        console.warn("Error parsing selected socios:", e);
        initialSelectedSocios = [];
      }
    }

    // Crear y montar la aplicación Vue - mucho más simple
    const app = createApp(InmuebleBolsaInmobiliaria, {
      textosAudienciaPrivada: textosAudienciaPrivada,
      textosAudienciaPublica: textosAudienciaPublica,
      // 🆕 VALORES INICIALES DESDE BASE DE DATOS
      initialActivarMultibolsa: initialActivarMultibolsa,
      initialIsPublic: initialIsPublic,
      initialSelectedSocios: initialSelectedSocios,
      initialSolicitarPublicacionWebsites: initialSolicitarPublicacionWebsites,
      // 🆕 CONTROL DE DUPLICACIÓN DE PUBLICACIONES
      yaPublicadoEnFeed: yaPublicadoEnFeed,
      fechaPublicacionFeed: fechaPublicacionFeed,
      fechaPublicacionFormateada: fechaPublicacionFormateada,
    });

    // Proporcionar el servicio DDP a toda la aplicación
    app.provide("ddpService", ddpService);

    app.mount(targetElement);

    console.log("✅ InmuebleBolsaInmobiliaria montado correctamente");
  } else {
    console.error(
      "❌ No se encontró el elemento [data-inmueble-bolsa-inmobiliaria]"
    );
  }
});
