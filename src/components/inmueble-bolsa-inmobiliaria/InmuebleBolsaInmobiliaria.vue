<template>
  <div class="my-5">
    <!-- Solo mostrar el componente si no fue publicado previamente O si está en modo republicación -->
    <div
      v-if="!yaPublicado || enModoRepublicacion"
      class="p-4 bg-white rounded-lg border border-gray-200 shadow-sm"
    >
      <div class="flex items-start space-x-3">
        <div class="flex items-center h-5">
          <input
            type="checkbox"
            id="activar-multibolsa"
            v-model="activarMultibolsa"
            class="hidden"
          /><label for="activar-multibolsa"></label>
        </div>
        <div class="flex-1">
          <label for="activar-multibolsa" class="cursor-pointer">
            <div class="flex items-center space-x-2">
              <h3 class="text-sm font-medium text-gray-900">
                Publicar en el Feed
              </h3>
              <a
                href="javascript:markdownModal.show('/md/bolsa_inmobiliaria.md');"
                class="flex justify-center items-center w-5 h-5 rounded-full bg-mulbin-100"
              >
                <ion-icon
                  name="information-outline"
                  class="text-xs text-mulbin-600"
                  title="Información sobre Multibolsa Inmobiliaria"
                ></ion-icon>
              </a>
            </div>
            <p class="mt-1 text-xs text-gray-500">
              Publica el inmueble en el Feed de la Multibolsa Inmobiliaria
            </p>
          </label>
        </div>
      </div>

      <!-- Información adicional cuando está activado -->
      <div v-if="activarMultibolsa" class="hidden mt-3 ml-7">
        <div
          class="flex items-start p-3 space-x-2 bg-blue-50 rounded-lg border border-blue-200"
        >
          <ion-icon
            name="checkmark-circle"
            class="flex-shrink-0 mt-0.5 text-lg text-blue-600"
          ></ion-icon>
          <div class="flex-1">
            <p class="text-sm font-medium text-blue-800">
              Multibolsa Inmobiliaria activada
            </p>
            <p class="mt-1 text-xs text-blue-600">
              Tu inmueble será compartido según la configuración de audiencia
              que selecciones a continuación
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Selector de socios - Solo visible cuando está activada la multibolsa -->
    <div v-if="activarMultibolsa" class="mt-4">
      <SelectorSociosWrapper
        v-if="!loading"
        :is-public="isPublic"
        :selected-socios="selectedSocios"
        :available-friends="availableFriends"
        :textos-audiencia-privada="textosAudienciaPrivada"
        :textos-audiencia-publica="textosAudienciaPublica"
        @update:isPublic="isPublic = $event"
        @update:selectedSocios="selectedSocios = $event"
        @add-socio="handleAddSocio"
      />

      <!-- Loading state minimalista -->
      <div v-else class="p-4 text-center text-gray-500">
        <div class="flex justify-center items-center space-x-2">
          <ion-icon
            name="reload-outline"
            class="animate-spin text-mulbin-600"
          ></ion-icon>
          <span class="text-sm">Cargando configuración...</span>
        </div>
      </div>
    </div>

    <!-- Control de solicitud de publicación en websites - Solo visible cuando está activada la multibolsa -->
    <div v-if="activarMultibolsa" class="mt-4">
      <div class="p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
        <div class="flex items-start space-x-3">
          <div class="flex items-center h-5">
            <input
              type="checkbox"
              id="solicitar-publicacion-websites"
              v-model="solicitarPublicacionWebsites"
              class="hidden"
            />
            <label for="solicitar-publicacion-websites"></label>
          </div>
          <div class="flex-1">
            <label for="solicitar-publicacion-websites" class="cursor-pointer">
              <div class="flex items-center space-x-2">
                <h3 class="text-sm font-medium text-gray-900">
                  Solicitar publicación en websites de mis socios
                </h3>
                <a
                  href="javascript:markdownModal.show('/md/publicacion_websites_socios.md');"
                  class="flex justify-center items-center w-5 h-5 rounded-full bg-mulbin-100"
                >
                  <ion-icon
                    name="information-outline"
                    class="text-xs text-mulbin-600"
                    title="Información sobre publicación en websites"
                  ></ion-icon>
                </a>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                <span v-if="isPublic">
                  Envía una solicitud a todos los socios de tu red para que
                  publiquen este inmueble en sus websites
                </span>
                <span v-else>
                  Envía una solicitud solo a los socios seleccionados para que
                  publiquen este inmueble en sus websites
                </span>
              </p>
            </label>
          </div>
        </div>

        <!-- Información adicional cuando está activado -->
        <div v-if="solicitarPublicacionWebsites" class="mt-3 ml-7">
          <div
            class="flex items-start p-3 space-x-2 bg-green-50 rounded-lg border border-green-200"
          >
            <ion-icon
              name="globe-outline"
              class="flex-shrink-0 mt-0.5 text-lg text-green-600"
            ></ion-icon>
            <div class="flex-1">
              <p class="text-sm font-medium text-green-800">
                Solicitud de publicación en websites activada
              </p>
              <p class="mt-1 text-xs text-green-600">
                <span v-if="isPublic">
                  Se enviará una solicitud a todos los socios de tu red
                </span>
                <span v-else-if="selectedSocios.length > 0">
                  Se enviará una solicitud a {{ selectedSocios.length }} socio{{
                    selectedSocios.length !== 1 ? "s" : ""
                  }}
                  seleccionado{{ selectedSocios.length !== 1 ? "s" : "" }}
                </span>
                <span v-else>
                  Selecciona al menos un socio para enviar la solicitud
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Campos ocultos para compatibilidad con formularios tradicionales -->
    <!-- NOTA: Estos campos siempre deben estar presentes independientemente del estado del componente -->
    <input
      type="hidden"
      name="usar_multibolsa"
      :value="activarMultibolsa ? 'Si' : 'No'"
    />
    <input
      type="hidden"
      name="publicacion_publica"
      :value="activarMultibolsa ? (isPublic ? 'Si' : 'No') : 'No'"
    />
    <input
      type="hidden"
      name="socios_seleccionados"
      :value="activarMultibolsa && !isPublic ? selectedSocios.join(',') : ''"
    />
    <input type="hidden" name="lista_socios" :value="listaSociosIds" />
    <input
      type="hidden"
      name="solicitar_publicacion_websites"
      :value="activarMultibolsa && solicitarPublicacionWebsites ? 'Si' : 'No'"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch, computed } from "vue";
import SelectorSociosWrapper from "../selector-socios-wrapper/SelectorSociosWrapper.vue";
import type { Socio } from "../../types/inmobiliario";
import axios from "axios";

export default defineComponent({
  name: "InmuebleBolsaInmobiliaria",

  components: {
    SelectorSociosWrapper,
  },

  props: {
    textosAudienciaPrivada: {
      type: Object,
      default: () => ({}),
    },
    textosAudienciaPublica: {
      type: Object,
      default: () => ({}),
    },
    // Valores iniciales opcionales
    initialActivarMultibolsa: {
      type: Boolean,
      default: false, // Por defecto desactivado
    },
    initialIsPublic: {
      type: Boolean,
      default: false, // Por defecto privado, muestra listado de socios para seleccionar destinatario(s)
    },
    initialSelectedSocios: {
      type: Array as () => string[],
      default: () => [],
    },
    initialSolicitarPublicacionWebsites: {
      type: Boolean,
      default: false, // Por defecto desactivado
    },
    // 🆕 Props para control de duplicación de publicaciones
    yaPublicadoEnFeed: {
      type: Boolean,
      default: false,
    },
    fechaPublicacionFeed: {
      type: String,
      default: null,
    },
    fechaPublicacionFormateada: {
      type: String,
      default: null,
    },
  },

  setup(props) {
    // Estados reactivos
    const loading = ref(false); // Cambiamos a false inicialmente

    // 🆕 Si ya fue publicado, inicialmente desactivar la multibolsa hasta que el usuario decida re-publicar
    const activarMultibolsa = ref(
      props.yaPublicadoEnFeed ? false : props.initialActivarMultibolsa
    );

    const isPublic = ref(props.initialIsPublic);
    const availableFriends = ref<Socio[]>([]);
    const selectedSocios = ref<string[]>([...props.initialSelectedSocios]);
    const connectionError = ref(false);
    const sociosCargados = ref(false); // Para evitar cargar múltiples veces
    const solicitarPublicacionWebsites = ref(
      props.initialSolicitarPublicacionWebsites
    );

    // 🆕 Estados para control de duplicación de publicaciones
    const yaPublicado = ref(props.yaPublicadoEnFeed);
    const enModoRepublicacion = ref(false);

    // Computed property para obtener la lista de IDs de todos los socios
    const listaSociosIds = computed(() => {
      return availableFriends.value.map((socio) => socio._id).join(",");
    });

    // Cargar socios - Solo cuando se activa la multibolsa
    const loadUserFriends = async () => {
      if (sociosCargados.value) {
        return; // Ya están cargados
      }

      try {
        loading.value = true;

        // Usar API PHP en lugar de Meteor
        const response = await axios.get("/msi-v5/owner/socios");

        console.log(response.data);

        if (response.data && response.data.statusCode === 200) {
          // Procesar tags del backend igual que en MisSocios.vue
          const tagsBackend = response.data.data.tags || [];
          const etiquetasDisponibles = tagsBackend.map((tag: any) => {
            let color = "#7C3AED";
            let backgroundColor = "#EDE9FE";
            if (tag.style) {
              const bgMatch = tag.style.match(/background-color:\s*([^;]+);?/);
              const borderMatch = tag.style.match(
                /border:\s*1px solid ([^;]+);?/
              );
              if (bgMatch) backgroundColor = bgMatch[1].trim();
              if (borderMatch) color = borderMatch[1].trim();
            }
            return {
              id: tag.id.toString(),
              nombre: tag.tag,
              color,
              backgroundColor,
              descripcion: tag.description || undefined,
            };
          });

          // Procesar socios y mapear a la estructura esperada por SelectorSociosWrapper
          availableFriends.value = (response.data.data.socios || [])
            .filter((socio: any) => socio.tipo === "directo") // Solo socios directos
            .map((socio: any) => {
              // Procesar etiquetas del socio
              const etiquetas = (socio.tags || [])
                .map((tagId: any) =>
                  etiquetasDisponibles.find(
                    (e: any) => e.id == tagId.toString()
                  )
                )
                .filter(Boolean);

              return {
                // Mapear estructura de API PHP a estructura esperada por SelectorSociosWrapper
                _id: socio.id,
                id: socio.id,
                contrato: socio.contrato,
                name: socio.nombre,
                company: socio.empresa,
                location: socio.ubicacion,
                avatar: socio.avatar,
                email: socio.email,
                phone: socio.telefono,
                wa: socio.wa,
                telegram: socio.telegram,
                verified: socio.verified || false,
                etiquetas: etiquetas,
              };
            });

          sociosCargados.value = true;

          console.log(
            "✅ Socios cargados para bolsa inmobiliaria:",
            availableFriends.value.length
          );
        } else {
          availableFriends.value = [];
        }
      } catch (error) {
        console.error(
          "❌ Error al cargar socios para bolsa inmobiliaria:",
          error
        );
        connectionError.value = true;
        availableFriends.value = [];
      } finally {
        loading.value = false;
      }
    };

    // Watcher para cargar socios cuando se activa la multibolsa
    watch(activarMultibolsa, async (newValue) => {
      if (newValue && !sociosCargados.value) {
        console.log("🔄 Multibolsa activada, cargando socios...");
        await loadUserFriends();
      }

      // Si se desactiva la multibolsa, resetear la selección
      if (!newValue) {
        isPublic.value = props.initialIsPublic;
        selectedSocios.value = [...props.initialSelectedSocios];
        solicitarPublicacionWebsites.value =
          props.initialSolicitarPublicacionWebsites;
        console.log("❌ Multibolsa desactivada, configuración reseteada");
      }
    });

    // Manejar adición de nuevo socio
    const handleAddSocio = () => {
      console.log(
        "🔄 Solicitud para agregar nuevo socio desde bolsa inmobiliaria"
      );

      // Por ahora, simplemente redirigimos a la página de socios
      if (window.location.pathname.includes("/inmueble/")) {
        // Estamos en contexto de inmueble, podríamos abrir en nueva pestaña
        window.open("/msi-v5/owner/socios", "_blank");
      } else {
        // Redirigir normalmente
        window.location.href = "/msi-v5/owner/socios";
      }
    };

    // 🆕 Función para habilitar modo de republicación
    const habilitarModoRepublicacion = () => {
      enModoRepublicacion.value = true;
      activarMultibolsa.value = props.initialActivarMultibolsa;
      console.log("🔄 Modo republicación habilitado desde Vue");

      // Sincronizar con el estado global
      (window as any).estadoRepublicacion = true;
    };

    // Inicializar al montar
    onMounted(async () => {
      // 🆕 Registrar función para habilitar republicación desde JavaScript
      (window as any).habilitarModoRepublicacionVue =
        habilitarModoRepublicacion;

      // Verificar si el componente debe estar activo basándose en las comisiones
      if (typeof (window as any).shouldShowMultibolsa === "function") {
        const shouldBeActive = (window as any).shouldShowMultibolsa();

        // Si ya fue publicado, no auto-activar a menos que esté en modo republicación
        if (shouldBeActive && !activarMultibolsa.value && !yaPublicado.value) {
          console.log(
            "🔄 Auto-activando Multibolsa Inmobiliaria basándose en comisiones"
          );
          activarMultibolsa.value = true;
        }
      }

      // Registrar callback para cambios en comisiones
      (window as any).notifyMultibolsaChange = () => {
        console.log("🔄 Notificación de cambio en comisiones recibida");
        if (typeof (window as any).shouldShowMultibolsa === "function") {
          const shouldBeActive = (window as any).shouldShowMultibolsa();
          console.log(
            `📊 Estado de comisiones: ${shouldBeActive ? "Activo" : "Inactivo"}`
          );

          // Solo activar automáticamente si no fue publicado previamente o está en modo republicación
          if (
            shouldBeActive &&
            !activarMultibolsa.value &&
            (!yaPublicado.value || enModoRepublicacion.value)
          ) {
            console.log("✅ Activando Multibolsa Inmobiliaria");
            activarMultibolsa.value = true;
          } else if (
            !shouldBeActive &&
            activarMultibolsa.value &&
            !enModoRepublicacion.value
          ) {
            console.log("❌ Desactivando Multibolsa Inmobiliaria");
            activarMultibolsa.value = false;
          }
        }
      };

      // Solo cargar socios si ya está activada la multibolsa
      if (activarMultibolsa.value) {
        await loadUserFriends();
      }
    });

    return {
      // Estados
      loading,
      activarMultibolsa,
      isPublic,
      availableFriends,
      selectedSocios,
      connectionError,
      solicitarPublicacionWebsites,
      listaSociosIds,
      // 🆕 Estados para control de duplicación
      yaPublicado,
      enModoRepublicacion,

      // Métodos
      handleAddSocio,
      loadUserFriends,
      habilitarModoRepublicacion,
    };
  },
});
</script>

<style scoped>
/* Animación suave para el aparecer/desaparecer del selector */
.v-enter-active,
.v-leave-active {
  transition: all 0.3s ease;
}
.v-enter-from,
.v-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
