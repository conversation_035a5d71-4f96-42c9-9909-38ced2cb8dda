# 🏠 InmuebleBolsaInmobiliaria - Documentación Técnica

> Componente de control para publicación en la Multibolsa Inmobiliaria con gestión de socios

## 📋 Tabla de Contenidos

- [🎯 Descripción General](#-descripción-general)
- [🏗️ Arquitectura del Componente](#️-arquitectura-del-componente)
- [🧩 Props y Estados](#-props-y-estados)
- [🔧 Funcionalidades Principales](#-funcionalidades-principales)
- [🎨 Interfaz de Usuario](#-interfaz-de-usuario)
- [📡 Integración con Backend](#-integración-con-backend)
- [🔄 Flujo de Activación](#-flujo-de-activación)
- [💡 Ejemplos de Uso](#-ejemplos-de-uso)

---

## 🎯 Descripción General

### ¿Qué es InmuebleBolsaInmobiliaria?

El **InmuebleBolsaInmobiliaria** es un componente Vue.js que controla la publicación de inmuebles en la **Multibolsa Inmobiliaria**. Permite a los usuarios activar/desactivar la publicación colaborativa y configurar la audiencia (pública o privada) con selección específica de socios.

### Características Principales

- ✅ **Control de activación** de la Multibolsa Inmobiliaria
- ✅ **Integración automática** con SelectorSociosWrapper
- ✅ **Carga lazy** de socios solo cuando se activa
- ✅ **Auto-activación** basada en configuración de comisiones
- ✅ **Campos de formulario** compatibles con backend
- ✅ **Estados de loading** y manejo de errores

### Contexto de Uso

```mermaid
graph TD
    A[Formulario de Inmueble] --> B[InmuebleBolsaInmobiliaria]
    B --> C{¿Activar Multibolsa?}
    C -->|Sí| D[SelectorSociosWrapper]
    C -->|No| E[Campos ocultos con valores por defecto]
    D --> F[Configuración de Audiencia]
    F --> G[Envío de Formulario]
    E --> G
```

---

## 🏗️ Arquitectura del Componente

### Estructura de Archivos

```
src/components/inmueble-bolsa-inmobiliaria/
├── InmuebleBolsaInmobiliaria.vue    # 🎯 Componente principal
├── index.js                         # 📦 Entry point y auto-inicialización
└── INMUEBLE-BOLSA-DOCS.md          # 📖 Esta documentación
```

### Dependencias

```typescript
import SelectorSociosWrapper from "../selector-socios-wrapper/SelectorSociosWrapper.vue";
import type { Socio } from "../../types/inmobiliario";
import axios from "axios";
```

### Flujo de Datos

```mermaid
sequenceDiagram
    participant U as Usuario
    participant C as InmuebleBolsaInmobiliaria
    participant S as SelectorSociosWrapper
    participant A as API Backend

    U->>C: Activa Multibolsa
    C->>A: GET /msi-v5/owner/socios
    A->>C: Datos de socios y etiquetas
    C->>S: Pasa datos adaptados
    S->>C: Eventos de cambio
    C->>U: Actualiza campos de formulario
```

---

## 🧩 Props y Estados

### Props del Componente

```typescript
interface Props {
  token: string; // Token de autenticación
  initialActivarMultibolsa: boolean; // Estado inicial de activación
  initialIsPublic: boolean; // Estado inicial público/privado
  initialSelectedSocios: string[]; // Socios preseleccionados
  initialSolicitarPublicacionWebsites: boolean; // Estado inicial solicitud websites
}
```

### Estados Reactivos

```typescript
// Estados principales
const loading = ref(false); // Estado de carga
const activarMultibolsa = ref(false); // Control de activación
const isPublic = ref(false); // Público/privado
const availableFriends = ref<Socio[]>([]); // Socios disponibles
const selectedSocios = ref<string[]>([]); // Socios seleccionados
const connectionError = ref(false); // Error de conexión
const sociosCargados = ref(false); // Flag de carga única
const solicitarPublicacionWebsites = ref(false); // Control solicitud websites
```

### Eventos Emitidos

```typescript
// No emite eventos directamente, maneja todo internamente
// Los cambios se reflejan en campos ocultos del formulario
```

---

## 🔧 Funcionalidades Principales

### 1. **Control de Activación**

```vue
<div class="flex items-center h-5">
  <input
    type="checkbox"
    id="activar-multibolsa"
    v-model="activarMultibolsa"
    class="hidden"
  />
  <label for="activar-multibolsa" class="cursor-pointer">
    <!-- UI del checkbox personalizado -->
  </label>
</div>
```

### 2. **Carga Lazy de Socios**

```typescript
// Watcher que carga socios solo cuando se activa
watch(activarMultibolsa, async (newValue) => {
  if (newValue && !sociosCargados.value) {
    console.log("🔄 Multibolsa activada, cargando socios...");
    await loadUserFriends();
  }

  // Reset al desactivar
  if (!newValue) {
    isPublic.value = props.initialIsPublic;
    selectedSocios.value = [...props.initialSelectedSocios];
    console.log("❌ Multibolsa desactivada, configuración reseteada");
  }
});
```

### 3. **Adaptación de Datos**

```typescript
// Mapeo de estructura API PHP a estructura esperada por SelectorSociosWrapper
availableFriends.value = (response.data.data.socios || [])
  .filter((socio: any) => socio.tipo === "directo")
  .map((socio: any) => {
    const etiquetas = (socio.tags || [])
      .map((tagId: any) =>
        etiquetasDisponibles.find((e) => e.id == tagId.toString())
      )
      .filter(Boolean);

    return {
      _id: socio.id,
      id: socio.id,
      contrato: socio.contrato,
      name: socio.nombre,
      company: socio.empresa,
      location: socio.ubicacion,
      avatar: socio.avatar,
      email: socio.email,
      phone: socio.telefono,
      wa: socio.wa,
      telegram: socio.telegram,
      verified: socio.verified || false,
      etiquetas: etiquetas,
    };
  });
```

### 4. **Control de Solicitud de Publicación en Websites**

```vue
<div v-if="activarMultibolsa" class="mt-4">
  <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
    <div class="flex items-start space-x-3">
      <input
        type="checkbox"
        id="solicitar-publicacion-websites"
        v-model="solicitarPublicacionWebsites"
        class="hidden"
      />
      <label for="solicitar-publicacion-websites">
        <!-- UI del checkbox personalizado -->
      </label>
    </div>
  </div>
</div>
```

**Características:**

- ✅ Solo visible cuando `activarMultibolsa` está activado
- ✅ Descripción dinámica basada en audiencia (pública/privada)
- ✅ Información contextual sobre cantidad de socios
- ✅ Integración con campos de formulario

### 5. **Auto-activación por Comisiones**

```typescript
// Verificar si debe activarse automáticamente
if (typeof (window as any).shouldShowMultibolsa === "function") {
  const shouldBeActive = (window as any).shouldShowMultibolsa();
  if (shouldBeActive && !activarMultibolsa.value) {
    console.log(
      "🔄 Auto-activando Multibolsa Inmobiliaria basándose en comisiones"
    );
    activarMultibolsa.value = true;
  }
}

// Callback para cambios en comisiones
(window as any).notifyMultibolsaChange = () => {
  console.log("🔄 Notificación de cambio en comisiones recibida");
  // Lógica de activación/desactivación automática
};
```

---

## 🎨 Interfaz de Usuario

### Estados Visuales

| Estado       | Descripción            | Clases CSS                                                   |
| ------------ | ---------------------- | ------------------------------------------------------------ |
| **Inactivo** | Multibolsa desactivada | `border-gray-200`                                            |
| **Activo**   | Multibolsa activada    | `border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50` |
| **Loading**  | Cargando socios        | `animate-spin text-mulbin-600`                               |
| **Error**    | Error de conexión      | `text-red-600`                                               |

### Componente Principal

```vue
<div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
  <div class="flex items-start space-x-3">
    <!-- Checkbox personalizado -->
    <div class="flex items-center h-5">
      <input type="checkbox" v-model="activarMultibolsa" class="hidden" />
      <label class="cursor-pointer">
        <!-- UI visual del checkbox -->
      </label>
    </div>

    <!-- Contenido descriptivo -->
    <div class="flex-1">
      <h3 class="text-sm font-medium text-gray-900">
        Publicar en la Multibolsa Inmobiliaria
      </h3>
      <p class="mt-1 text-xs text-gray-500">
        Comparte tu inmueble automáticamente con los socios registrados
      </p>
    </div>
  </div>
</div>
```

### Selector de Socios Condicional

```vue
<div v-if="activarMultibolsa" class="mt-4">
  <SelectorSociosWrapper
    v-if="!loading"
    :is-public="isPublic"
    :selected-socios="selectedSocios"
    :available-friends="availableFriends"
    @update:isPublic="isPublic = $event"
    @update:selectedSocios="selectedSocios = $event"
    @add-socio="handleAddSocio"
  />

  <!-- Estado de loading -->
  <div v-else class="p-4 text-center text-gray-500">
    <div class="flex items-center justify-center space-x-2">
      <ion-icon name="reload-outline" class="animate-spin text-mulbin-600"></ion-icon>
      <span class="text-sm">Cargando configuración...</span>
    </div>
  </div>
</div>
```

---

## 📡 Integración con Backend

### Endpoint de Socios

```http
GET /msi-v5/owner/socios
```

**Respuesta esperada:**

```json
{
  "statusCode": 200,
  "data": {
    "socios": [
      {
        "id": "123",
        "contrato": "ABC123",
        "nombre": "Juan Pérez",
        "empresa": "Inmobiliaria XYZ",
        "ubicacion": "Madrid, España",
        "avatar": "https://...",
        "verified": true,
        "tipo": "directo",
        "tags": ["1", "3"]
      }
    ],
    "tags": [
      {
        "id": 1,
        "tag": "VIP",
        "style": "background-color: #EDE9FE; border: 1px solid #7C3AED;"
      }
    ]
  }
}
```

### Campos de Formulario Generados

```html
<!-- Control principal -->
<input type="hidden" name="usar_multibolsa" value="Si|No" />

<!-- Configuración de audiencia -->
<input type="hidden" name="publicacion_publica" value="Si|No" />

<!-- Socios seleccionados (solo si es privado) -->
<input type="hidden" name="socios_seleccionados" value="123,456,789" />

<!-- Solicitud de publicación en websites -->
<input type="hidden" name="solicitar_publicacion_websites" value="Si|No" />
```

### Lógica de Campos

```typescript
// Campos reactivos basados en estado
const campoMultibolsa = computed(() => (activarMultibolsa.value ? "Si" : "No"));
const campoPublico = computed(() =>
  activarMultibolsa.value ? (isPublic.value ? "Si" : "No") : "No"
);
const campoSocios = computed(() =>
  activarMultibolsa.value && !isPublic.value
    ? selectedSocios.value.join(",")
    : ""
);
const campoSolicitarWebsites = computed(() =>
  activarMultibolsa.value && solicitarPublicacionWebsites.value ? "Si" : "No"
);
```

---

## 🔄 Flujo de Activación

### Activación Manual

```mermaid
graph TD
    A[Usuario hace clic en checkbox] --> B[activarMultibolsa = true]
    B --> C{¿Socios ya cargados?}
    C -->|No| D[loadUserFriends()]
    C -->|Sí| E[Mostrar SelectorSociosWrapper]
    D --> F[Procesar datos del backend]
    F --> G[Adaptar estructura de datos]
    G --> H[Mostrar SelectorSociosWrapper]
    E --> I[Usuario configura audiencia]
    H --> I
    I --> J[Actualizar campos ocultos]
```

### Activación Automática

```mermaid
graph TD
    A[Cambio en comisiones] --> B[shouldShowMultibolsa()]
    B --> C{¿Debe activarse?}
    C -->|Sí| D[activarMultibolsa = true]
    C -->|No| E[activarMultibolsa = false]
    D --> F[Cargar socios automáticamente]
    E --> G[Reset configuración]
```

### Desactivación

```mermaid
graph TD
    A[activarMultibolsa = false] --> B[Reset isPublic]
    B --> C[Reset selectedSocios]
    C --> D[Ocultar SelectorSociosWrapper]
    D --> E[Actualizar campos ocultos]
```

---

## 💡 Ejemplos de Uso

### Uso Básico en Formulario

```html
<!-- Container para auto-inicialización -->
<div
  data-inmueble-bolsa-inmobiliaria
  data-token="user-auth-token"
  data-initial-activar="false"
  data-initial-public="true"
  data-initial-solicitar-websites="false"
></div>

<!-- Script de inicialización -->
<script src="dist/assets/inmuebleBolsaInmobiliaria.js"></script>
```

### Uso Programático

```vue
<template>
  <InmuebleBolsaInmobiliaria
    :token="userToken"
    :initial-activar-multibolsa="false"
    :initial-is-public="true"
    :initial-selected-socios="[]"
    :initial-solicitar-publicacion-websites="false"
  />
</template>

<script>
import InmuebleBolsaInmobiliaria from "./InmuebleBolsaInmobiliaria.vue";

export default {
  components: { InmuebleBolsaInmobiliaria },
  data() {
    return {
      userToken: "eyJhbGciOiJIUzI1NiIs...",
    };
  },
};
</script>
```

### Integración con Sistema de Comisiones

```javascript
// Función global para determinar si mostrar multibolsa
window.shouldShowMultibolsa = function () {
  const comisionBolsa = document.querySelector(
    '[name="comision_bolsa"]'
  )?.value;
  const comisionBolsaPorcentaje = document.querySelector(
    '[name="comision_bolsa_porcentaje"]'
  )?.value;

  return (
    (comisionBolsa && comisionBolsa !== "0") ||
    (comisionBolsaPorcentaje && comisionBolsaPorcentaje !== "0")
  );
};

// Notificar cambios en comisiones
document.addEventListener("change", function (e) {
  if (e.target.name?.includes("comision_bolsa")) {
    if (typeof window.notifyMultibolsaChange === "function") {
      window.notifyMultibolsaChange();
    }
  }
});
```

### Manejo de Errores

```vue
<template>
  <div v-if="connectionError" class="p-4 text-center text-red-600">
    <ion-icon name="warning-outline" class="mb-2 text-2xl"></ion-icon>
    <p class="text-sm">Error al cargar la configuración de socios</p>
    <button @click="loadUserFriends" class="mt-2 text-xs underline">
      Reintentar
    </button>
  </div>
</template>
```

---

## 🔧 Desarrollo y Mantenimiento

### Scripts de Build

```bash
# Desarrollo
npm run build

# Producción
npm run prod

# Testing
npm run preview
```

### Testing del Componente

```javascript
// Verificar auto-inicialización
console.log(
  "InmuebleBolsaInmobiliaria loaded:",
  window.InmuebleBolsaInmobiliaria
);

// Verificar función de comisiones
console.log("shouldShowMultibolsa:", window.shouldShowMultibolsa?.());

// Simular cambio en comisiones
window.notifyMultibolsaChange?.();
```

### Debugging

```javascript
// Estado del componente
const component = document.querySelector(
  "[data-inmueble-bolsa-inmobiliaria]"
).__vueParentComponent;
console.log("Estado:", {
  activarMultibolsa: component.setupState.activarMultibolsa,
  isPublic: component.setupState.isPublic,
  selectedSocios: component.setupState.selectedSocios,
  availableFriends: component.setupState.availableFriends.length,
  solicitarPublicacionWebsites:
    component.setupState.solicitarPublicacionWebsites,
});

// Verificar campos de formulario
const form = document.querySelector("form");
console.log("Campos multibolsa:", {
  usar_multibolsa: form.querySelector('[name="usar_multibolsa"]')?.value,
  publicacion_publica: form.querySelector('[name="publicacion_publica"]')
    ?.value,
  socios_seleccionados: form.querySelector('[name="socios_seleccionados"]')
    ?.value,
  solicitar_publicacion_websites: form.querySelector(
    '[name="solicitar_publicacion_websites"]'
  )?.value,
});
```

---

## 📚 Referencias

- [README Principal](../../../README.md)
- [SelectorSocios Docs](../selector-socios-wrapper/SELECTOR-SOCIOS-DOCS.md)
- [Guía Multibolsa Inmobiliaria](../../../public/md/bolsa_inmobiliaria.md)
- [API Backend](../../../../Meteor/MulbinComponents/app/API-REST-External-Backends.md)
