<?php

setlocale(LC_ALL, "es_MX");

include_once("languages/esp.php");
include_once("funciones.php");
include_once("verifica_pago.php");
include_once("theme.php");
@mysql_select_db(DB_SI);
cm_valida_usuario('ADMIN');
cm_rec_permisos();

// Al llamar a esta función el programa solo podrá se ejecutado con el permiso adecuado
cm_permiso('p_hace_citas');

/*
    Clases a usar
*/
include_once("clases/databases.inc.php");
$databases = new databases();
include_once("clases/images.inc.php");
$images = new images();
/*
    / Clases a usar
*/

// Ej. $tipo_inmuebles[01][singular] vale "Casa"
$tipo_inmuebles = $databases->rec_tipo_inmuebles();

// Ej. $monedas[MXP][singular] vale "Peso mexicano"
$monedas = $databases->rec_monedas();

// Recupero el encabezado y lo despliego, también defino variables para socios AMPI
$plantilla = cm_extrae_plantilla_mto();
include_once("enc_mto.php");

// Recupero las sucursales y las pongo en el arreglo $sucursales[id]
$consulta1 = $sql->query("SELECT id, nombre FROM sucursales WHERE (contrato='$config[contrato]')");
$sucursales = array();
while ($sucursal = $consulta1->fetch_assoc()) {
    if ($sucursal['id'] > 0) {
        $sucursales[$sucursal['id']] = $sucursal['nombre'];
    }
}

// Listado de citas
if (trim($id) == '' && $nueva != 'Si' && !isset($clave) && !isset($que_hacer) && !isset($lugar_cita) && !isset($fecha_cita)) {

    include __DIR__ . '/oficina/citas/listado_citas.php';

} //

// En caso de ver citas para un determinado inmueble
elseif (isset($clave)) {

    include __DIR__ . '/oficina/citas/citas_de_un_inmueble.php';

} //

// En caso de presentar una pregunta para responder
elseif ((isset($id) || isset($ids)) && !isset($lugar_cita) && !isset($fecha_cita) && $que_hacer != 'borrar' && $que_hacer != 'mail' && $que_hacer != 'cancelar') {

    include __DIR__ . '/oficina/citas/cita_para_responder.php';

} //

// En caso de haber confirmado la cita o haber solicitado cambio al cliente en la cita se procede a confirmarla en BD y por e-mail
elseif (trim($fecha_cita) != '' && $nueva != 'Si' && $paso != 7 && $paso != 'cambiar') {

    include __DIR__ . '/oficina/citas/cita_proceso_01.php';

} //

// En caso de responder por medio del mismo sistema
elseif ($que_hacer == 'mail') {

    include __DIR__ . '/oficina/citas/cita_email.php';

} //

// En caso de borrar la pregunta sin responder
elseif ($que_hacer == 'borrar') {

    include __DIR__ . '/oficina/citas/borrar_cita.php';

} //

// En caso de registrar una nueva cita en el sistema
elseif ($nueva == 'Si') {

    include __DIR__ . '/oficina/citas/nueva_cita.php';

} //

// En caso de registrar una nueva cita en el sistema
elseif ($paso == 7) {

    include __DIR__ . '/oficina/citas/registrar_cita.php';

} //

// En caso de registrar una nueva cita en el sistema
elseif ($paso == 'cambiar') {

    include __DIR__ . '/oficina/citas/cambiar_cita.php';

} //

// En caso de registrar una nueva cita en el sistema
elseif ($que_hacer == 'cancelar') {

    include __DIR__ . '/oficina/citas/cancelar_cita.php';

}

// Desconectamos de la base de datos
$sql->close();


// Despliego el pie de página y termino la ejecución del programa
echo $mustache->render($plantilla['pie'], $render);
exit;
