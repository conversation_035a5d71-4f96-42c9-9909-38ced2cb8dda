<?php

$clave_sistema = (int) $_POST['clave_sistema'];

// Recupero el token desde la DB
$token = $sql->row(
    "SELECT token FROM api_tokens 
    WHERE contrato_id = {$config['contrato']} AND name = 'SI.v4:ControlPanel' AND 
        (expires_at >= NOW() OR expires_at IS NULL) 
    LIMIT 1"
);
if (!$token) {
    $token = array(
        'token' => 'tk_' . md5(uniqid(rand().time(), true))
    );
    $sql->insert("api_tokens", array(
        "contrato_id" => $config['contrato'],
        "name" => "SI.v4:ControlPanel",
        "token" => $token['token']
    ));
}

// Usamos la clase Curl para hacer la petición a la API
try {
    require_once __DIR__ . '/clases/Curl.php';
    $msi_v5_service = getenv('PROXY_SERVICE_MSI_V5');
    $curl = new Curl();
    $curl->setUrl('http://' . $msi_v5_service . '/owner/inmuebles/' . $clave_sistema);
    $curl->setHeaders(array(
        'Authorization' => 'Bearer ' . $token['token'],
    ));
    $response = json_decode($curl->get()->getResponse());

    if ($response->statusCode == 200) {
        $apiInmueble = $response->data;
    } else {
        throw new \Exception('Error al obtener la propiedad desde la API');
    }
} catch (\Exception $e) {
    die($e->getMessage());
}

// Verifico que la propiedad exista, si no es así termino la ejecución del programa
$consulta1 = $sql->query(sprintf(
    "SELECT claveprop
        FROM propiedades 
        WHERE clave_sistema='%s' AND contrato='%s'",
    $clave_sistema,
    $config['contrato']
));
if (!$consulta1->num_rows) {
    echo 'Ha ocurrido un error, el inmueble no existe an la base de datos.';

    // Desconectamos de la base de datos
    $sql->close();

    // Despliego el pie de página y termino la ejecución del programa
    echo $mustache->render($plantilla['pie'], $render);
    exit;
}
$propiedad = $consulta1->fetch_assoc();

// Integración con Meteor movida a inmueble-paso-7.php (Multibolsa)
$publicacionExitosaEnFeed = false; // Variable para controlar si se registra fecha de publicación

//////////////
// Trato el asunto del propietario
if ($i_propietario == -1) {
    $i_propietario = 0;
}

// Al cumplirse lo siguiente se pone el nuevo propietario en la base de datos
if ($i_propietario == 0 && (trim($c_nombre) != '' || trim($c_apellidos) != '' || trim($c_telefono) != '' || trim($c_email) != '')) {
    $query = sprintf(
        "SELECT id FROM contactos WHERE contrato='%s' AND nombre='%s' AND apellidos='%s' AND telefono='%s' AND email='%s'",
        $config['contrato'],
        $c_nombre,
        $c_apellidos,
        $c_telefono,
        $c_email
    );
    $consulta1 = $sql->query($query);
    if ($consulta1->num_rows == 0) {
        $c_sexo = empty($c_sexo) ? 'hombre' : $c_sexo;
        $query = sprintf(
            "INSERT INTO contactos (contrato, nombre, apellidos, sexo, telefono, celular, fax, nextel_tel, nextel_radio, email, domicilio, ciudad, estado, pais, ocupacion, empresa, telefono_trabajo, tipo) VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', 'Propietarios')",
            $config['contrato'],
            $c_nombre,
            $c_apellidos,
            $c_sexo,
            $c_telefono,
            $c_celular,
            $c_fax,
            $c_nextel_tel,
            $c_nextel_radio,
            $c_email,
            $c_domicilio,
            $c_ciudad,
            $c_estado,
            $c_pais,
            $c_ocupacion,
            $c_empresa,
            $c_telefono_trabajo
        );
        $sql->query($query);
    }
    $query = sprintf(
        "SELECT id FROM contactos WHERE contrato='%s' AND nombre='%s' AND apellidos='%s' AND telefono='%s' AND email='%s'",
        $config['contrato'],
        $c_nombre,
        $c_apellidos,
        $c_telefono,
        $c_email
    );
    $consulta1 = $sql->query($query);
    $consulta1->data_seek($consulta1->num_rows - 1);
    $contacto = $consulta1->fetch_assoc();
    $i_propietario = (int)$contacto['id'];
}

$i_observaciones = trim($i_observaciones);

if (empty($t_comparto_comision)) {
    $t_comparto_comision = fieldDefault('propiedades', 't_comparto_comision');
}
if (empty($comparto_comision)) {
    $comparto_comision = fieldDefault('propiedades', 'comparto_comision');
}
if (empty($comision_ampi)) {
    $comision_ampi = fieldDefault('propiedades', 'comision_ampi');
}
if (empty($rta_comparto_comision)) {
    $rta_comparto_comision = fieldDefault('propiedades', 'rta_comparto_comision');
}

// Variables de multibolsa removidas - ahora se manejan en inmueble-paso-7.php

$ca = ", comision_ampi='$comparto_comision', t_comision_ampi='$t_comparto_comision', rta_comision_ampi='$rta_comparto_comision'";

if ($ampi_plus == 'Si') {
    $ampi_plus = ", ampi_plus='Si'";

    $query = "SELECT * FROM ampi_usuarios WHERE (contrato='$config[contrato]' && seccion='1') LIMIT 1";
    $consulta1 = $sql->query($query);
    if ($consulta1->num_rows == 1) {
        $consulta1 = $sql->query("SELECT * FROM ampi_comercializacion WHERE (seccion='1' && clave_sistema='$clave_sistema') LIMIT 1");
        if ($consulta1->num_rows == 0) {
            $sql->query("INSERT INTO ampi_comercializacion (seccion, clave_sistema, propuesto, status) VALUES ('1', '$clave_sistema', '$ahorita', 'en espera')");
        }	// if
    }	// if
} else {
    $ampi_plus = ", ampi_plus='No'";

    $query = "SELECT * FROM ampi_usuarios WHERE (contrato='$config[contrato]' && seccion='1') LIMIT 1";
    $consulta1 = $sql->query($query);
    if ($consulta1->num_rows == 1) {
        $consulta1 = $sql->query("SELECT * FROM ampi_comercializacion WHERE (seccion='1' && clave_sistema='$clave_sistema') LIMIT 1");
        if ($consulta1->num_rows == 1) {
            $sql->query("DELETE FROM ampi_comercializacion WHERE (seccion='1' && clave_sistema='$clave_sistema') LIMIT 1");
        }	// if
    }	// if
}

if (empty($i_comision_rta)) {
    $i_comision_rta = fieldDefault('propiedades', 'i_comision_rta');
}
if (empty($i_porcentaje_comision)) {
    $i_porcentaje_comision = fieldDefault('propiedades', 'i_porcentaje_comision');
}
if (empty($i_tipo_comision_rta)) {
    $i_tipo_comision_rta = fieldDefault('propiedades', 'i_tipo_comision_rta');
}
if (empty($tipo_promocion)) {
    $tipo_promocion = fieldDefault('propiedades', 'tipo_promocion');
}
if (empty($i_requisito_renta)) {
    $i_requisito_renta = fieldDefault('propiedades', 'i_requisito_renta');
}

if ($sucursal == 0) {
    $sucursal = 'null';
}

// Fecha de publicación se maneja en inmueble-paso-7.php (Multibolsa)
$fecha_publicacion_sql = '';

$query = "
	UPDATE propiedades 
	SET i_observaciones='$i_observaciones', i_propietario='$i_propietario', fecha_modificaciones='$ahorita', 
		i_inventario='$i_inventario', i_calle_numero='$i_calle_numero', i_entre_calles='$i_entre_calles', 
		sucursal=$sucursal, asesor='$asesor', 
		i_porcentaje_comision='$i_porcentaje_comision', i_comision_rta='$i_comision_rta', i_tipo_comision_rta='$i_tipo_comision_rta',
		tipo_promocion=" . ($tipo_promocion ? "'{$tipo_promocion}'" : 'NULL') . ", i_requisito_renta='$i_requisito_renta' 
		$fecha_publicacion_sql 
	WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema') 
	LIMIT 1
	";

if (!$sql->query($query)) {
    // mail('<EMAIL>', 'Error al actualizar Datos Internos de un inmueble', "Error al ejecutar la siguiente consulta:\n\n$query\n\n" . mysql_error($db_link) . "\n\n" . __FILE__ . "\n\n" . __LINE__);
    ?>
Error<br><br>Ha ocurrido un error al intentar guardar la información,<br><br>Favor de reportarlo a:<br>
<A HREF="mailto:<EMAIL>"><EMAIL></A>
<?php

} else {

    ////////////////////////////////
    //	Registro en la tabla de movimientos de propiedades
    $sql->query("INSERT INTO movpropiedades (contrato, clave_sistema, movimiento) VALUES ('$config[contrato]', '$clave_sistema', 'UPDATE')");

    // Ahora grabo la información de los campos
    $consulta1 = $sql->query("SELECT * FROM campos_inmuebles WHERE ((contrato IS NULL || contrato='$config[contrato]') && uso='interno')");
    while ($row = $consulta1->fetch_assoc()) {
        $val_variable = ${$row['variable']};
        $valor_esp = '';
        $valor_ing = '';
        if ($row['tipo'] == 'selector') {

            // En caso de haber especificado valores
            if ($val_variable == '-1') {
                $d_val_variable_esp = ${"d_$row[variable]_esp"};
                $d_val_variable_ing = ${"d_$row[variable]_ing"};
                if ($d_val_variable_esp == '*** en español ***') {
                    $d_val_variable_esp = '';
                }
                if ($d_val_variable_ing == '*** in english ***') {
                    $d_val_variable_ing = '';
                }

                $valor_esp = ucfirst(mb_strtolower(str_replace('  ', ' ', trim($d_val_variable_esp))));
                $valor_ing = ucfirst(mb_strtolower(str_replace('  ', ' ', trim($d_val_variable_ing))));


                ///////////////////////////////////////////
                // Grabo los valores en los aprendidos
                if ($valor_esp != '' || $valor_ing != '') {
                    $consulta2 = $sql->query("SELECT * FROM valores_campos_aprendidos WHERE ((contrato IS NULL || contrato='$config[contrato]') && variable='$row[variable]' && valor_esp='$valor_esp' && valor_ing='$valor_ing')");

                    // Al cumplirse esto quiere decir que no hay registrado algo exactamente igual en español e inglés pero aún existe la posibilidad de que haya cualquiera de las 2 probabilidades registradas
                    if ($consulta2->num_rows == 0) {

                        // Esta consulta me dirá si lo que voy a hacer es sobreescribir un valor ya existente
                        $consulta2 = $sql->query("SELECT * FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp='')))");
                        $num_res = $consulta2->num_rows;

                        // Al cumplirse lo siguiente es que hay 2 valores para la misma entrada, borro lo que hay y hago una entrada para los 2 campos
                        if ($num_res >= 2) {
                            $sql->query("DELETE FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp='')))");
                            $sql->query("INSERT INTO valores_campos_aprendidos (contrato, variable, valor_esp, valor_ing) VALUES ('$config[contrato]', '$row[variable]', '$valor_esp', '$valor_ing')");

                            // En caso de que haya un valor ya sea en esp o ing pero su contraparte esté vacía sustituyo por los valores ya completos
                        } elseif ($num_res == 1) {
                            $sql->query("UPDATE valores_campos_aprendidos SET valor_esp='$valor_esp', valor_ing='$valor_ing' WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp='')))");

                            // Al cumplirse esto es que es 0 y no hay entrada bajo ningun valor por lo que inserto
                        } else {
                            $sql->query("INSERT INTO valores_campos_aprendidos (contrato, variable, valor_esp, valor_ing) VALUES ('$config[contrato]', '$row[variable]', '$valor_esp', '$valor_ing')");
                        }
                    }
                }

                // En caso de haber seleccionado un valor aprendido
            } elseif ($val_variable != '') {
                $consulta2 = $sql->query("SELECT * FROM valores_campos_aprendidos WHERE (id='$val_variable')");
                $v0 = $consulta2->fetch_assoc();
                $valor_esp = $v0['valor_esp'];
                $valor_ing = $v0['valor_ing'];
            }
        } else {
            $valor_esp = $val_variable;
        }

        // Borro el contenido del campo antes de insertar el nuevo
        $sql->query("DELETE FROM valores_campos WHERE (clave_sistema='$clave_sistema' && variable='$row[variable]') LIMIT 1");

        // En caso de tener información en esp o ing hago la entrada en la BD
        if ($valor_esp != '' || $valor_ing != '') {
            if ($row['tipo'] == 'numerico' && $valor_ing == '') {
                $valor_ing = $valor_esp;
            }

            $sql->query("
					INSERT INTO valores_campos 
					(contrato, clave_sistema, variable_id, variable, valor_esp, valor_ing) 
					VALUES 
					('{$config['contrato']}', '{$clave_sistema}', '{$row['id']}', '{$row['variable']}', '{$valor_esp}', '{$valor_ing}')
				");
        }
    }

    // Grabo o actualizo en la base para búsquedas
    cm_rec_datos_prop($clave_sistema, $config['contrato']);

    ?>
<div align="center">

    <table cellpadding="0" cellspacing="0" class="t1">
        <tr>
            <td width="100%" class="t1">
                <?php echo local_menu_propiedad(); ?>
            </td>
        </tr>

        <tr>
            <td width="100%" class="t1">
        <!-- Tarjeta principal -->
        <div class="p-8 text-center bg-white rounded-2xl shadow-xl">
            <!-- Icono de éxito -->
            <div class="mb-6">
                <div class="flex justify-center items-center mx-auto w-16 h-16 bg-green-100 rounded-full">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
            </div>

            <!-- Título -->
            <h1 class="mb-4 text-3xl font-bold text-gray-800">¡Información Guardada!</h1>
            
            <!-- Mensaje -->
            <p class="mb-8 text-lg text-gray-600">
                La información del inmueble con clave <span class="font-semibold text-indigo-600"><?php echo $claveprop; ?></span> se ha guardado con éxito
            </p>

            <!-- Botón con contador -->
            <div class="space-y-4">
                <a href="/inmueble.php?claveprop=<?php echo $claveprop; ?>&paso=6" 
                   class="inline-flex items-center px-8 py-4 font-semibold text-white bg-indigo-600 rounded-lg shadow-lg transition-all duration-200 transform hover:bg-indigo-700 hover:scale-105">
                    <svg class="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Este inmueble en la Multibolsa
                </a>
                
                <!-- Contador -->
                <p class="text-sm text-gray-500">
                    Redirigiendo automáticamente en <span id="countdown" class="font-semibold text-indigo-600">...</span> segundos...
                </p>
            </div>
        </div>
            </td>
        </tr>
    </table>

</div>

<script>
let countdown = 5;
const countdownElement = document.getElementById('countdown');

const timer = setInterval(() => {
    countdown--;
    countdownElement.textContent = countdown;
    
    if (countdown <= 0) {
        clearInterval(timer);
        window.location.href = '/inmueble.php?claveprop=<?php echo $claveprop; ?>&paso=6';
    }
}, 1000);
</script>
<?php

}
?>