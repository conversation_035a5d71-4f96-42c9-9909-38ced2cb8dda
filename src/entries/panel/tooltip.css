.tooltip-container {
  position: relative;
  display: inline-block;
}

.tooltip-container .tooltip-text {
  visibility: hidden;
  font-size: 10px;
  font-family: Verdana, Geneva, Tahoma, sans-serif;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1000;
  transform: translateX(-50%) translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip-container .tooltip-text-down {
  top: 100%; /* Cambia el valor según sea necesario */
  bottom: auto;
}

.tooltip-container .tooltip-text-up {
  bottom: 100%;
  top: auto;
}

.tooltip-container .tooltip-text-left {
  left: 0;
}

.tooltip-container .tooltip-text-right {
  right: 0;
}

.tooltip-container .tooltip-text-center {
  left: 50%;
}

.tooltip-container:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}
