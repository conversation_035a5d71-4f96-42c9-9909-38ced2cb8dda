// Importamos los estilos para que Vite los procese
import "./tailwind.css";

// Panel v4
import "./style_mto2.css";
import "./panel.css";
import "./tooltip.css";

// Panel v4.5
import "./styles.css";

// import "../../../../../css/style_mto.css";

// Importar scripts específicos
import { ayuda, BImuestra, newWindow } from "../js/old_functions.js";
window.ayuda = ayuda;
window.BImuestra = BImuestra;
window.newWindow = newWindow;

import "../js/jquery.js";
import "./script.js";

import "../inmueble";

// Cualquier código de inicialización global puede ir aquí
console.log("Panel inicializado");

// Detectar si es la primera vez que el usuario visita la página
let firstVisit = localStorage.getItem("firstVisit") === null;

if (firstVisit) {
  // Código para primera visita
  localStorage.setItem("firstVisit", "false");
}
