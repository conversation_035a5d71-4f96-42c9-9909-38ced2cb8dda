.sidebar-transition {
  transition: all 0.3s ease-in-out;
}
.main-content-transition {
  transition: all 0.3s ease-in-out;
}
@media (max-width: 1024px) {
  .sidebar-mobile {
    position: fixed;
    right: 0;
    top: 0;
    transform: translateX(100%);
    width: 100% !important;
    max-width: 20rem;
    z-index: 50;
    display: flex;
    flex-direction: column;
  }
  .sidebar-mobile.active {
    transform: translateX(0);
  }
  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.sidebar-content {
  overflow-y: auto;
  height: calc(100% - 56px); /* Restamos la altura del header */
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 40;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}
.overlay.active {
  opacity: 1;
  visibility: visible;
}
