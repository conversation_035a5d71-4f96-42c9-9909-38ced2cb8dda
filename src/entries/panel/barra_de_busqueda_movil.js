document.addEventListener("DOMContentLoaded", function () {
  const mobileSearchToggle = document.getElementById("mobile-search-toggle");
  const mobileSearchBar = document.getElementById("mobile-search-bar");

  if (mobileSearchToggle && mobileSearchBar) {
    mobileSearchToggle.addEventListener("click", function () {
      if (mobileSearchBar.classList.contains("hidden")) {
        // Mostrar barra de búsqueda
        mobileSearchBar.classList.remove("hidden");
        // Cambiar color del icono a gris
        mobileSearchToggle.classList.remove(
          "text-gray-600",
          "hover:text-mulbin-500"
        );
        mobileSearchToggle.classList.add("text-gray-400");
        setTimeout(() => {
          mobileSearchBar.classList.add("animate-fade-in");
          mobileSearchBar.querySelector("input").focus();
        }, 10);
      } else {
        // Ocultar barra de búsqueda
        mobileSearchBar.classList.add("animate-fade-out");
        // Restaurar color original del icono
        mobileSearchToggle.classList.remove("text-gray-400");
        mobileSearchToggle.classList.add(
          "text-gray-600",
          "hover:text-mulbin-500"
        );
        setTimeout(() => {
          mobileSearchBar.classList.add("hidden");
          mobileSearchBar.classList.remove(
            "animate-fade-in",
            "animate-fade-out"
          );
        }, 300);
      }
    });

    // Cerrar la búsqueda al hacer clic en X
    const closeButton = mobileSearchBar.querySelector("#mobile-search-close");
    if (closeButton) {
      closeButton.addEventListener("click", function () {
        mobileSearchBar.classList.add("animate-fade-out");
        // Restaurar color original del icono
        mobileSearchToggle.classList.remove("text-gray-400");
        mobileSearchToggle.classList.add(
          "text-gray-600",
          "hover:text-mulbin-500"
        );
        setTimeout(() => {
          mobileSearchBar.classList.add("hidden");
          mobileSearchBar.classList.remove(
            "animate-fade-in",
            "animate-fade-out"
          );
        }, 300);
      });
    }
  }
});
