document.addEventListener("DOMContentLoaded", function () {
  const sidebar = document.getElementById("sidebar");
  const mainContent = document.getElementById("main-content");
  const sidebarToggle = document.getElementById("sidebar-toggle");
  const closeSidebar = document.getElementById("close-sidebar");
  const overlay = document.getElementById("sidebar-overlay");
  const icon1 = document.getElementById("sidebar-toggle-icon-1");
  const icon2 = document.getElementById("sidebar-toggle-icon-2");

  // Inicializar sidebar en estado visible
  sidebar.style.width = "16rem"; // 64 en Tailwind = 16rem
  // Inicializar estado de los iconos
  icon1.classList.add("hidden");
  icon2.classList.remove("hidden");

  // Función para detectar si es dispositivo móvil
  function isMobile() {
    return window.innerWidth < 1024;
  }

  // Aplicar configuración inicial
  function initLayout() {
    if (isMobile()) {
      icon1.classList.remove("hidden");
      icon2.classList.add("hidden");
      // En móvil, sidebar se oculta fuera de la pantalla
      sidebar.classList.add("sidebar-mobile");
      if (!sidebar.classList.contains("active")) {
        sidebar.classList.remove("active");
        overlay.classList.remove("active");
      }

      // Asegurar que el contenido principal ocupe todo el ancho
      mainContent.style.width = "100%";
      mainContent.style.marginRight = "0";
    } else {
      // En desktop, sidebar visible
      sidebar.classList.remove("sidebar-mobile");
      sidebar.classList.remove("active");
      overlay.classList.remove("active");

      // Restaurar sidebar al tamaño correcto
      sidebar.style.width = "16rem";

      // Restaurar main content
      mainContent.style.width = "";
      mainContent.style.marginRight = "";
    }
  }

  // Inicializar al cargar
  initLayout();

  // Función para alternar el sidebar
  function toggleSidebar() {
    //
    // console.log("toggleSidebar");
    // console.log(isMobile());
    // console.log(sidebar.classList.contains("active"));
    //
    if (isMobile()) {
      icon1.classList.remove("hidden");
      icon2.classList.add("hidden");
      // En móvil, mostrar/ocultar deslizando (sin afectar el contenido principal)
      sidebar.classList.toggle("active");
      overlay.classList.toggle("active");

      // Alternar iconos
      if (sidebar.classList.contains("active")) {
        icon2.classList.remove("hidden");
        icon1.classList.add("hidden");
      } else {
        icon2.classList.add("hidden");
        icon1.classList.remove("hidden");
      }
    } else {
      // En desktop, cambiar ancho
      if (sidebar.style.width === "16rem") {
        // Cerrar sidebar
        sidebar.style.width = "0";
        icon1.classList.remove("hidden");
        icon2.classList.add("hidden");
      } else {
        // Abrir sidebar
        sidebar.style.width = "16rem";
        icon1.classList.add("hidden");
        icon2.classList.remove("hidden");
      }
    }
  }

  // Asignar eventos
  sidebarToggle.addEventListener("click", toggleSidebar);
  closeSidebar.addEventListener("click", toggleSidebar);
  overlay.addEventListener("click", toggleSidebar); // Cerrar al hacer clic en el overlay

  // Reconfigura al cambiar tamaño de ventana
  window.addEventListener("resize", initLayout);
});
