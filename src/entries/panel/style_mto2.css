td {
  font-size: 1rem;
}

a {
  color: #018bf1;
  text-decoration: none;
}
a:hover {
  color: navy;
  text-decoration: none;
}
#logo img {
  height: 55px;
  margin-bottom: 5px;
  border-right: 3px solid #ddedf8;
  border-bottom: 3px solid #ddedf8;
  border-top: 0;
  border-left: 0;
  border-radius: 0 5px 5px 5px;
}
.blanco,
a.blanco {
  color: #ffffff;
  text-decoration: none;
}
a.blanco:hover {
  color: #ffffff;
  text-decoration: underline;
}

a.barra_navegacion,
a.barra_navegacion2 {
  color: #c9dfee;
  text-decoration: none;
}

.subbarra_navegacion,
a.subbarra_navegacion {
  color: #000000;
  text-decoration: none;
}
a.subbarra_navegacion:hover {
  color: #000080;
  text-decoration: none;
}

.blanco {
  color: #ffffff;
  text-decoration: none;
}

.formularios {
  font-family: "Roboto";
}

.observaciones {
  font-size: 0.9rem;
}

.msg_error {
  font-family: "Roboto", Helvetica, sans-serif;
  font-weight: bold;
  font-style: italic;
  color: Red;
}

.campos {
  font-family: "Roboto", Helvetica, sans-serif;
  font-weight: normal;
}

table.t1 {
  border: 1px solid #e6e6ff;
  background-color: #e6e6ff;
  border-radius: 5px;
  border-spacing: 0;
}
table.t1 > tbody > tr > th {
  background-color: #e6e6ff;
  font-family: "Roboto", Helvetica, sans-serif;
  font-weight: bold;
  text-align: center;
  padding: 1rem;
}
table.t1 > tbody > tr > td {
  padding: 0;
}

table.t2 {
  border-spacing: 0;
  border: none;
  width: 100%;
}

table.t3 {
  border-collapse: collapse;
  border-color: #c0c0c0;
  border-style: solid;
  border-width: 1px;
  width: 100%;
}
th.t3 {
  background-color: #f0f0f0;
  padding: 2px;
  font-family: "Roboto", Helvetica, sans-serif;
  font-weight: bold;
  text-align: center;
}
td.t3 {
  background: #f8f8f8;
  border: 5px solid #e6e6ff;
  padding: 10px;
  font-family: "Roboto", Helvetica, sans-serif;
}

.form_peque {
  font-family: "Roboto", Helvetica, sans-serif;
  font-weight: normal;
}

table.lateral_bolsas {
  width: 150px;
}
th.lateral_bolsas {
  padding: 3px;
  font-family: "Roboto", sans-serif, Verdana, charter;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
}
td.lateral_bolsas {
  background-color: #ffffff;
  font-family: "Roboto", sans-serif, Verdana, charter;
  color: Navy;
  border-bottom: 1px solid #808080;
  padding-bottom: 3px;
}
th.lateral_bolsas_in {
  font-family: "Roboto", sans-serif, Verdana, charter;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
}
td.lateral_bolsas_in {
  font-family: "Roboto", sans-serif, Verdana, charter;
  color: Navy;
  padding: 0 5px;
}

table.ventana_emergente {
  width: 150px;
  border-color: Gray;
  border-style: solid;
  border-width: 1px;
}
td.ventana_emergente {
  padding: 2px;
  background-color: White;
  font-family: "Roboto", sans-serif, Verdana, charter;
  font-weight: normal;
  color: Gray;
}

#ac-list {
  border: 1px solid;
  border-color: #6badde;
  background: #f0f4ff;
  color: #a0a0a0;
  font-family: "Roboto", sans-serif;
  padding: 2px;
}
#ac-list div {
  padding: 1px 3px 1px 3px;
}
#ac-list .selected {
  background: #d0d3ff;
  color: #000000;
}

#contp {
  width: 100%;
}

#conth {
  margin: 0 auto;
}

@media (max-width: 881px) {
  #ClubNegociosLateral {
    display: none;
  }
  table.t1 {
    width: 100%;
    margin: 0;
    border: none;
  }
}
@media (min-width: 882px) and (max-width: 1280px) {
  table.t1 {
    width: 100%;
    margin: 0 auto;
  }
}
@media (min-width: 1281px) {
  table.t1 {
    width: 880px;
    margin: 0 auto;
  }
}

@media (min-width: 1024px) {
  #conth {
    width: 1024px;
  }
}
div#navigation {
  background-color: #c9dfee;
  padding: 5px 0 5px 0;
}
div#navigation a:hover {
  color: blue;
}
div#navigation.flex a {
  margin: -10px;
  display: block;
  padding: 10px 1.5rem;
}
div#navigation.flex a:hover {
  background-color: #0077c9;
  color: #fff;
  z-index: 99999;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
#tableTopPage > tbody > tr > td {
  width: 33%;
}
#menuSuperiorH {
  display: none;
}

/* MENÚ HAMBURGUESA */
.header {
  display: flex;
  justify-content: flex-end;
  padding: 10px 10px 0 0;
}

.hamburger-menu {
  display: inline-block;
  cursor: pointer;
}

.hamburger-menu .bar {
  width: 30px;
  height: 4px;
  margin: 6px 0;
  background-color: #0077c9;
  border-radius: 3px;
}

.navigation {
  display: none;
  z-index: 99999;
}

.navigation.active {
  display: block;
  border: 3px solid #fff;
  border-top: none;
}

.navigation ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.navigation a {
  color: white;
  font-size: 18px;
  text-decoration: none;
  margin-top: 20px;
  margin-bottom: 5px;
  display: block;
  text-align: center;
}
.navigation li.topUser {
  margin-top: 10px;
  color: #d0d0d0;
  font-size: 14px;
}
.navigation ul ul {
  display: none;
}
.navigation ul ul li a {
  background-color: #00b2e2;
  margin: 0;
  padding: 10px 15px;
  border-bottom: 1px solid #0077c9;
}

.navigation li:hover > ul {
  display: block;
}

td.t3 > a,
.a-block {
  display: block;
  margin: 5px 0;
  padding: 5px;
  text-align: center;
}
#table12 td {
  width: 50%;
  text-align: center;
}

.menuSupHorizontal {
  width: 25%;
}
.menuSupHorizontal.head {
  background-color: #e6e6ff;
  color: #0077c9;
}
.menuSupHorizontal.head a {
  color: #0077c9;
  font-weight: bold;
}
.menuSupHorizontal a.barra_navegacion {
  display: block;
  padding: 10px 20px;
  border-right: 1px solid #569ed0;
}
.menuSupHorizontal a.barra_navegacion:hover {
  background-color: #c9dfee;
  color: #0077c9;
}
.menuSupHorizontal a.barra_navegacion2 {
  display: block;
  padding: 10px 5px;
}
.menuSupHorizontal a.barra_navegacion2.active {
  background-color: #e6e6ff;
  color: #0077c9;
}

.menuSupHorizontal a.barra_navegacion2 ion-icon {
  font-size: 1.4rem;
}

#byMulbin,
#byMulbin2 {
  width: 100%;
}
#byMulbin td {
  text-align: right;
  padding: 5px 5px 0 0;
  font-size: 0.9rem;
}
#byMulbin2 td {
  text-align: center;
  padding-right: 5px;
  font-size: 0.9rem;
}
#byMulbin img,
#byMulbin2 img {
  height: 25px;
}
.noBig {
  display: none;
}

.no-big-1 {
  display: none;
}
@media (max-width: 600px) {
  .no-peque-1 {
    display: none;
  }
  .no-big-1 {
    display: block;
  }
}

@media (max-width: 767px) {
  #menuSuperiorH {
    display: block;
  }
  .noPeque {
    display: none !important;
  }
  .navigation {
    position: absolute;
    top: 60px;
    right: 10px;
    left: 10px;
    background-color: rgba(0, 119, 201, 0.95);
    padding: 0 20px 20px 20px;
    border-radius: 5px;
  }
  .noBig {
    display: block;
  }
  table.t4 > tbody > tr > td,
  table.t2 > tbody > tr > td {
    border-left: none !important;
    border-right: none !important;
  }
}
.min-300 {
  min-width: 300px;
}

.d-flex {
  display: flex;
  align-items: center;
}
.flex.between,
.d-flex.between {
  justify-content: space-between;
}
.d-grid {
  display: grid;
}
.d-flex.top,
.flex.top {
  align-items: flex-start !important;
}
.d-flex > * {
  flex-grow: 1;
  margin: 0 3px;
}

.form-control {
  display: block;
  box-sizing: border-box;
  width: 100%;
  padding: 7px 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 5px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  color: #0077c9 !important;
}
.btn-primary,
.btn.primary {
  background-color: #0077c9 !important;
  color: #fff !important;
}
.btn.secondary {
  background-color: #e6e6ff !important;
  color: #0077c9 !important;
}

/* BUSCAR */
.search-container {
  margin: 0.75rem 0;
}
.search-container input[type="search"] {
  padding: 0.75rem;
  border: 2px solid #0077c9;
  border-radius: 5px 0 0 5px;
}
.search-container button[type="submit"] {
  padding: 0.75rem;
  background-color: #0077c9;
  border: 2px solid #0077c9;
  border-radius: 0 5px 5px 0;
  margin-left: -5px;
  color: #fff;
}
@media (max-width: 767px) {
  td,
  p {
    font-size: 1.1rem;
  }
  .search-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
  }
  .search-container form {
    display: flex;
    align-items: center;
  }
  .search-container input[type="search"] {
    padding: 0.75rem;
    border: 2px solid #0077c9;
    border-radius: 5px 0 0 5px;
    outline: none;
    margin-right: 3px;
  }
  .search-container button[type="submit"] {
    padding: 0.75rem;
    background-color: #0077c9;
    color: white;
    border: 2px solid #0077c9;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
  }
  .search-container button[type="submit"]:hover {
    background-color: #00b2e2;
  }
  #table12 td {
    width: 100%;
  }
}

/**
 * TABLAS
 */
table.t4,
table.t2 {
  width: 100%;
  background-color: #fff;
  border-spacing: 0;
}
table.t2 > tbody > tr > th {
  border: 2px solid #e6e6ff;
  border-bottom: none;
  padding: 10px;
  text-align: left;
  font-size: 1rem;
  font-weight: bold;
}
table.t4 > tbody > tr > td,
table.t2 > tbody > tr > td {
  border: 2px solid #e6e6ff;
  padding: 10px;
}
table.t2 > tbody > tr > td {
  border-top: none;
}
/**
 * / TABLAS
 */

/**
 * CHECKBOX
 */
/* Eliminamos los estilos por defecto del checkbox */
input[type="checkbox"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  outline: none;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

/* Añadimos estilos personalizados al checkbox */
input[type="checkbox"] + label {
  display: flex;
  align-items: center;
  padding-left: 30px;
  position: relative;
  font-size: 16px;
  color: #333;
  cursor: pointer;
}

/* Estilo del icono del checkbox cuando no está marcado */
input[type="checkbox"] + label::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 4px;
}

/* Estilo del icono del checkbox cuando está marcado */
input[type="checkbox"]:checked + label::before {
  content: "\2713"; /* Unicode del símbolo de marca de verificación */
  display: inline-block;
  position: absolute;
  left: 0;
  width: 20px;
  height: 20px;
  border: none;
  background-color: #007bff;
  color: #fff;
  text-align: center;
  font-size: 14px;
  line-height: 20px;
  border-radius: 4px;
}

/* Estilo del icono del checkbox cuando está deshabilitado */
input[type="checkbox"]:disabled + label::before {
  border-color: #ccc;
  background-color: #f5f5f5;
  color: #ccc;
}

/* Estilo de la etiqueta cuando el checkbox está deshabilitado */
input[type="checkbox"]:disabled + label {
  color: #ccc;
  cursor: not-allowed;
}
/**/

/**
 * RADIO
 */
/* Eliminamos los estilos por defecto del radio button */
input[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  outline: none;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

/* Añadimos estilos personalizados al radio button */
input[type="radio"] + label {
  display: flex;
  align-items: center;
  padding-left: 30px;
  position: relative;
  font-size: 16px;
  color: #333;
  cursor: pointer;
}

/* Estilo del icono del radio button cuando no está marcado */
input[type="radio"] + label::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
}

/* Estilo del icono del radio button cuando está marcado */
input[type="radio"]:checked + label::before {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  width: 20px;
  height: 20px;
  border: none;
  background-color: #007bff;
  color: #fff;
  text-align: center;
  font-size: 14px;
  line-height: 20px;
  border-radius: 50%;
}

/* Estilo del icono del radio button cuando está deshabilitado */
input[type="radio"]:disabled + label::before {
  border-color: #ccc;
  background-color: #f5f5f5;
  color: #ccc;
}

/* Estilo de la etiqueta cuando el radio button está deshabilitado */
input[type="radio"]:disabled + label {
  color: #ccc;
  cursor: not-allowed;
}
/**/

.contenedor-img {
  border: 0.4rem solid;
  border-radius: 5px;
  width: 80px;
  height: 60px;
}
.contenedor-img img {
  width: 100%;
  height: 100%;
}

.input-with-complement-right-1,
.input-with-complement-right-2 {
  position: relative;
}
.input-with-complement-right-1 > span {
  position: absolute;
  top: 2px;
  right: 2px;
  border-radius: 0 5px 5px 0;
  padding: 9px 5px;
  font-size: 0.9rem;
  background-color: #f0f0f0;
}
.input-with-complement-right-1 > input {
  padding-right: 3rem;
}

.input-with-complement-right-2 > span {
  position: absolute;
  right: 0;
  border-radius: 0 5px 5px 0;
  border: 1px solid #ced4da;
  padding: 2px 0.3rem;
  font-size: 0.9rem;
  margin-left: -0.3rem;
  border-left: none;
  background-color: #f0f0f0;
}
.input-with-complement-right-2 > select,
.input-with-complement-right-2 > input {
  margin-right: 2.2rem;
}

.price-format {
  display: flex;
  align-items: center;
}
.price-format * {
  flex-grow: 1;
  margin: 0 3px;
}
.price-format span {
  border-radius: 5px 0 0 5px;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.4rem;
  font-size: 1.2rem;
  margin-right: -0.3rem;
  border-right: none;
  background-color: #f8f8f8;
}

.price-format input[type="text"] {
  font-weight: bold;
  font-size: 1.4rem;
  text-align: right;
}

.check-price {
  margin: 0 0 12px 22px;
}

.mceSelectList {
  padding: 5px !important;
}

.with-counter .form-control {
  border-radius: 5px 5px 0 0;
}
.with-counter .counter {
  font-family: monospace;
  font-size: 1rem;
  display: block;
  background-color: #d0d0d0;
  padding: 3px 10px;
  border-radius: 0 0 5px 5px;
  color: #fff;
}

input:disabled {
  background-color: #f0f0f0;
  color: #a0a0a0;
}

.pointer {
  cursor: pointer;
}

button:disabled {
  background-color: #f0f0f0 !important;
  color: #a0a0a0 !important;
}

.tox-promotion,
.tox-menubar {
  display: none !important;
}

.contenedor {
  display: grid;
  gap: 1rem;
}
.contenedor.min-50 {
  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
}
.contenedor.min-75 {
  grid-template-columns: repeat(auto-fit, minmax(75px, 1fr));
}
.contenedor.min-100 {
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
}
.contenedor.min-125 {
  grid-template-columns: repeat(auto-fit, minmax(125px, 1fr));
}
.contenedor.min-150 {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}
.contenedor.min-175 {
  grid-template-columns: repeat(auto-fit, minmax(175px, 1fr));
}
.contenedor.min-200 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}
.contenedor.min-250 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.contenedor.min-300 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
.contenedor.min-325 {
  grid-template-columns: repeat(auto-fit, minmax(325px, 1fr));
}
.contenedor.min-350 {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}
.contenedor.min-375 {
  grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
}
.contenedor.min-400 {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}
.contenedor.min-450 {
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
}
.contenedor.min-500 {
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
}
.gap-05 {
  gap: 0.5rem !important;
}
.gap-025 {
  gap: 0.25rem !important;
}
.gap-0 {
  gap: 0 !important;
}
.max-50 {
  max-width: 50px;
}
.max-75 {
  max-width: 75px;
}
.max-100 {
  max-width: 100px;
}
.max-125 {
  max-width: 125px;
}
.max-150 {
  max-width: 150px;
}
.max-200 {
  max-width: 200px;
}
.max-250 {
  max-width: 250px;
}
.max-300 {
  max-width: 300px;
}
.max-350 {
  max-width: 350px;
}
.max-400 {
  max-width: 400px;
}
.max-450 {
  max-width: 450px;
}
.max-500 {
  max-width: 500px;
}
.max-550 {
  max-width: 550px;
}
.max-600 {
  max-width: 600px;
}
.max-650 {
  max-width: 650px;
}
.max-700 {
  max-width: 700px;
}
.max-750 {
  max-width: 750px;
}
.max-800 {
  max-width: 800px;
}
.max-850 {
  max-width: 850px;
}
.max-900 {
  max-width: 900px;
}
.max-1024 {
  max-width: 1024px;
}
.center {
  text-align: center;
  margin: 0 auto;
}
.center-y {
  display: flex;
  align-items: center;
}
.right {
  text-align: -webkit-right;
}
.left {
  display: block;
  text-align: left !important;
}

.w-50 {
  width: 50% !important;
}
.w-80 {
  width: 80% !important;
}
.w-100 {
  width: 100% !important;
}
.font-normal {
  font-weight: normal !important;
}
.bold {
  font-weight: bold !important;
}
.italic {
  font-style: italic !important;
}
.underline {
  text-decoration: underline !important;
}

.container-img {
  border: 0.4rem solid;
  border-radius: 5px;
  width: 80px;
  height: 60px;
}
.container-img img {
  width: 100%;
  height: 100%;
}

.rounded-1 {
  border-radius: 5px !important;
}
.rounded-2 {
  border-radius: 10px !important;
}
.rounded-top-1 {
  border-top-right-radius: 5px;
  border-top-left-radius: 5px;
}
.rounded-top-left-1 {
  border-top-left-radius: 5px;
}
.rounded-top-right-1 {
  border-top-right-radius: 5px;
}
