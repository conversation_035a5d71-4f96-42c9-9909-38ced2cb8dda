main {
  width: 100%;
  margin: 0 auto;
}
main > header {
  text-align: left;
}
main > header > h1 {
  color: #0077c9;
  font-weight: bold;
  font-size: 1.2rem;
  padding: 1rem 1rem 0 1rem;
  margin: 0;
}
main > header > cite {
  display: block;
  padding: 0 1rem 0 1rem;
  color: #ffffff;
  text-shadow: 1px 1px 2px #0077c9;
  font-style: normal;
}

main div.dashboard {
  padding: 0.5rem;
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

main section.dashboard,
main div.dashboard section {
  box-shadow: 1px 2px 7px rgba(0, 0, 0, 0.3);
  background-color: #ffffff;
  border-radius: 0.5rem;
  padding: 1rem;
}
main section.working {
  box-shadow: 1px 2px 7px rgba(0, 0, 0, 0.3);
  background-color: #ffffff !important;
  border-radius: 5px !important;
  border: 1px solid #ccc;
  margin: 1rem;
}
main section.working article {
  /*	center mx-1 mt-1*/
  /*	margin: 0 auto;*/
  margin: 1rem 1rem 0 1rem;
}

main div.dashboard section .quantity {
  /*	bold left px-1 m-0 size-14 block*/
  font-weight: bold;
  text-align: left;
  display: block;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1.2rem;
}

main div.dashboard section p {
  text-align: left;
}
main div.dashboard section h3 {
  margin: 0 0 1rem 0;
  text-align: left;
}

main div.dashboard section h2 {
  text-align: left;
  font-weight: normal;
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity));
}

.dashboard section ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.dashboard section ul li {
  margin: 1rem;
}
.dashboard section ul li a {
  color: #0077c9;
}

@media (max-width: 881px) {
  main {
    border-radius: 0px;
  }
}

hr {
  border: 2px solid #0077c9;
  margin: 20px 0;
  display: block;
}
hr.color-2 {
  border-color: #e6e6ff;
}

.nav2 * {
  display: block;
  color: navy;
  background-color: #c9dfee;
  border: 1px solid #fff;
  padding: 10px 5px;
  text-align: center;
  border-radius: 5px;
}
.nav2 a:hover {
  background-color: #e6e6ff;
}
.nav2 div {
  background-color: #f0f0f0;
  color: #000000;
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity));
}
