<script>
  function val_claveprop(campo) {
    var checkOK =
      "0123456789-_,.abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    var checkStr = campo.value;
    var allValid = true;
    var allNum = "";
    for (i = 0; i < checkStr.length; i++) {
      ch = checkStr.charAt(i);
      for (j = 0; j < checkOK.length; j++) if (ch == checkOK.charAt(j)) break;
      if (j == checkOK.length) {
        allValid = false;
        break;
      }
      allNum += ch;
    }
    if (!allValid) {
      alert(
        "La clave del inmueble solo puede tener letras, números y guiones(-) y sin espacios."
      );
      campo.focus();
      return false;
    }
    return true;
  }

  function Valida1(theForm) {
    if (theForm.claveprop.value == "") {
      alert("Teclee una clave para el inmueble a registrar o modificar");
      theForm.claveprop.focus();
      return false;
    }
    return true;
  }
  function abre(pag, ventana) {
    window.open(
      pag,
      ventana,
      "toolbar=yes,scrollbars=yes,resizable=yes,width=580,height=250"
    );
    return;
  }
</script>
<script src="jsc/search_history.js"></script>
<script type="text/javascript">
  var v1 = _setup_ac([$CLAVESPROPS], "claveprop");
  var lsep = "";
</script>

<main>
  <header>
    <h1>Hola {{ user.name }}</h1>
    <cite
      >{{ frase.texto }}{{#frase.autor}}<span class="text-8">
        - {{frase.autor}}</span
      >{{/frase.autor}}</cite
    >
  </header>
  <div class="lg:!hidden dashboard">
    <section>
      <!-- Cabecera colapsable con indicador de notificaciones -->
      <div id="panel-header">
        <div class="flex gap-3 justify-between items-center cursor-pointer">
          <a
            href="/inmueble.php"
            class="px-4 py-1 text-center text-blue-700 bg-blue-50 rounded-md border border-blue-200 hover:bg-blue-100"
            id="header-title"
          >
            Mi Sistema Inmobiliario
          </a>
          <div class="flex items-center">
            <!-- Indicador de inmuebles estilo iOS (azul) -->
            <span
              class="inline-flex justify-center items-center px-2 py-1 mr-2 text-xs font-bold leading-none text-white bg-indigo-500 rounded-full"
              id="total-inmuebles"
            >
              {{ num_props }}
            </span>
            <!-- Indicador de notificaciones estilo iOS -->
            <span
              class="inline-flex justify-center items-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full"
              id="total-pendientes"
            >
              {{#total_pendientes}}{{ total_pendientes }}{{/total_pendientes}}
              {{^total_pendientes}}{{ sum_pendientes }}{{/total_pendientes}}
            </span>
            <svg
              id="collapse-icon"
              class="mb-1 ml-2 w-5 h-5 transition-transform duration-200 transform rotate-180"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Contenido colapsable -->
      <div
        id="panel-content"
        class="overflow-hidden transition-all duration-300"
        style="max-height: 0px"
      >
        <div class="flex flex-col gap-4 w-full lg:flex-row">
          {{#num_props}}
          <div
            class="flex-1 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow"
          >
            <div class="flex gap-3 justify-between items-start">
              <div class="flex items-start text-blue-700">
                <ion-icon name="home-outline" class="mr-2 mi-si"></ion-icon>
                <a href="/inmueble.php" class="font-bold">Mis inmuebles</a>
              </div>
              <a
                href="{{ links.inmuebles.listado }}"
                class="text-white bg-indigo-500 rounded-lg quantity hover:bg-indigo-600 hover:text-white"
                >{{ num_props }}</a
              >
            </div>
          </div>
          {{/num_props}}
          <div class="flex flex-col gap-4 lg:w-3/5 lg:flex-row">
            {{#num_citas}}
            <div
              class="flex-1 p-4 bg-red-50 rounded-lg border border-red-200 shadow"
            >
              <div class="flex gap-3 justify-between items-start">
                <div class="flex items-start text-red-700">
                  <ion-icon
                    name="calendar-outline"
                    class="mr-2"
                    style="width: 20px; height: 20px"
                  ></ion-icon>
                  <div class="text-red-700">Citas esperando respuesta</div>
                </div>
                <a
                  href="{{ links.oficina.citas }}"
                  class="text-white bg-red-500 rounded-lg quantity hover:bg-red-600 hover:text-white"
                  >{{ num_citas }}</a
                >
              </div>
            </div>
            {{/num_citas}} {{#num_preguntas}}
            <div
              class="flex-1 p-4 bg-red-50 rounded-lg border border-red-200 shadow"
            >
              <div class="flex gap-3 justify-between items-start">
                <div class="flex items-start text-red-700">
                  <ion-icon name="chatbox-outline" class="mr-2"></ion-icon>
                  <div class="text-red-700">Preguntas esperando respuesta</div>
                </div>
                <a
                  href="{{ links.oficina.preguntas }}"
                  class="text-white bg-red-500 rounded-lg quantity hover:bg-red-600 hover:text-white"
                  >{{ num_preguntas }}</a
                >
              </div>
            </div>
            {{/num_preguntas}}
          </div>
        </div>
      </div>

      <!-- Script para el comportamiento de contraer/expandir -->
      <script>
        document.addEventListener("DOMContentLoaded", function () {
          // Elementos
          const panelHeader = document.getElementById("panel-header");
          const panelContent = document.getElementById("panel-content");
          const collapseIcon = document.getElementById("collapse-icon");
          const totalPendientes = document.getElementById("total-pendientes");
          const totalInmuebles = document.getElementById("total-inmuebles");
          const headerTitle = document.getElementById("header-title");

          // Calcular total de pendientes
          function actualizarTotalPendientes() {
            const numCitas = parseInt("{{ num_citas }}") || 0;
            const numPreguntas = parseInt("{{ num_preguntas }}") || 0;
            const total = numCitas + numPreguntas;

            totalPendientes.textContent = total.toString();
            // Ocultar el indicador si no hay pendientes
            totalPendientes.classList.toggle("hidden", total === 0);

            // Configurar el badge de inmuebles
            totalInmuebles.textContent = "{{ num_props }}";
          }

          // Inicializar
          actualizarTotalPendientes();

          // Detectar si es móvil o desktop
          let esDesktop = window.matchMedia("(min-width: 1024px)").matches;

          // Estado inicial (depende del tamaño de pantalla)
          let isExpanded = esDesktop;

          // Actualizar estado inicial según tamaño de pantalla
          function configurarEstadoInicial() {
            if (esDesktop) {
              // Desktop: Expandido
              panelContent.style.maxHeight = "none";
              collapseIcon.classList.remove("rotate-180");

              // Ocultar título y badges cuando está expandido
              headerTitle.classList.add("hidden");
              totalPendientes.classList.add("hidden");
              totalInmuebles.classList.add("hidden");
              panelHeader.classList.add("flex");
              panelHeader.classList.add("justify-end");
            } else {
              // Mobile: Contraído
              panelContent.style.maxHeight = "0px";
              collapseIcon.classList.add("rotate-180");

              // Mostrar título y badges cuando está contraído
              headerTitle.classList.remove("hidden");
              totalPendientes.classList.remove("hidden");
              totalInmuebles.classList.remove("hidden");
              panelHeader.classList.remove("flex");
              panelHeader.classList.remove("justify-end");
            }
          }

          // Aplicar configuración inicial
          configurarEstadoInicial();

          // Función para contraer/expandir
          function togglePanel() {
            isExpanded = !isExpanded;

            if (isExpanded) {
              // Expandir
              panelContent.style.maxHeight = panelContent.scrollHeight + "px";
              setTimeout(function () {
                panelContent.style.maxHeight = "none";
              }, 300);
              collapseIcon.classList.remove("rotate-180");

              // Ocultar título y badges cuando está expandido
              headerTitle.classList.add("hidden");
              totalPendientes.classList.add("hidden");
              totalInmuebles.classList.add("hidden");
              panelHeader.classList.add("flex");
              panelHeader.classList.add("justify-end");
            } else {
              // Contraer
              panelContent.style.maxHeight = panelContent.scrollHeight + "px";
              setTimeout(function () {
                panelContent.style.maxHeight = "0px";
              }, 10);
              collapseIcon.classList.add("rotate-180");

              // Mostrar título y badges cuando está contraído
              headerTitle.classList.remove("hidden");
              totalPendientes.classList.remove("hidden");
              totalInmuebles.classList.remove("hidden");
              panelHeader.classList.remove("flex");
              panelHeader.classList.remove("justify-end");
            }
          }

          // Evento click
          panelHeader.addEventListener("click", togglePanel);

          // Responder a cambios de tamaño de ventana
          window.addEventListener("resize", function () {
            const esDesktopNuevo = window.matchMedia(
              "(min-width: 1024px)"
            ).matches;
            // Solo cambiar automáticamente si hubo un cambio de categoría (móvil <-> desktop)
            if (esDesktopNuevo !== esDesktop) {
              esDesktop = esDesktopNuevo;
              // Si hubo cambio de categoría, reiniciar al valor por defecto para esa categoría
              isExpanded = esDesktop;
              configurarEstadoInicial();
            }
          });
        });
      </script>
    </section>
  </div>
  <div class="dashboard">
    <div
      data-base-multibolsa-inmobiliaria
      data-token="{{ meteor_auth_token }}"
      data-user-avatar="{{ avatar }}"
      class="w-full"
    ></div>
    <!-- Cargamos el componente específico -->
    <script
      type="module"
      src="/assets/muroInmobiliarioSocial.js?t={{ tkt }}"
      defer
    ></script>
  </div>
</main>
