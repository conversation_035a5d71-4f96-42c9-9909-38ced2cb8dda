# 🖥️ Panel Layout - Documentación Técnica

> Sistema de layout principal con sidebar responsive y fixes de compatibilidad

## 📋 Tabla de Contenidos

- [🎯 Descripción General](#-descripción-general)
- [🏗️ Arquitectura del Layout](#️-arquitectura-del-layout)
- [🔧 Componentes del Sistema](#-componentes-del-sistema)
- [🐛 Layout Fixes](#-layout-fixes)
- [📱 Responsive Design](#-responsive-design)
- [💡 Ejemplos de Uso](#-ejemplos-de-uso)

---

## 🎯 Descripción General

### ¿Qué es el Panel Layout?

El **Panel Layout** es el sistema de layout principal que proporciona la estructura base para todas las páginas del panel administrativo. Incluye un sidebar navegable, área de contenido principal y manejo responsive para dispositivos móviles.

### Características Principales

- ✅ **Sidebar responsive** con toggle para móvil
- ✅ **Layout h-screen** con scroll interno
- ✅ **Overlay para móviles** con cierre automático
- ✅ **Fixes de compatibilidad** para componentes Vue.js
- ✅ **Estados persistentes** del sidebar
- ✅ **Iconografía consistente** con Ionicons

---

## 🏗️ Arquitectura del Layout

### Estructura de Archivos

```
src/entries/panel/
├── main.js                 # 🎯 Entry point principal
├── script.js               # 🔧 Lógica del layout y fixes
├── README.md               # 📖 Esta documentación
└── ../../partials/
    └── sidebar.html        # 🧩 Template del sidebar
```

### HTML Base (panel.html)

```html
<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Panel Administrativo</title>
    <script
      type="module"
      crossorigin
      src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"
    ></script>
    <link rel="stylesheet" href="dist/assets/panel.css" />
  </head>
  <body class="bg-gray-50">
    <!-- Layout principal con h-screen -->
    <div class="relative flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <div id="sidebar" class="sidebar-transition">
        <!-- Contenido del sidebar -->
      </div>

      <!-- Overlay para móviles -->
      <div id="sidebar-overlay" class="overlay-mobile"></div>

      <!-- Contenido principal -->
      <div class="flex flex-col flex-1 overflow-hidden">
        <!-- Header con toggle -->
        <header class="header-layout">
          <button id="sidebar-toggle" class="sidebar-toggle-btn">
            <ion-icon id="sidebar-toggle-icon-1" name="menu-outline"></ion-icon>
            <ion-icon
              id="sidebar-toggle-icon-2"
              name="close-outline"
              class="hidden"
            ></ion-icon>
          </button>
        </header>

        <!-- Área de contenido con scroll -->
        <main id="main-content" class="flex-1 overflow-y-auto">
          <!-- Contenido dinámico -->
        </main>
      </div>
    </div>

    <script src="dist/assets/panel.js"></script>
  </body>
</html>
```

---

## 🔧 Componentes del Sistema

### 1. **Sidebar Responsive**

```javascript
// Estados del sidebar
const sidebar = document.getElementById("sidebar");
const sidebarToggle = document.getElementById("sidebar-toggle");
const closeSidebar = document.getElementById("close-sidebar");
const overlay = document.getElementById("sidebar-overlay");

// Inicializar sidebar en estado visible (desktop)
sidebar.style.width = "16rem"; // 64 en Tailwind = 16rem

// Función para detectar dispositivo móvil
function isMobile() {
  return window.innerWidth < 768; // md breakpoint de Tailwind
}

// Toggle del sidebar
function toggleSidebar() {
  const isOpen = sidebar.style.width === "16rem";

  if (isMobile()) {
    // Comportamiento móvil: overlay
    if (isOpen) {
      sidebar.style.width = "0";
      overlay.classList.add("hidden");
    } else {
      sidebar.style.width = "16rem";
      overlay.classList.remove("hidden");
    }
  } else {
    // Comportamiento desktop: resize
    sidebar.style.width = isOpen ? "0" : "16rem";
  }

  updateToggleIcons(!isOpen);
}
```

### 2. **Gestión de Iconos**

```javascript
function updateToggleIcons(isOpen) {
  const icon1 = document.getElementById("sidebar-toggle-icon-1"); // menu
  const icon2 = document.getElementById("sidebar-toggle-icon-2"); // close

  if (isOpen) {
    icon1.classList.add("hidden");
    icon2.classList.remove("hidden");
  } else {
    icon1.classList.remove("hidden");
    icon2.classList.add("hidden");
  }
}
```

### 3. **Manejo de Resize**

```javascript
// Ajustar comportamiento en cambio de tamaño
window.addEventListener("resize", () => {
  if (!isMobile() && sidebar.style.width === "0") {
    // En desktop, mostrar sidebar si estaba oculto
    sidebar.style.width = "16rem";
    overlay.classList.add("hidden");
    updateToggleIcons(true);
  } else if (isMobile() && sidebar.style.width === "16rem") {
    // En móvil, ocultar sidebar si estaba visible
    overlay.classList.add("hidden");
  }
});
```

---

## 🐛 Layout Fixes

### Fix de h-screen con Componentes Vue.js

**Problema identificado:** Al hacer clic en items de socios en componentes Vue.js, el layout con `h-screen` se descomponía, causando problemas de altura y scroll.

#### Solución Implementada

```javascript
// SOLUCIÓN: Función para forzar recálculo del layout después de clicks en socios
function forceLayoutRecalculation() {
  console.log("forceLayoutRecalculation");

  const mainContainer = document.querySelector(".relative.flex.h-screen");
  const mainContent = document.getElementById("main-content");

  if (mainContainer && mainContent) {
    // Guardar posición del scroll antes del recálculo
    const scrollPosition = mainContent.scrollTop;

    // Forzar reflow quitando y volviendo a poner la clase h-screen
    mainContainer.classList.remove("h-screen");

    // Usar requestAnimationFrame para asegurar que el cambio se procese
    requestAnimationFrame(() => {
      mainContainer.classList.add("h-screen");

      // Segundo requestAnimationFrame para restaurar scroll después del recálculo
      requestAnimationFrame(() => {
        mainContent.scrollTop = scrollPosition; // Restaurar posición del scroll
      });
    });
  }
}

// Event listener específico para clicks en items de socios
document.addEventListener("click", function (event) {
  // Detectar si el click fue específicamente en un item de socio del selector
  const socioItemElement = event.target.closest(".socio-item-selector");

  if (socioItemElement) {
    console.log("Click detectado en item de socio:", socioItemElement);
    // Pequeño delay para permitir que el DOM se actualice primero
    setTimeout(forceLayoutRecalculation, 10);
  }
});
```

#### ¿Por qué funciona esta solución?

1. **Detección específica**: Solo se activa en clicks de elementos con clase `.socio-item-selector`
2. **Preservación del scroll**: Guarda y restaura la posición del scroll
3. **Recálculo forzado**: Quita y vuelve a poner `h-screen` para forzar reflow
4. **Timing correcto**: Usa `requestAnimationFrame` para sincronizar con el navegador

#### Componentes Afectados

- ✅ **SelectorSocios.vue** - Agregada clase `socio-item-selector`
- ❌ **NO afecta** otros elementos con `cursor-pointer`
- ❌ **NO afecta** toggles público/privado

---

## 📱 Responsive Design

### Breakpoints y Comportamientos

| Dispositivo | Ancho          | Comportamiento Sidebar      |
| ----------- | -------------- | --------------------------- |
| **Mobile**  | < 768px        | Overlay con backdrop        |
| **Tablet**  | 768px - 1024px | Collapsible lateral         |
| **Desktop** | > 1024px       | Siempre visible por defecto |

### CSS Classes

```css
/* Sidebar base */
.sidebar-transition {
  @apply fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-all duration-300 ease-in-out;
}

/* Overlay móvil */
.overlay-mobile {
  @apply fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300 ease-in-out md:hidden;
}

/* Header layout */
.header-layout {
  @apply flex items-center justify-between px-4 py-3 bg-white border-b border-gray-200;
}

/* Toggle button */
.sidebar-toggle-btn {
  @apply p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}
```

### Estados Responsive

```javascript
// Media query listener para cambios de breakpoint
const mediaQuery = window.matchMedia("(min-width: 768px)");

mediaQuery.addListener((e) => {
  if (e.matches) {
    // Desktop: mostrar sidebar, ocultar overlay
    sidebar.style.width = "16rem";
    overlay.classList.add("hidden");
    updateToggleIcons(true);
  } else {
    // Mobile: ocultar sidebar por defecto
    sidebar.style.width = "0";
    overlay.classList.add("hidden");
    updateToggleIcons(false);
  }
});
```

---

## 💡 Ejemplos de Uso

### Integración Básica

```html
<!-- Estructura mínima requerida -->
<div class="relative flex h-screen overflow-hidden">
  <div id="sidebar" class="sidebar-transition">
    <!-- Contenido del sidebar -->
  </div>

  <div id="sidebar-overlay" class="hidden overlay-mobile"></div>

  <div class="flex flex-col flex-1 overflow-hidden">
    <header class="header-layout">
      <button id="sidebar-toggle" class="sidebar-toggle-btn">
        <ion-icon id="sidebar-toggle-icon-1" name="menu-outline"></ion-icon>
        <ion-icon
          id="sidebar-toggle-icon-2"
          name="close-outline"
          class="hidden"
        ></ion-icon>
      </button>
    </header>

    <main id="main-content" class="flex-1 overflow-y-auto">
      <!-- Tu contenido aquí -->
    </main>
  </div>
</div>

<!-- Script requerido -->
<script src="dist/assets/panel.js"></script>
```

### Personalización del Sidebar

```javascript
// Personalizar ancho del sidebar
const SIDEBAR_WIDTH = "20rem"; // En lugar de 16rem por defecto

// Personalizar breakpoint móvil
function isMobile() {
  return window.innerWidth < 1024; // lg breakpoint en lugar de md
}

// Agregar animaciones personalizadas
sidebar.style.transition = "width 0.3s cubic-bezier(0.4, 0, 0.2, 1)";
```

### Integración con Componentes Vue.js

```javascript
// Para componentes que pueden afectar el layout
document.addEventListener("DOMContentLoaded", function () {
  // Inicializar layout
  initializePanelLayout();

  // Configurar fix para componentes Vue.js
  setupVueComponentsFix();
});

function setupVueComponentsFix() {
  // Observar cambios en componentes Vue que puedan afectar el layout
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
        // Verificar si se agregaron componentes Vue.js
        const hasVueComponents = Array.from(mutation.addedNodes).some(
          (node) =>
            node.nodeType === 1 &&
            node.querySelector &&
            node.querySelector("[data-v-]") // Detectar componentes Vue.js
        );

        if (hasVueComponents) {
          setTimeout(forceLayoutRecalculation, 50);
        }
      }
    });
  });

  observer.observe(document.getElementById("main-content"), {
    childList: true,
    subtree: true,
  });
}
```

---

## 🔧 Desarrollo y Mantenimiento

### Scripts de Build

```bash
# Desarrollo
npm run build

# Producción
npm run prod

# Testing del layout
npm run preview
```

### Testing del Layout

```javascript
// Verificar inicialización
console.log("Panel layout initialized:", {
  sidebar: document.getElementById("sidebar"),
  overlay: document.getElementById("sidebar-overlay"),
  mainContent: document.getElementById("main-content"),
});

// Probar responsive
window.dispatchEvent(new Event("resize"));

// Probar fix de h-screen
const testElement = document.createElement("div");
testElement.className = "socio-item-selector";
testElement.click(); // Debería activar el fix
```

### Debugging

```javascript
// Verificar estado del sidebar
console.log("Sidebar state:", {
  width: sidebar.style.width,
  isMobile: isMobile(),
  overlayVisible: !overlay.classList.contains("hidden"),
});

// Verificar fix de layout
console.log(
  "Layout fix active:",
  typeof forceLayoutRecalculation === "function"
);

// Monitorear clicks en socios
document.addEventListener("click", (e) => {
  if (e.target.closest(".socio-item-selector")) {
    console.log("Socio item clicked, layout fix triggered");
  }
});
```

---

## 📚 Referencias

- [README Principal](../../../README.md)
- [SelectorSocios Docs](../../components/selector-socios-wrapper/SELECTOR-SOCIOS-DOCS.md)
- [Tailwind CSS](https://tailwindcss.com/)
- [Ionicons](https://ionic.io/ionicons)
