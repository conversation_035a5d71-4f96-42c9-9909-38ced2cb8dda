// Definición de las opciones del menú
const menuItems = [
  {
    name: "Inicio",
    href: "/",
    target: "submenu-inicio",
    submenu: [
      {
        name: "Multibolsa Inmobiliaria",
        href: "/",
      },
    ],
  },
  {
    name: "Inmuebles",
    href: "/inmueble.php",
    target: "submenu-inmuebles",
    submenu: [
      {
        name: "Registrar / Editar",
        href: "/inmueble.php",
      },
      {
        name: "Listado",
        href: "/verinmuebles.php",
      },
      {
        name: "Personalizar ficha",
        href: "/ficha_dinamica.php",
      },
    ],
  },
  {
    name: "Oficina",
    href: "/mioficina.php",
    target: "submenu-oficina",
    submenu: [
      {
        name: "Citas solicitadas",
        href: "/vercitas.php",
      },
      {
        name: "Preguntas hechas",
        href: "/verpreguntas.php",
      },
      {
        name: "Reportes de visitas",
        href: "/reportevisita.php",
      },
      {
        name: "Contratos",
        href: "/contratos.php",
      },
      {
        name: "Contactos",
        href: "/contactos.php",
      },
      {
        name: "Asesores",
        href: "/asesores.php",
      },
    ],
  },
  {
    name: "Sitio Web",
    href: "/misitioweb.php",
    target: "submenu-sitio-web",
    submenu: [
      {
        name: "Vínculos",
        href: "/vinculos.php",
      },
      {
        name: "Temas",
        href: "/temas.php",
      },
      {
        name: "Configuración",
        href: "/conf_inmuebles.php",
      },
      {
        name: "Estadísticas",
        href: "#",
      },
    ],
  },
  {
    name: "Inmobiliaria",
    href: "/config.php",
    target: "submenu-inmobiliaria",
    submenu: [
      {
        name: "Datos y contraseña",
        href: "/datosinmobiliaria.php",
      },
      {
        name: "Sucursales",
        href: "/sucursales.php",
      },
    ],
  },
  {
    name: "Salir",
    href: "/salir.php",
    target: "submenu-salir",
    submenu: [
      {
        name: "Cierra la sesión actual y regresa al login",
        href: "/salir.php",
      },
    ],
  },
];

/**
 * Inicializa y configura el menú horizontal y sus submenús
 */
function initializeHorizontalMenu() {
  const mainMenu = document.getElementById("main-menu");
  const mobileMenu = document.getElementById("mobile-menu");
  const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
  const submenusContainer = document.getElementById("submenus");
  const submenusContent = submenusContainer?.querySelector(".pl-8.pr-4");

  // Limpiar contenido actual
  if (mainMenu) mainMenu.innerHTML = "";
  if (mobileMenu) mobileMenu.innerHTML = "";
  if (submenusContent) submenusContent.innerHTML = "";

  // Generar menú principal
  menuItems.forEach((item) => {
    // Crear elemento para menú desktop
    if (mainMenu) {
      const menuLink = document.createElement("a");
      menuLink.href = item.href;
      menuLink.className =
        "px-4 py-2 text-mulbin-100 hover:text-white hover:bg-mulbin-400 menu-item";
      menuLink.setAttribute("data-target", item.target);
      menuLink.textContent = item.name;
      mainMenu.appendChild(menuLink);
    }

    // Crear elemento para menú móvil
    if (mobileMenu) {
      const mobileLink = document.createElement("a");
      mobileLink.href = item.href;
      mobileLink.className = "block px-2 py-2 text-mulbin-100 hover:text-white";
      mobileLink.textContent = item.name;
      mobileMenu.appendChild(mobileLink);
    }

    // Generar submenú
    if (item.submenu && item.submenu.length > 0 && submenusContent) {
      // Crear el contenedor del submenú
      const submenuDiv = document.createElement("div");
      submenuDiv.id = item.target;
      submenuDiv.className = "hidden py-1 submenu";

      // Crear el contenedor flex para los enlaces
      const flexContainer = document.createElement("div");
      flexContainer.className = "flex space-x-6";

      // Añadir los enlaces del submenú
      item.submenu.forEach((subItem) => {
        const subLink = document.createElement("a");
        subLink.href = subItem.href;
        subLink.className = "text-sm text-mulbin-800 hover:text-mulbin-500";
        subLink.textContent = subItem.name;
        flexContainer.appendChild(subLink);
      });

      // Añadir el flex container al submenú
      submenuDiv.appendChild(flexContainer);

      // Añadir el submenú al contenedor principal
      submenusContent.appendChild(submenuDiv);
    }
  });

  // Configurar evento para alternar menú móvil
  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener("click", () => {
      mobileMenu.classList.toggle("hidden");
    });
  }

  // Configurar eventos para los submenús
  setupSubmenuHandlers();
}

/**
 * Configura los manejadores de eventos para los submenús
 */
function setupSubmenuHandlers() {
  const menuItems = document.querySelectorAll(".menu-item");
  const submenus = document.querySelectorAll(".submenu");

  // Función para mostrar un submenú específico
  function showSubmenu(submenuId) {
    // Ocultar todos los submenús primero
    submenus.forEach((submenu) => {
      submenu.classList.add("hidden");
    });

    // Mostrar el submenú solicitado
    const targetSubmenu = document.getElementById(submenuId);
    if (targetSubmenu) {
      targetSubmenu.classList.remove("hidden");
    }

    // Actualizar clase active en elementos del menú
    menuItems.forEach((item) => {
      if (item.dataset.target === submenuId) {
        item.classList.add("active");
      } else {
        item.classList.remove("active");
      }
    });
  }

  // Configurar eventos de hover para los elementos del menú
  menuItems.forEach((item) => {
    // Mostrar submenú al pasar el mouse
    item.addEventListener("mouseenter", function () {
      showSubmenu(this.dataset.target);
    });
  });

  // Mostrar el primer submenú por defecto si hay elementos de menú
  if (menuItems.length > 0) {
    showSubmenu(menuItems[0].dataset.target);
  }
}

// Exportar funciones y datos para su uso en otros archivos
export { menuItems, initializeHorizontalMenu, setupSubmenuHandlers };

// Inicializar el menú al cargar el documento
document.addEventListener("DOMContentLoaded", initializeHorizontalMenu);
