<?php
setlocale(LC_ALL, "es_MX");

include_once("languages/esp.php");
include_once("funciones.php");
include_once("verifica_pago.php");
// include_once("theme.php");
@mysql_select_db(DB_SI);
cm_valida_usuario('ADMIN');
cm_rec_permisos();

// Al llamar a esta función el programa solo podrá se ejecutado con el permiso adecuado
cm_permiso('p_contesta_preguntas');

/*
    Clases a usar
*/
include_once("clases/images.inc.php");
$images = new images();
/*
    / Clases a usar
*/

//////////////
// Recupero el encabezado y lo despliego, también defino variables para socios AMPI
$plantilla = cm_extrae_plantilla_mto();
include_once("enc_mto.php");


//////////////
// Recupero las sucursales y las pongo en el arreglo $sucursales[id]
$consulta1 = mysqlQuery("SELECT id, nombre FROM sucursales WHERE (contrato='$config[contrato]')", $db_link);
$sucursales = array();
while ($v1 = mysql_fetch_array($consulta1)) {
    if ($v1[id] > 0) {
        $sucursales[$v1[id]] = $v1[nombre];
    }
}


/////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////  O P C I Ó N   P R I N C I P A L /////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////
// En caso de la primer página
if (trim($id) == '' && !isset($clave)) {
    include __DIR__ . '/oficina/preguntas/verpreguntas_sub3.v5.php';
} //

//////////////  O P C I Ó N   P R I N C I P A L
// En caso de la primer página
elseif (isset($clave) && !isset($sucursal)) {

    $v1 = explode('---z24charly---', $clave);
    $clave_sistema = $v1[0];
    $claveprop = $v1[1];
    $clave = urlencode($clave);

    $sucursal = $v1[2];
    if ($sucursal > 0) {
        $consulta1 = mysqlQuery("SELECT nombre FROM sucursales WHERE (contrato='$config[contrato]' && id='$sucursal')", $db_link);
        $dsucursal = mysql_fetch_array($consulta1);
    }

    ?>
	<div id="overDiv" style="POSITION: absolute; Z-INDEX: 1"></div>
	<script language="JavaScript" src="jsc/overlib.js"></script>

	<div align="center">
		<center>
			<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#000080" width="770" id="AutoNumber1" bgcolor="#E6E6FF">
				<tr>
					<td width="100%">
						<p align="center"><b>
								<font face="Arial">Historial de preguntas del inmueble con clave: <a href="detallesnew.php?clave=<?php echo $clave_sistema; ?>" target="_blank"><?php echo $claveprop; ?></a></font>
							</b><?php

                                    if ($sucursal > 0) {
                                        echo "<br><font face=\"Arial\" size=\"2\" color=\"#339900\">$dsucursal[nombre]</font>";
                                    };

    ?></p>
						<table border="1" width="100%" id="table1" cellpadding="3" bgcolor="#FFFFFF" style="border-collapse: collapse" cellspacing="0">
							<tr>
								<td width="16%" align="center" bgcolor="#000080">
									<b>
										<font color="#FFFFFF" size="2" face="Arial"><A class="blanco" HREF="<?php echo "$PHP_SELF?orden=respondida&clave=$clave"; ?>">Estado de la pregunta</A></font>
									</b>
								</td>
								<td width="42%" align="center" bgcolor="#000080">
									<b>
										<font face="Arial" size="2" color="#FFFFFF"><A class="blanco" HREF="<?php echo "$PHP_SELF?orden=fecha&clave=$clave"; ?>">Fecha y hora de la pregunta</A></font>
									</b>
								</td>
								<td width="29%" align="center" bgcolor="#000080">
									<b>
										<font face="Arial" size="2" color="#FFFFFF"><A class="blanco" HREF="<?php echo "$PHP_SELF?orden=nombre&clave=$clave"; ?>">Quién hizo la pregunta</A></font>
									</b>
								</td>
								<td width="13%" align="center" bgcolor="#000080">
									&nbsp;</td>
							</tr>
							<?php

                            if ($orden == '' || $orden == 'respondida') {
                                $orden = 'fecha_hora_r';
                            } elseif ($orden == 'nombre') {
                                $orden = 'usuario, nombre, apellidos';
                            } elseif ($orden == 'fecha') {
                                $orden = 'fecha_hora_p';
                            }

    $consulta1 = mysqlQuery("SELECT * FROM preguntas WHERE (contrato='$config[contrato]' && clave_sistema=$clave_sistema) ORDER BY $orden, fecha_hora_p ASC", $db_link);

    $bgcolor = "#FFFFFF";
    while ($pregunta = mysql_fetch_array($consulta1)) {

        // Datos del usuario
        if ($pregunta[usuario] != '') {
            @mysql_select_db(DB_PW);
            $consulta3 = mysqlQuery("SELECT nombre, apellidos FROM clientes WHERE (usuario='$pregunta[usuario]')", $db_link);
            $visitante = mysql_fetch_array($consulta3);
            @mysql_select_db(DB_SI);
            $nombre = $visitante[nombre];
            $apellidos = $visitante[apellidos];
        } else {
            $nombre = $pregunta[nombre];
            $apellidos = $pregunta[apellidos];
        }

        $pregunta[pregunta] = cm_quita_saltos($pregunta[pregunta]);

        ?>
								<tr bgcolor="<?php echo $bgcolor; ?>" onmouseout="nd(); return true;" onmouseover="drs('<?php echo $pregunta[pregunta]; ?>'); return true;">
									<td width="16%" align="center">
										<font face="Arial" size="2"><?php

                                                if ($pregunta[fecha_hora_r] == '0000-00-00 00:00:00') {
                                                    echo '<b>SIN RESPONDER</b>';
                                                } else {
                                                    echo 'RESPONDIDA';
                                                }

        ?></font>
									</td>
									<td width="42%" align="center">
										<font face="Arial" size="2"><?php echo cm_formato_fecha($pregunta[fecha_hora_p], 3); ?></font>
										<?php

                                        // En caso de haber recibido copia un asesor
                                        if ($pregunta['cc_asesor'] > 0) {
                                            $consulta2 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (id='$pregunta[cc_asesor]')", $db_link);
                                            $asesor = mysql_fetch_array($consulta2);
                                            if (mysql_num_rows($consulta2) == 1) {
                                                echo "<br><span class=\"observaciones\">CC: <B>$asesor[nombre] $asesor[apellidos]</B></span>";
                                            }
                                        }	// if

        ?>
									</td>
									<td width="29%" align="center">
										<font face="Arial" size="2"><?php echo "$nombre $apellidos"; ?></font>
									</td>
									<td width="13%" align="center">
										<font face="Arial" size="2"><a href="<?php echo "$PHP_SELF?id=$pregunta[numero]&sucursal=$sucursal"; ?>">Ver pregunta</a></font>
									</td>
								</tr>
							<?php

                                if ($bgcolor == '#FFFFFF') {
                                    $bgcolor = '#F0F0F0';
                                } else {
                                    $bgcolor = '#FFFFFF';
                                }
    }

    ?>
						</table>
					</td>
				</tr>
				<tr>
					<td width="100%" bgcolor="#FFFFFF" align="center">
						<p align="center">
							<?php echo "<font face=\"Arial\" size=\"2\"><B><A HREF=\"$PHP_SELF?contestadas=No\">Ver todas las preguntas pendientes de respuesta</A></B></font>"; ?>
						</p>
					</td>
				</tr>
				<tr>
					<td width="100%" bgcolor="#FFFFFF">
						<p align="center">
							<?php echo "<font face=\"Arial\" size=\"2\"><B><A HREF=\"$PHP_SELF?contestadas=Si\">Ver todas las preguntas ya contestadas</A></B></font>"; ?>
						</p>
					</td>
				</tr>
			</table>
		</center>
	</div>

	<?php


    //////////////  O P C I Ó N   P R I N C I P A L
    // En caso de presentar una pregunta para responder
} elseif (isset($id) && !isset($que_hacer) && !isset($respuesta)) {

    if ($publicar == 'Si' || $publicar == 'No') {
        mysqlQuery("UPDATE preguntas SET publicar='$publicar' WHERE (contrato='$config[contrato]' && numero='$id') LIMIT 1", $db_link);
    }

    // Recupero los datos de la pregunta
    $consulta1 = mysqlQuery("SELECT * FROM preguntas WHERE (contrato='$config[contrato]' && numero='$id')", $db_link);

    if (mysql_num_rows($consulta1) > 0) {

        $pregunta = mysql_fetch_array($consulta1);

        // Recupero los datos de la propiedad en cuestión
        $consulta1 = mysqlQuery("SELECT claveprop FROM propiedades WHERE (clave_sistema='$pregunta[clave_sistema]')", $db_link);
        $propiedad = mysql_fetch_array($consulta1);

        // Si es un usuario registrado el que relizó la pregunta se toman los datos de la base de usuarios registrados
        if (trim($pregunta[usuario]) != '') {
            @mysql_select_db(DB_PW);
            $consulta1 = mysqlQuery("SELECT * FROM clientes WHERE (usuario='$pregunta[usuario]')", $db_link);
            $visitante = mysql_fetch_array($consulta1);
            @mysql_select_db(DB_SI);

            $nombre_completo = trim("$visitante[nombre] $visitante[apellidos]");
            $nombre = $visitante[nombre];
            $apellidos = $visitante[apellidos];
            $telefono = $visitante[telefono];
            $email = $visitante[email];

            if ($visitante[colonia] != '') {
                ', $visitante[colonia]';
            }
            if ($visitante[codigo_postal] != '') {
                ', C.P. $visitante[codigo_postal]';
            }
        } else {
            $nombre_completo = "$pregunta[nombre] $pregunta[apellidos]";
            $nombre = $pregunta[nombre];
            $apellidos = $pregunta[apellidos];
            $telefono = $pregunta[telefono];
            $email = $pregunta[email];
        }

        include __DIR__ . '/oficina/preguntas/verpreguntas_sub1.php';

        // En caso de que la pregunta aún no esté respondida
        if ($pregunta[fecha_hora_r] == '0000-00-00 00:00:00') {

            ?>

			<table border="0" width="100%" id="table6" cellspacing="0" cellpadding="0">
				<tr>
					<td width="182" align="right" valign="top">
						<font size="2" face="Arial">Que deseas hacer:&nbsp;&nbsp;&nbsp;&nbsp;
						</font>
					</td>
					<td>
						<form method="GET" action="<?php echo $PHP_SELF; ?>" onsubmit="return verifica();" name="Formulario1">
							<INPUT TYPE="hidden" name="id" value="<?php echo $id ?>">
							<INPUT TYPE="hidden" name="email" value="<?php echo $email ?>">
							<INPUT TYPE="hidden" name="sucursal" value="<?php echo $sucursal; ?>">
							<p><select size="1" name="que_hacer">
									<option value="contestar">RESPONDER A LA PREGUNTA</option>
									<option value="mail">Enviar un e-mail externo al sistema y registrar pregunta como contestada</option>
									<option value="borrar">Eliminar pregunta sin contestar</option>
								</select><br>
								<input type="submit" value="Continuar -&gt;">
							</p>
						</form>
					</td>
				</tr>
			</table>

		<?php

        } else {

            ?>
			<p align="center">
				<font face="Arial" size="3" color="#CC6600"><b>La pregunta ya fué respondida el <?php echo cm_formato_fecha($pregunta[fecha_hora_r], 3); ?></b></font><br>
				<font face="Arial" size="2" color="#CC6600">
					<?php

                        if ($pregunta[publicar] == 'Si' && $pregunta[tipo_respuesta] == 'sistema') {
                            echo "La pregunta está publicada con la respuesta en el sitio web, si desea quitarla del sitio web haga <A HREF=\"$PHP_SELF?id=$id&publicar=No\">clic aquí</A>";
                        } elseif ($pregunta[publicar] == 'No' && $pregunta[tipo_respuesta] == 'sistema') {
                            echo "Para publicar esta pregunta con la respuesta en el sitio web haga <A HREF=\"$PHP_SELF?id=$id&publicar=Si\">clic aquí</A>";
                        } elseif ($pregunta[tipo_respuesta] == 'mail') {
                            echo "Se respondió a esta pregunta por medio de un e-mail externo";
                        }

            if ($pregunta[asesor] > 0) {
                $consulta1 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]' && id='$pregunta[asesor]')", $db_link);
                if (mysql_num_rows($consulta1) == 0) {
                    echo '<br>El asesor que respondió a esta pregunta ya fué dado de baja';
                } else {
                    $row = mysql_fetch_array($consulta1);
                    echo "<br>Asesor que respondió esta pregunta: <B>$row[nombre] $row[apellidos]</B>";
                }
            }

            ?>
				</font>
			</p>
			<table border="0" width="100%" id="table6" cellpadding="0" style="border-collapse: collapse">
				<tr>
					<td width="153" align="right" valign="top">
						<font face="Arial" size="2">Respuesta:&nbsp;&nbsp;&nbsp;&nbsp;
						</font>
					</td>
					<td>
						<font face="Arial" size="2">
							<?php echo $pregunta[respuesta]; ?></font>
					</td>
				</tr>
			</table>


		<?php

        }

        include __DIR__ . '/oficina/preguntas/verpreguntas_sub2.php';
    } else {

        ?>

		<p align="center">&nbsp;</p>
		<p align="center"><b>
				<font size="2" face="Arial">No existe la pregunta solicitada</font>
			</b></p>
		<p align="center">&nbsp;</p>

	<?php

    }


    //////////////  O P C I Ó N   P R I N C I P A L
    // En caso de responder por medio del mismo sistema
} elseif ($que_hacer == 'contestar') {

    // Recupero los datos de la pregunta
    $consulta1 = mysqlQuery("SELECT * FROM preguntas WHERE (contrato='$config[contrato]' && numero='$id')", $db_link);
    $pregunta = mysql_fetch_array($consulta1);

    // No se puede volver a responder una pregunta ya contestada
    if ($pregunta[fecha_hora_r] != '0000-00-00 00:00:00') {
        echo "Acceso inválido";
        mysql_close($db_link);
        echo $plantilla[pie];
        exit;
    }

    // Recupero los datos de la propiedad en cuestión
    $consulta1 = mysqlQuery("SELECT claveprop FROM propiedades WHERE (clave_sistema='$pregunta[clave_sistema]')", $db_link);
    $propiedad = mysql_fetch_array($consulta1);

    // Si es un usuario registrado el que relizó la pregunta se toman los datos de la base de usuarios registrados
    if (trim($pregunta[usuario]) != '') {
        @mysql_select_db(DB_PW);
        $consulta1 = mysqlQuery("SELECT * FROM clientes WHERE (usuario='$pregunta[usuario]')", $db_link);
        $visitante = mysql_fetch_array($consulta1);
        @mysql_select_db(DB_SI);

        $nombre_completo = trim("$visitante[nombre] $visitante[apellidos]");
        $nombre = $visitante[nombre];
        $apellidos = $visitante[apellidos];
        $telefono = $visitante[telefono];
        $email = $visitante[email];

        if ($visitante[colonia] != '') {
            ', $visitante[colonia]';
        }
        if ($visitante[codigo_postal] != '') {
            ', C.P. $visitante[codigo_postal]';
        }
    } else {
        $nombre_completo = "$pregunta[nombre] $pregunta[apellidos]";
        $nombre = $pregunta[nombre];
        $apellidos = $pregunta[apellidos];
        $telefono = $pregunta[telefono];
        $email = $pregunta[email];
    }

    include __DIR__ . '/oficina/preguntas/verpreguntas_sub1.php';

    ?>

	<script language="Javascript1.2">
		<!-- // load htmlarea
		var cmvar = "<?php echo time(); ?>";
		_editor_url = "jsc/"; // URL to htmlarea files
		var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
		if (navigator.userAgent.indexOf('Mac') >= 0) {
			win_ie_ver = 0;
		}
		if (navigator.userAgent.indexOf('Windows CE') >= 0) {
			win_ie_ver = 0;
		}
		if (navigator.userAgent.indexOf('Opera') >= 0) {
			win_ie_ver = 0;
		}
		if (win_ie_ver >= 5.5) {
			document.write('<scr' + 'ipt src="' + _editor_url + 'editor_inserta_casa.js"');
			document.write(' language="Javascript1.2"></scr' + 'ipt>');
		} else {
			document.write('<scr' + 'ipt>function editor_generate() { return false; }</scr' + 'ipt>');
		}
		// 
		-->
	</script>

	<form method="POST" action="<?php echo $PHP_SELF; ?>">
		<table border="0" width="100%" id="table6" cellpadding="0" style="border-collapse: collapse">
			<tr>
				<td width="153" align="right" valign="top" bgcolor="#E1E1E1">
					<font face="Arial" size="2">Asunto:&nbsp;&nbsp;&nbsp;&nbsp;
					</font>
				</td>
				<td bgcolor="#E1E1E1">
					<input type="text" name="asunto" size="70" value="<?php echo "Re: Acerca del inmueble con clave $propiedad[claveprop] en $config[dominio]"; ?>">
				</td>
			</tr>
			<tr>
				<td width="153" align="right" valign="top" bgcolor="#E1E1E1">
					<font face="Arial" size="2">Respuesta:&nbsp;&nbsp;&nbsp;&nbsp;
					</font>
				</td>
				<td bgcolor="#E1E1E1">
					<INPUT TYPE="hidden" name="id" value="<?php echo $id ?>">
					<INPUT TYPE="hidden" name="email" value="<?php echo $email; ?>">
					<INPUT TYPE="hidden" name="clave" value="<?php echo $pregunta[clave_sistema]; ?>">
					<INPUT TYPE="hidden" name="sucursal" value="<?php echo $sucursal; ?>">
					<textarea rows="8" name="respuesta" cols="69"></textarea><br>
					<font size="2" face="Arial">
						<input type="checkbox" name="publicar" value="Si">Publicar esta pregunta y repuesta en mi sitio web.
					</font><br>
					<script language="javascript1.2">
						<!-- //
						editor_generate('respuesta');
						// 
						-->
					</script>
					<input type="submit" value="Enviar respuesta"></p>
				</td>
			</tr>
		</table>
	</form>

<?php

    include __DIR__ . '/oficina/preguntas/verpreguntas_sub2.php';


    //////////////  O P C I Ó N   P R I N C I P A L
    // En caso de que esté definida la respuesta
} elseif (isset($respuesta)) {

    $fecha_hora_r = strftime("%Y-%m-%d %H:%M:%S", time());
    if ($publicar != 'Si') {
        $publicar = 'No';
    }

    mysqlQuery("UPDATE preguntas SET respuesta='$respuesta', publicar='$publicar', fecha_hora_r='$fecha_hora_r', tipo_respuesta='sistema', asesor='$cookie_asesor' WHERE (contrato='$config[contrato]' && numero='$id') LIMIT 1", $db_link);

    if ($sucursal > 0) {
        $consulta1 = mysqlQuery("SELECT email FROM sucursales WHERE (contrato='$config[contrato]' && id='$sucursal')", $db_link);
        $row = mysql_fetch_array($consulta1);
    }

    // Por cual cuenta saldrá el mensaje
    if ($sucursal > 0 && trim($row[email]) != '') {
        $config[correo_ventas] = $row[email];
    }
    if ($config[correo_ventas] == '') {
        $de = $cliente[email];
    } else {
        $de = $config[correo_ventas];
    }

    //para el envío en formato HTML
    $headers = "MIME-Version: 1.0\n";
    $headers .= "Content-type: text/html; charset=iso-8859-1\n";

    //dirección del remitente
    $headers .= "From: $de ($cliente[empresa])\n";

    // Envio el mensaje
    $sust = array(
        "\n" => "\r\n",
        '\"' => '"',
    );
    $respuesta = strtr($respuesta, $sust);
    mail($email, $asunto, $respuesta, $headers);

    ?>

	<p align="center">&nbsp;</p>
	<p align="center"><b>
			<font size="2" face="Arial">Se ha enviado la respuesta con éxito</font>
		</b></p>
	<p align="center">&nbsp;</p>

<?php


        //////////////  O P C I Ó N   P R I N C I P A L
        // En caso de responder por medio del mismo sistema
} elseif ($que_hacer == 'mail') {

    $fecha_hora_r = strftime("%Y-%m-%d %H:%M:%S", time());

    mysqlQuery("UPDATE preguntas SET fecha_hora_r='$fecha_hora_r', tipo_respuesta='mail', asesor='$cookie_asesor' WHERE (contrato='$config[contrato]' && numero='$id') LIMIT 1", $db_link);

    ?>

	<script language="javascript1.2">
		<!-- //
		window.open('mailto:<?php echo $email; ?>');
		// 
		-->
	</script>
	<p align="center">&nbsp;</p>
	<p align="center"><b>
			<font size="2" face="Arial">Se ha registrado como respondida la pregunta</font>
		</b></p>
	<p align="center">&nbsp;</p>

<?php


        //////////////  O P C I Ó N   P R I N C I P A L
        // En caso de borrar la pregunta sin responder
} elseif ($que_hacer == 'borrar') {

    mysqlQuery("DELETE FROM preguntas WHERE (contrato='$config[contrato]' && numero='$id') LIMIT 1", $db_link);

    ?>

	<p align="center">&nbsp;</p>
	<p align="center"><b>
			<font size="2" face="Arial">Se ha eliminado la pregunta</font>
		</b></p>
	<p align="center">&nbsp;</p>

<?php

}

$sql->close();

echo $mustache->render($plantilla['pie'], $render);
exit;
