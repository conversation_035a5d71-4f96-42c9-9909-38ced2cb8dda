window.campos_mostrar_todos = false;

$(document).ready(function() {
  $('#tipo').on('change', function() {
    if (campos_mostrar_todos) return;
    let tipo = $(this);
    let clase = 'ti_'+tipo.val();
    let tiSelected = document.querySelectorAll('.'+clase+'.ti_v');
    let tivElements = document.querySelectorAll('.ti_v:not(.'+clase+')');
    tiSelected.forEach((element) => {
      element.classList.remove('hidden');
    });
    tivElements.forEach((element) => {
      element.classList.add('hidden');
    });
  });
});
