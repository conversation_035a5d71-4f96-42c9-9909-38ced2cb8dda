$(document).ready(function () {
  // Al cambiar el país
  $("#pais").change(function () {
    var pais = $(this).val();

    $("#provincia").html('<option value="">-------</option>');
    $("#ciudad").html('<option value="">-------</option>');
    $("#colonia").html('<option value="">-------</option>');

    if (pais > 0) {
      // Si selecciona un país, hacemos la petición AJAX para obtener los estados
      $.ajax({
        type: "POST",
        url: "./aj/ap_inmueble.php",
        data: { pais: pais },
        dataType: "json",
        success: function (data) {
          // Llenamos el select de estados
          var opciones = '<option value="">selecciona...</option>';
          $.each(data, function (i, estado) {
            opciones +=
              '<option value="' +
              estado.id +
              '">' +
              estado.estado +
              "</option>";
          });
          $("#provincia").html(opciones);
          // Limpiamos los selects de ciudad y colonia
          $("#ciudad").html('<option value="">-------</option>');
          $("#colonia").html('<option value="">-------</option>');
        },
      });
    }
  });

  // Al cambiar el estado
  $("#provincia").change(function () {
    var estado = $(this).val();
    $("#ciudad").html('<option value="">-------</option>');
    $("#colonia").html('<option value="">-------</option>');
    if (estado > 0) {
      // Si selecciona un estado, hacemos la petición AJAX para obtener las ciudades
      $.ajax({
        type: "POST",
        url: "./aj/ap_inmueble.php",
        data: { estado: estado },
        dataType: "json",
        success: function (data) {
          // Llenamos el select de ciudades
          var opciones = '<option value="">selecciona...</option>';
          $.each(data, function (i, ciudad) {
            opciones +=
              '<option value="' +
              ciudad.id +
              '">' +
              ciudad.ciudad +
              "</option>";
          });
          $("#ciudad").html(opciones);
          // Limpiamos el select de colonia
          $("#colonia").html('<option value="">-------</option>');
        },
      });
    }
  });

  // Al cambiar la ciudad
  $("#ciudad").change(function () {
    var ciudad = $(this).val();
    $("#colonia").html('<option value="">-------</option>');
    document.getElementById("e_colonia").innerHTML = "";
    if (ciudad > 0) {
      // Si selecciona una ciudad, hacemos la petición AJAX para obtener las colonias
      $.ajax({
        type: "POST",
        url: "./aj/ap_inmueble.php",
        data: { ciudad: ciudad },
        dataType: "json",
        success: function (data) {
          // Llenamos el select de colonias
          var opciones = '<option value="0">selecciona...</option>';
          $.each(data, function (i, colonia) {
            opciones +=
              '<option value="' +
              colonia.id +
              '">' +
              colonia.colonia +
              "</option>";
          });
          opciones += '<option value="especificar">especificar =&gt;</option>';
          $("#colonia").html(opciones);
        },
      });
    }
  });
});
