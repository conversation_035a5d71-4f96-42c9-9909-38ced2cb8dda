<?php
/*
Paso 1, es donde se muestra la primer forma para captura de información del inmueble
*/
if (!isset($plantilla)) {
    die('No se puede entrar a este programa directamente');
}

/**
 * RECUPERO LA INFO DEL INMUEBLE
 */
$query = "SELECT * FROM propiedades 
		WHERE contrato='{$config['contrato']}' AND claveprop='{$claveprop}'
		LIMIT 1";
$consulta1 =  $sql->query($query);

/* ESTOY EDITANDO INMUEBLE EXISTENTE */
if ($consulta1->num_rows >= 1) {
    $propiedad = $consulta1->fetch_assoc();
    $nuevo = 'No';

    // Permisos de ejecución de este programa
    if ($propiedad['aid'] != $cookie_asesor) {
        cm_permiso('p_edita_todos_los_inmuebles');
    } else {
        cm_permiso('p_registra_inmuebles');
    }

    /* TRANSICIÓN HACIA EL MANEJO DEL INMUEBLE POR MEDIO DE SU CLASS */
    $prop = new \Estate\Inmueble($propiedad['clave_sistema']);

    /* SE ESTÁ REGISTRANDO UN INMUEBLE NUEVO */
} else {

    /*
            Verifico la integridad de la clave de inmueble
        */
    $permitidos = "abcdefghijklmnñopqrstuvwxyzABCDEFGHIJKLMNÑOPQRSTUVWXYZ0123456789-_,.";
    for ($i = 0; $i < strlen($claveprop); $i++) {
        if (strpos($permitidos, substr($claveprop, $i, 1)) === false) {
            echo "<CENTER>La clave del inmueble tiene caracteres no válidos</CENTER>";

            // Desconectamos de la base de datos y terminamos el programa
            $sql->close();
            echo $plantilla['pie'];
            exit;
        }
    }
    /*
            / Verifico la integridad de la clave de inmueble
        */

    // Hago la verificación para ver si ha excedido el número máximo de inmuebles permitidos
    $consulta1 = $sql->query("SELECT claveprop FROM propiedades WHERE contrato='$config[contrato]'");
    if ($consulta1->num_rows >= $config['inmuebles']) {
        echo '<p align="center">Ha excedido el número de inmuebles de la licencia</p><p align="center">Póngase en contacto con su proveedor de SistemaInmobiliario.com para ampliar el número de inmuebles permitido<br><br>Gracias.</p>' . "<p align='center'>El número máximo de inmuebles es de: $config[inmuebles]</p>";
        echo $plantilla['pie'];

        // Desconectamos de la base de datos
        $sql->close();

        // Termina la ejecución del programa
        exit;
    }

    $nuevo = 'Si';

    // Permisos de ejecución de este programa
    cm_permiso('p_registra_inmuebles');
}

// En caso de venir con algún mensaje de error quiere decir que el usuario ya ha capturado información y es necesario desplegar la ya capturada
if (!empty($msg_error)) {

    $propiedad['pais'] = $pais;
    $propiedad['provincia'] = $provincia;
    $propiedad['ciudad'] = $ciudad;
    $propiedad['colonia'] = $colonia;
    $propiedad['muestra_colonia'] = $muestra_colonia;
    $propiedad['zona'] = $zona;
    $propiedad['tipo'] = $tipo;
    if ($expiracion == 'si expira') {
        $propiedad['status_web'] == 'publicado';
        if ($exp_mes < 10) {
            $exp_mes = "0$exp_mes";
        }
        if ($exp_dia < 10) {
            $exp_dia = "0$exp_dia";
        }
        $propiedad['fecha_expiracion'] = "$exp_agno-$exp_mes-$exp_dia";
    } else {
        $propiedad['status_web'] = $expiracion;
        $propiedad['fecha_expiracion'] = '0000-00-00';
    }
    $propiedad['enventa'] = $enventa;
    $propiedad['enrenta'] = $enrenta;
    $propiedad['endiaria'] = $endiaria;
    $propiedad['entraspaso'] = $entraspaso;
    $propiedad['precio_venta'] = $precio_venta;
    $propiedad['precio_renta'] = $precio_renta;
    $propiedad['precio_diaria'] = $precio_diaria;
    $propiedad['precio_traspaso'] = $precio_traspaso;
    $propiedad['moneda'] = $moneda;
    $propiedad['precio_por_metro'] = $precio_por_metro;
    $propiedad['en_resumen'] = $en_resumen;
    $propiedad['operacion_hecha'] = $operacion_hecha;
    $propiedad['anuncio_esp'] = $anuncio_esp;
    $propiedad['anuncio_ing'] = $anuncio_ing;
    $propiedad['anuncio_fra'] = $anuncio_fra;
    $propiedad['caract_esp'] = $caract_esp;
    $propiedad['caract_ing'] = $caract_ing;
    $propiedad['caract_fra'] = $caract_fra;
    $propiedad['keywords_esp'] = $keywords_esp;
    $propiedad['keywords_ing'] = $keywords_ing;
    $propiedad['keywords_fra'] = $keywords_fra;
}

$propiedad['CamposMC'] = json_decode($propiedad['CamposMC'], true);
foreach (array('Venta', 'Renta') as $key) {
    if (!isset($propiedad['CamposMC'][$key])) {
        $propiedad['CamposMC'][$key] = $key;
    }
}

//////////////
// Si la colonia donde se encuentra el inmueble no existe, así es que la creo
$propiedad['colonia'] = cm_trata_ciudad_colonia($propiedad['colonia']);
$propiedad['ciudad'] = cm_trata_ciudad_colonia($propiedad['ciudad']);

if ($propiedad['colonia'] != '' && $propiedad['ciudad'] != '' && $propiedad['provincia'] != '') {
    $consulta1 = $sql->query("SELECT colonia FROM colonias WHERE (contrato='{$config['contrato']}' AND colonia='{$propiedad['colonia']}' AND ciudad='{$propiedad['ciudad']}' AND estado='{$propiedad['provincia']}')");
    if (!$consulta1->num_rows) {
        $sql->query("INSERT INTO colonias (contrato, colonia, ciudad, estado, pais) VALUES ('{$config['contrato']}', '{$propiedad['colonia']}', '{$propiedad['ciudad']}', '{$propiedad['provincia']}', '{$propiedad['pais']}')");
    }
}
//////////////

?>
<script>
	var contaerror = 0;

	function verifica_longitud(valor, longitud, campo_cambia) {
		numero3 = valor.value.length;
		campo_cambia.value = longitud - numero3;
		return true;
	}

	function lTrim(sStr) {
		while (sStr.charAt(0) == " ")
			sStr = sStr.substr(1, sStr.length - 1);
		return sStr;
	}

	function rTrim(sStr) {
		while (sStr.charAt(sStr.length - 1) == " ")
			sStr = sStr.substr(0, sStr.length - 1);
		return sStr;
	}

	function allTrim(sStr) {
		return rTrim(lTrim(sStr));
	}

	function enableSelectsTiny() {
		document.getElementById('mce_editor_0_fontNameSelect').removeAttribute('disabled');
		document.getElementById('mce_editor_1_fontNameSelect').removeAttribute('disabled');
		document.getElementById('mce_editor_0_fontSizeSelect').removeAttribute('disabled');
		document.getElementById('mce_editor_1_fontSizeSelect').removeAttribute('disabled');
	}


	function chk_otra_colonia(campo) {
		if (campo.checked == true) {
			document.getElementById('h_otra_colonia').innerHTML = '<span class="observaciones">Mostrar en mi página web la Colonia como:<br><input type="text" name="muestra_colonia"  class="form-control" maxlength="30" onchange="return v_colonia(this);"></span>';
			document.Formulario1.muestra_colonia.focus();
		} else {
			document.getElementById('h_otra_colonia').innerHTML = '';
		}
	}

	function especificaColonia() {

		if (document.Formulario1.colonia.value == 'especificar') {
			document.getElementById('e_colonia').innerHTML = '<INPUT TYPE="text" NAME="esp_colonia" class="form-control" maxlength="70" onchange="return v_colonia(this);">';
			document.Formulario1.esp_colonia.focus();
		} else if (document.Formulario1.colonia.value == '0') {
			document.getElementById('e_colonia').innerHTML = '';
		} else {
			document.getElementById('e_colonia').innerHTML = '<INPUT TYPE="checkbox" NAME="otra_colonia" id="otra_colonia" onClick="chk_otra_colonia(this);"><label for="otra_colonia" class="text-9">Deseo que en mi página aparezca la colonia de otra forma</label><div id="h_otra_colonia"></div>';
		}
	}

	function queZona() {

		if (document.Formulario1.zona.value == '---otra---') {
			document.getElementById('e_zona').innerHTML = ' <INPUT TYPE="text" NAME="esp_zona" class="form-control" maxlength="30">';
			document.Formulario1.esp_zona.focus();
		} else {
			document.getElementById('e_zona').innerHTML = '';
		}
		document.getElementById('hMapaZonas').innerHTML = '<IMG SRC="mapas/' + mapas[document.Formulario1.zona.value] + '.gif" BORDER="0">';
	}

	function cambio_exp() {
		console.log(document.Formulario1.expiracion.value)
		if (document.Formulario1.expiracion.value == 'si expira') {
			document.getElementById('view_fecha_expira').classList.remove("hidden");
		} else {
			document.getElementById('view_fecha_expira').classList.add("hidden");
		}
	}

	function val_numerico(campo, dec) {
		var checkStr = campo.value;
		var allValid = true;
		var allNum = "";
		if (dec > 0) {
			var checkOK = "0123456789.";
			var msg = 'Teclee sólo dígitos sin comas o deje el campo en blanco.';
		} else {
			var checkOK = "0123456789";
			var msg = 'Teclee sólo dígitos sin puntos ni comas o deje el campo en blanco.';
		}
		for (i = 0; i < checkStr.length; i++) {
			ch = checkStr.charAt(i);
			for (j = 0; j < checkOK.length; j++)
				if (ch == checkOK.charAt(j)) break;
			if (j == checkOK.length) {
				allValid = false;
				break;
			}
			allNum += ch;
		}
		if (!allValid) {
			alert(msg);
			campo.focus();
			contaerror++;
			if (contaerror > 2) campo.value = "";
			return (false);
		}
		return (true);
	}

	function v_colonia(campo) {
		var checkOK = "0123456789-abcdefghijklmnñopqrstuvwxyzABCDEFGHIJKLMNÑOPQRSTUVWXYZáéíóúÁÉÍÓÚ ";
		var checkStr = campo.value;
		var allValid = true;
		var allNum = "";
		for (i = 0; i < checkStr.length; i++) {
			ch = checkStr.charAt(i);
			for (j = 0; j < checkOK.length; j++)
				if (ch == checkOK.charAt(j)) break;
			if (j == checkOK.length) {
				allValid = false;
				break;
			}
			allNum += ch;
		}
		if (!allValid) {
			alert('El valor tecleado tiene caracteres no válidos, por favor chécalos.');
			campo.focus();
			return (false);
		}
		return (true);
	}

	function Validando_Formulario(form) {
		var nclaveprop = document.getElementById('nclaveprop');
		if (nclaveprop.value.trim() !== '' && !val_claveprop(nclaveprop))
			return false;

		if (form.tipo.value == '') {
			alert('Debes seleccionar el tipo de inmueble a registrar ...');
			form.tipo.focus();
			return (false);
		}
		if (form.pais.value == '0') {
			alert('Debes seleccionar el País donde se encuentra el inmueble a registrar ...');
			form.pais.focus();
			return (false);
		}
		if (form.provincia.value == '0') {
			alert('Debes seleccionar el Estado donde se encuentra el inmueble a registrar ...');
			form.provincia.focus();
			return (false);
		}
		if (form.provincia.value == 'especificar') {
			if (allTrim(form.esp_provincia.value) == '') {
				alert('Debes especificar el Estado donde se encuentra el inmueble a registrar ...');
				form.esp_provincia.focus();
				return (false);
			}
		}
		if (form.ciudad.value == '0') {
			alert('Debes seleccionar la Ciudad donde se encuentra el inmueble a registrar ...');
			form.ciudad.focus();
			return (false);
		}
		if (form.ciudad.value == 'especificar') {
			if (allTrim(form.esp_ciudad.value) == '') {
				alert('Debes especificar la Ciudad donde se encuentra el inmueble a registrar ...');
				form.esp_ciudad.focus();
				return (false);
			}
		}
		if (form.colonia.value == '0') {
			alert('Debes seleccionar la Colonia donde se encuentra el inmueble a registrar ...');
			form.colonia.focus();
			return (false);
		}
		if (form.colonia.value == 'especificar') {
			if (allTrim(form.esp_colonia.value) == '') {
				alert('Debes especificar la Colonia donde se encuentra el inmueble a registrar ...');
				form.esp_colonia.focus();
				return (false);
			}
		}
		if (!val_numerico(form.codigo_postal)) {
			return (false);
		}
		document.Formulario1.action = "<?= $PHP_SELF ?>";
		AutorizaSalida = 'Si';
		return (true);
	}

	var AutorizaSalida = 'No';
</script>