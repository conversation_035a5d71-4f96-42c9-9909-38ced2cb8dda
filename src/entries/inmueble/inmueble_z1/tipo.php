<?php

$m_file = __DIR__ . '/../templates/tipo.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Recuperar tipos de inmuebles
$tipos_inmuebles_array = array();
$tipos_inmuebles = array();
$consulta1 = mysqlQuery("SELECT clave, tipo_esp FROM tipo_inmuebles", $db_link);
while ($campo = mysql_fetch_array($consulta1)) {
    if ($campo['clave'] == '00') {
        continue;
    }
    $tipos_inmuebles[] = $campo['clave'];
    $tipos_inmuebles_array[] = array(
        'clave' => $campo['clave'],
        'tipo_esp' => $campo['tipo_esp'],
        'selected' => ($campo['clave'] === $propiedad['tipo'])
    );
}

$m_data['tipos_inmuebles'] = $tipos_inmuebles_array;

// Usos de suelo - checkboxes
$m_data['residencial_checked'] = ($propiedad['residencial'] == 'Si');
$m_data['comercial_checked'] = ($propiedad['comercial'] == 'Si');
$m_data['industrial_checked'] = ($propiedad['industrial'] == 'Si');
$m_data['vacacional_checked'] = ($propiedad['vacacional'] == 'Si');

echo $mustache->render($m_template, $m_data);
