<?php

$m_file = __DIR__ . '/../templates/desarrollos.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Trato los desarrollos
$ades = array();

if ($msg_error == '') {
    $consulta1 = mysqlQuery($query = "
			SELECT rel_prop_des.* 
			FROM rel_prop_des, desarrollos 
			WHERE rel_prop_des.prop_clave_sistema='{$propiedad['clave_sistema']}' AND 
				rel_prop_des.des_clave_sistema=desarrollos.clave_sistema AND 
				desarrollos.contrato='{$config['contrato']}'
			", $db_link);
    while ($row = mysql_fetch_array($consulta1)) {
        $ades['desarrollo'] = 'checked';
        $ades['nombreprop'] = $propiedad['nombreprop'];
        $ades[$row['des_clave_sistema']] = 'checked';
    }
} else {
    if ($desarrollo == 'Si') {
        $ades['desarrollo'] = 'checked';
    }
    if (trim($nombreprop) != '') {
        $ades['nombreprop'] = $nombreprop;
    }
    $consulta1 = mysqlQuery("
			SELECT clave_sistema FROM desarrollos
			WHERE contrato='{$config['contrato']}'
			", $db_link);
    while ($row = mysql_fetch_array($consulta1)) {
        if (${"d_$row[clave_sistema]"} == 'Si') {
            $ades[$row['clave_sistema']] = 'checked';
        }
    }
}

// Verificar si hay desarrollos
$consulta1 = mysqlQuery("
		SELECT clave_sistema, nombredes 
		FROM desarrollos 
		WHERE contrato='{$config['contrato']}'
		", $db_link);

$hay_desarrollos = (mysql_num_rows($consulta1) > 0);
$desarrollos_array = array();

if ($hay_desarrollos) {
    while ($des = mysql_fetch_array($consulta1)) {
        $desarrollos_array[] = array(
            'clave_sistema' => $des['clave_sistema'],
            'nombredes' => $des['nombredes'],
            'checked' => (!empty($ades[$des['clave_sistema']]))
        );
    }
}

// Determinar qué mostrar
$mostrar_seccion_desarrollos = ($hay_desarrollos && count($ades) > 0 && $padesarrollo == 'Si');

$m_data['mostrar_seccion_desarrollos'] = $mostrar_seccion_desarrollos;

if ($mostrar_seccion_desarrollos) {
    $m_data['desarrollo_checked'] = (!empty($ades['desarrollo']));
    $m_data['tiene_desarrollos'] = (count($ades) > 0);
    $m_data['nombreprop'] = $ades['nombreprop'];
    $m_data['desarrollos'] = $desarrollos_array;
} else {
    $m_data['nombreprop'] = $propiedad['nombreprop'];
}

echo $mustache->render($m_template, $m_data);
