<?php

$m_file = __DIR__ . '/../templates/anuncio_esp.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

$m_data['anuncio_esp'] = $propiedad['anuncio_esp'];
$m_data['anuncio_ing'] = $propiedad['anuncio_ing'];
$m_data['caract_esp'] = htmlspecialchars($propiedad['caract_esp']);

// Title SEO
$m_data['title_seo_es'] = ($nuevo === 'No') ? $prop->getTitleSeo('es-MX') : '';

// Description SEO
$desc_seo = json_decode($propiedad['desc_seo'], true);
$m_data['desc_seo_es'] = htmlspecialchars($desc_seo['es-MX']);

echo $mustache->render($m_template, $m_data);
