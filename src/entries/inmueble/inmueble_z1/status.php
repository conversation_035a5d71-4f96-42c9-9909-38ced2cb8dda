<?php

$m_file = __DIR__ . '/../templates/status.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Determinar clase de línea
if ($propiedad['status_web'] === 'sin publicar') {
    $class_line = 'bg-color-5';
} elseif ($propiedad['fecha_expiracion'] != '' && $propiedad['fecha_expiracion'] != '0000-00-00' && $propiedad['fecha_expiracion'] < $hoy) {
    $class_line = 'bg-color-7';
} else {
    $class_line = 'bg-white';
}

$m_data['class_line'] = $class_line;

// Lógica de permisos y selecciones
$expiracion_disabled = (isset($aspermisos['p_publicar_en_web']) && $aspermisos['p_publicar_en_web'] == 'No');
$m_data['expiracion_disabled'] = $expiracion_disabled;

// Status del select principal
$m_data['status_publicado_selected'] = ($propiedad['status_web'] == 'publicado' && $propiedad['fecha_expiracion'] == '0000-00-00');
$m_data['status_sin_publicar_selected'] = ($propiedad['status_web'] == 'sin publicar' || ($nuevo == 'Si' && $aspermisos['p_publicar_en_web'] == 'No'));
$m_data['status_si_expira_selected'] = ($propiedad['fecha_expiracion'] != '' && $propiedad['fecha_expiracion'] != '0000-00-00' && $propiedad['status_web'] != 'sin publicar');

// Hidden input logic
$mostrar_hidden = false;
$hidden_value = '';

if (isset($aspermisos['p_publicar_en_web']) && $aspermisos['p_publicar_en_web'] == 'No' && $nuevo == 'No') {
    $mostrar_hidden = true;
    if ($propiedad['fecha_expiracion'] != '' && $propiedad['fecha_expiracion'] != '0000-00-00' && $propiedad['status_web'] != 'sin publicar') {
        $hidden_value = 'si expira';
    } else {
        $hidden_value = $propiedad['status_web'];
    }
} elseif ($nuevo == 'Si' && $aspermisos['p_publicar_en_web'] == 'No') {
    $mostrar_hidden = true;
    $hidden_value = 'sin publicar';
}

$m_data['mostrar_hidden_expiracion'] = $mostrar_hidden;
$m_data['expiracion_hidden_value'] = $hidden_value;

// Lógica de fechas
$agno_exp = 0;
$mes_exp = 0;
$dia_exp = 0;

if ($propiedad['status_web'] != 'sin publicar') {
    $agno_exp = (int)substr($propiedad['fecha_expiracion'], 0, 4);
    $mes_exp = (int)substr($propiedad['fecha_expiracion'], 5, 2);
    $dia_exp = (int)substr($propiedad['fecha_expiracion'], 8, 2);
}

// Mostrar/ocultar fecha de expiración
$m_data['fecha_expira_hidden'] = !($propiedad['status_web'] === 'publicado' && $propiedad['fecha_expiracion'] !== '0000-00-00');

// Generar días
$dias_array = array();
for ($i = 1; $i <= 31; $i++) {
    $dias_array[] = array(
        'dia' => $i,
        'selected' => ($dia_exp == $i)
    );
}
$m_data['dias'] = $dias_array;

// Generar meses
$ameses = array('', 'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre');
$meses_array = array();
for ($i = 1; $i <= 12; $i++) {
    $meses_array[] = array(
        'id' => $i,
        'nombre' => $ameses[$i],
        'selected' => ($mes_exp == $i)
    );
}
$m_data['meses'] = $meses_array;

// Generar años (definir YEAR si no existe)
$current_year = defined('YEAR') ? YEAR : date('Y');
$agnos_array = array();
for ($i = ($current_year + 2); $i >= 2017; $i--) {
    $agnos_array[] = array(
        'agno' => $i,
        'selected' => ($agno_exp == $i)
    );
}
$m_data['agnos'] = $agnos_array;

echo $mustache->render($m_template, $m_data);
