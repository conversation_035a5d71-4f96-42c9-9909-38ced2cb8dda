<?php

$m_file = __DIR__ . '/../templates/precio.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Venta
$m_data['enventa_checked'] = ($propiedad['enventa'] == 'Si');
$m_data['enventa_hidden'] = ($propiedad['enventa'] !== 'Si');
$m_data['campos_mc_venta'] = $propiedad['CamposMC']['Venta'];
$m_data['precio_venta'] = $propiedad['precio_venta'] ?: '';
$m_data['precio_por_metro_checked'] = ($propiedad['precio_por_metro'] == 'Si');

// Renta
$m_data['enrenta_checked'] = ($propiedad['enrenta'] == 'Si');
$m_data['enrenta_hidden'] = ($propiedad['enrenta'] !== 'Si');
$m_data['enrenta_disabled'] = ($padesarrollo == 'Si');
$m_data['campos_mc_renta'] = $propiedad['CamposMC']['Renta'];
$m_data['precio_renta'] = $propiedad['precio_renta'] ?: '';

// Diaria (Vacacional)
$m_data['endiaria_checked'] = ($propiedad['endiaria'] == 'Si');
$m_data['endiaria_hidden'] = ($propiedad['endiaria'] !== 'Si');
$m_data['endiaria_disabled'] = ($padesarrollo == 'Si');
$m_data['precio_diaria'] = $propiedad['precio_diaria'] ?: '';

// Traspaso
$m_data['entraspaso_checked'] = ($propiedad['entraspaso'] == 'Si');
$m_data['entraspaso_hidden'] = ($propiedad['entraspaso'] !== 'Si');
$m_data['entraspaso_disabled'] = ($padesarrollo == 'Si');
$m_data['precio_traspaso'] = $propiedad['precio_traspaso'] ?: '';

// Monedas
$monedas_array = array();
$sql->each("SELECT * FROM monedas ORDER BY id ASC", function ($r) use (&$monedas_array, &$propiedad) {
    $monedas_array[] = array(
        'siglas' => $r['siglas'],
        'p_nombre_esp' => $r['p_nombre_esp'],
        'selected' => ($propiedad['moneda'] === $r['siglas'])
    );
});
$m_data['monedas'] = $monedas_array;

echo $mustache->render($m_template, $m_data);
