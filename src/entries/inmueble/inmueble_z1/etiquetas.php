<?php

$m_file = __DIR__ . '/../templates/etiquetas.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

$m_data['en_resumen_checked'] = ($propiedad['en_resumen'] == 'Si');

// Solo mostrar opciones de operación hecha si no es una propiedad nueva
$m_data['mostrar_operacion_hecha'] = ($nuevo == 'No');

if ($m_data['mostrar_operacion_hecha']) {
    $m_data['operacion_hecha_vendida_selected'] = ($propiedad['operacion_hecha'] == 'vendida');
    $m_data['operacion_hecha_rentada_selected'] = ($propiedad['operacion_hecha'] == 'rentada');
    $m_data['operacion_hecha_oferta_selected'] = ($propiedad['operacion_hecha'] == 'oferta');
}

echo $mustache->render($m_template, $m_data);
