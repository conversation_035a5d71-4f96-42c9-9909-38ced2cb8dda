$(function() {

	// Agregar un controlador de eventos al evento 'change' de TinyMCE
	// editor.on('change', function() {
	// 	$('#generated_by').val('human');
	// 	console.log(`Ha cambiado el contenido del TinyMCE`)
	// });

	$('#ai_generate').on('click', function(e) {
		e.preventDefault();
		var formData = $('#Formulario1').serialize();
		var elementos = $('#Formulario1 input[required], #Formulario1 select[required], #Formulario1 textarea[required]');
		var valido = true;

		elementos.each(function() {
			if (!valido) return;
			if ($(this).val().trim() === '' || $(this).val() == 0) {
				valido = false;
				$(this).focus();
				alert('Toma en cuenta que requiero algunos campos básicos para entregarte algo que muy probablemente te encante :)');
			}
		});
		if (!valido) return;

		$('#ai_generate')
			.html('Dame un momento, estoy generando los contenidos...<br/>🤔')
			.attr('disabled', 'disabled');
		$('#ai_observacion')
			.attr('disabled', 'disabled');

		// Obtener la instancia de TinyMCE
		var editor = tinymce.get('anuncio_esp');
		editor.mode.set('readonly');

		$.ajax({
		    type: 'POST',
		    url: '/aj/ai-generate',
		    data: formData+'&aigen=new-prop-bulk',
		    dataType: 'json',
		    success: function(data) {
		    	if (!data.content) {
		    		var message = 'Algo pasó, no logré la redacción<br/>🥺<br/>Intenta nuevamente';
		    	}else{
		    		var message = '¡Listo!<br/>🙂<br/>Espero sea de tu agrado';
					$('#generated_by').val('ai');
					editor.setContent(data.content);
		    	}
				$('#ai_generate')
					.html(message)
					.removeAttr('disabled');
				$('#ai_observacion')
					.removeAttr('disabled');
				editor.mode.set('design');
		    },
			error: function(jqXHR, textStatus, errorThrown) {
				if (jqXHR.status === 504) {
		    		var message = 'Tiempo de respuesta excedido<br/>🫥<br/>Intenta nuevamente';
				} else {
		    		var message = '¡Ocurrió un error desconocido!<br/>🤯';
				}
				$('#ai_generate')
					.html(message)
					.removeAttr('disabled');
				$('#ai_observacion')
					.removeAttr('disabled');
				editor.mode.set('design');
			}
		});
	});
});
