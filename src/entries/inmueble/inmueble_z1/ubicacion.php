<?php

$m_file = __DIR__ . '/../templates/ubicacion.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Variables para ubicación
$vUbi = '';
$m_provincia = 0;
$m_ciudad = 0;
$m_colonia = 0;

// Mostrar ubicación actual
$m_data['mostrar_ubicacion_actual'] = ($propiedad['colonia'] != '' && $msg_error == '');
if ($m_data['mostrar_ubicacion_actual']) {
    $m_data['ubicacion_completa'] = "{$propiedad['pais']} / {$propiedad['provincia']} / {$propiedad['ciudad']} / {$propiedad['colonia']}";
}

// PAÍSES
$paises_array = array();
$p_pais = '';
$consulta1 = mysqlQuery("SELECT id, pais FROM paises WHERE activo='Si' AND pais='México' ORDER BY pais", $db_link);
while ($row = mysql_fetch_array($consulta1)) {
    $selected = ($row['pais'] == $propiedad['pais']);
    if ($selected) {
        $p_pais = $row['id'];
        $vUbi .= "var f_pais = '$row[id]';\n";
    }
    $paises_array[] = array(
        'id' => $row['id'],
        'pais' => $row['pais'],
        'selected' => $selected
    );
}
$m_data['paises'] = $paises_array;

// ESTADOS/PROVINCIAS
$estados_array = array();
$p_estado = '';
if ($p_pais == '') {
    $m_provincia = 0;
    $estados_array[] = array('id' => '0', 'estado' => '-------', 'selected' => false);
} else {
    $consulta1 = mysqlQuery("SELECT * FROM estados WHERE (pais='{$p_pais}')", $db_link);
    if (mysql_num_rows($consulta1) > 0) {
        $m_provincia = 0;
        $estados_array[] = array('id' => '0', 'estado' => 'seleccione ...', 'selected' => false);
        while ($row = mysql_fetch_array($consulta1)) {
            $selected = ($row['estado'] == $propiedad['provincia']);
            if ($selected) {
                $p_estado = $row['id'];
                $vUbi .= "var f_provincia = '$row[id]';\n";
            }
            $estados_array[] = array(
                'id' => $row['id'],
                'estado' => $row['estado'],
                'selected' => $selected
            );
        }
    } else {
        $m_provincia = 1;
        $estados_array[] = array('id' => 'especificar', 'estado' => 'especificar =>', 'selected' => false);
    }
}
$m_data['estados'] = $estados_array;

// CIUDADES
$ciudades_array = array();
$p_ciudad = '';
if ($p_estado == '' && $m_provincia == 0) {
    $m_ciudad = 0;
    $ciudades_array[] = array('id' => '0', 'ciudad' => '-------', 'selected' => false);
} else {
    $consulta1 = mysqlQuery("SELECT * FROM ciudades WHERE (estado='{$p_estado}')", $db_link);
    if (mysql_num_rows($consulta1) > 0) {
        $m_ciudad = 0;
        $ciudades_array[] = array('id' => '0', 'ciudad' => 'seleccione ...', 'selected' => false);
        while ($row = mysql_fetch_array($consulta1)) {
            $selected = ($row['ciudad'] == $propiedad['ciudad']);
            if ($selected) {
                $p_ciudad = $row['id'];
                $vUbi .= "var f_ciudad = '$row[id]';\n";
            }
            $ciudades_array[] = array(
                'id' => $row['id'],
                'ciudad' => $row['ciudad'],
                'selected' => $selected
            );
        }
    } else {
        $m_ciudad = 1;
        $ciudades_array[] = array('id' => 'especificar', 'ciudad' => 'especificar =>', 'selected' => false);
    }
}
$m_data['ciudades'] = $ciudades_array;

// ZONAS
$zonas_array = array();
$cjs = "mapas = Array();\n";
$nMapa = 0;

// Verificar si la zona existe
if ($propiedad['zona'] != '') {
    $consulta1 = mysqlQuery("SELECT zona_esp FROM zonas WHERE ((contrato='$config[contrato]' || contrato IS NULL) && zona_esp='$propiedad[zona]')", $db_link);
    if (mysql_num_rows($consulta1) == 0) {
        mysqlQuery("INSERT INTO zonas (contrato, zona_esp) VALUES ('$config[contrato]', '$propiedad[zona]')", $db_link);
    }
}

$consulta1 = mysqlQuery("SELECT zona_esp, mapa FROM zonas WHERE ((contrato='$config[contrato]' || contrato IS NULL) && ((ciudad='' && estado='') || (ciudad='$propiedad[ciudad]' && estado='$propiedad[provincia]'))) ORDER BY mapa, zona_esp", $db_link);
while ($row = mysql_fetch_array($consulta1)) {
    $tiene_mapa = ($row['mapa'] > 0);
    $selected = ($propiedad['zona'] == $row['zona_esp']);

    if ($tiene_mapa) {
        $cjs .= "mapas['$row[zona_esp]'] = $row[mapa];\n";
    }

    if ($selected && $tiene_mapa) {
        $nMapa = $row['mapa'];
    }

    $zonas_array[] = array(
        'zona_esp' => $row['zona_esp'],
        'tiene_mapa' => $tiene_mapa,
        'selected' => $selected
    );
}
$m_data['zonas'] = $zonas_array;
$m_data['script_mapas'] = $cjs;
$m_data['mostrar_mapa'] = ($nMapa > 0);
$m_data['numero_mapa'] = $nMapa;

// COLONIAS
$colonias_array = array();
$p_colonia = '';
if ($p_ciudad == '' && $m_ciudad == 0) {
    $m_colonia = 0;
    $colonias_array[] = array('id' => '0', 'colonia' => '-------', 'selected' => false);
} else {
    $consulta1 = mysqlQuery("SELECT * FROM new_colonias WHERE ((contrato IS NULL || contrato='$config[contrato]') && id_ciudad='$p_ciudad') ORDER BY colonia", $db_link);
    if (mysql_num_rows($consulta1) > 0) {
        $m_colonia = 0;
        $colonias_array[] = array('id' => '0', 'colonia' => 'seleccione ...', 'selected' => false);
        while ($row = mysql_fetch_array($consulta1)) {
            $col = mb_strtolower(utf8_decode($row['colonia']));
            $pcol = mb_strtolower(utf8_decode($propiedad['colonia']));
            $selected = ($col === $pcol);
            if ($selected) {
                $p_colonia = $row['id'];
                $vUbi .= "var f_colonia = '$row[id]';\n";
            }
            $colonias_array[] = array(
                'id' => $row['id'],
                'colonia' => $row['colonia'],
                'selected' => $selected
            );
        }
        $colonias_array[] = array('id' => 'especificar', 'colonia' => 'especificar =>', 'selected' => false);
    } else {
        $m_colonia = 1;
        $colonias_array[] = array('id' => 'especificar', 'colonia' => 'especificar =>', 'selected' => false);
    }
}
$m_data['colonias'] = $colonias_array;

// Campos de texto especiales
$m_data['mostrar_input_provincia'] = ($m_provincia == 1);
if ($m_data['mostrar_input_provincia']) {
    $m_data['provincia_value'] = htmlentities($propiedad['provincia']);
}

$m_data['mostrar_input_ciudad'] = ($m_ciudad == 1);
if ($m_data['mostrar_input_ciudad']) {
    $m_data['ciudad_value'] = htmlentities($propiedad['ciudad']);
}

$m_data['mostrar_input_colonia'] = ($m_colonia == 1);
if ($m_data['mostrar_input_colonia']) {
    $m_data['colonia_value'] = htmlentities($propiedad['colonia']);
}

$m_data['mostrar_otra_colonia'] = ($propiedad['muestra_colonia'] != '');
if ($m_data['mostrar_otra_colonia']) {
    $m_data['muestra_colonia_value'] = htmlentities($propiedad['muestra_colonia']);
}

// Código postal
$codigo_postal = ($propiedad['codigo_postal'] > 0) ? $propiedad['codigo_postal'] : '';
$m_data['codigo_postal'] = $codigo_postal;

// Script de variables JavaScript
$codigo_postal_js = ($propiedad['codigo_postal'] == '00000') ? '' : $propiedad['codigo_postal'];
$script_variables = "var f_tipo = '$propiedad[tipo]';\n";
$script_variables .= "var f_expiracion = '$propiedad[status_web]';\n";
$script_variables .= $vUbi;
$script_variables .= "var f_zona = '$propiedad[zona]';\n";
$script_variables .= "var f_codigo_postal = '$codigo_postal_js';\n";
$script_variables .= "var f_enventa = '$propiedad[enventa]';\n";

$m_data['script_variables'] = $script_variables;

echo $mustache->render($m_template, $m_data);
