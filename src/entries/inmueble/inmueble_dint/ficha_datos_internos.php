<?php

/**
 * Ficha de datos internos del inmueble - Template Mustache
 * Datos que NO aparecerán publicados ni en página web ni en Club de Negocios
 */

$m_file = __DIR__ . '/../templates/ficha_datos_internos.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Cargar templates para renderizar los campos
$templ = array(
    'selector' => file_get_contents(__DIR__ . '/../templates/selector.inmueble_z1.htm'),
    'numerico' => file_get_contents(__DIR__ . '/../templates/numerico.inmueble_z1.htm'),
    'caracter' => file_get_contents(__DIR__ . '/../templates/caracter.inmueble_z1.htm'),
    'texto' => file_get_contents(__DIR__ . '/../templates/texto.inmueble_z1.htm')
);

// Recuperar valores para este inmueble
$valores_esp = array();
$valores_ing = array();
if ($msg_error == '') {
    $consulta1 = mysqlQuery("
    SELECT * FROM valores_campos 
    WHERE clave_sistema='{$propiedad['clave_sistema']}'
    ", $db_link);
    while ($row = mysql_fetch_array($consulta1)) {
        $valores_esp[$row['variable']] = $row['valor_esp'];
        $valores_ing[$row['variable']] = $row['valor_ing'];
    }
} else {
    $consulta1 = mysqlQuery("
    SELECT * FROM campos_inmuebles
    WHERE (contrato='{$config['contrato']}' OR contrato IS NULL) AND uso='interno' AND activo='Si'
    ", $db_link);
    while ($row = mysql_fetch_array($consulta1)) {
        if ($row['tipo'] == 'selector') {
            if (${$row['variable']} == '-1') {
                $valores_esp[$row['variable']] = ${"d_$row[variable]_esp"};
                $valores_ing[$row['variable']] = ${"d_$row[variable]_ing"};
            } else {
                $consulta2 = mysqlQuery("SELECT * FROM valores_campos_aprendidos WHERE (id='" . ${$row['variable']} . "' && (contrato IS NULL || contrato='$config[contrato]'))", $db_link);
                $v0 = mysql_fetch_array($consulta2);
                $valores_esp[$row['variable']] = $v0['valor_esp'];
                $valores_ing[$row['variable']] = $v0['valor_ing'];
            }
        } else {
            $valores_esp[$row['variable']] = ${$row['variable']};
        }
    }
}

// Recuperar campos inhabilitados
$campos_inhabilitados = array();
$consulta1 = mysqlQuery("
SELECT variable FROM campos_inhabilitados 
WHERE contrato='{$config['contrato']}'
", $db_link);
while ($row = mysql_fetch_array($consulta1)) {
    array_push($campos_inhabilitados, $row['variable']);
}

// Recuperar campos que utilizan los inmuebles de la BD
$consulta1 = mysqlQuery("
SELECT * FROM campos_inmuebles 
WHERE (contrato='$config[contrato]' OR contrato IS NULL) AND uso='interno' AND activo='Si'
ORDER BY contrato, orden
", $db_link);

$campos = array();
$vals_ing_js = array();

// Armado de los campos para su posterior renderizado
while ($campo = mysql_fetch_assoc($consulta1)) {
    // Descartar si el campo no debe ser incluido
    if (in_array($campo['variable'], $campos_inhabilitados) || $campo['activo'] === 'No') {
        continue;
    }

    // Definir los valores del campo del inmueble
    $campo['value'] = array(
        'es' => $valores_esp[$campo['variable']],
        'en' => $valores_ing[$campo['variable']]
    );

    // Para los selects recuperar los valores aprendidos para las options
    if ($campo['tipo'] === 'selector') {
        $options = array();
        $valor_encontrado = false;
        $vals_ing_js[$campo['variable']] = array();

        $sql->each("
        SELECT id, valor_esp, valor_ing FROM valores_campos_aprendidos
        WHERE (contrato IS NULL OR contrato='{$config['contrato']}') AND variable='{$campo['variable']}'
        ORDER BY contrato
        ", function ($r) use (&$options, &$campo, &$valor_encontrado, &$vals_ing_js) {
            $selected = $campo['value']['es'] === $r['valor_esp'];
            $options[] = array(
                'id' => $r['id'],
                'valor_esp' => $r['valor_esp'],
                'valor_ing' => $r['valor_ing'],
                'selected' => $selected
            );
            $vals_ing_js[$campo['variable']][$r['id']] = $r['valor_ing'];
            if ($selected) {
                $valor_encontrado = true;
            }
        });

        // Si el inmueble tiene un valor pero no está en los aprendidos, lo añadimos
        if (!$valor_encontrado && !empty($campo['value']['es'])) {
            $valor_esp = $campo['value']['es'];
            $valor_ing = $campo['value']['en'] ?: '';

            // Verificar si ya existe
            $existe = $sql->row("
                    SELECT id FROM valores_campos_aprendidos 
                    WHERE (contrato='{$config['contrato']}' AND variable='{$campo['variable']}' 
                    AND valor_esp='{$valor_esp}')
                ");

            $nuevo_id = 0;
            if (!$existe) {
                // Insertar el nuevo valor
                $sql->insert(
                    'valores_campos_aprendidos',
                    array(
                        'contrato' => $config['contrato'],
                        'variable' => $campo['variable'],
                        'valor_esp' => $valor_esp,
                        'valor_ing' => $valor_ing
                    ),
                    true,
                    function ($id) use (&$nuevo_id) {
                        $nuevo_id = $id;
                    }
                );
            } else {
                $nuevo_id = $existe['id'];
                // Actualizar el valor inglés si está vacío
                if (empty($existe['valor_ing']) && !empty($valor_ing)) {
                    $sql->update_reg(
                        'valores_campos_aprendidos',
                        array('valor_ing' => $valor_ing),
                        "id='{$nuevo_id}'"
                    );
                }
            }

            // Añadir el valor a las opciones
            if ($nuevo_id > 0) {
                $options[] = array(
                    'id' => $nuevo_id,
                    'valor_esp' => $valor_esp,
                    'valor_ing' => $valor_ing,
                    'selected' => true
                );
                $vals_ing_js[$campo['variable']][$nuevo_id] = $valor_ing;
            }
        }

        $campo['options'] = $options;
    }

    // Renderizar el HTML del campo usando su template correspondiente
    $campo['html_rendered'] = $mustache->render($templ[$campo['tipo']], $campo);

    $campos[] = $campo;
}

// Agregar campos obligatorios de sistema al inicio
$campos_sistema = array(
    array(
        'nombre_esp' => 'Entre calles',
        'tipo' => 'caracter',
        'uso' => 'interno',
        'variable' => 'i_entre_calles',
        'longitud' => 100,
        'value' => array(
            'es' => $propiedad['i_entre_calles']
        )
    ),
    array(
        'nombre_esp' => 'Calle y número',
        'tipo' => 'caracter',
        'uso' => 'interno',
        'variable' => 'i_calle_numero',
        'longitud' => 100,
        'value' => array(
            'es' => $propiedad['i_calle_numero']
        )
    )
);

// Renderizar campos de sistema y agregarlos al inicio
foreach ($campos_sistema as $campo_sistema) {
    $campo_sistema['html_rendered'] = $mustache->render($templ[$campo_sistema['tipo']], $campo_sistema);
    array_unshift($campos, $campo_sistema);
}

// Preparar datos para el template
$m_data['campos'] = $campos;
$m_data['vals_ing_json'] = json_encode($vals_ing_js);

echo $mustache->render($m_template, $m_data);
