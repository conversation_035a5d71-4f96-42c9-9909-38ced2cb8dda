<?php

$m_file = __DIR__ . '/../templates/sucursales.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Recuperar sucursales
$sucursales = array();
$sql->each(
    "SELECT id, nombre, matriz
	FROM sucursales 
	WHERE (contrato='{$config['contrato']}')
	ORDER BY matriz DESC",
    function ($sucursal) use (&$sucursales, $propiedad) {
        $sucursales[] = array(
            'id' => $sucursal['id'],
            'nombre' => $sucursal['nombre'],
            'matriz' => $sucursal['matriz'],
            'matriz_prefix' => $sucursal['matriz'] ? '* ' : '',
            'selected' => ($propiedad['sucursal'] == $sucursal['id'])
        );
    }
);

// Preparar datos para el template
$m_data['tiene_sucursales'] = (count($sucursales) > 0);
$m_data['sucursales'] = $sucursales;

echo $mustache->render($m_template, $m_data);
