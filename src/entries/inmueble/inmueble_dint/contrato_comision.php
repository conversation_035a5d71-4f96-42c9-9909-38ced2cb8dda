<?php

$m_file = __DIR__ . '/../templates/contrato_comision.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Verificar si hay asesores registrados
$consulta_verificar_asesores = mysqlQuery("SELECT COUNT(*) as total_asesores FROM asesores WHERE contrato='$config[contrato]'", $db_link);
$resultado_asesores = mysql_fetch_array($consulta_verificar_asesores);
$hay_asesores = $resultado_asesores['total_asesores'] > 0;

$m_data['hay_asesores'] = $hay_asesores;

// Lógica del asesor
$m_data['asesor_solo_lectura'] = false;
$m_data['asesor_seleccionable'] = false;
$m_data['asesor_nombre_completo'] = '';
$m_data['asesor_id'] = 0;
$m_data['asesores'] = array();

if ($hay_asesores) {
    if ($aspermisos['p_edita_todos_los_inmuebles'] == 'No' && $_COOKIE['cookie_asesor'] > 0) {
        // Asesor en modo solo lectura
        $consulta1 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]' && id='$_COOKIE[cookie_asesor]')", $db_link);
        $asesor = mysql_fetch_array($consulta1);
        $m_data['asesor_solo_lectura'] = true;
        $m_data['asesor_nombre_completo'] = $asesor['nombre'] . ' ' . $asesor['apellidos'];
        $m_data['asesor_id'] = $_COOKIE['cookie_asesor'];
    } else {
        // Asesor seleccionable
        $m_data['asesor_seleccionable'] = true;
        $consulta1 = mysqlQuery("SELECT id, nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]') ORDER BY nombre, apellidos", $db_link);
        while ($asesor = mysql_fetch_array($consulta1)) {
            $m_data['asesores'][] = array(
                'id' => $asesor['id'],
                'nombre' => $asesor['nombre'],
                'apellidos' => $asesor['apellidos'],
                'selected' => ($propiedad['asesor'] == $asesor['id'])
            );
        }
    }
}

// Tipo de promoción
$m_data['socio_ampi_onchange'] = ($config['socio_ampi'] == 'Si');
$m_data['tipo_promocion_de_palabra_selected'] = ($propiedad['tipo_promocion'] == 'DE PALABRA');
$m_data['tipo_promocion_carta_autorizacion_selected'] = ($propiedad['tipo_promocion'] == 'CARTA AUTORIZACION');
$m_data['tipo_promocion_en_opcion_selected'] = ($propiedad['tipo_promocion'] == 'EN OPCION');
$m_data['tipo_promocion_en_exclusiva_selected'] = ($propiedad['tipo_promocion'] == 'EN EXCLUSIVA');

// Portal AMPI
$mostrar_portal_ampi = ($config['socio_ampi'] == 'Si' && $propiedad['tipo_promocion'] == 'EN EXCLUSIVA' && false);
$m_data['mostrar_portal_ampi'] = $mostrar_portal_ampi;

if ($mostrar_portal_ampi) {
    $m_data['clave_sistema'] = $propiedad['clave_sistema'];
    if ($propiedad['conf_portal_ampi'] != '0000-00-00') {
        $m_data['portal_ampi_actualizar'] = true;
        $m_data['portal_ampi_publicar'] = false;
        $m_data['conf_portal_ampi_fecha'] = cm_formato_fecha($propiedad['conf_portal_ampi']);
    } else {
        $m_data['portal_ampi_actualizar'] = false;
        $m_data['portal_ampi_publicar'] = true;
    }
}

// Comisiones de venta
$m_data['enventa'] = ($propiedad['enventa'] == 'Si');
if ($m_data['enventa']) {
    $m_data['precio_venta_formateado'] = number_format($propiedad['precio_venta'], 0, '.', ',');
    $m_data['moneda'] = $propiedad['moneda'];
    $m_data['i_porcentaje_comision'] = ($propiedad['i_porcentaje_comision'] > 0) ? $propiedad['i_porcentaje_comision'] : '';
}

// Comisiones de renta
$m_data['enrenta'] = ($propiedad['enrenta'] == 'Si');
if ($m_data['enrenta']) {
    $m_data['moneda'] = $propiedad['moneda'];
    $m_data['i_tipo_comision_rta_1_mes_checked'] = ($propiedad['i_tipo_comision_rta'] == '1 mes');
    $m_data['i_tipo_comision_rta_cantidad_fija_checked'] = ($propiedad['i_tipo_comision_rta'] == 'cantidad fija');
    $m_data['i_comision_rta_disabled'] = ($propiedad['i_tipo_comision_rta'] == '1 mes');

    // Calcular valor del campo comisión renta
    if ($propiedad['i_tipo_comision_rta'] == 'cantidad fija' && $propiedad['i_comision_rta'] > 0) {
        $m_data['i_comision_rta_value'] = $propiedad['i_comision_rta'];
    } elseif ($propiedad['i_tipo_comision_rta'] == '1 mes' && $propiedad['precio_renta'] > 0) {
        $m_data['i_comision_rta_value'] = $propiedad['precio_renta'];
    } else {
        $m_data['i_comision_rta_value'] = '';
    }
}

echo $m->render($m_template, $m_data);
