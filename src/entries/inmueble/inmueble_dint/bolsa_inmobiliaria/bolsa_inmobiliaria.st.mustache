<!-- MULTIBOLSA INMOBILIARIA -->
<tr>
	<td class="bg-mulbin-10">
		<!-- 🆕 FICHA COMPACTA DEL INMUEBLE -->
		<div class="p-3 mx-auto mb-4 max-w-3xl bg-white rounded-lg shadow-md">
			<div class="flex gap-3">
				<!-- Imagen del inmueble (compacta) -->
				<div class="flex-shrink-0">
					{{#foto_principal}}
						<img src="{{s_fotos}}/{{foto_principal}}" 
								alt="Foto del inmueble {{claveprop}}"
								class="object-cover w-32 h-28 rounded-lg border border-gray-300">
					{{/foto_principal}}
					{{^foto_principal}}
						<div class="flex justify-center items-center w-32 h-28 bg-gray-100 rounded-lg border border-gray-300">
							<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
							</svg>
						</div>
					{{/foto_principal}}
				</div>

				<!-- Información del inmueble -->
				<div class="flex-1 space-y-2">
					<!-- Encabezado con clave -->
					<div class="flex justify-between items-start">
						<div>
							<h3 class="text-base font-bold leading-tight text-gray-800">{{titulo_inmueble}}</h3>
						</div>
						<div class="px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded">
							{{claveprop}}
						</div>
					</div>

					<!-- Ubicación -->
					<div class="flex items-center text-gray-600">
						<svg class="mr-1 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
						</svg>
						<span class="text-xs">{{ubicacion_completa}}</span>
					</div>

					<!-- Precios dinámicos -->
					{{#tiene_operaciones}}
					<div class="flex flex-wrap gap-1">
						{{#operaciones_disponibles}}
						<div class="px-2 py-1 bg-green-50 rounded border border-green-200">
							<div class="text-xs font-bold text-green-800">
								{{precio_formateado}} {{moneda}}
								{{#periodo}}<span class="font-normal text-green-600">/{{periodo}}</span>{{/periodo}}
								{{#por_metro}}<span class="font-normal text-green-600">/m²</span>{{/por_metro}}
							</div>
							<div class="flex gap-1 items-center">
								<span class="text-xs font-medium text-green-700 uppercase">{{nombre}}</span>
								<!-- Badge dinámico que se actualiza con JavaScript -->
								<div id="badge_{{tipo}}" class="hidden px-1 py-0.5 text-xs font-bold text-white bg-orange-500 rounded">
									Comparte
								</div>
							</div>
						</div>
						{{/operaciones_disponibles}}
					</div>
					{{/tiene_operaciones}}
				</div>
			</div>
		</div>
		<!-- /FICHA COMPACTA DEL INMUEBLE -->
		
		<!-- Nota informativa -->
		<div class="p-2 mb-4 text-xs text-blue-700 bg-blue-50 rounded border border-blue-200">
			<span class="font-medium">💡 Tip:</span> Solo las operaciones que comparten comisión aparecerán en la Multibolsa Inmobiliaria.
		</div>

		{{#socio_ampi}}
		<div class="hidden pt-2 pb-2 text-9 text-color-6">
			Para publicación en Bolsa AMPI es necesario que la contratación haya sido EN EXCLUSIVA.
		</div>
		{{/socio_ampi}}
					
		<!-- MI BOLSA INMOBILIARIA -->
		<!-- INPUTS DE COMISIÓN: SIEMPRE VISIBLES -->
		<div class="grid gap-4 grid-cols-[repeat(auto-fit,minmax(300px,1fr))]">
			{{#enventa}}
			<!-- COMPARTO COMISION DE VENTA -->
			<div id="comparto_comision_de_venta">
				<div class="text-center">Comparto comisión de venta del:</div>
				<div class="grid gap-4 grid-cols-[repeat(auto-fit,minmax(100px,1fr))]">
					<div class="flex items-center max-w-[150px] mx-auto relative">
						<span class="absolute top-1 right-1 px-1 py-2 text-sm bg-gray-100 rounded-r">%</span>
						<input type="text" name="comparto_comision" 
							value="{{comparto_comision}}" 
							maxlength="5" 
							class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)] pr-8 number-format"
							d-config='{"decimals": 2}'
							onChange="return calcula_comision_mibolsa();"
						>
					</div>
					<div>
						<select name="t_comparto_comision" id="t_comparto_comision"
							class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)] text-center"
							onChange="return calcula_comision_mibolsa();"
						>
							<option value="sobre comision"{{#t_comparto_comision_sobre_comision_selected}} selected{{/t_comparto_comision_sobre_comision_selected}}>de la comisión</option>
							<option value="sobre precio"{{#t_comparto_comision_sobre_precio_selected}} selected{{/t_comparto_comision_sobre_precio_selected}}>del valor del inmueble</option>
						</select>
					</div>
				</div>
				<div class="py-2 text-center" id="h_comparto_comision">{{{h_comparto_comision}}}</div>
			</div>
			<!-- / COMPARTO COMISION DE VENTA -->
			{{/enventa}}
			
			{{#enrenta}}
			<div id="comparto_comision_de_renta">
				<div class="text-center">Comparto de mi comisión de renta el:</div>
				<div class="flex items-center max-w-[150px] mx-auto relative">
					<span class="absolute top-1 right-1 px-1 py-2 text-sm bg-gray-100 rounded-r">%</span>
					<input type="text" name="rta_comparto_comision" id="rta_comparto_comision" 
						value="{{rta_comparto_comision}}" 
						maxlength="5" 
						class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)] pr-8 number-format" 
						d-config='{"decimals": 2}'
						onChange="return calcula_comision_rta_mibolsa();"
					>
				</div>
				<div class="text-center" id="h_rta_comparto_comision">{{{h_rta_comparto_comision}}}</div>
			</div>
			{{/enrenta}}
		</div>
		
		{{#ya_publicado_en_feed_boolean}}
		<!-- AVISO DE PUBLICACIÓN PREVIA -->
		<div class="p-4 mb-4 bg-blue-50 border-l-4 border-blue-400" id="aviso_ya_publicado">
			<div class="flex items-center">
				<div class="flex-shrink-0">
					<svg class="w-5 h-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-blue-800">
						Inmueble ya publicado en el Feed
					</h3>
					<div class="mt-2 text-sm text-blue-700">
						<p>Este inmueble fue publicado en el Feed de Multibolsa Inmobiliaria el <strong>{{fecha_completa_formateada}}</strong>.</p>
						<p class="mt-1">Puedes continuar editando las comisiones normalmente. Si quieres volver a publicar en el Feed, haz clic en el botón de abajo.</p>
					</div>
					<div class="mt-4">
						<button type="button" onclick="habilitarRepublicacion()" 
							class="px-4 py-2 text-sm font-medium text-blue-800 bg-blue-100 rounded transition-colors duration-200 hover:bg-blue-200">
							Volver a publicar en el Feed
						</button>
					</div>
				</div>
			</div>
		</div>
		{{/ya_publicado_en_feed_boolean}}
		<!-- /MI BOLSA INMOBILIARIA -->
		
		<!-- CHECKBOX PARA PUBLICAR EN MULTIBOLSA INMOBILIARIA -->
		<div class="mbi" style="display: none;">
			<input type="hidden" name="author_id" value="{{ meteor_user_id }}">
			<div
				data-inmueble-bolsa-inmobiliaria
				data-activar-multibolsa="{{ usar_multibolsa }}"
				data-is-public="{{ publicacion_publica }}"
				data-selected-socios="{{ socios_seleccionados }}"
				data-solicitar-publicacion-websites="{{ solicitar_publicacion_websites }}"
				data-ya-publicado-feed="{{ ya_publicado_en_feed }}"
				data-fecha-publicacion-feed="{{ fecha_publicacion_feed }}"
				data-fecha-publicacion-formateada="{{ fecha_completa_formateada }}"
				class="w-full"
			></div>
			<!-- Cargamos el componente específico -->
			<script
			type="module"
			src="/assets/inmuebleBolsaInmobiliaria.js?t={{ tkt }}"
			></script>
		</div>

		<!-- BOLSA AMPI -->
		<div class="text-center">
			<div id="h_bolsa_ampi">
				{{#mostrar_bolsa_ampi}}
				{{#enventa_ampi}}
				<div class="text-center">
					<span class="font-normal font-roboto">Comparto comisión de venta del:
					<input type="text" name="comision_ampi" size="5" value="{{comision_ampi}}" maxlength="5" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" onChange="return calcula_comision_bolsaampi();">% - 
					<select size="1" name="t_comision_ampi" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" onChange="return calcula_comision_bolsaampi();">
						<option value="sobre comision"{{#t_comision_ampi_sobre_comision_selected}} selected{{/t_comision_ampi_sobre_comision_selected}}>de la comisión</option>
						<option value="sobre precio"{{#t_comision_ampi_sobre_precio_selected}} selected{{/t_comision_ampi_sobre_precio_selected}}>del valor del inmueble</option>
					</select></span>
					<span class="text-gray-500 text-9">
						<div id="h_comision_ampi">{{{h_comision_ampi}}}</div>
					</span>
				</div>
				{{/enventa_ampi}}
				
				{{#enrenta_ampi}}
				<div class="text-center border-t border-r border-b border-l border-gray-200">
					<span class="font-normal font-roboto">Comparto comisión de renta del:
					<input type="text" name="rta_comision_ampi" size="5" value="{{rta_comision_ampi}}" maxlength="5" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" onChange="return calcula_comision_rta_bolsaampi();">% - de la comisión</span>
					<span class="text-gray-500 text-9">
						<div id="h_rta_comision_ampi">{{{h_rta_comision_ampi}}}</div>
					</span>
				</div>
				{{/enrenta_ampi}}
				
				<div class="text-right">
					<a href="javascript:igualamibolsa();">
						<span class="text-9">Igual que <i>&quot;Mi Bolsa Inmobiliaria&quot;</i></span>
					</a>
				</div>
				{{/mostrar_bolsa_ampi}}
			</div>
		</div>
		<!-- /BOLSA AMPI -->
	</td>
</tr>

<script>
function calcula_comision_mibolsa() {
	if (!val_numerico(document.Formulario1.comparto_comision, 2)) {
		return(false);
	}
	
	var elementoComision = document.getElementById('h_comparto_comision');
	var selectComision = document.Formulario1.t_comparto_comision;
	
	// Si no encontramos el elemento, salir silenciosamente
	if (!elementoComision) {
		console.warn('Elemento h_comparto_comision no encontrado');
		return false;
	}
	
	var comisionValue = document.Formulario1.comparto_comision.value;
	var tipoComision = selectComision ? selectComision.value : 'sobre comision';
	
	if (comisionValue > 0 && tipoComision == 'sobre precio') {
		vComision = new oNumero(precio_venta * (comisionValue / 100));
		elementoComision.innerHTML = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' + vComision.formato(0, true) + ' {{moneda}}{{#precio_por_metro}} / m²{{/precio_por_metro}}{{#contenido_ampi_false}}{{{contenido_ampi_html}}}{{/contenido_ampi_false}}</span>';
	}else if (comisionValue > 0 && tipoComision == 'sobre comision' && g_comision > 0) {
		vComision = new oNumero(g_comision * (comisionValue / 100));
		elementoComision.innerHTML = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' + vComision.formato(0, true) + ' {{moneda}}{{#precio_por_metro}} / m²{{/precio_por_metro}}{{#contenido_ampi_false}}{{{contenido_ampi_html}}}{{/contenido_ampi_false}}</span>';
	}else if (comisionValue == 0) {
		elementoComision.innerHTML = '<span class="text-gray-300">NO COMPARTO COMISIÓN DE VENTA</span>';
	}else {
		elementoComision.innerHTML = '';
	}
	mostrar_checkbox_multibolsa();
	
	// 🆕 Actualizar badges dinámicos en la ficha
	actualizarBadgesFicha();
	
	// Notificar al componente Vue si existe
	if (window.notifyMultibolsaChange) {
		window.notifyMultibolsaChange();
	}
}

function calcula_comision_rta_mibolsa() {
	if (!val_numerico(document.Formulario1.rta_comparto_comision, 2)) {
		return(false);
	}
	
	var elementoComisionRenta = document.getElementById('h_rta_comparto_comision');
	
	// Si no encontramos el elemento, salir silenciosamente
	if (!elementoComisionRenta) {
		console.warn('Elemento h_rta_comparto_comision no encontrado');
		return false;
	}
	
	var comisionRentaValue = document.Formulario1.rta_comparto_comision.value;
	
	if (comisionRentaValue > 0) {
		vComision = new oNumero(comision_rta * (comisionRentaValue / 100));
		elementoComisionRenta.innerHTML = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' + vComision.formato(0, true) + ' {{moneda}}</span>';
	}else {
		elementoComisionRenta.innerHTML = '<span class="text-gray-300">NO COMPARTO COMISIÓN DE RENTA</span>';
	}
	mostrar_checkbox_multibolsa();
	
	// 🆕 Actualizar badges dinámicos en la ficha
	actualizarBadgesFicha();
	
	// Notificar al componente Vue si existe
	if (window.notifyMultibolsaChange) {
		window.notifyMultibolsaChange();
	}
}

{{#socio_ampi}}
function calcula_comision_bolsaampi() {
	if (!val_numerico(document.Formulario1.comision_ampi, 2)) {
		return(false);
	}
	if (document.Formulario1.comision_ampi.value > 0 && document.Formulario1.t_comision_ampi.value == 'sobre precio') {
		vComision = new oNumero(precio_venta * (document.Formulario1.comision_ampi.value / 100));
		document.getElementById('h_comision_ampi').innerHTML = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' + vComision.formato(0, true) + ' {{moneda}}{{#precio_por_metro}} / m²{{/precio_por_metro}}</span>';
	} else if (document.Formulario1.comision_ampi.value > 0 && document.Formulario1.t_comision_ampi.value == 'sobre comision' && g_comision > 0) {
		vComision = new oNumero(g_comision * (document.Formulario1.comision_ampi.value / 100));
		document.getElementById('h_comision_ampi').innerHTML = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' + vComision.formato(0, true) + ' {{moneda}}{{#precio_por_metro}} / m²{{/precio_por_metro}}</span>';
	} else {
		document.getElementById('h_comision_ampi').innerHTML = '<span class="text-gray-300">NO COMPARTO COMISIÓN DE VENTA</span>';
	}
}

function calcula_comision_rta_bolsaampi() {
	if (document.Formulario1.tipo_promocion.value == 'EN EXCLUSIVA') {
		if (!val_numerico(document.Formulario1.rta_comision_ampi, 2)) {
			return(false);
		}
		if (document.Formulario1.rta_comision_ampi.value > 0) {
			vComision = new oNumero(comision_rta * (document.Formulario1.rta_comision_ampi.value / 100));
			document.getElementById('h_rta_comision_ampi').innerHTML = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' + vComision.formato(0, true) + ' {{moneda}}</span>';
		}else {
			document.getElementById('h_rta_comision_ampi').innerHTML = '<span class="text-gray-300">NO COMPARTO COMISIÓN DE RENTA</span>';
		}
	}
}

function igualamibolsa() {
	{{#enventa}}
	document.Formulario1.comision_ampi.value = document.Formulario1.comparto_comision.value;
	document.Formulario1.t_comision_ampi.value = document.Formulario1.t_comparto_comision.value;
	calcula_comision_bolsaampi();
	{{/enventa}}
	{{#enrenta}}
	document.Formulario1.rta_comision_ampi.value = document.Formulario1.rta_comparto_comision.value;
//	calcula_comision_rta_bolsaampi();
	{{/enrenta}}
	return;
}

function v_bolsa_ampi(campo) {
	if (campo.value == 'EN EXCLUSIVA') {
		fAjax('ajPublicaPortalAmpi.php?id={{clave_sistema}}&op=boton', 'hPortalAMPI')
	}else {
		document.getElementById('hPortalAMPI').innerHTML = '';
	}
}
{{/socio_ampi}}

// Función para habilitar la re-publicación
function habilitarRepublicacion() {
	// Ocultar el aviso
	document.getElementById('aviso_ya_publicado').style.display = 'none';
	
	// Activar el estado de republicación
	window.estadoRepublicacion = true;
	
	// Notificar al componente Vue si existe
	if (window.habilitarModoRepublicacionVue) {
		window.habilitarModoRepublicacionVue();
	}
	
	// Ejecutar los cálculos iniciales si hay valores
	{{#enventa}}
	if (document.Formulario1.comparto_comision && document.Formulario1.comparto_comision.value) {
		calcula_comision_mibolsa();
	}
	{{/enventa}}
	
	{{#enrenta}}
	if (document.Formulario1.rta_comparto_comision && document.Formulario1.rta_comparto_comision.value) {
		calcula_comision_rta_mibolsa();
	}
	{{/enrenta}}
	
	// Mostrar checkbox de multibolsa si corresponde
	mostrar_checkbox_multibolsa();
	
	// 🆕 Actualizar badges dinámicos
	actualizarBadgesFicha();
}

// 🆕 Función para actualizar badges dinámicos en la ficha del inmueble
function actualizarBadgesFicha() {
	// Verificar comisión de venta
	{{#enventa}}
	var badgeVenta = document.getElementById('badge_venta');
	if (badgeVenta) {
		var comisionVenta = document.Formulario1.comparto_comision ? parseFloat(document.Formulario1.comparto_comision.value) : 0;
		if (comisionVenta > 0) {
			badgeVenta.classList.remove('hidden');
		} else {
			badgeVenta.classList.add('hidden');
		}
	}
	{{/enventa}}
	
	// Verificar comisión de renta
	{{#enrenta}}
	var badgeRenta = document.getElementById('badge_renta');
	if (badgeRenta) {
		var comisionRenta = document.Formulario1.rta_comparto_comision ? parseFloat(document.Formulario1.rta_comparto_comision.value) : 0;
		if (comisionRenta > 0) {
			badgeRenta.classList.remove('hidden');
		} else {
			badgeRenta.classList.add('hidden');
		}
	}
	{{/enrenta}}
	
	// Verificar comisión de traspaso (usa mismo campo que venta)
	var badgeTraspaso = document.getElementById('badge_traspaso');
	if (badgeTraspaso) {
		var comisionTraspaso = document.Formulario1.comparto_comision ? parseFloat(document.Formulario1.comparto_comision.value) : 0;
		if (comisionTraspaso > 0) {
			badgeTraspaso.classList.remove('hidden');
		} else {
			badgeTraspaso.classList.add('hidden');
		}
	}
}

// Ejecutar al cargar la página para mostrar el checkbox si corresponde
document.addEventListener('DOMContentLoaded', function() {
	mostrar_checkbox_multibolsa();
	// 🆕 Actualizar badges al cargar la página
	actualizarBadgesFicha();
});

// Función global para que el componente Vue pueda verificar si debe estar activo
window.shouldShowMultibolsa = function() {
	// Si está en modo de republicación, permitir la activación basada en comisiones
	if (window.estadoRepublicacion) {
		var compartir_venta = false;
		var compartir_renta = false;
		
		// Verificar si se comparte comisión de venta
		{{#enventa}}
		var elemento_venta = document.getElementById('h_comparto_comision');
		if (elemento_venta && !elemento_venta.innerHTML.includes('NO COMPARTO COMISIÓN')) {
			compartir_venta = true;
		}
		{{/enventa}}
		
		// Verificar si se comparte comisión de renta
		{{#enrenta}}
		var elemento_renta = document.getElementById('h_rta_comparto_comision');
		if (elemento_renta && !elemento_renta.innerHTML.includes('NO COMPARTO COMISIÓN')) {
			compartir_renta = true;
		}
		{{/enrenta}}
		
		return compartir_venta || compartir_renta;
	}
	
	// Si ya fue publicado y no está en modo republicación, no auto-activar
	{{#ya_publicado_en_feed}}
	return false;
	{{/ya_publicado_en_feed}}
	
	var compartir_venta = false;
	var compartir_renta = false;
	
	// Verificar si se comparte comisión de venta
	{{#enventa}}
	var elemento_venta = document.getElementById('h_comparto_comision');
	if (elemento_venta && !elemento_venta.innerHTML.includes('NO COMPARTO COMISIÓN')) {
		compartir_venta = true;
	}
	{{/enventa}}
	
	// Verificar si se comparte comisión de renta
	{{#enrenta}}
	var elemento_renta = document.getElementById('h_rta_comparto_comision');
	if (elemento_renta && !elemento_renta.innerHTML.includes('NO COMPARTO COMISIÓN')) {
		compartir_renta = true;
	}
	{{/enrenta}}
	
	return compartir_venta || compartir_renta;
};
</script>
