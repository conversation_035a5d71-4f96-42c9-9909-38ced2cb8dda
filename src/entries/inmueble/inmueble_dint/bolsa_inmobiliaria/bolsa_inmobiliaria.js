window.mostrar_checkbox_multibolsa = function () {
  var compartir_venta = false;
  var compartir_renta = false;

  // Verificar si se comparte comisión de venta
  var elemento_venta = document.getElementById("h_comparto_comision");
  if (elemento_venta) {
    if (!elemento_venta.innerHTML.includes("NO COMPARTO COMISIÓN"))
      compartir_venta = true;
  }

  // Verificar si se comparte comisión de renta
  var elemento_renta = document.getElementById("h_rta_comparto_comision");
  if (elemento_renta) {
    if (!elemento_renta.innerHTML.includes("NO COMPARTO COMISIÓN"))
      compartir_renta = true;
  }

  // Mostrar u ocultar el componente de Multibolsa Inmobiliaria según corresponda
  var multibolsa_div = document
    .querySelector("[data-inmueble-bolsa-inmobiliaria]")
    ?.closest(".mbi");
  if (multibolsa_div) {
    if (compartir_venta || compartir_renta) {
      multibolsa_div.style.display = "block";
      console.log("✅ Multibolsa Inmobiliaria habilitada:", {
        compartir_venta: compartir_venta,
        compartir_renta: compartir_renta,
      });
    } else {
      multibolsa_div.style.display = "none";
      console.log(
        "❌ Multibolsa Inmobiliaria deshabilitada: No se comparte comisión"
      );
    }
  }
};
