<?php

/**
 * Bolsa Inmobiliaria - Template Mustache
 * Club de negocios (Mi Bolsa Inmobiliaria y Bolsa AMPI)
 */

$m_file = __DIR__ . '/bolsa_inmobiliaria.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Configuraciones básicas
$m_data['tkt'] = time();
$m_data['meteor_user_id'] = isset($_SESSION['meteor_user_id']) ? $_SESSION['meteor_user_id'] : false;
$m_data['meteor_auth_token'] = isset($_SESSION['meteor_auth_token']) ? $_SESSION['meteor_auth_token'] : false;
$m_data['socio_ampi'] = ($config['socio_ampi'] == 'Si');
$m_data['enventa'] = ($propiedad['enventa'] == 'Si');
$m_data['enrenta'] = ($propiedad['enrenta'] == 'Si');

// Datos básicos para JavaScript
$m_data['moneda'] = $propiedad['moneda'];
$m_data['precio_por_metro'] = ($propiedad['precio_por_metro'] == 'Si');
$m_data['clave_sistema'] = $propiedad['clave_sistema'];
$m_data['claveprop'] = isset($claveprop) ? $claveprop : $propiedad['claveprop'];

// Fotos del inmueble
$m_data['s_fotos'] = sprintf("/photos/propiedades/%s/peque/", $propiedad['contrato']);

// Obtener la foto principal
$query = "SELECT archivo FROM f_fotos WHERE clave_sistema = '{$propiedad['clave_sistema']}' AND bolsa = 1 order by orden asc, id asc LIMIT 1";
$foto_query = $sql->query($query);
if ($foto_query && $foto_query->num_rows > 0) {
    $foto_data = $foto_query->fetch_assoc();
    $m_data['foto_principal'] = $foto_data['archivo'];
} else {
    $m_data['foto_principal'] = null;
}

// 🆕 DATOS ADICIONALES PARA FICHA MEJORADA DEL INMUEBLE

// Obtener el nombre legible del tipo de inmueble
$tipo_query = $sql->query("SELECT tipo_esp FROM tipo_inmuebles WHERE clave = '{$propiedad['tipo']}' LIMIT 1");
if ($tipo_query && $tipo_query->num_rows > 0) {
    $tipo_data = $tipo_query->fetch_assoc();
    $m_data['tipo_inmueble_nombre'] = $tipo_data['tipo_esp'];
} else {
    $m_data['tipo_inmueble_nombre'] = 'Tipo no definido';
}

// Título del inmueble (usar nombreprop o generar uno)
$m_data['titulo_inmueble'] = !empty($propiedad['nombreprop']) ? $propiedad['nombreprop'] : $m_data['tipo_inmueble_nombre'] . ' en ' . $propiedad['colonia'];

// Simplificado: solo título del inmueble

// Ubicación completa formateada
$ubicacion_partes = array_filter(array($propiedad['colonia'], $propiedad['ciudad'], $propiedad['provincia']));
$m_data['ubicacion_completa'] = implode(', ', $ubicacion_partes);

// Información de operaciones y precios
$m_data['operaciones_disponibles'] = array();

// Operación de venta
if ($propiedad['enventa'] == 'Si' && $propiedad['precio_venta'] > 0) {
    $m_data['operaciones_disponibles'][] = array(
        'tipo' => 'venta',
        'nombre' => 'Venta',
        'precio_formateado' => '$' . number_format($propiedad['precio_venta'], 0, '.', ','),
        'moneda' => $propiedad['moneda'],
        'por_metro' => ($propiedad['precio_por_metro'] == 'Si')
    );
}

// Operación de renta
if ($propiedad['enrenta'] == 'Si' && $propiedad['precio_renta'] > 0) {
    $m_data['operaciones_disponibles'][] = array(
        'tipo' => 'renta',
        'nombre' => 'Renta',
        'precio_formateado' => '$' . number_format($propiedad['precio_renta'], 0, '.', ','),
        'moneda' => $propiedad['moneda'],
        'periodo' => 'mensual'
    );
}

// Operación de traspaso
if ($propiedad['entraspaso'] == 'Si' && !empty($propiedad['precio_traspaso']) && $propiedad['precio_traspaso'] > 0) {
    $m_data['operaciones_disponibles'][] = array(
        'tipo' => 'traspaso',
        'nombre' => 'Traspaso',
        'precio_formateado' => '$' . number_format($propiedad['precio_traspaso'], 0, '.', ','),
        'moneda' => $propiedad['moneda']
    );
} elseif ($propiedad['entraspaso'] == 'Si' && $propiedad['precio_venta'] > 0) {
    // Si no hay precio específico de traspaso, usar precio de venta
    $m_data['operaciones_disponibles'][] = array(
        'tipo' => 'traspaso',
        'nombre' => 'Traspaso',
        'precio_formateado' => '$' . number_format($propiedad['precio_venta'], 0, '.', ','),
        'moneda' => $propiedad['moneda'],
        'por_metro' => ($propiedad['precio_por_metro'] == 'Si')
    );
}

// Indicadores para template
$m_data['tiene_operaciones'] = !empty($m_data['operaciones_disponibles']);

// 🆕 DATOS DE MULTIBOLSA INMOBILIARIA (para precargar configuración existente)
$m_data['usar_multibolsa'] = isset($propiedad['usar_multibolsa']) ? ($propiedad['usar_multibolsa'] == 'Si' ? 'true' : 'false') : 'false';
$m_data['publicacion_publica'] = isset($propiedad['publicacion_publica']) ? ($propiedad['publicacion_publica'] == 'Si' ? 'true' : 'false') : 'true';
$m_data['socios_seleccionados'] = isset($propiedad['socios_seleccionados']) ? $propiedad['socios_seleccionados'] : '';
$m_data['solicitar_publicacion_websites'] = isset($propiedad['solicitar_publicacion_websites']) ? ($propiedad['solicitar_publicacion_websites'] == 'Si' ? 'true' : 'false') : 'false';

// 🆕 CONTROL DE DUPLICACIÓN DE PUBLICACIONES EN FEED
$fecha_publicacion = isset($propiedad['fecha_publicacion_feed']) ? $propiedad['fecha_publicacion_feed'] : null;
$m_data['ya_publicado_en_feed'] = !empty($fecha_publicacion) ? 'true' : 'false';
$m_data['ya_publicado_en_feed_boolean'] = !empty($fecha_publicacion) ? true : false;
$m_data['fecha_publicacion_feed'] = $fecha_publicacion;

// Formatear fecha para mostrar al usuario (si existe)
if (!empty($fecha_publicacion)) {
    $timestamp = strtotime($fecha_publicacion);
    $m_data['fecha_publicacion_formateada'] = date('d/m/Y', $timestamp);
    $m_data['hora_publicacion_formateada'] = date('H:i', $timestamp);
    // Fecha completa en español
    $meses_espanol = array(
        'Jan' => 'ene', 'Feb' => 'feb', 'Mar' => 'mar', 'Apr' => 'abr',
        'May' => 'may', 'Jun' => 'jun', 'Jul' => 'jul', 'Aug' => 'ago',
        'Sep' => 'sep', 'Oct' => 'oct', 'Nov' => 'nov', 'Dec' => 'dic'
    );
    $fecha_base = date('d/M/Y \a \l\a\s H:i', $timestamp);
    $mes_ingles = date('M', $timestamp);
    $mes_espanol = $meses_espanol[$mes_ingles];
    $m_data['fecha_completa_formateada'] = str_replace($mes_ingles, $mes_espanol, $fecha_base);
}

// Calcular comisión de venta para uso en JavaScript
$comision_vta = $propiedad['precio_venta'] * ($propiedad['i_porcentaje_comision'] / 100);

// Datos de comisión de venta
if ($m_data['enventa']) {
    $m_data['comparto_comision'] = $propiedad['comparto_comision'];
    $m_data['t_comparto_comision_sobre_comision_selected'] = ($propiedad['t_comparto_comision'] == 'sobre comision');
    $m_data['t_comparto_comision_sobre_precio_selected'] = ($propiedad['t_comparto_comision'] == 'sobre precio');

    // Calcular texto de comisión compartida de VENTA
    if ($propiedad['comparto_comision'] > 0) {
        if ($propiedad['t_comparto_comision'] == 'sobre precio') {
            $comision_compartida = $propiedad['precio_venta'] * ($propiedad['comparto_comision'] / 100);
            $texto_comision = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' . number_format($comision_compartida, 0, '.', ',') . " {$propiedad['moneda']}</span>";
        } elseif ($propiedad['t_comparto_comision'] == 'sobre comision') {
            $comision_compartida = $comision_vta * ($propiedad['comparto_comision'] / 100);
            $texto_comision = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' . number_format($comision_compartida, 0, '.', ',') . " {$propiedad['moneda']}</span>";
        }
        if ($propiedad['precio_por_metro'] == 'Si') {
            $texto_comision .= ' / m²';
        }
        $m_data['h_comparto_comision'] = $texto_comision;
    } else {
        $m_data['h_comparto_comision'] = '<span class="text-gray-300">NO COMPARTO COMISIÓN DE VENTA</span>';
    }
}

// Datos de comisión de renta
if ($m_data['enrenta']) {
    $m_data['rta_comparto_comision'] = $propiedad['rta_comparto_comision'];

    // Calcular texto de comisión compartida de RENTA
    if ($propiedad['rta_comparto_comision'] > 0) {
        if ($propiedad['i_tipo_comision_rta'] == '1 mes') {
            $comision_rta = $propiedad['precio_renta'];
        } else {
            $comision_rta = $propiedad['i_comision_rta'];
        }
        $comision_compartida_renta = $comision_rta * ($propiedad['rta_comparto_comision'] / 100);
        $m_data['h_rta_comparto_comision'] = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' . number_format($comision_compartida_renta, 0, '.', ',') . " {$propiedad['moneda']}</span>";
    } else {
        $m_data['h_rta_comparto_comision'] = '<span class="text-gray-300">NO COMPARTO COMISIÓN DE RENTA</span>';
    }
}

// Lógica AMPI Plus
$m_data['mostrar_ampi_plus'] = ($config['socio_ampi'] == 'Si' && false);
if ($m_data['mostrar_ampi_plus']) {
    $m_data['ampi_plus_ya'] = ($propiedad['ampi_plus'] == 'Ya');
    $m_data['ampi_plus_checkbox_checked'] = ($propiedad['ampi_plus'] == 'Si');
    $m_data['ampi_plus_checkbox_unchecked'] = (!$m_data['ampi_plus_ya'] && !$m_data['ampi_plus_checkbox_checked']);
}

// Contenido AMPI para JavaScript (condición original del código)
$m_data['contenido_ampi_false'] = ($config['socio_ampi'] == 'Si' && false);
if ($m_data['contenido_ampi_false']) {
    $contenido_ampi = '';
    if ($propiedad['ampi_plus'] == 'Ya') {
        $contenido_ampi = '<br>Inmueble en intercambio especial AMPI';
    } else {
        $contenido_ampi = '<br><INPUT TYPE="checkbox" NAME="ampi_plus" id="ampi_plus" value="Si"><label for="ampi_plus">Proponer para comercialización AMPI</label>';
    }
    $m_data['contenido_ampi_html'] = $contenido_ampi;
}

// Lógica Bolsa AMPI
$mostrar_bolsa_ampi = ($propiedad['tipo_promocion'] == 'EN EXCLUSIVA' && $config['socio_ampi'] == 'Si' && 'perro' == 'gato');
$m_data['mostrar_bolsa_ampi'] = $mostrar_bolsa_ampi;

if ($mostrar_bolsa_ampi) {
    // Comisión AMPI de venta
    if ($m_data['enventa']) {
        $m_data['enventa_ampi'] = true;
        $m_data['comision_ampi'] = $propiedad['comision_ampi'];
        $m_data['t_comision_ampi_sobre_comision_selected'] = ($propiedad['t_comision_ampi'] == 'sobre comision');
        $m_data['t_comision_ampi_sobre_precio_selected'] = ($propiedad['t_comision_ampi'] == 'sobre precio');

        // Calcular texto de comisión AMPI de venta
        if ($propiedad['comision_ampi'] > 0) {
            if ($propiedad['t_comision_ampi'] == 'sobre precio') {
                $comision_ampi_venta = $propiedad['precio_venta'] * ($propiedad['comision_ampi'] / 100);
                $texto_ampi = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' . number_format($comision_ampi_venta, 0, '.', ',') . " {$propiedad['moneda']}</span>";
            } elseif ($propiedad['t_comision_ampi'] == 'sobre comision') {
                $comision_ampi_venta = $comision_vta * ($propiedad['comision_ampi'] / 100);
                $texto_ampi = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' . number_format($comision_ampi_venta, 0, '.', ',') . " {$propiedad['moneda']}</span>";
            }
            if ($propiedad['precio_por_metro'] == 'Si') {
                $texto_ampi .= ' / m²';
            }

            // Agregar lógica AMPI Plus al texto
            if ($config['socio_ampi'] == 'Si') {
                if ($propiedad['ampi_plus'] == 'Ya') {
                    $texto_ampi .= 'Inmueble en intercambio especial AMPI';
                } elseif ($propiedad['ampi_plus'] == 'Si') {
                    $texto_ampi .= '<INPUT TYPE="checkbox" NAME="ampi_plus" value="Si" checked>Proponer para comercialización AMPI';
                } else {
                    $texto_ampi .= '<INPUT TYPE="checkbox" NAME="ampi_plus" value="Si">Proponer para comercialización AMPI';
                }
            }
            $m_data['h_comision_ampi'] = $texto_ampi;
        } else {
            $m_data['h_comision_ampi'] = '<span class="text-gray-300">NO COMPARTO COMISIÓN DE VENTA</span>';
        }
    }

    // Comisión AMPI de renta
    if ($m_data['enrenta']) {
        $m_data['enrenta_ampi'] = true;
        $m_data['rta_comision_ampi'] = $propiedad['rta_comision_ampi'];

        // Calcular texto de comisión AMPI de renta
        if ($propiedad['rta_comision_ampi'] > 0) {
            if ($propiedad['i_tipo_comision_rta'] == '1 mes') {
                $comision_rta = $propiedad['precio_renta'];
            } else {
                $comision_rta = $propiedad['i_comision_rta'];
            }
            $comision_ampi_renta = $comision_rta * ($propiedad['rta_comision_ampi'] / 100);
            $m_data['h_rta_comision_ampi'] = '<span class="p-1 font-bold text-green-700 bg-green-100 rounded-md">Comparto: $' . number_format($comision_ampi_renta, 0, '.', ',') . " {$propiedad['moneda']}</span>";
        } else {
            $m_data['h_rta_comision_ampi'] = '<span class="text-gray-300">NO COMPARTO COMISIÓN DE RENTA</span>';
        }
    }
}

echo $mustache->render($m_template, $m_data);
