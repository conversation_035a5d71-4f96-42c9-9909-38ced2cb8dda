<?php

/**
 * Información adicional del inmueble - Template Mustache
 */

$m_file = __DIR__ . '/../templates/informacion_adicional.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Generar HTML del propietario usando la clase databases
include_once("clases/databases.inc.php");
$databases = new databases();

// Capturar el HTML generado por rec_contactos
ob_start();
$databases->rec_contactos($nombre_campo, $propiedad['i_propietario'], $mostrar);
$m_data['propietario_html'] = ob_get_clean();

// Verificar si está en renta
$m_data['enrenta'] = ($propiedad['enrenta'] == 'Si');

// Opciones de requisito de renta
if ($m_data['enrenta']) {
    $m_data['requisito_renta_fiador_selected'] = ($propiedad['i_requisito_renta'] == 'Fiador');
    $m_data['requisito_renta_fianza_selected'] = ($propiedad['i_requisito_renta'] == 'Fianza');
    $m_data['requisito_renta_negociable_selected'] = ($propiedad['i_requisito_renta'] == 'Negociable');

    // Inventario del inmueble
    $m_data['i_inventario'] = $propiedad['i_inventario'];
}

// Observaciones adicionales
$m_data['i_observaciones'] = $propiedad['i_observaciones'];

echo $mustache->render($m_template, $m_data);
