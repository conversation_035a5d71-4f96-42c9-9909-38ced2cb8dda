<?php

/**
 * JavaScript dinámico para cálculos de comisiones - Template Mustache
 * Genera variables y funciones JavaScript basadas en los datos del inmueble
 */

$m_file = __DIR__ . '/../templates/cero.st.mustache';
$m_template = file_get_contents($m_file);
$m_data = array();

// Configuración básica de venta y renta
$m_data['enventa'] = ($propiedad['enventa'] == 'Si');
$m_data['enrenta'] = ($propiedad['enrenta'] == 'Si');

// Variables de precios
if ($m_data['enventa']) {
    $m_data['precio_venta'] = $propiedad['precio_venta'];
}

if ($m_data['enrenta']) {
    $m_data['precio_renta'] = $propiedad['precio_renta'];
    $m_data['comision_rta_1_mes'] = ($propiedad['i_tipo_comision_rta'] == '1 mes');
    $m_data['comision_rta_cantidad_fija'] = ($propiedad['i_tipo_comision_rta'] == 'cantidad fija');
    $m_data['i_comision_rta'] = $propiedad['i_comision_rta'];
}

// Calcular comisión global
$comision_vta = $propiedad['precio_venta'] * ($propiedad['i_porcentaje_comision'] / 100);
$m_data['g_comision'] = $comision_vta;

// Datos de moneda y formato
$m_data['moneda'] = $propiedad['moneda'];
$m_data['precio_por_metro'] = ($propiedad['precio_por_metro'] == 'Si');

// Solo mantenemos datos generales que puedan necesitar otras funciones

echo $mustache->render($m_template, $m_data);
