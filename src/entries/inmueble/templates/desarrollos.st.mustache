<script>
	function v_desarrollo(campo) {
		if (campo.checked == true) {
			document.getElementById("m_desarrollo").classList.remove("hidden");
			document.Formulario1.nombreprop.focus();
		} else {
			document.getElementById('m_desarrollo').classList.add("hidden");
		}
	}
</script>

{{#mostrar_seccion_desarrollos}}
<tr>
	<td width="100%">
		<div>
			<div class="center max-500">
				<div>
					<input type="checkbox" id="desarrollo" name="desarrollo" value="Si" onClick="v_desarrollo(this);"{{#desarrollo_checked}} checked{{/desarrollo_checked}} /><label for="desarrollo">Este modelo de inmueble pertenece a uno o varios desarrollos</label>
				</div>
				<div class="pb-1 text-8">(podrás especificar posteriormente el precio y observaciones de cada unos de los inmuebles que conforman el desarrollo)</div>
			</div>
			<div id="m_desarrollo">
				{{#tiene_desarrollos}}
				<div class="center max-300">
					<label for="nombreprop" class="text-11">Nombre/Modelo de casa:</label>
					<input type="text" name="nombreprop" value="{{nombreprop}}" maxlength="30" class="form-control" />
					<div class="py-1 text-11">En el(los) desarrollo(s):</div>
				</div>
				<div class="px-3 contenedor min-200">
					{{#desarrollos}}
					<div><input type="checkbox" name="d_{{clave_sistema}}" id="d_{{clave_sistema}}" value="Si"{{#checked}} checked{{/checked}}><label for="d_{{clave_sistema}}">{{nombredes}}</label></div>
					{{/desarrollos}}
				</div>
				{{/tiene_desarrollos}}
			</div>
		</div>
	</td>
</tr>
<tr>
	<td width="100%">
		<div class="center max-300">
			<label for="nombreprop" class="text-11">Nombre del modelo en el desarrollo:</label>
			<input type="text" name="nombreprop" value="{{nombreprop}}" maxlength="30" class="form-control center" />
		</div>
	</td>
</tr>
{{/mostrar_seccion_desarrollos}}
