<div class="{{ ti_classes }}">
	<label for="{{ variable }}" class="d-flex">
		<div class="left">{{ nombre_esp }}</div>
	</label>
	<div class="d-flex input-with-complement-right-1">
		{{# complemento_al_valor }}<span>{{ complemento_al_valor }}</span>{{/ complemento_al_valor }}
		<input type="text" name="{{ variable }}" id="{{ variable }}"
			autocomplete="off" 
			class="form-control number-format"
			d-config='{"decimals": {{ decimales }}}'
			value="{{ value.es }}" 
		/>
	</div>
</div>
