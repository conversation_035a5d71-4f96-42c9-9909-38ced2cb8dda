<tr>
	<td>
		<script>
			const vals_ing = {{{vals_ing_json}}};

			function v_selector(campo, did, max) {
				document.getElementById('EN_' + did).innerHTML = vals_ing[did][campo.value] ?
					`EN: ${vals_ing[did][campo.value]}` : '';
				if (campo.value == -1) {
					document.getElementById(did).classList.remove("hidden");
					document.getElementById(did).focus();
				} else {
					document.getElementById(did).classList.add("hidden");
				}
			}
		</script>
		<div class="px-3 py-2 font-bold rounded text-mulbin-700 bg-mulbin-50">Datos internos del inmueble</div>
		<div class="pb-1 text-9 text-color-6">Los siguientes datos NO APARECERÁN PUBLICADOS ni en tu página web ni en la <span class="bold">Multibolsa Inmobiliaria</span>.</div>
		
		<!-- CAMPOS -->
		<div class="contenedor min-300">
			{{#campos}}
			{{{html_rendered}}}
			{{/campos}}
		</div>
		<!-- / CAMPOS -->
	</td>
</tr> 