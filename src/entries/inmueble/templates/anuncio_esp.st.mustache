<!-- ANUNCIO A DESPLEGAR EN ESPAÑOL -->
<tr>
  <td align="left" width="100%" bgcolor="#E1E1E1">
    <label for="anuncio_esp" class="bold">
      Anuncio a desplegar
    </label>
    <textarea id="anuncio_esp" name="anuncio_esp" style="width: 100%; height: 300px" class="anuncio_a_desplegar">{{{anuncio_esp}}}</textarea>
  </td>
</tr>
<!-- / ANUNCIO A DESPLEGAR EN ESPAÑOL -->
<!-- ANUNCIO A DESPLEGAR EN INGLÉS -->
<tr>
  <td align="left" width="100%" bgcolor="#E1E1E1">
    <label for="anuncio_ing" class="bold">
      Anuncio a desplegar en inglés
    </label>
    <textarea id="anuncio_ing" name="anuncio_ing" style="width: 100%; height: 300px" class="anuncio_a_desplegar">{{{anuncio_ing}}}</textarea>
  </td>
</tr>
<!-- / ANUNCIO A DESPLEGAR EN INGLÉS -->
<!-- PDF EN ESPAÑOL -->
<tr>
  <td align="left" width="100%">
    <label for="caract_esp" class="bold">
      Descripción para generador de PDF
    </label>
    <div class="with-counter">
      <textarea name="caract_esp" id="caract_esp" style="width: 100%; height: 125px;" class="form-control" d-config='{"limit": 450}'>{{caract_esp}}</textarea>
      <div class="family-mono"><span class="counter"></span></div>
    </div>
  </td>
</tr>
<!-- / PDF EN ESPAÑOL -->

<!-- TÍTULO EN ESPAÑOL DEL INMUEBLE PARA SEO Y OTROS PORTALES -->
<tr>
  <td align="left" width="100%">
    <label for="title_seo[es-MX]" class="bold">
      Título del inmueble para SEO
    </label>
    <div class="with-counter">
      <input type="text" name="title_seo[es-MX]" id="title_seo[es-MX]" autocomplete="off" class="form-control" d-config='{"limit": 80}' value="{{title_seo_es}}" />
      <div class="family-mono"><span class="counter"></span></div>
    </div>
  </td>
</tr>
<!-- / TÍTULO EN ESPAÑOL DEL INMUEBLE PARA SEO Y OTROS PORTALES -->
<!-- DESCRIPCIÓN DEL INMUEBLE PARA SEO Y OTROS PORTALES -->
<tr>
  <td align="left" width="100%">
    <label for="desc_seo[es-MX]" class="bold">
      Descripción del inmueble para SEO
    </label>
    <div class="with-counter">
      <input type="text" name="desc_seo[es-MX]" id="desc_seo[es-MX]" autocomplete="off" class="form-control" d-config='{"limit": 155}' value="{{desc_seo_es}}" />
      <div class="family-mono"><span class="counter"></span></div>
    </div>
  </td>
</tr>
<!-- / DESCRIPCIÓN DEL INMUEBLE PARA SEO Y OTROS PORTALES -->

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Obtener todos los elementos con la clase 'with-counter'
    const counters = document.querySelectorAll('.with-counter > .form-control');

    // Iterar a través de cada elemento
    counters.forEach(counter => {
      // Obtener el límite de caracteres y el elemento de conteo
      const limit = counter.getAttribute('d-config') ? JSON.parse(counter.getAttribute('d-config')).limit : null;
      const countEl = counter.nextElementSibling.querySelector('.counter');

      counter.setAttribute('maxlength', limit);

      // Actualizar el conteo de caracteres cuando el usuario escriba
      counter.addEventListener('input', () => {
        const count = counter.value.length;
        countEl.innerText = limit ? `${limit - count} caracteres restantes` : `${count} caracteres`;
        if (limit && count > limit) {
          counter.classList.add('is-invalid');
        } else {
          counter.classList.remove('is-invalid');
        }
      });

      counter.dispatchEvent(new Event("input"));
    });
  });
</script> 