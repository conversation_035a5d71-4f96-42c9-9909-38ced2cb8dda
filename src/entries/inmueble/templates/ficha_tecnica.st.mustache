<tr>
    <td align="left" width="100%">
        <script>
            const vals_ing = {{{vals_ing_json}}};

            function v_selector(campo, did, max) {
                document.getElementById('EN_' + did).innerHTML = vals_ing[did][campo.value] ?
                    `EN: ${vals_ing[did][campo.value]}` : '';
                if (campo.value == -1) {
                    document.getElementById(did).classList.remove("hidden");
                    document.getElementById(did).focus();
                } else {
                    document.getElementById(did).classList.add("hidden");
                }
            }
        </script>
        <div>
            <div class="px-3 py-2 font-bold rounded text-mulbin-700 bg-mulbin-50">Datos a publicar del inmueble</div>
        </div>
        
        <!-- CAMPOS -->
        {{#grupos}}
        <div class="my-4 font-bold">{{nombre}}</div>
        <div class="grid gap-4 grid-cols-[repeat(auto-fit,minmax(150px,1fr))]">
            {{#campos}}
            {{{html_rendered}}}
            {{/campos}}
        </div>
        {{/grupos}}
        
        <button class="px-5 py-2 text-sm font-bold tracking-wide uppercase border-none rounded cursor-pointer text-mulbin-500 bg-color-2 hover:bg-mulbin-100" id="ver_todos_campos">Ver todos los campos</button>
        <!-- / CAMPOS -->
    </td>
</tr>

<style>
    /* Estilos para componentes de input con idiomas */
    .input-container {
        position: relative;
        display: inline-block;
    }

    .input-container input[type="text"] {
        padding: 8px;
        padding-right: 40px;
        box-sizing: border-box;
    }

    .input-container .lang-btn {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        border: none;
        background-color: #ddd;
        cursor: pointer;
        padding: 0 10px;
        font-size: 12px;
        font-weight: 600;
        color: #727272;
    }

    .input-container .lang-btn:focus {
        outline: none;
    }

    .input-container .dropdown {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background-color: #f0f0f0;
        border: 1px solid #ccc;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        width: 100%;
    }

    .input-container .dropdown label {
        display: block;
        padding: 8px 10px;
        cursor: pointer;
    }

    .input-container .dropdown input[type="radio"] {
        margin-right: 10px;
    }

    .input-container .dropdown label:hover {
        background-color: #c0c0c0;
        color: #fff;
    }

    .input-container-display-values {
        margin-top: 5px;
        font-size: 14px;
        color: #a0a0a0;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const components = document.querySelectorAll('.input-container');
        components.forEach((component) => {
            const variable = component.id.replace('component_', '');
            const textInput = document.getElementById(`textInput_${variable}`);
            const languageButton = document.getElementById(`languageButton_${variable}`);
            const languageDropdown = document.getElementById(`languageDropdown_${variable}`);
            const displayValues = document.getElementById(`displayValues_${variable}`);
            const hiddenInputs = {
                'EN': document.getElementById(`textInput_${variable}_EN`),
                'FR': document.getElementById(`textInput_${variable}_FR`),
                'ES': document.getElementById(`textInput_${variable}_ES`)
            };

            // Mostrar/Ocultar el dropdown al hacer clic en el botón
            languageButton.addEventListener('click', function() {
                languageDropdown.style.display = (languageDropdown.style.display === 'block') ? 'none' : 'block';
            });

            // Cambiar el idioma seleccionado y actualizar el valor del botón
            languageDropdown.addEventListener('change', function(event) {
                const selectedLanguage = event.target.value;
                hiddenInputs[languageButton.textContent].value = textInput.value;

                textInput.value = hiddenInputs[selectedLanguage].value;

                languageButton.textContent = selectedLanguage;
                updateDisplayValues();

                languageDropdown.style.display = 'none';
                textInput.focus();
            });

            // Actualizar el campo hidden correspondiente cuando se escribe en el input
            textInput.addEventListener('input', function() {
                const selectedLanguage = languageButton.textContent;
                hiddenInputs[selectedLanguage].value = textInput.value;
                updateDisplayValues();
            });

            // Función para actualizar todos los valores mostrados debajo del input
            function updateDisplayValues() {
                let displayText = '';
                for (let lang in hiddenInputs) {
                    const value = hiddenInputs[lang].value;
                    if (value) {
                        displayText += `${lang}: ${value}<br>`;
                    }
                }
                displayValues.innerHTML = displayText;
            }

            // Mostrar valores iniciales si existen
            updateDisplayValues();
        });
    });
</script> 