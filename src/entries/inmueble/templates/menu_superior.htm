<table border="0" width="100%" cellspacing="0" cellpadding="0">
  <tr>
    <td align="right" class="observaciones">{{ dAsesor }}</td>
  </tr>
</table>
<div id="modalPanel" class="modalDialog">
  <img src="imagessi/times.png" />
  <div>
    <iframe id="panelIFrame" src="" name="socialIFrame"></iframe>
  </div>
</div>
<script>
  var pnlFotosUrl = "/photos/?a=pnlFotos&idi={{ clave_sistema }}";
  function pnl() {
    document.getElementById("panelIFrame").src = pnlFotosUrl;
    document.body.className = document.body.className + " modalPanel";
  }
  document.getElementById("modalPanel").onclick = function () {
    document.body.className = document.body.className.replace(
      " modalPanel",
      ""
    );
    document.getElementById("panelIFrame").src = "";
  };

  // Auto-abrir panel de fotos para inmuebles nuevos
  {{#nuevo_inmueble}}
  setTimeout(function() {
    pnl();
  }, 500);
  {{/nuevo_inmueble}}
</script>

<nav class="flex center p-2px">
  <div class="menuSupHorizontal">
    <a
      class="barra_navegacion2 {{#op1.active}}active{{/op1.active}} justify-items-center bg-mulbin-600 hover:bg-mulbin-400 rounded-tl-md h-full"
      href="{{ op1.href }}"
      target="_top"
    >
      <ion-icon
        name="globe-outline"
        role="img"
        class="md hydrated"
        aria-label="globe outline"
      ></ion-icon>
      <div class="text-sm md:text-base">Público</div>
    </a>
  </div>
  <div class="menuSupHorizontal bg-mulbin-600">
    <a
      class="h-full barra_navegacion2 {{#op2.active}}active{{/op2.active}} justify-items-center bg-mulbin-600 hover:bg-mulbin-400 align-middle"
      href="{{ op2.href }}"
      target="_top"
    >
      <ion-icon
        name="lock-closed-outline"
        role="img"
        class="md hydrated"
        aria-label="lock closed outline"
      ></ion-icon>
      <div class="text-sm md:text-base">Privado</div>
    </a>
  </div>
  <div class="menuSupHorizontal bg-mulbin-600">
    <a
      class="h-full barra_navegacion2 {{#op3.active}}active{{/op3.active}} justify-items-center bg-mulbin-600 hover:bg-mulbin-400 align-middle"
      href="{{ op3.href }}"
      target="_top"
    >
      <ion-icon
        name="people-outline"
        role="img"
        class="md hydrated"
        aria-label="people outline"
      ></ion-icon>
      <div class="text-sm md:text-base">Multibolsa</div>
    </a>
  </div>
  <div class="menuSupHorizontal bg-mulbin-600">
    <a
      class="justify-items-center h-full barra_navegacion2 bg-mulbin-600 hover:bg-mulbin-400"
      href="javascript:pnl();"
      target="_top"
    >
      <ion-icon
        name="images-outline"
        role="img"
        class="md hydrated"
        aria-label="images outline"
      ></ion-icon>
      <div class="text-sm md:text-base">
        Fotos <span class="hidden sm:inline">y 360º</span>
      </div>
    </a>
  </div>
  <div class="rounded-tr-md menuSupHorizontal bg-mulbin-600">
    <a
      class="barra_navegacion2 {{#op4.active}}active{{/op4.active}} justify-items-center bg-mulbin-600 hover:bg-mulbin-400 rounded-tr-md h-full"
      href="{{ op4.href }}"
      target="_top"
    >
      <ion-icon
        name="trash-outline"
        role="img"
        class="md hydrated"
        aria-label="trash outline"
      ></ion-icon>
      <div class="text-sm md:text-base">Eliminar</div>
    </a>
  </div>
</nav>
