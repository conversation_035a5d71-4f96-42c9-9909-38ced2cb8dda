<script>
	function val_claveprop(campo) {
		var checkOK = "0123456789-_,.abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
		var checkStr = campo.value;
		var allValid = true;
		var allNum = "";
		for (i = 0;  i < checkStr.length;  i++) {
			ch = checkStr.charAt(i);
			for (j = 0;  j < checkOK.length;  j++) if (ch == checkOK.charAt(j)) break;
			if (j == checkOK.length) {
			allValid = false;
			break;
			}
			allNum += ch;
		}
		if (!allValid) {
			alert('La clave del inmueble solo puede tener letras, números y guiones(-) y sin espacios.');
			campo.focus();
			return (false);
		}
		return (true);
	}

	// Funciones para el modal
	function openInmuebleModal(clave) {
		const modal = document.getElementById('inmuebleModal');
		const iframe = document.getElementById('inmuebleIframe');
		const url = `https://{{usuario}}{{master_web_domain}}/detallesnew.php?clave=${clave}&pp=NV&idioma=esp`;
		
		iframe.src = url;
		modal.classList.remove('hidden');
		document.body.style.overflow = 'hidden';
	}

	function closeInmuebleModal() {
		const modal = document.getElementById('inmuebleModal');
		const iframe = document.getElementById('inmuebleIframe');
		
		modal.classList.add('hidden');
		iframe.src = '';
		document.body.style.overflow = 'auto';
	}

	// Cerrar modal al hacer clic fuera de él
	document.addEventListener('DOMContentLoaded', function() {
		const modal = document.getElementById('inmuebleModal');
		modal.addEventListener('click', function(e) {
			if (e.target === modal) {
				closeInmuebleModal();
			}
		});

		// Cerrar modal con ESC
		document.addEventListener('keydown', function(e) {
			if (e.key === 'Escape') {
				closeInmuebleModal();
			}
		});
	});
</script>

<!-- Modal para mostrar el inmueble -->
<div id="inmuebleModal" class="flex hidden fixed inset-0 z-50 justify-center items-center p-4 bg-black bg-opacity-50">
	<div class="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[90vh] flex flex-col">
		<!-- Header del modal -->
		<div class="flex justify-between items-center p-4 border-b border-gray-200">
			<h3 class="text-lg font-semibold text-gray-900">Detalles del Inmueble</h3>
			<button type="button" onclick="closeInmuebleModal()" class="text-gray-400 transition-colors hover:text-gray-600">
				<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
				</svg>
			</button>
		</div>
		<!-- Contenido del modal -->
		<div class="relative flex-1">
			<iframe id="inmuebleIframe" class="w-full h-full border-0" frameborder="0"></iframe>
		</div>
	</div>
</div>

<tr>
	<td class="center">
		{{#es_nuevo}}
		<div>Con clave: <strong>{{claveprop}}</strong></div>
		{{/es_nuevo}}
		{{^es_nuevo}}
		<div class="contenedor min-200">
			<div class="flex justify-center items-center py-2">
				<div class="text-lg font-bold text-center">
					Con clave:
					<button type="button" onclick="openInmuebleModal('{{clave_sistema}}')" class="text-blue-600 underline transition-colors hover:text-blue-800">{{claveprop}}</button>
				</div>
			</div>
			<div class="flex flex-col gap-2 items-center mt-2 sm:flex-row">
				<label for="nclaveprop" class="sm:text-right">Cambiar clave:</label>
				<input type="text" name="nclaveprop" id="nclaveprop" placeholder="(opcional)" 
					class="font-bold w-full max-w-[150px] border border-gray-300 rounded px-2 py-1" autocomplete="off" 
					maxlength="25" onchange="return val_claveprop(this);">
			</div>
		</div>
		{{/es_nuevo}}
	</td>
</tr> 