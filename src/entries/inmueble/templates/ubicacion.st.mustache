<tr>
  <td class="w-full text-left align-top">
    <table class="w-full border-spacing-0" id="table25">
      <tr>
        <td colspan="4" class="align-top">
          <div class="px-3 py-2 font-bold rounded text-mulbin-700 bg-mulbin-50">Ubicación a publicar del inmueble</div>
          {{#mostrar_ubicacion_actual}}
          <div class="text-10 text-color-5">
            <span class="font-bold">Ubicación publicada:</span>
            <span class="italic">
              {{ubicacion_completa}}
            </span>
          </div>
          {{/mostrar_ubicacion_actual}}
        </td>
      </tr>
      <!-- UBICACIÓN DEL INMUEBLE (parte 1) -->
      <tr>
        <td colspan="4" class="ubica-inmueble">
          <!-- GROUP-500 -->
          <div class="grid gap-4 grid-cols-[repeat(auto-fit,minmax(300px,1fr))]">
            <div class="grid gap-4 grid-cols-[repeat(auto-fit,minmax(150px,1fr))]">
              <!-- PAIS -->
              <div class="mb-4">
                <label for="pais" class="block mb-2 text-sm font-medium text-gray-700">* País</label>
                <select name="pais" id="pais" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" required>
                  <option value="0">*** seleccione ***</option>
                  <option value="1">México</option>
                  <option value="0">-------</option>
                  {{#paises}}
                  <option value="{{id}}"{{#selected}} selected{{/selected}}>{{pais}}</option>
                  {{/paises}}
                </select>
              </div>
              <!-- / PAIS -->
              <!-- ESTADO -->
              <div class="mb-4">
                <label for="provincia" class="block mb-2 text-sm font-medium text-gray-700">* Estado</label>
                <select name="provincia" id="provincia" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" required>
                  {{#estados}}
                  <option value="{{id}}"{{#selected}} selected{{/selected}}>{{estado}}</option>
                  {{/estados}}
                </select>
              </div>
              <!-- / ESTADO -->
            </div>
            <div class="grid gap-4 grid-cols-[repeat(auto-fit,minmax(150px,1fr))]">
              <!-- CIUDAD -->
              <div class="mb-4">
                <label for="ciudad" class="block mb-2 text-sm font-medium text-gray-700">* Ciudad</label>
                <select name="ciudad" id="ciudad" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" required>
                  {{#ciudades}}
                  <option value="{{id}}"{{#selected}} selected{{/selected}}>{{ciudad}}</option>
                  {{/ciudades}}
                </select>
              </div>
              <!-- / CIUDAD -->
              <!-- ZONA -->
              <div class="mb-4">
                <label for="zona" class="block mb-2 text-sm font-medium text-gray-700">Zona en la ciudad</label>
                <div id="hZonas">
                  <select name="zona" id="zona" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" onChange="queZona();">
                    <option value="">-----</option>
                    {{#zonas}}
                    <option value="{{zona_esp}}"{{#selected}} selected{{/selected}}>{{zona_esp}}{{#tiene_mapa}} (c/mapa){{/tiene_mapa}}</option>
                    {{/zonas}}
                    <option value="---otra---">especificar zona =&gt;</option>
                  </select>
                  <script language="javascript">
                    {{{script_mapas}}}
                  </script>
                </div>
                <div id="e_zona"></div>
                <div id="hMapaZonas">
                  {{#mostrar_mapa}}
                  <img src="mapas/{{numero_mapa}}.gif" class="border-0" alt="Mapa de zona">
                  {{/mostrar_mapa}}
                </div>
              </div>
              <!-- / ZONA -->
            </div>
          </div>
        </td>
      </tr>
      <!-- / UBICACIÓN DEL INMUEBLE (parte 1) -->
      <tr>
        <td class="align-top">
          <div id="e_provincia">
            {{#mostrar_input_provincia}}
            <input type="text" name="esp_provincia" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" value="{{provincia_value}}" maxlength="50">
            {{/mostrar_input_provincia}}
          </div>
        </td>
        <td class="align-top">
          <div id="e_ciudad">
            {{#mostrar_input_ciudad}}
            <input type="text" name="esp_ciudad" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" value="{{ciudad_value}}" maxlength="70">
            {{/mostrar_input_ciudad}}
          </div>
        </td>
      </tr>
      <!-- UBICACIÓN DEL INMUEBLE (parte 2) -->
      <tr>
        <td colspan="4" class="ubica-inmueble">
          <div class="grid gap-4 grid-cols-[repeat(auto-fit,minmax(150px,1fr))]">
            <!-- COLONIA -->
            <div class="mb-4">
              <label for="colonia" class="block mb-2 text-sm font-medium text-gray-700">* Colonia</label>
              <select name="colonia" id="colonia" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" onChange="especificaColonia();" required>
                {{#colonias}}
                <option value="{{id}}"{{#selected}} selected{{/selected}}>{{colonia}}</option>
                {{/colonias}}
              </select>
              <div id="e_colonia">
                {{#mostrar_input_colonia}}
                <input type="text" name="esp_colonia" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" value="{{colonia_value}}" maxlength="70" onchange="return v_colonia(this);">
                {{/mostrar_input_colonia}}
                {{#mostrar_otra_colonia}}
                <span class="text-9">
                  <input type="checkbox" name="otra_colonia" id="otra_colonia" onClick="chk_otra_colonia(this);" checked>
                  <label for="otra_colonia" class="text-9">Deseo que en mi página aparezca la colonia de otra forma</label>
                </span>
                <div id="h_otra_colonia">
                  <span class="text-9">
                    Mostrar en mi página web la Colonia como:<br>
                    <input type="text" name="muestra_colonia" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" maxlength="30" value="{{muestra_colonia_value}}" onchange="return v_colonia(this);">
                  </span>
                </div>
                {{/mostrar_otra_colonia}}
              </div>
            </div>
            <!-- / COLONIA -->
            <!-- CODIGO POSTAL -->
            <div class="mb-4 max-w-[150px] mx-auto">
              <div class="text-left">
                <label for="codigo_postal" class="block mb-2 text-sm font-medium text-gray-700">Código postal</label>
                <input type="text" name="codigo_postal" id="codigo_postal" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" value="{{codigo_postal}}" maxlength="5" onchange="return val_numerico(this);">
              </div>
            </div>
            <!-- / CODIGO POSTAL -->
          </div>
          <script language="javascript">
            <!--
            function vCambios() {
              {{{script_variables}}}
              if ((document.Formulario1.nclaveprop.value != '' || document.Formulario1.tipo.value != f_tipo || document.Formulario1.expiracion.value != f_expiracion || document.Formulario1.pais.value != f_pais || document.Formulario1.provincia.value != f_provincia || document.Formulario1.ciudad.value != f_ciudad || document.Formulario1.colonia.value != f_colonia || document.Formulario1.zona.value != f_zona || document.Formulario1.codigo_postal.value != f_codigo_postal || (document.Formulario1.enventa.checked == true && f_enventa == 'No') || (document.Formulario1.enventa.checked == false && f_enventa == 'Si')) && AutorizaSalida == 'No') {
                event.returnValue = "¿ ESTÁS SEGURO(A) DE SALIR SIN GUARDAR LOS CAMBIOS HECHOS AL INMUEBLE ?";
              }
            }
            // 
            -->
          </script>
        </td>
      </tr>
      <!-- / UBICACIÓN DEL INMUEBLE (parte 2) -->
    </table>
  </td>
</tr> 