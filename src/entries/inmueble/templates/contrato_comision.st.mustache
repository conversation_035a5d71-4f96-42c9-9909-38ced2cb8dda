<!-- TIPO DE CONTRATO, ASESOR QUE CONTRATÓ Y COMISIONES ACORDADAS -->
<tr>
	<td>
		<div class="px-3 py-2 font-bold rounded text-mulbin-700 bg-mulbin-50">Detalles de la contratación</div>
		<div class="pt-2 pb-4 text-9 text-color-6">
			Los siguientes datos <span class="underline">NO APARECERÁN PUBLICADOS</span> en tu página web; en la <span class="font-bold">Multibolsa Inmobiliaria</span> solo aparecerán los porcentajes de comisión que compartes, SIN TIPO DE CONTRATACIÓN{{#hay_asesores}} NI ASESOR{{/hay_asesores}}.
		</div>
		<div class="grid gap-4 grid-cols-[repeat(auto-fit,minmax(250px,1fr))]">
			{{#hay_asesores}}
			<!-- ASESOR -->
			<div class="text-center max-w-[350px] mx-auto">
				<label for="asesor">As<PERSON><PERSON> que contrató</label>
				{{#asesor_solo_lectura}}
					{{asesor_nombre_completo}}<input type="hidden" name="asesor" value="{{asesor_id}}">
				{{/asesor_solo_lectura}}
				{{#asesor_seleccionable}}
				<select name="asesor" id="asesor" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]">
					<option value="0">seleccione ...</option>
					{{#asesores}}
					<option value="{{id}}"{{#selected}} selected{{/selected}}>{{nombre}} {{apellidos}}</option>
					{{/asesores}}
				</select>
				{{/asesor_seleccionable}}
			</div>
			<!-- / ASESOR -->
			{{/hay_asesores}}
			{{^hay_asesores}}
			<input type="hidden" name="asesor" value="0">
			{{/hay_asesores}}
			
			<!-- TIPO DE CONTRATACION -->
			<div class="text-center max-w-[350px] mx-auto">
				<label for="tipo_promocion">Tipo de contratación</label>
				<select name="tipo_promocion" id="tipo_promocion" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]"{{#socio_ampi_onchange}} onchange="v_bolsa_ampi(this);"{{/socio_ampi_onchange}}>
					<option value="">seleccione ...</option>
					<option value="DE PALABRA"{{#tipo_promocion_de_palabra_selected}} selected{{/tipo_promocion_de_palabra_selected}}>De palabra</option>
					<option value="CARTA AUTORIZACION"{{#tipo_promocion_carta_autorizacion_selected}} selected{{/tipo_promocion_carta_autorizacion_selected}}>Carta Autorización</option>
					<option value="EN OPCION"{{#tipo_promocion_en_opcion_selected}} selected{{/tipo_promocion_en_opcion_selected}}>Carta Opción</option>
					<option value="EN EXCLUSIVA"{{#tipo_promocion_en_exclusiva_selected}} selected{{/tipo_promocion_en_exclusiva_selected}}>En Exclusiva</option>
				</select>
			</div>
			<!-- / TIPO DE CONTRATACION -->
		</div> <!-- / grid contenedor -->
		
		{{#mostrar_portal_ampi}}
		<div>
			<!-- PARA PUBLICAR EN PORTAL AMPI -->
			<div>
				<div id="hPortalAMPI">
					{{#portal_ampi_actualizar}}
					<input type="button" name="portal_ampi" value="Actualizar confirmación de publicación del {{conf_portal_ampi_fecha}}" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" onClick="fAjax('ajPublicaPortalAmpi.php?id={{clave_sistema}}', 'hPortalAMPI')" />
					{{/portal_ampi_actualizar}}
					{{#portal_ampi_publicar}}
					<input type="button" name="portal_ampi" value="Publicar en portal AMPI" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]" onClick="fAjax('ajPublicaPortalAmpi.php?id={{clave_sistema}}', 'hPortalAMPI')" />
					{{/portal_ampi_publicar}}
				</div>
			</div>
			<!-- / PARA PUBLICAR EN PORTAL AMPI -->
		</div>
		{{/mostrar_portal_ampi}}
		
		<!-- COMISIONES -->
		<div class="pt-4 grid gap-4 grid-cols-[repeat(auto-fit,minmax(200px,1fr))]">
			{{#enventa}}
			<!-- COMISION DE VENTA -->
			<div>
				<div class="font-bold text-center">Comisión sobre venta de</div>
				<div class="font-bold text-center">${{precio_venta_formateado}} {{moneda}}</div>
				<div class="pt-4 flex items-center max-w-[350px] mx-auto text-center">
					<label for="i_porcentaje_comision">Porcentaje de la venta</label>
					<div class="flex items-center max-w-[150px] relative">
						<span class="absolute px-1 py-2 text-sm bg-gray-100 rounded-r right-1 top-1">%</span>
						<input type="text" name="i_porcentaje_comision" id="i_porcentaje_comision" maxlength="5" value="{{i_porcentaje_comision}}" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)] font-bold pr-8 number-format" d-config='{"decimals": 2}'>
					</div>
				</div>
				<div class="font-bold text-center text-12" id="h_comision"></div>
			</div>
			<!-- /COMISION DE VENTA -->
			{{/enventa}}

			{{#enrenta}}
			<!-- COMISION DE RENTA MENSUAL -->
			<div>
				<div class="font-bold text-center">
					Comisión de renta mensual ({{moneda}})
				</div>
				<div class="p-4 grid gap-4 grid-cols-[repeat(auto-fit,minmax(100px,1fr))]">
					<div class="max-w-[350px] mx-auto text-center">
						<input type="radio" name="i_tipo_comision_rta" id="i_tipo_comision_rta_1" value="1 mes" onClick="v_tcr(this);"{{#i_tipo_comision_rta_1_mes_checked}} checked{{/i_tipo_comision_rta_1_mes_checked}}>
						<label for="i_tipo_comision_rta_1">1 mes de renta</label>
					</div>
					<div class="max-w-[350px] mx-auto text-center">
						<input type="radio" name="i_tipo_comision_rta" id="i_tipo_comision_rta_2" value="cantidad fija" onClick="v_tcr(this);"{{#i_tipo_comision_rta_cantidad_fija_checked}} checked{{/i_tipo_comision_rta_cantidad_fija_checked}}>
						<label for="i_tipo_comision_rta_2">Cantidad fija</label>
					</div>
				</div>
				<div class="flex items-center max-w-[150px] mx-auto">
					<span class="px-2 py-2 text-lg border border-r-0 border-gray-300 rounded-l bg-gray-50">$</span>
					<input type="text" name="i_comision_rta" id="i_comision_rta" class="block w-full px-3 py-2 text-base leading-6 text-gray-700 bg-white border border-gray-300 rounded-r transition-all duration-150 ease-in-out focus:text-gray-700 focus:bg-white focus:border-blue-400 focus:outline-none focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)] font-bold text-right text-14 currency-format" maxlength="9"{{#i_comision_rta_disabled}} disabled{{/i_comision_rta_disabled}} value="{{i_comision_rta_value}}">
				</div>
			</div>
			<!-- /COMISION DE RENTA MENSUAL -->
			{{/enrenta}}
		</div>
	</td>
</tr>
<!-- /TIPO DE CONTRATO, ASESOR QUE CONTRATÓ Y COMISIONES ACORDADAS -->

<script>
	document.addEventListener("DOMContentLoaded", function() {
		var porcentaje = document.getElementById('i_porcentaje_comision');
		if (porcentaje) {
			porcentaje.addEventListener("input", function(e) {
				calcula_comision(porcentaje);
			});
			porcentaje.dispatchEvent(new Event("input"));
		}
	});
</script>