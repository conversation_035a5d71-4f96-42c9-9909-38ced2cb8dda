<!-- <selector.inmueble_z1> -->
<script>
vals_ing.d_{{ variable }} = {
    {{# options }}
    "{{ id }}": "{{ valor_ing }}",
    {{/ options }}
};
</script>
<div class="{{ ti_classes }}">
	<label for="{{ variable }}" class="d-flex">
		<div class="left">{{ nombre_esp }}</div>
	</label>
	<div>
		<div class="d-flex {{# complemento_al_valor }}input-with-complement-right-2{{/ complemento_al_valor }}">
			{{# complemento_al_valor }}<span>{{ complemento_al_valor }}</span>{{/ complemento_al_valor }}
			<select name="{{ variable }}" id="{{ variable }}" class="form-control"
				onChange="return v_selector(this, 'd_{{ variable }}', {{ longitud }});"
			>
				<option value=""></option>
				{{# options }}
				<option value="{{ id }}" {{# selected }}selected{{/ selected }}>{{ valor_esp }}</option>
				{{/ options }}
				<option value="-1">&lt;--definir--&gt;</option>
			</select>
		</div>
		<div id="EN_d_{{ variable }}" class="input-container-display-values">
			{{# value.en }}EN: {{ value.en }}{{/ value.en }}
		</div>
		<div id="d_{{ variable }}" class="hidden">
			<!-- <input type="text" class="form-control" name="d_{{ variable }}_esp" placeholder="..."> -->

			<div class="input-container" id="component_{{ variable }}">
			    <input type="text" id="textInput_{{ variable }}" name="textInput_{{ variable }}" placeholder="..." class="form-control">
			    <button type="button" id="languageButton_{{ variable }}" class="lang-btn">ES</button>
			    <div id="languageDropdown_{{ variable }}" class="dropdown">
			        <label>
			            <input type="radio" name="language_{{ variable }}" value="EN">
			            ENGLISH
			        </label>
			        <label>
			            <input type="radio" name="language_{{ variable }}" value="ES" checked>
			            ESPAÑOL
			        </label>
			    </div>
			    <!-- Campos hidden para cada idioma -->
			    <input type="hidden" name="d_{{ variable }}_esp" id="textInput_{{ variable }}_ES" value="{{ value_esp }}">
			    <input type="hidden" name="d_{{ variable }}_ing" id="textInput_{{ variable }}_EN" value="{{ value_ing }}">
			    <input type="hidden" name="d_{{ variable }}_fra" id="textInput_{{ variable }}_FR" value="{{ value_fra }}">
			</div>
			<div id="displayValues_{{ variable }}" class="input-container-display-values"></div>

		</div>
	</div>
</div>
