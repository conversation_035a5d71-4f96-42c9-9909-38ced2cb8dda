<!-- PRECIO -->
<tr>
	<td align="left" width="100%" valign="top" bgcolor="#F0F0F0">
		<div class="pb-1 bold">
	  		Promover inmueble para
	  	</div>
	  	<div class="contenedor min-325">
		  	<div class="contenedor min-150">
		  		<!-- VENTA -->
		  		<div id="pventa" class="max-200">
		  			<div>
		  				<label for="precio_venta" class="d-flex">
		  					<div class="check-price">
		  						<input type="checkbox" name="enventa" id="enventa" value="Si" 
		  							onclick="act_deact_price(this, 'pventa')"
		  							{{#enventa_checked}}checked{{/enventa_checked}}
		  						/><label for="enventa"></label>		  						
		  					</div>
		  					<div class="left w-100 mb-1/2">
		  						<span class="campos" id="chMostrarVenta">
		  							<input type="hidden" name="MC_Venta" 
		  								value="{{campos_mc_venta}}"
		  							/>
		  							{{campos_mc_venta}}
		  						</span>		  						
		  					</div>
		  				</label>
		  				<div class="price-container{{#enventa_hidden}} hidden{{/enventa_hidden}}">
			  				<div class="price-format">
			  					<span>$</span>
			  					<input type="text" name="precio_venta" id="precio_venta" 
			  						autocomplete="off" 
			  						class="form-control currency-format"
			  						value="{{precio_venta}}"
		  						/>
			  				</div>
			  				<div class="p-5px center">
			  					<input type="checkbox" name="precio_por_metro" id="precio_por_metro" value="Si" 
				  					{{#precio_por_metro_checked}}checked{{/precio_por_metro_checked}}
			  					/><label for="precio_por_metro">Precio por m²</label>
			  				</div>
		  				</div>
		  			</div>
		  		</div>
		  		<!-- / VENTA -->
		  		<!-- RENTA MENSUAL -->
		  		<div id="prenta" class="max-200">
		  			<div>
		  				<label for="precio_renta" class="d-flex">
		  					<div class="check-price">
		  						<input type="checkbox" name="enrenta" id="enrenta" value="Si"
	                	onclick="act_deact_price(this, 'prenta')"
	                	{{#enrenta_checked}}checked{{/enrenta_checked}}
	                	{{#enrenta_disabled}}disabled{{/enrenta_disabled}}
									/><label for="enrenta"></label>
		  					</div>
		  					<div class="left w-100 mb-1/2">
		  						<span class="campos" id="chMostrarRenta">
		  							<input type="hidden" name="MC_Renta" 
		  								value="{{campos_mc_renta}}"
		  							/>
		  							{{campos_mc_renta}}
		  						</span>
		  					</div>
		  				</label>
		  				<div class="price-container{{#enrenta_hidden}} hidden{{/enrenta_hidden}}">
		  					<div class="price-format">
			  					<span>$</span>
			  					<input type="text" name="precio_renta" id="precio_renta" 
			  						autocomplete="off" 
			  						class="form-control currency-format"
			  						value="{{precio_renta}}" 
		  						/>
		  					</div>
		  					<div class="center">
		  						por mes
		  					</div>
		  				</div>
		  			</div>
		  		</div>
		  		<!-- / RENTA MENSUAL -->
		  	</div>
		  	<div class="contenedor min-150">
		  		<!-- VACACIONAL -->
		  		<div id="pdiaria" class="max-200">
		  			<div>
		  				<label for="precio_diaria" class="d-flex">
		  					<div class="check-price">
                  <input type="checkbox" name="endiaria" id="endiaria" value="Si" 
                  	onclick="act_deact_price(this, 'pdiaria')" 
                  	{{#endiaria_checked}}checked{{/endiaria_checked}}
                  	{{#endiaria_disabled}}disabled{{/endiaria_disabled}}
                	/><label for="endiaria"></label>
		  					</div>
		  					<div class="left w-100 mb-1/2">
		  						Eventual
		  					</div>
		  				</label>
		  				<div class="price-container{{#endiaria_hidden}} hidden{{/endiaria_hidden}}">
		  					<div class="price-format">
			  					<span>$</span>
			  					<input type="text" name="precio_diaria" id="precio_diaria" 
			  						autocomplete="off" 
			  						class="form-control currency-format" 
			  						value="{{precio_diaria}}" 
		  						/>
		  					</div>
		  					<div class="center">
		  						por día
		  					</div>
		  				</div>
		  			</div>
		  		</div>
		  		<!-- / VACACIONAL -->
		  		<!-- TRASPASO -->
		  		<div id="ptraspaso" class="max-200">
		  			<div>
		  				<label for="precio_traspaso" class="d-flex">
		  					<div class="check-price">
                  <input type="checkbox" name="entraspaso" id="entraspaso" value="Si" 
                  	onclick="act_deact_price(this, 'ptraspaso')" 
                  	{{#entraspaso_checked}}checked{{/entraspaso_checked}}
                  	{{#entraspaso_disabled}}disabled{{/entraspaso_disabled}}
                	/><label for="entraspaso"></label>
		  					</div>
		  					<div class="left w-100 mb-1/2">
		  						Traspaso
		  					</div>
		  				</label>
		  				<div class="price-container{{#entraspaso_hidden}} hidden{{/entraspaso_hidden}}">
		  					<div class="price-format">
			  					<span>$</span>
					  			<input type="text" name="precio_traspaso" id="precio_traspaso"
					  				autocomplete="off"
					  				class="form-control currency-format"
			  						value="{{precio_traspaso}}" 
				  				/>
		  					</div>
		  				</div>
		  			</div>
		  		</div>
		  		<!-- / TRASPASO -->
		  	</div>
		  </div>
	  	<div class="m-1">
	  		<div class="center d-flex max-300">
	  			<label for="divisa" class="right">Promoción en:</label>
				<select name="moneda" id="moneda" class="form-control">
					{{#monedas}}
					<option value="{{siglas}}"{{#selected}} selected{{/selected}}>({{siglas}}) {{p_nombre_esp}}</option>
					{{/monedas}}
				</select>
	  		</div>
	  	</div>
    </td>
</tr>
<!-- /PRECIO --> 