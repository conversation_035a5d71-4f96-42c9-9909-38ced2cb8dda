<script>
contaerror = 0;

function abre(pag, ventana) {
	window.open(pag,ventana,"toolbar=yes,scrollbars=yes,resizable=yes,width=580,height=350");
	return;
}

function lTrim(sStr){ 
	while (sStr.charAt(0) == " ")  
		sStr = sStr.substr(1, sStr.length - 1); 
	return sStr; 
} 

function rTrim(sStr){ 
	while (sStr.charAt(sStr.length - 1) == " ")  
		sStr = sStr.substr(0, sStr.length - 1); 
	return sStr; 
} 

function allTrim(sStr){ 
	return rTrim(lTrim(sStr)); 
} 

function oNumero(numero) {
	//Propiedades 
	this.valor = numero || 0
	this.dec = -1;
	//Métodos 
	this.formato = numFormat;
	this.ponValor = ponValor;
	//Definición de los métodos 

	function ponValor(cad) {
		if (cad =='-' || cad=='+') return
		if (cad.length ==0) return
		if (cad.indexOf('.') >=0)
			this.valor = parseFloat(cad);
		else 
			this.valor = parseInt(cad);
	} 
	function numFormat(dec, miles) {
		var num = this.valor, signo=3, expr;
		var cad = ""+this.valor;
		var ceros = "", pos, pdec, i;
		for (i=0; i < dec; i++)
		ceros += '0';
		pos = cad.indexOf('.')
		if (pos < 0)
			cad = cad+"."+ceros;
		else {
			pdec = cad.length - pos -1;
			if (pdec <= dec)
				{
				for (i=0; i< (dec-pdec); i++)
					cad += '0';
				}
			else
				{
				num = num*Math.pow(10, dec);
				num = Math.round(num);
				num = num/Math.pow(10, dec);
				cad = new String(num);
				}
		}
		pos = cad.indexOf('.')
		if (pos < 0) pos = cad.lentgh
		if (cad.substr(0,1)=='-' || cad.substr(0,1) == '+') 
			   signo = 4;
		if (miles && pos > signo)
			do{
				expr = /([+-]?\d)(\d{3}[\.\,]\d*)/
				cad.match(expr)
				cad=cad.replace(expr, RegExp.$1+','+RegExp.$2)
				}
		while (cad.indexOf(',') > signo)
			if (dec<0) cad = cad.replace(/\./,'')
				return cad;
	}
}

{{#enventa}}
var precio_venta = {{precio_venta}};
{{/enventa}}

{{#enrenta}}
var precio_renta = {{precio_renta}};
{{#comision_rta_1_mes}}
var comision_rta = {{precio_renta}};
{{/comision_rta_1_mes}}
{{#comision_rta_cantidad_fija}}
var comision_rta = {{i_comision_rta}};
{{/comision_rta_cantidad_fija}}
{{/enrenta}}

var g_comision = {{g_comision}};

function val_numerico(campo, dec) {
	var checkStr = campo.value;
	var allValid = true;
	var allNum = "";
	if (dec > 0) {
		var checkOK = "0123456789.";
		var msg = 'Teclee sólo dígitos sin comas o deje el campo en blanco.';
	}else {
		var checkOK = "0123456789";
		var msg = 'Teclee sólo dígitos sin puntos ni comas o deje el campo en blanco.';
	}
	for (i = 0;  i < checkStr.length;  i++) {
		ch = checkStr.charAt(i);
		for (j = 0;  j < checkOK.length;  j++) if (ch == checkOK.charAt(j)) break;
		if (j == checkOK.length) {
		allValid = false;
		break;
		}
		allNum += ch;
	}
	if (!allValid) {
		alert(msg);
		campo.focus();
		contaerror++;
		if (contaerror > 2) campo.value = "";
		return (false);
	}
	return (true);
}

function calcula_comision(comision) {
	if (!val_numerico(comision, 2)) {
		return(false);
	}
	if (comision.value > 0) {
		vComision = new oNumero(precio_venta * (comision.value / 100));
		g_comision = vComision.valor;
		document.getElementById('h_comision').innerHTML = 'Comisión: $' + vComision.formato(0, true) + 
			' {{moneda}}' + 
			'{{#precio_por_metro}} /m²{{/precio_por_metro}}';
	}else {
		document.getElementById('h_comision').innerHTML = '';
		g_comision = 0;
	}

	// Las funciones de bolsa inmobiliaria se manejan en su propio template
	if (document.Formulario1.t_comparto_comision && document.Formulario1.t_comparto_comision.value == 'sobre comision' && typeof calcula_comision_mibolsa === 'function') {
		calcula_comision_mibolsa();
	}

	return;
}





function v_tcr(campo) {
	if (campo.value == '1 mes') {
		document.Formulario1.i_comision_rta.disabled = true;
	}else {
		document.Formulario1.i_comision_rta.disabled = false;
		document.Formulario1.i_comision_rta.focus();
	}
}

function Valida_Campos(theForm) {
	{{#enventa}}
	if (theForm.comparto_comision.value <= 7 && theForm.t_comparto_comision.value == 'sobre comision') {
		theForm.t_comparto_comision.value = 'sobre precio';
	}else if (theForm.comparto_comision.value >= 20 && theForm.t_comparto_comision.value == 'sobre precio') {
		theForm.t_comparto_comision.value = 'sobre comision';
	}
	{{/enventa}}
	{{#validacion_ampi}}
	if (theForm.tipo_promocion.value == 'EN EXCLUSIVA') {
		if (theForm.comision_ampi.value <= 7 && theForm.t_comision_ampi.value == 'sobre comision') {
			theForm.t_comision_ampi.value = 'sobre precio';
		}else if (theForm.comision_ampi.value >= 20 && theForm.t_comision_ampi.value == 'sobre precio') {
			theForm.t_comision_ampi.value = 'sobre comision';
		}
	}
	{{/validacion_ampi}}

	if (!verificaContacto(theForm)) {
		return(false);
	}	// if

	return(true);
}
</script> 