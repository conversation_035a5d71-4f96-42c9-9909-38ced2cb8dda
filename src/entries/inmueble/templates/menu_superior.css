.modalDialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 5;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
  -webkit-transition: opacity 0.3s, visibility 0.3s;
  -moz-transition: opacity 0.3s, visibility 0.3s;
  transition: opacity 0.3s, visibility 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
body.modalPanel {
  overflow: hidden;
}
body.modalPanel #modalPanel {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  z-index: 100;
}
body.modalBolsaInfo {
  overflow: hidden;
}
body.modalBolsaInfo #modalInfoBolsa {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  z-index: 100;
}
body.modalmarkdownModal {
  overflow: hidden;
}
body.modalmarkdownModal #markdownModal {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  z-index: 100;
}
.modalDialog > div.modal-container {
  margin: 0 auto;
  border-radius: 8px;
  max-width: 99vw;
  max-height: 98vh;
  width: 99vw;
  height: 98vh;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  background-color: white;
  position: relative;
}
.modalDialog > div {
  margin: 0 auto;
  border-radius: 8px;
  max-width: 99vw;
  max-height: 98vh;
  width: 99vw;
  height: 98vh;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.modalDialog > div > iframe {
  width: 100%;
  border: none;
  min-height: 200px;
  height: 100%;
  overflow-x: hidden;
  background-color: #fff;
}
.modalDialog > img,
.modalDialog .modal-close {
  color: #fff;
  font-size: 25px;
  width: 25px;
  position: absolute;
  right: 25px;
  top: 16px;
  opacity: 0.7;
  cursor: pointer;
  z-index: 101;
}
.modalDialog > img:hover,
.modalDialog .modal-close:hover {
  opacity: 0.9;
}
