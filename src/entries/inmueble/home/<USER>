<?php

$rec_template = function ($template) {
    return file_get_contents(__DIR__.'/templates/'.$template.'.htm');
};

/* RECUPERO LOS TEMPLATES */
$templ = array(
  'dashboard' => $rec_template('dashboard'),
  'subs' => array(
    'reg_edit_del' => $rec_template('reg_edit_del'),
    'reg_edit_desarrollos' => $rec_template('reg_edit_desarrollos'),
    'listado_impresion' => $rec_template('listado_impresion'),
    'personaliza_ficha' => $rec_template('personaliza_ficha'),
    'publicacion_externa' => $rec_template('publicacion_externa'),
  )
);

$data = array(
  'desarrollos' => array(
    'exists' => 0,
    'items' => array()
  )
);


/* Obtenemos los datos de los desarrollos */
// Deshabilitamos temporalmente el área de desarrollos por cuestiones de UPGRADE
// $query = "SELECT claveprop, nombredes
//   FROM desarrollos
//   WHERE contrato='{$config['contrato']}'
//   ORDER BY nombredes ASC";
// $consulta1 = $sql->query($query);
// $data['desarrollos']['exists'] = $consulta1->num_rows;
// while ($des = $consulta1->fetch_assoc()) {
//     $data['desarrollos']['items'][] = array(
//         'claveprop' => $des['claveprop'],
//         'nombredes' => $des['nombredes']
//     );
// }

// Recupero los tipos de propiedades
$data['tipo_inmuebles'] = array();
$consulta1 = mysqlQuery("SELECT * FROM tipo_inmuebles", $db_link);
while ($row = mysql_fetch_array($consulta1)) {
    $data['tipo_inmuebles'][$row['clave']] = $row['p_tipo_esp'];
}

// Recuperar los tipos de propiedades
$data['tipos_propiedades'] = array();
$consulta1 = mysqlQuery("
  SELECT DISTINCT(tipo) FROM propiedades 
  WHERE contrato='{$config['contrato']}'
  ORDER BY tipo ASC
  ", $db_link);
while ($row = mysql_fetch_assoc($consulta1)) {
    $data['tipos_propiedades'][] = array(
      'valor' => $row['tipo'],
      'texto' => $data['tipo_inmuebles'][$row['tipo']]
    );
}

// Recuperar las colonias
$data['colonias'] = array();
$consulta1 = mysqlQuery("
  SELECT DISTINCT(colonia) FROM propiedades 
  WHERE contrato='{$config['contrato']}' 
  ORDER BY colonia ASC
  ", $db_link);
while ($row = mysql_fetch_assoc($consulta1)) {
    $data['colonias'][] = $row;
}

// Recupero los asesores
$data['asesores'] = array();
$consulta1 = mysqlQuery("
  SELECT * FROM asesores 
  WHERE contrato='{$config['contrato']}'
  ", $db_link);
while ($row = mysql_fetch_assoc($consulta1)) {
    $data['asesores'][] = $row;
}

$partials = array_map(function ($templ) use (&$data, &$mustache) {
    return $mustache->render($templ, $data);
}, $templ['subs']);
$mustache->setPartials($partials);

$num_inmuebles = $sql->query("SELECT COUNT(*) FROM propiedades WHERE contrato='$config[contrato]'")->fetch_row()[0];

echo $mustache->render($templ['dashboard'], array(
  'title' => 'Mis Inmuebles',
  'data' => &$data,
  'num_inmuebles' => $num_inmuebles
));
