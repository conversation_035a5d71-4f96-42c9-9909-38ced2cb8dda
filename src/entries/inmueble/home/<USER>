<?php

$consulta1 = mysqlQuery("
  SELECT claveprop, nombredes FROM desarrollos 
  WHERE contrato='{$config['contrato']}'
  ORDER BY nombredes
  ", $db_link);

if (mysql_num_rows($consulta1) > 0) : ?>
  <div>
    <script language="javascript">
      function valida_clvdes(theForm) {
        if (theForm.claveprop.value == '') {
          alert('Selecciona la clave del desarrollo que deseas editar ...');
          theForm.claveprop.focus();
          return (false);
        } // if
        return (true);
      }
    </script>
    <form method="POST" action="minmueble.php" onsubmit="return valida_clvdes(this);" class="center max-350">
      <div class="d-flex">
        <select name="claveprop" id="clavedes" class="form-control">
          <option value="">seleccione desarrollo</option>
          <?php

          while ($des = mysql_fetch_array($consulta1))
            echo '<option value="' . htmlspecialchars($des['claveprop']) . '">' . htmlspecialchars($des['claveprop']) . ' - ' . htmlspecialchars($des['nombredes']) . "</option>\n";

          ?>
        </select>
        <input type="submit" value="..." class="form-control max-50 btn-primary">
      </div>
      <INPUT TYPE="hidden" name="ppinmueble" value="Desarrollo">
      <INPUT TYPE="hidden" name="paso" value="1">
    </form>
  </div>

<?php endif ?>