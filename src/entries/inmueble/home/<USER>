      <form method="POST" action="minmueble.php" onsubmit="return Valida1(this);" language="JavaScript">
        <div class="d-flex">
          <div class="contenedor min-250 gap-05">
              <div>
                <label for="autoc_claveprop_1">Registrar o editar la clave</label>
                <input type="text" autocomplete=off name="claveprop"
                  class="form-control"
                  maxlength="25"
                  onChange="return val_claveprop(this);" style="font-weight: bold"
                  id="autoc_claveprop_1">
              </div>
            <div>
              <label for="ppinmueble" class="left">Tipo</label>
              <div class="flex">
                <select name="ppinmueble" id="ppinmueble" 
                  class="form-control" 
                  onchange="if (this.value=='Desarrollo') {this.form.padesarrollo.disabled=true}else{this.form.padesarrollo.disabled=false}">
                  <option value="Inmueble">Inmueble</option>
                  <option value="Desarrollo">Desarrollo</option>
                </select>
                <input type="submit" value="Continuar -&gt;" class="form-control max-100 btn-primary">
              </div>
            </div>
          </div>
          <div class="w-50 noPeque">
            <div class="text-9">Para registrar un nuevo inmueble o desarrollo: Teclea la clave que deseas asignar al nuevo inmueble y haz clic en "Continuar-&gt;"</div>
            <div class="text-9">Para editar un inmueble o desarrollo existente: Teclea la clave del inmueble que deseas modificar y haz clic en "Continuar-&gt;"</div>
          </div>
        </div>
<?php 
IF ($sql->row("SELECT * FROM desarrollos WHERE contrato='{$config['contrato']}'")) : ?>
        <div>
          <INPUT TYPE="checkbox" NAME="padesarrollo" id="padesarrollo" VALUE="Si">
          <label for="padesarrollo">El inmueble pertenecerá a un desarrollo</label>
        </div>
<?php ENDIF ?>
          <input type="hidden" name="paso" value="1">
      </form>
