document.addEventListener("DOMContentLoaded", function() {
  const tipoListado = document.getElementsByName('tipo_listado');
  const listaPorCaracteristicas = document.getElementById('lista_por_caracteristicas');
  const listaPorClaves = document.getElementById('lista_por_claves');
  // Agregar un escucha de eventos para detectar cuando el usuario cambia la opción seleccionada
  for (let i = 0; i < tipoListado.length; i++) {
    tipoListado[i].addEventListener('change', function() {
      listaPorCaracteristicas.querySelectorAll('select, input, label, div').forEach(elem => {
        elem.disabled = (tipoListado[i].value === 'por claves');
      });
      listaPorClaves.querySelectorAll('select, input, label, div').forEach(elem => {
        elem.disabled = (tipoListado[i].value === 'por caracteristicas');
      });
    });
  }
});
