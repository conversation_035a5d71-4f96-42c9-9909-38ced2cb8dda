<!-- LISTADO E IMPRESION DE INMUEBLES -->
<?php

// Recupero los tipos de propiedades
$tipo_inmuebles = array();
$consulta1 = mysqlQuery("SELECT * FROM tipo_inmuebles", $db_link);
while ($row = mysql_fetch_array($consulta1)) {
  $tipo_inmuebles[$row['clave']] = $row['p_tipo_esp'];
}

?>
<div>
  <form method="POST" action="verinmuebles.php" name="Formulario1">
    <!-- LISTAR POR CLAVES -->
    <div class="max-350 center">
      <div class="center max-150">
        <input type="radio" value="por claves" name="tipo_listado" id="tipo_listado_por_claves" checked>
        <label for="tipo_listado_por_claves">Listar por claves | Listar todo</label>
      </div>

      <div id="lista_por_claves">
        <div class="py-1">
          <input type="text" autocomplete=off name="claves_props" size="25" class="form-control" onChange="return val_claveprop(this);" id="autoc_claveprop_2" placeholder="Claves separadas por coma (clave1,clave2)">
        </div>

        <div width="100%" align="center" colspan="3">
          <input type="submit" value="Continuar -&gt;" name="bsubmit2" class="form-control btn-primary">
          <div class="observaciones">Deja en blanco las claves y haz clic en <I>Continuar -&gt;</I> para listar todos los inmuebles</div>
        </div>

      </div>

    </div>

    <!-- /LISTAR POR CLAVES -->
    <hr class="text-color-2" />
    <!-- LISTAR POR CARACTERÍSTICAS -->

    <div>


      <div class="mb-1 center max-250">
        <input type="radio" value="por caracteristicas" name="tipo_listado" id="tipo_listado_por_caracteristicas">
        <label for="tipo_listado_por_caracteristicas">Listar por Características</label>
      </div>


    </div>

    <!-- LISTA_POR_CARACTERISTICAS -->
    <div id="lista_por_caracteristicas">
      <!-- BLOQUE RESPONSIVO -->
      <div class="contenedor min-200">
        <!-- TIPO_INMUEBLES -->
        <div>
          <select size="5" name="tipo[]" id="tipo" multiple class="form-control" disabled>
            <option value="" selected>Todos los inmuebles ...</option>
            <?php
            $sql->each(
              "
  SELECT DISTINCT(tipo) FROM propiedades 
  WHERE contrato='{$config['contrato']}' 
  ORDER BY tipo ASC",
              function ($row) use (&$tipo_inmuebles) {
            ?><option value="<?= $row['tipo'] ?>"><?= $tipo_inmuebles[$row['tipo']] ?></option><?php
                                                                                      }
                                                                                    );
                                                                                        ?>
          </select>
        </div>
        <!-- / TIPO_INMUEBLES -->
        <!-- EN VENTA | EN RENTA |... -->
        <div>
          <div>
            <input type="checkbox" name="enventa" id="enventa" value="Si" checked disabled>
            <label for="enventa">En venta</label>
          </div>
          <div>
            <input type="checkbox" name="enrenta" id="enrenta" value="Si" checked disabled>
            <label for="enrenta">En renta</label>
          </div>
          <div>
            <input type="checkbox" name="endiaria" id="endiaria" value="Si" checked disabled>
            <label for="endiaria">Eventual</label>
          </div>
          <div>
            <input type="checkbox" name="entraspaso" id="entraspaso" value="Si" checked disabled>
            <label for="entraspaso">En traspaso</label>
          </div>
        </div>
        <!-- / EN VENTA | EN RENTA |... -->
        <!-- PUBLICADOS / NO PUBL... -->
        <div>
          <div>
            <input type="radio" value="todos" name="status_web" id="status_web_todos" checked disabled>
            <label for="status_web_todos">Publicados y no publicados</label>
          </div>
          <div>
            <input type="radio" value="publicados" name="status_web" id="status_web_publicados" disabled>
            <label for="status_web_publicados">Los publicados</label>
          </div>
          <div>
            <input type="radio" value="sin publicar" name="status_web" id="status_web_sin_publicar" disabled>
            <label for="status_web_sin_publicar">Los no publicados</label>
          </div>
        </div>
        <!-- / PUBLICADOS / NO PUBL... -->
        <!-- CONTRATOS VIGENTES,... -->
        <div>
          <input type="radio" value="todos" name="ver_contrataciones" id="ver_contrataciones_todos" checked disabled>
          <label for="ver_contrataciones_todos">Todos los contratos</label>
          <input type="radio" value="vigentes" name="ver_contrataciones" id="ver_contrataciones_vigentes" disabled>
          <label for="ver_contrataciones_vigentes">Contratos vigentes</label>
          <input type="radio" value="caducados" name="ver_contrataciones" id="ver_contrataciones_caducados" disabled>
          <label for="ver_contrataciones_caducados">Contratos caducados</label>
        </div>
        <!-- /CONTRATOS VIGENTES,... -->
        <!-- COLONIAS -->
        <div>
          <select size="5" name="colonia[]" id="colonia" multiple class="form-control" disabled>
            <option value="" selected>Todas las colonias ...</option>
            <?php
            $sql->each(
              "
  SELECT DISTINCT(colonia) FROM propiedades 
  WHERE contrato='{$config['contrato']}' 
  ORDER BY colonia ASC",
              function ($row) {
            ?><option><?= $row['colonia'] ?></option><?php
                                            }
                                          );
                                              ?>
          </select>
        </div>
        <!-- / COLONIAS -->
        <!-- PRECIO FILTRO -->
        <div>
          <span class="campos">Filtrar precio (MXP) de</span>
          <select size="1" name="t_precio" class="form-control" disabled>
            <option value="precio_venta_mxp">venta</option>
            <option value="precio_renta_mxp">renta mensual</option>
            <option value="precio_diaria_mxp">renta diaria</option>
            <option value="precio_traspaso_mxp">traspaso</option>
          </select>
          <div class="gap-0 contenedor min-100">
            <div class="price-format">
              <span>$</span>
              <input type="text" name="precio1" id="precio1" class="px-0 font-normal form-control currency-format text-1" disabled placeholder="desde">
            </div>
            <div class="price-format">
              <span>$</span>
              <input type="text" name="precio2" id="precio2" class="px-0 font-normal form-control currency-format text-1" disabled placeholder="hasta">
            </div>
          </div>
        </div>
        <!-- /PRECIO FILTRO -->
      </div>
      <!-- / BLOQUE RESPONSIVO -->
      <!-- ASESORES -->
      <div class="mt-1 max-350 center">
        <label for="asesor">Listar inmuebles contratados por</label>
        <select name="asesor" id="asesor" class="form-control" disabled>
          <option value="">Todos los asesores</option>
          <?php

          // Recupero los asesores
          $consulta1 = mysqlQuery("SELECT * FROM asesores WHERE (contrato='$config[contrato]')", $db_link);
          while ($row = mysql_fetch_array($consulta1)) echo "<option value=\"$row[id]\">$row[nombre] $row[apellidos]</option>";

          ?>
        </select>
        <input type="submit" value="Continuar -&gt;" name="bsubmit1" disabled class="my-1 form-control btn-primary">
      </div>
      <!-- / ASESORES -->
    </div>
    <!-- /LISTA_POR_CARACTERISTICAS -->

    <!-- /LISTAR POR CARACTERÍSTICAS -->
  </form>
</div>
<!-- /LISTADO E IMPRESION DE INMUEBLES -->