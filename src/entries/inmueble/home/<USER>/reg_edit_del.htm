<form
  method="POST"
  action="minmueble.php"
  onsubmit="return Valida1(this);"
  language="JavaScript"
>
  <div class="d-flex">
    <div class="contenedor min-250 gap-05">
      <div>
        <label for="autoc_claveprop_1"
          >Editar o registrar nuevo inmueble con clave</label
        >
        <input
          type="text"
          autocomplete="off"
          name="claveprop"
          class="form-control"
          maxlength="25"
          placeholder="Clave del inmueble"
          onChange="return val_claveprop(this);"
          style="font-weight: bold"
          id="autoc_claveprop_1"
        />
      </div>
      <div>
        <label for="ppinmueble" class="left">Tipo</label>
        <div class="flex">
          <select
            name="ppinmueble"
            id="ppinmueble"
            class="form-control"
            onchange="if (this.value=='Desarrollo') {this.form.padesarrollo.disabled=true}else{this.form.padesarrollo.disabled=false}"
          >
            <option value="Inmueble">Inmueble</option>
            <option value="Desarrollo">Desarrollo</option>
          </select>
          <input
            type="submit"
            value="Continuar"
            class="form-control max-100 btn-primary"
          />
        </div>
      </div>
    </div>
    <div class="hidden w-50 noPeque">
      <div class="text-9">
        Para registrar un nuevo inmueble o desarrollo: Teclea la clave que
        deseas asignar al nuevo inmueble y haz clic en "Continuar"
      </div>
      <div class="text-9">
        Para editar un inmueble o desarrollo existente: Teclea la clave del
        inmueble que deseas modificar y haz clic en "Continuar"
      </div>
    </div>
  </div>
  {{#desarrollos.exists}}
  <div>
    <input type="checkbox" name="padesarrollo" id="padesarrollo" value="Si" />
    <label for="padesarrollo">El inmueble pertenecerá a un desarrollo</label>
  </div>
  {{/desarrollos.exists}}
  <input type="hidden" name="paso" value="1" />
</form>
