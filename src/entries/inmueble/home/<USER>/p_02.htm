<form method="POST" action="verinmuebles.php" name="Formulario1">
  <table border="0" width="100%" id="table1" cellspacing="0" cellpadding="0">
    <tr>
      <td colspan="3" style="padding-bottom: 10px">Listar inmuebles con las siguientes características:</td>
    </tr>
    <!-- LISTAR POR CLAVES -->
    <tr>
      <td valign="top" width="100%" colspan="3">
        <table border="0" width="100%" id="table3" cellspacing="0" cellpadding="0">
          <tr>
            <td width="41%" align="right">
              <input type="radio" value="por claves" name="tipo_listado" onClick="vTipoListado(this);" checked>
            </td>
            <td width="59%">Listar por Claves</td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td valign="top" width="100%" style="padding-bottom: 5px" colspan="3">
        <table border="0" width="100%" id="table2" cellspacing="0" cellpadding="0">
          <tr>
            <td width="23%" align="right"><span class="campos">Listar las claves:</span></td>
            <td width="28%" align="center">
              <input type="text" autocomplete=off name="claves_props" size="25" class="form_peque" onChange="return val_claveprop(this);" id=autoc_claveprop_2>
            </td>
            <td width="49%"><span class="observaciones">Teclea las claves separadas por coma(,)<br>por ejemplo: Clave01,Clave02,Clave03</span></td>
          </tr>
          <tr>
            <td width="100%" align="center" colspan="3">
              <input type="submit" value="Continuar -&gt;"  name="bsubmit2"><br>
              <span class="observaciones">Deja en blanco las claves y haz clic en <I>Continuar -&gt;</I> para listar todos los inmuebles</span>
            </td>
          </tr>
          <!-- /LISTAR POR CLAVES -->
          <!-- LISTAR POR CARACTERÍSTICAS -->
          <tr>
            <td valign="top" width="100%" colspan="3" style="border-top:1px solid #C0C0C0; padding-top: 5px; border-left-width:1px; border-right-width:1px; border-bottom-width:1px">
              <table border="0" width="100%" id="table4" cellspacing="0" cellpadding="0">
                <tr>
                  <td width="41%" align="right">
                    <input type="radio" value="por caracteristicas" name="tipo_listado" onClick="vTipoListado(this);">
                  </td>
                  <td width="59%">Listar por Características</td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td valign="top" width="28%" style="padding-bottom: 5px">
              <span class="campos">Mostrar:</span><br>
              <select size="3" name="tipo[]" multiple class="form_peque" disabled>
                <option value="" selected>Todos los inmuebles ...</option>
                {{#tipos_propiedades}}
                <option value="{{valor}}">{{texto}}</option>
                {{/tipos_propiedades}}
              </select><br>
              <input type="radio" value="todos" name="status_web" checked disabled><span class="campos">Publicados y no publicados</span><br>
              <input type="radio" value="publicados" name="status_web" disabled><span class="campos">Los publicados</span><br>
              <input type="radio" value="sin publicar" name="status_web" disabled><span class="campos">Los no publicados</span>
            </td>
            <td valign="top" width="23%" style="border-left:1px solid #E0E0E0; padding-bottom: 5px; border-right-width:1px; border-top-width:1px; border-bottom-width:1px">
              <input type="checkbox" name="enventa" value="Si" checked disabled><span class="campos">En venta</span><br>
              <input type="checkbox" name="enrenta" value="Si" checked disabled><span class="campos">En renta mensual</span><br>
              <input type="checkbox" name="endiaria" value="Si" checked disabled><span class="campos">En renta diaria</span><br>
              <input type="checkbox" name="entraspaso" value="Si" checked disabled><span class="campos">En traspaso</span>
                </td>
                <td valign="top" width="49%" style="border-left:1px solid #E0E0E0; padding-bottom: 5px; border-right-width:1px; border-top-width:1px; border-bottom-width:1px">
                  <span class="campos">En las colonias:</span><br>
                  <select size="5" name="colonia[]" multiple class="form_peque" disabled>
                    <option value="" selected>Todas las colonias ...</option>
                    {{#colonias}}
                    <option value="{{colonia}}">{{colonia}}</option>
                    {{/colonias}}
                  </select><br>
                  <span class="observaciones">Para seleccionar más de una clave debe mantener presionada la tecla Ctrl mientras hace clic sobre la clave deseada</span>
                </td>
              </tr>
              <tr>
                <td valign="top" width="28%" style="padding-bottom: 5px; padding-top:5px" rowspan="2">
                  <input type="radio" value="todos" name="ver_contrataciones" checked disabled><span class="campos">Todos los contratos<br></span>
                  <input type="radio" value="vigentes" name="ver_contrataciones" disabled>Contratos vigentes<br>
                  <input type="radio" value="caducados" name="ver_contrataciones" disabled>Contratos caducados
                </td>
                <td valign="top" width="72%" style="border-left:1px solid #E0E0E0; padding-bottom: 5px; padding-top:5px; border-right-width:1px; border-top-width:1px; border-bottom-width:1px" colspan="2" align="center">
                  <span class="campos">Precio de </span>
                  <select size="1" name="t_precio" class="form_peque" disabled>
                    <option value="precio_venta_mxp">venta</option>
                    <option value="precio_renta_mxp">renta mensual</option>
                    <option value="precio_diaria_mxp">renta diaria</option>
                    <option value="precio_traspaso_mxp">traspaso</option>
                  </select>
                  <span class="campos"> de: $<input type="text" name="precio1" size="7" class="form_peque" onChange="return val_numerico(this, 0);" disabled>
                  a: $<input type="text" name="precio2" size="7" class="form_peque" onChange="return val_numerico(this, 0);" disabled> 
                  MXP</span>
                </td>
              </tr>
              <tr>
                <td valign="top" colspan="2" style="border-left:1px solid #E0E0E0;" align="center">
                  <span class="campos">Listar inmuebles contratados por:</span>
                  <select name="asesor" class="form_peque" disabled>
                    <option value="">Todos los asesores</option>
                    {{#asesores}}             
                      <option value="{{id}}">{{nombre}} {{apellidos}}</option>
                      {{/asesores}}
                    </select>
                      </td>
                    </tr>
                    <tr>
                      <td valign="top" width="100%" style="padding-bottom: 5px" colspan="3" align="center">
                        <input type="submit" value="Continuar ->" name="bsubmit1" disabled>
                      </td>
                    </tr>
                    <!-- /LISTAR POR CARACTERÍSTICAS -->
                    </table>
                    </form>          
