{{#desarrollos.exists}}
<script language="javascript">
function valida_clvdes(theForm) {
  if (theForm.claveprop.value == '') {
    alert('Selecciona la clave del desarrollo que deseas editar ...');
    theForm.claveprop.focus();
    return(false);
  } // if
  return(true);
}
</script>
  <div>
    <form method="POST" action="minmueble.php"
      onsubmit="return valida_clvdes(this);"
      class="max-350"
    >
      <div class="d-flex">
        <select name="claveprop" id="clavedes" class="form-control">
      <option value="">seleccione desarrollo</option>
      {{#desarrollos.items}}
      <option value="{{claveprop}}">{{claveprop}} - {{nombredes}}</option>
      {{/desarrollos.items}}
      </select>
      <input type="submit" value="..." class="form-control max-50 btn-primary">
      </div>
      <INPUT TYPE="hidden" name="ppinmueble" value="Desarrollo">
      <INPUT TYPE="hidden" name="paso" value="1">
    </form>
  </div>
{{/desarrollos.exists}}
