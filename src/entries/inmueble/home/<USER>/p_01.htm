<table border="0" width="100%" id="table5" cellspacing="0" cellpadding="0" height="100">
  <tr>
    <td width="350" align="center">
      <form method="POST" action="minmueble.php" onsubmit="return Valida1(this);" language="JavaScript">
      <table border="0" width="100%" cellspacing="0" cellpadding="2">
        <tr>
          <td width="40%" align="right">Registrar o editar la clave:</td>
          <td>
          <input type="text" autocomplete=off name="claveprop" size="17" maxlength="25" onChange="return val_claveprop(this);" style="font-weight: bold" id=autoc_claveprop_1></td>
        </tr>
        <tr>
          <td width="40%" align="right"><span class="campos">Tipo:</span></td>
          <td>
          <select size="1" name="ppinmueble" class="form_peque" onchange="if (this.value=='Desarrollo') {this.form.padesarrollo.disabled=true}else{this.form.padesarrollo.disabled=false}">
          <option value="Inmueble">Inmueble</option>
          <option value="Desarrollo">Desarrollo</option>
          </select><input type="submit" value="Continuar -&gt;" class="form_peque"></td>
        </tr>
        {{#desarrollos}}
        <tr>
          <td colspan="2" align="center"><span class="observaciones"><INPUT TYPE="checkbox" NAME="padesarrollo" VALUE="Si">El inmueble pertenecerá a un desarrollo</span></td>
        </tr>
        {{/desarrollos}}
      </table>
      <input type="hidden" name="paso" value="1">
      </form>
    </td>
    <td align="center" valign="top">
    <table border="0" width="100%" cellspacing="0" cellpadding="0">
      <tr>
        <td class="observaciones">Para registrar un nuevo 
                    inmueble o desarrollo: Teclea la clave que deseas 
                    asignar al nuevo inmueble y haz clic en 
                    &quot;Continuar-&gt;&quot;</td>
      </tr>
      <tr>
        <td class="observaciones" style="padding-top: 7px">
        Para editar un inmueble o desarrollo existente: Teclea la clave 
                    del inmueble que deseas modificar y haz 
                    clic en &quot;Continuar-&gt;&quot;</td>
      </tr>
    </table>
    </td>
  </tr>
  {{#desarrollos}}
  <tr>
    <td align="center" colspan="2" style="border-left-width: 1px; border-right-width: 1px; border-top: 1px solid #C0C0C0; border-bottom-width: 1px; padding-top: 2px">
    <script language="javascript">
    <!--
    function valida_clvdes(theForm) {
      if (theForm.claveprop.value == '') {
        alert('Selecciona la clave del desarrollo que deseas editar ...');
        theForm.claveprop.focus();
        return(false);
      } // if
      return(true);
    }
    // -->
    </script>
      <form method="POST" action="minmueble.php" onsubmit="return valida_clvdes(this);">
<p>Editar desarrollo: <select size="1" name="claveprop">
<option value="">seleccione desarrollo</option>
{{#desarrollos}}
<option value="{{claveprop}}">{{claveprop}} - {{nombredes}}</option>
{{/desarrollos}}
</select>
<INPUT TYPE="hidden" name="ppinmueble" value="Desarrollo">
<INPUT TYPE="hidden" name="paso" value="1">
<input type="submit" value="..." class="form_peque"></p>
</form>
</td>

  </tr>
  {{/desarrollos}}
</table>

