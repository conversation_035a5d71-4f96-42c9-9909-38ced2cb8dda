import "../../js/lazyload.js";

$(document).ready(function () {
  $("#que_buscas").on("input", function () {
    var value = this.value.toLowerCase();
    var paBuscar = document.querySelectorAll(".pabuscar");
    paBuscar.forEach(function (item) {
      var article = item.parentNode;
      if (item.textContent.toLowerCase().indexOf(value) === -1) {
        article.classList.add("hidden");
      } else {
        article.classList.remove("hidden");
      }
    });
    var count = document.querySelectorAll("article:not(.hidden)").length;
    document.getElementById("counter").textContent = count;
  });
});
