# 📋 PASO 4 - DOCUMENTACIÓN TÉCNICA COMPLETA

## Sistema de Captura de Datos Internos e Integración con Meteor

### 🎯 Propósito General

El **Paso 4** es la etapa final del proceso de captura de inmuebles en el sistema padre (PHP). Su función principal es:

1. **Procesar datos internos** del inmueble (comisiones, propietario, observaciones)
2. **Actualizar información** en la base de datos principal
3. **Integrar automáticamente** con Meteor (red social inmobiliaria)
4. **Sincronizar datos** entre sistemas para mantener consistencia

---

## 🏗️ Arquitectura del Sistema

### Flujo General de Datos

```
[Formulario Web] → [inmueble.php] → [inmueble-paso-4.php] → [API msi-v5] → [MeteorPost] → [Meteor API]
                                                        ↓
                                            [Base de Datos MySQL] ← [cm_rec_datos_prop()]
```

### Componentes Principales

#### 1. **inmueble.php** (Controlador Principal)

- **Ubicación**: `panel4-templates/src/entries/inmueble.php`
- **Función**: Router que maneja los diferentes pasos del proceso
- **Paso 4**: Línea 386 - `include __DIR__ . '/inmueble-paso-4.php';`

#### 2. **inmueble-paso-4.php** (Procesador de Datos Internos)

- **Ubicación**: `panel4-templates/src/entries/inmueble-paso-4.php`
- **Función**: Lógica específica para procesar datos internos y crear posts en Meteor

#### 3. **API msi-v5** (Microservicio de Inmuebles)

- **Ubicación**: `msi-v5/src/Application/Actions/Owner/Inmuebles/ViewInmuebleOwnerAction.php`
- **Endpoint**: `GET /owner/inmuebles/{id}`
- **Función**: Proporciona datos completos del inmueble en formato estructurado

#### 4. **MeteorPost.class.php** (Integración con Meteor)

- **Ubicación**: `panel4-templates/src/entries/clases/MeteorPost.class.php`
- **Función**: Maneja la comunicación con la API REST de Meteor

---

## 🔄 Flujo Detallado del Paso 4

### 1. **Inicialización y Autenticación**

```php
// Obtener clave del sistema desde POST
$clave_sistema = (int) $_POST['clave_sistema'];

// Recuperar/crear token de API para msi-v5
$token = $sql->row("
    SELECT token FROM api_tokens
    WHERE contrato_id = {$config['contrato']} AND name = 'SI.v4:ControlPanel' AND
        (expires_at >= NOW() OR expires_at IS NULL)
    LIMIT 1");

if (!$token) {
    // Crear nuevo token si no existe
    $token = array('token' => 'tk_' . md5(uniqid(rand().time(), true)));
    $sql->insert("api_tokens", array(
        "contrato_id" => $config['contrato'],
        "name" => "SI.v4:ControlPanel",
        "token" => $token['token']
    ));
}
```

**Propósito**: Establecer autenticación segura con el microservicio msi-v5.

### 2. **Obtención de Datos del Inmueble**

```php
// Petición a API msi-v5 para obtener datos completos
$curl = new Curl();
$curl->setUrl('http://msi-v5/owner/inmuebles/' . $clave_sistema);
$curl->setHeaders(array('Authorization' => 'Bearer ' . $token['token']));
$response = json_decode($curl->get()->getResponse());

if ($response->statusCode == 200) {
    $apiInmueble = $response->data;
}
```

**Datos Obtenidos de msi-v5**:

- **Información básica**: ID, clave interna, nombre, tipo
- **Ubicación**: colonia, ciudad, provincia, coordenadas
- **Precios**: venta, renta, moneda
- **Disponibilidad**: venta, renta, traspaso
- **Campos personalizados**: terreno, construcción, recámaras, baños, etc.
- **Descripción**: anuncio en múltiples idiomas

### 3. **Integración Automática con Meteor**

#### 3.1. Validación de Condiciones

```php
// Solo publicar si comparte comisión
$compartirVenta = isset($formData['comparto_comision']) && floatval($formData['comparto_comision']) > 0;
$compartirRenta = isset($formData['rta_comparto_comision']) && floatval($formData['rta_comparto_comision']) > 0;

if (!$compartirVenta && !$compartirRenta) {
    // No crear post - no se comparte comisión
    return array('status' => 200, 'data' => (object) array('skipped' => true));
}
```

#### 3.2. Mapeo de Datos Sistema Padre → Meteor

| **Campo Sistema Padre** | **Campo Meteor** | **Transformación**              |
| ----------------------- | ---------------- | ------------------------------- |
| `clave_sistema`         | `externalId`     | Directo                         |
| `claveprop`             | `externalKey`    | Directo                         |
| `ubicacion.ciudad`      | `location`       | Mapeo a zonas                   |
| `precios.venta/renta`   | `price`          | Prioriza venta                  |
| `ci_recamaras`          | `bedrooms`       | Extrae de campos personalizados |
| `ci_banos`              | `bathrooms`      | Convierte "4 ½" → 4.5           |
| `ci_construccion`       | `area`           | Extrae número de "500.00 m²"    |
| `descripcion.anuncio`   | `description`    | Limpia HTML                     |
| `meteor_user_id`        | `authorId`       | Desde formulario                |

#### 3.3. Procesamiento de Destinatarios

```php
// Extraer socios seleccionados para posts privados
$targetUserIds = $this->extractTargetUserIds($formData);
if (!empty($targetUserIds)) {
    $postData['targetUserIds'] = $targetUserIds;
}
```

**Fuentes de Destinatarios**:

- `socios_seleccionados`: Array de IDs de socios
- Componente Vue `SelectorSocios.vue`
- Publicación pública vs privada

#### 3.4. Creación del Post en Meteor

```php
// Crear post en Meteor vía API REST
$response = $this->makeMeteorRequest('/posts', 'POST', $postData);

// Endpoint: http://localhost:3000/api/posts
// Headers: Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

### 4. **Procesamiento de Datos Internos**

#### 4.1. Gestión del Propietario

```php
// Crear nuevo propietario si no existe
if ($i_propietario == 0 && (trim($c_nombre) != '' || trim($c_apellidos) != '')) {
    // Buscar contacto existente
    $query = "SELECT id FROM contactos WHERE contrato='$config[contrato]' AND ...";

    if ($consulta1->num_rows == 0) {
        // Insertar nuevo contacto
        $query = "INSERT INTO contactos (contrato, nombre, apellidos, ...) VALUES (...)";
        $sql->query($query);
    }
}
```

#### 4.2. Actualización de Comisiones

```php
// Actualizar comisiones en base de datos
$query = "UPDATE propiedades SET
    comparto_comision='$comparto_comision',
    t_comparto_comision='$t_comparto_comision',
    rta_comparto_comision='$rta_comparto_comision',
    comision_ampi='$comision_ampi',
    ...
    WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema')";
```

#### 4.3. Campos Personalizados

```php
// Procesar campos dinámicos
$consulta1 = $sql->query("SELECT * FROM campos_inmuebles WHERE uso='interno'");
while ($row = $consulta1->fetch_assoc()) {
    $val_variable = ${$row['variable']};

    // Manejar selectores con valores personalizados
    if ($row['tipo'] == 'selector' && $val_variable == '-1') {
        $valor_esp = ${"d_$row[variable]_esp"};
        // Guardar en valores_campos_aprendidos
    }

    // Insertar/actualizar valor del campo
    $sql->query("INSERT INTO valores_campos (clave_sistema, variable, valor_esp, valor_ing) VALUES (...)");
}
```

### 5. **Sincronización de Búsquedas**

```php
// Actualizar índices de búsqueda
cm_rec_datos_prop($clave_sistema, $config['contrato']);
```

**Función `cm_rec_datos_prop()`**:

- Regenera datos optimizados para búsquedas
- Actualiza tabla `busca_propiedades`
- Combina información de múltiples fuentes
- Optimiza consultas del frontend

---

## 🔗 Integraciones Externas

### API REST de Meteor

#### Endpoint de Posts

```http
POST http://localhost:3000/api/posts
Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
Content-Type: application/json

{
  "type": "venta",
  "title": "Casa en Venta - Centro, Cuernavaca",
  "description": "Hermosa casa en el centro de Cuernavaca...",
  "price": 2500000,
  "location": "norte",
  "propertyType": "casa",
  "bedrooms": 3,
  "bathrooms": 2.5,
  "area": 300,
  "landArea": 100,
  "features": ["alberca", "estacionamiento"],
  "authorId": "abc123...",
  "targetUserIds": ["socio1", "socio2"],
  "externalId": "131350",
  "externalKey": "CASA001"
}
```

#### Respuesta Exitosa

```json
{
  "message": "Post creado exitosamente",
  "post": {
    "_id": "xyz789...",
    "title": "Casa en Venta - Centro, Cuernavaca",
    "price": 2500000,
    "authorId": "abc123...",
    "externalId": "131350",
    "createdAt": "2025-01-07T10:30:00.000Z"
  }
}
```

### API msi-v5 (Microservicio de Inmuebles)

#### Endpoint de Consulta

```http
GET http://msi-v5/owner/inmuebles/131350
Authorization: Bearer tk_abc123...
```

#### Estructura de Respuesta

```json
{
  "data": {
    "id": 131350,
    "clave_interna": "CASA001",
    "ubicacion": {
      "colonia": "Centro",
      "ciudad": "Cuernavaca",
      "coordenadas": { "lat": "18.9186", "lng": "-99.2342" }
    },
    "precios": {
      "venta": 2500000,
      "renta": 15000,
      "moneda": "MXP"
    },
    "campos_personalizados": [
      {
        "variable": "ci_terreno",
        "nombre": "Terreno",
        "valor": "100.00 m²",
        "tipo": "numerico"
      },
      {
        "variable": "ci_construccion",
        "nombre": "Construcción",
        "valor": "300.00 m²",
        "tipo": "numerico"
      }
    ]
  }
}
```

---

## 🎨 Frontend - Componente Vue

### SelectorSocios.vue

**Ubicación**: `panel4-templates/src/components/selector-socios-wrapper/SelectorSocios.vue`

**Funcionalidad**:

- Permite seleccionar socios específicos para posts privados
- Se integra con el formulario de bolsa inmobiliaria
- Envía datos como `socios_seleccionados` en el POST

### InmuebleBolsaInmobiliaria.vue

**Ubicación**: `panel4-templates/src/components/inmueble-bolsa-inmobiliaria/InmuebleBolsaInmobiliaria.vue`

**Funcionalidad**:

- Controla la publicación pública vs privada
- Integra el selector de socios
- Maneja el estado de la multibolsa inmobiliaria

---

## 🗄️ Base de Datos

### Tablas Principales

#### `propiedades`

- **Campos clave**: `clave_sistema`, `claveprop`, `contrato`
- **Comisiones**: `comparto_comision`, `rta_comparto_comision`, `comision_ampi`
- **Fechas**: `fecha_modificaciones`, `fecha_ingreso`

#### `valores_campos`

- **Relación**: `clave_sistema` → `propiedades.clave_sistema`
- **Campos**: `variable`, `valor_esp`, `valor_ing`
- **Uso**: Almacena campos personalizados dinámicos

#### `contactos`

- **Relación**: `id` → `propiedades.i_propietario`
- **Campos**: `nombre`, `apellidos`, `telefono`, `email`

#### `api_tokens`

- **Uso**: Autenticación con microservicio msi-v5
- **Campos**: `token`, `contrato_id`, `name`, `expires_at`

#### `busca_propiedades`

- **Uso**: Índices optimizados para búsquedas
- **Actualización**: Función `cm_rec_datos_prop()`

---

## 🚨 Manejo de Errores

### Estrategia de Resiliencia

```php
try {
    // Integración con Meteor
    $meteorResponse = $meteorPost->createPost($apiInmueble, $_POST);

    if ($meteorResponse['status'] === 201) {
        error_log("✅ Post creado exitosamente en Meteor");
    } else {
        error_log("⚠️ Error al crear post en Meteor: " . $errorDetail);
    }

} catch (\Exception $e) {
    // Log del error pero continuar con el flujo normal
    error_log("❌ Excepción en integración Meteor: " . $e->getMessage());

    // El sistema padre NO se interrumpe por errores de Meteor
}
```

**Principios**:

1. **No interrumpir**: Errores de Meteor no afectan el sistema padre
2. **Logging detallado**: Todos los eventos se registran con emojis
3. **Fallback graceful**: Sistema continúa funcionando sin Meteor

---

## 🔧 Configuración

### Variables de Entorno

```bash
# Meteor API
API_URL_METEOR=http://localhost:3000/api
API_KEY_METEOR=c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb

# msi-v5 API
MSO_INMUEBLES=http://msi-v5
```

### Configuración de Base de Datos

```php
// Configuración en config.php
$config = array(
    'contrato' => 12345,
    'inmuebles' => 7000,
    'socio_ampi' => 'Si' // Para mostrar campos AMPI
);
```

---

## 📊 Métricas y Monitoreo

### Logs Generados

```php
// Ejemplos de logs del sistema
error_log("🔄 Iniciando integración con Meteor: Casa en Venta - Centro, Cuernavaca");
error_log("✅ Post creado exitosamente en Meteor - ID: xyz789, Inmueble: 131350");
error_log("ℹ️ Post no creado en Meteor para inmueble 131350 - No se comparte comisión");
error_log("⚠️ Error al crear post en Meteor: Datos insuficientes");
```

### Indicadores de Éxito

- **Posts creados**: Contador de integraciones exitosas
- **Errores de validación**: Datos insuficientes o inválidos
- **Tiempo de respuesta**: Latencia de APIs externas
- **Cobertura de integración**: % de inmuebles sincronizados

---

## 🔄 Flujo de Actualización

### Detección Automática

```php
// El sistema detecta automáticamente si crear o actualizar
$existingPost = $meteorPost->findPostByExternalId($apiInmueble->id);

if ($existingPost) {
    // Actualizar post existente
    $response = $meteorPost->updatePost($existingPost->_id, $apiInmueble, $_POST);
} else {
    // Crear nuevo post
    $response = $meteorPost->createPost($apiInmueble, $_POST);
}
```

### Sincronización Bidireccional

1. **Sistema Padre → Meteor**: Automática en paso 4
2. **Meteor → Sistema Padre**: Via webhooks (futuro)
3. **Resolución de Conflictos**: Sistema padre es fuente de verdad

---

## 🚀 Optimizaciones Futuras

### Mejoras Planificadas

1. **Cache de API**: Reducir llamadas repetitivas a msi-v5
2. **Queue de Jobs**: Procesar integraciones de forma asíncrona
3. **Webhooks**: Sincronización bidireccional con Meteor
4. **Bulk Operations**: Sincronización masiva de inmuebles
5. **Health Checks**: Monitoreo automático de APIs externas

### Extensibilidad

- **Nuevos Portales**: Fácil agregar más integraciones
- **Campos Personalizados**: Sistema dinámico de mapeo
- **Reglas de Negocio**: Configuración flexible de condiciones
- **Multi-tenant**: Soporte para múltiples contratos

---

## 📞 Soporte y Mantenimiento

### Archivos Clave para Debugging

1. **`inmueble-paso-4.php`**: Lógica principal del paso 4
2. **`MeteorPost.class.php`**: Integración con Meteor
3. **`ViewInmuebleOwnerAction.php`**: API de consulta de inmuebles
4. **Logs del sistema**: `/var/log/php/errors.log`

### Comandos Útiles

```bash
# Ver logs en tiempo real
tail -f /var/log/php/errors.log | grep -E "(🔄|✅|❌|⚠️)"

# Verificar conectividad con APIs
curl -H "Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb" \
     http://localhost:3000/api/posts

# Probar endpoint msi-v5
curl -H "Authorization: Bearer tk_abc123..." \
     http://msi-v5/owner/inmuebles/131350
```

---

**Documentación actualizada**: 2025-01-07  
**Versión**: 2.0  
**Autor**: Sistema de Documentación Automática
