<?php

$clave_sistema = (int) $_POST['clave_sistema'];

// Recupero el token desde la DB
$token = $sql->row(
    "SELECT token FROM api_tokens 
    WHERE contrato_id = {$config['contrato']} AND name = 'SI.v4:ControlPanel' AND 
        (expires_at >= NOW() OR expires_at IS NULL) 
    LIMIT 1"
);
if (!$token) {
    $token = array(
        'token' => 'tk_' . md5(uniqid(rand().time(), true))
    );
    $sql->insert("api_tokens", array(
        "contrato_id" => $config['contrato'],
        "name" => "SI.v4:ControlPanel",
        "token" => $token['token']
    ));
}

// Usamos la clase Curl para hacer la petición a la API
try {
    require_once __DIR__ . '/clases/Curl.php';
    $msi_v5_service = getenv('PROXY_SERVICE_MSI_V5');
    $curl = new Curl();
    $curl->setUrl('http://' . $msi_v5_service . '/owner/inmuebles/' . $clave_sistema);
    $curl->setHeaders(array(
        'Authorization' => 'Bearer ' . $token['token'],
    ));
    $response = json_decode($curl->get()->getResponse());

    if ($response->statusCode == 200) {
        $apiInmueble = $response->data;
    } else {
        throw new \Exception('Error al obtener la propiedad desde la API');
    }
} catch (\Exception $e) {
    die($e->getMessage());
}

// Verifico que la propiedad exista y sobre todo que esté vigente para este paso,
// si no es así termino la ejecución del programa
// status_id: 1 = Activo, 8 = Oferta
$query = sprintf(
    "SELECT claveprop
        FROM propiedades 
        WHERE clave_sistema='%s' AND contrato='%s'
            AND status_id IN (1, 8) 
        LIMIT 1
    ",
    $clave_sistema,
    $config['contrato'],
    $hoy
);
$consulta1 = $sql->query($query);
if (!$consulta1->num_rows) {
    echo 'Ha ocurrido un error, el inmueble no existe an la base de datos.';

    // Desconectamos de la base de datos
    $sql->close();

    // Despliego el pie de página y termino la ejecución del programa
    echo $mustache->render($plantilla['pie'], $render);
    exit;
}
$propiedad = $consulta1->fetch_assoc();

// 🚀 INTEGRACIÓN CON METEOR - CREAR POST INMOBILIARIO
$publicacionExitosaEnFeed = false; // 🆕 Variable para controlar si se registra fecha de publicación

try {
    require_once __DIR__ . '/clases/MeteorPost.class.php';
    $meteorPost = new Mulbin\MeteorPost($config['contrato']);

    // Log del intento de integración
    $integrationSummary = $meteorPost->getIntegrationSummary($apiInmueble, $_POST);
    error_log("🔄 Iniciando integración con Meteor: {$integrationSummary}");

    // Validar datos antes de crear el post
    $validation = $meteorPost->validatePostData($apiInmueble, $_POST);

    if (!$validation['valid']) {
        error_log("❌ Validación fallida para inmueble {$apiInmueble->id}: " . implode(', ', $validation['errors']));
        throw new \RuntimeException('Datos insuficientes: ' . implode(', ', $validation['errors']));
    }

    // Crear nuevo post
    $meteorResponse = $meteorPost->createPost($apiInmueble, $_POST);
    $action = 'creado';

    // Log del resultado detallado
    if ($meteorResponse['status'] === 201) {
        $postId = 'N/A';
        if (isset($meteorResponse['data']->post->_id)) {
            $postId = $meteorResponse['data']->post->_id;
        } elseif (isset($existingPost->_id)) {
            $postId = $existingPost->_id;
        }
        error_log("✅ Post {$action} exitosamente en Meteor - Inmueble: {$apiInmueble->id}, Post: {$postId}");

        // 🆕 Marcar publicación como exitosa para registrar fecha
        $publicacionExitosaEnFeed = true;

        // Opcional: Guardar el post ID en la base de datos local para futuras referencias
        // $sql->query("UPDATE propiedades SET meteor_post_id = '{$postId}' WHERE clave_sistema = '{$clave_sistema}'");

    } elseif (isset($meteorResponse['data']->skipped)) {
        error_log("ℹ️ Post no {$action} en Meteor para inmueble {$apiInmueble->id} - No se comparte comisión");
    } else {
        $errorDetail = isset($meteorResponse['data']->error) ? $meteorResponse['data']->error : 'Error desconocido';
        $actionText = ($action === 'creado') ? 'crear' : 'actualizar';
        error_log("⚠️ Error al {$actionText} post en Meteor para inmueble {$apiInmueble->id}: {$errorDetail}");
    }

} catch (\Exception $e) {
    // Log del error pero continuar con el flujo normal del sistema padre
    error_log("❌ Excepción en integración Meteor para inmueble {$apiInmueble->id}: " . $e->getMessage());
    error_log("📋 Datos recibidos: " . json_encode(array(
        'inmueble_id' => isset($apiInmueble->id) ? $apiInmueble->id : 'N/A',
        'post_keys' => array_keys($_POST),
        'has_author_id' => isset($_POST['author_id']),
        'author_id' => isset($_POST['author_id']) ? $_POST['author_id'] : 'N/A'
    ), JSON_UNESCAPED_UNICODE));
}

// Manejo de propietarios y datos internos movido a inmueble-paso-4.php

// Variables de comisión necesarias para los cálculos de multibolsa
if (empty($t_comparto_comision)) {
    $t_comparto_comision = fieldDefault('propiedades', 't_comparto_comision');
}
if (empty($comparto_comision)) {
    $comparto_comision = fieldDefault('propiedades', 'comparto_comision');
}
if (empty($comision_ampi)) {
    $comision_ampi = fieldDefault('propiedades', 'comision_ampi');
}
if (empty($rta_comparto_comision)) {
    $rta_comparto_comision = fieldDefault('propiedades', 'rta_comparto_comision');
}

// 🆕 VARIABLES DE MULTIBOLSA INMOBILIARIA
$usar_multibolsa = isset($_POST['usar_multibolsa']) ? $_POST['usar_multibolsa'] : 'No';
$publicacion_publica = isset($_POST['publicacion_publica']) ? $_POST['publicacion_publica'] : 'No';
$socios_seleccionados = isset($_POST['socios_seleccionados']) ? $_POST['socios_seleccionados'] : '';
$solicitar_publicacion_websites = isset($_POST['solicitar_publicacion_websites']) ? $_POST['solicitar_publicacion_websites'] : 'No';

// Manejo de AMPI Plus - se mantiene aquí ya que está relacionado con multibolsa
if ($ampi_plus == 'Si') {
    $ampi_plus = ", ampi_plus='Si'";

    $query = "SELECT * FROM ampi_usuarios WHERE (contrato='$config[contrato]' && seccion='1') LIMIT 1";
    $consulta1 = $sql->query($query);
    if ($consulta1->num_rows == 1) {
        $consulta1 = $sql->query("SELECT * FROM ampi_comercializacion WHERE (seccion='1' && clave_sistema='$clave_sistema') LIMIT 1");
        if ($consulta1->num_rows == 0) {
            $sql->query("INSERT INTO ampi_comercializacion (seccion, clave_sistema, propuesto, status) VALUES ('1', '$clave_sistema', '$ahorita', 'en espera')");
        }	// if
    }	// if
} else {
    $ampi_plus = ", ampi_plus='No'";

    $query = "SELECT * FROM ampi_usuarios WHERE (contrato='$config[contrato]' && seccion='1') LIMIT 1";
    $consulta1 = $sql->query($query);
    if ($consulta1->num_rows == 1) {
        $consulta1 = $sql->query("SELECT * FROM ampi_comercializacion WHERE (seccion='1' && clave_sistema='$clave_sistema') LIMIT 1");
        if ($consulta1->num_rows == 1) {
            $sql->query("DELETE FROM ampi_comercializacion WHERE (seccion='1' && clave_sistema='$clave_sistema') LIMIT 1");
        }	// if
    }	// if
}

// Variables de datos internos movidas a inmueble-paso-4.php

// 🆕 PREPARAR CAMPO DE FECHA DE PUBLICACIÓN
$fecha_publicacion_sql = '';
if ($publicacionExitosaEnFeed) {
    $fecha_publicacion_sql = ", fecha_publicacion_feed='".date('Y-m-d H:i:s')."'";
    error_log("🕒 Registrando fecha de publicación en Feed para inmueble {$clave_sistema}");
}

// Variables de comisión relacionadas con multibolsa
$ca_multibolsa = ", comision_ampi='$comision_ampi', t_comision_ampi='$t_comparto_comision', rta_comision_ampi='$rta_comparto_comision'";

$query = "
	UPDATE propiedades 
	SET fecha_modificaciones='$ahorita', 
		comparto_comision='$comparto_comision', t_comparto_comision='$t_comparto_comision', 
		rta_comparto_comision='$rta_comparto_comision' $ca_multibolsa,
		usar_multibolsa='$usar_multibolsa', publicacion_publica='$publicacion_publica', 
		socios_seleccionados='$socios_seleccionados', solicitar_publicacion_websites='$solicitar_publicacion_websites' 
		$fecha_publicacion_sql $ampi_plus 
	WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema') 
	LIMIT 1
	";

if (!$sql->query($query)) {
    // mail('<EMAIL>', 'Error al actualizar Datos Internos de un inmueble', "Error al ejecutar la siguiente consulta:\n\n$query\n\n" . mysql_error($db_link) . "\n\n" . __FILE__ . "\n\n" . __LINE__);
    ?>
Error<br><br>Ha ocurrido un error al intentar guardar la información,<br><br>Favor de reportarlo a:<br>
<A HREF="mailto:<EMAIL>"><EMAIL></A>
<?php

} else {

    ////////////////////////////////
    //	Registro en la tabla de movimientos de propiedades (solo para multibolsa)
    $sql->query("INSERT INTO movpropiedades (contrato, clave_sistema, movimiento) VALUES ('$config[contrato]', '$clave_sistema', 'UPDATE-MULTIBOLSA')");

    // Procesamiento de campos internos movido a inmueble-paso-4.php
    // Solo actualizamos la base de búsquedas después del cambio de multibolsa
    cm_rec_datos_prop($clave_sistema, $config['contrato']);

    ?>
<div align="center">

    <table cellpadding="0" cellspacing="0" class="t1">
        <tr>
            <td width="100%" class="t1">
                <?php echo local_menu_propiedad(); ?>
            </td>
        </tr>

        <tr>
            <td width="100%" class="t1">
                <p>&nbsp;</p>
                <p align="center">La configuración de multibolsa del inmueble con
                    clave <?php echo $claveprop; ?> se ha guardado con éxito</p>
                <p align="center">&nbsp;</p>
            </td>
        </tr>
    </table>

</div>
<?php

}
?>