<?php
setlocale(LC_ALL, "es_MX");

include_once("languages/esp.php");
include_once("funciones.php");
include_once("verifica_pago.php");
// include_once("theme.php");
@mysql_select_db(DB_SI);
cm_valida_usuario('ADMIN');
cm_rec_permisos();

//foreach ($_POST as $valor=>$variable) {
//	echo "$valor = $variable<br />";
//}	// foreach

//////////////
// Recupero el encabezado y lo despliego, también defino variables para socios AMPI
$plantilla = cm_extrae_plantilla_mto();
include_once("enc_mto.php");

if ($_POST['mop'] == 'act' && is_array($props_imp)) {
    $QueHace = '';
    if ($_POST['QueHace'] == 'Publicar') {
        $QueHace = 'publicado';
    } elseif ($_POST['QueHace'] == 'Quitar') {
        $QueHace = 'sin publicar';
    }
    if ($QueHace != '') {
        foreach ($props_imp as $tmp) {
            $query = "UPDATE propiedades SET status_web='$QueHace' WHERE (contrato='$config[contrato]' && clave_sistema='$tmp') LIMIT 1";
            mysqlQuery($query, $db_link);
        }	// foreach
        //		echo 'Se hicieron ' . mysql_affected_rows($db_link) . ' cambios ...';
    }	// if
}	// if

$meses = array('', 'ene', 'feb', 'mar', 'abr', 'may', 'jun', 'jul', 'ago', 'sep', 'oct', 'nov', 'dic');

// Recupero los tipos de propiedades
$tipo_inmuebles = array();
$consulta1 = mysqlQuery("SELECT * FROM tipo_inmuebles", $db_link);
while ($row = mysql_fetch_array($consulta1)) {
    $tipo_inmuebles[$row['clave']] = $row['tipo_esp'];
}


// Veo si es AMPI y comercialización para Cuernavaca
if ($config['socio_ampi'] == 'Si') {
    $consulta1 = mysqlQuery("SELECT contrato FROM ampi_usuarios WHERE (seccion='1' && contrato='$config[contrato]' && contrato_adhesion='aceptado') LIMIT 1", $db_link);
    if (mysql_num_rows($consulta1) == 1) {
        $aAmpi = array();
        $consulta1 = mysqlQuery("SELECT * FROM ampi_comercializacion WHERE (seccion='1')", $db_link);
        while ($row = mysql_fetch_array($consulta1)) {
            array_push($aAmpi, $row['clave_sistema']);
        }	// while
    }	// if
}	// if

////////////////////////////////////////////////////////
// En caso de ser listado por claves
if ($tipo_listado == 'por claves') {

    $claves_props = str_replace(' ', '', $claves_props);
    if ($claves_props != '') {
        $c_claves = '';
        $claves = explode(',', $claves_props);
        foreach ($claves as $tmp) {
            if ($tmp != '') {
                if ($c_claves == '') {
                    $c_claves .= " && (claveprop='$tmp'";
                } else {
                    $c_claves .= " || claveprop='$tmp'";
                }
            }
        }
        if ($c_claves != '') {
            $c_claves .= ')';
        }
    }


    ////////////////////////////////////////////////////////
    // En caso de ser listado por características
} elseif ($tipo_listado == 'por caracteristicas') {

    // Que tipo de inmuebles a recuperar
    $c_tipo = '';					// Hasta aquí despliega todos
    if ($tipo[0] != '') {			// En caso de ser tipos específicos de inmuebles
        foreach ($tipo as $tmp) {
            if ($c_tipo == '') {
                $c_tipo .= " && (tipo='$tmp'";
            } else {
                $c_tipo .= " || tipo='$tmp'";
            }
        }
        $c_tipo .= ')';
    }

    // Si despliego publicados, no publicados o ambos
    $c_status_web = '';				// Hasta aquí despliega todos
    if ($status_web == 'publicados') {
        $c_status_web .= " && (fecha_expiracion='0000-00-00' || fecha_expiracion>='$hoy') && status_web='publicado'";
    } elseif ($status_web == 'sin publicar') {
        $c_status_web .= " && ((fecha_expiracion!='0000-00-00' && fecha_expiracion<'$hoy') || status_web='sin publicar')";
    }

    // En caso de desplegar inmuebles en venta, renta o traspaso
    $c_operacion = '';				// Hasta aquí no despliega ni en venta ni renta ni traspaso
    if ($enventa == 'Si') {
        $c_operacion .= " && (enventa='Si'";
    }
    if ($enrenta == 'Si') {
        if ($c_operacion == '') {
            $c_operacion .= " && (enrenta='Si'";
        } else {
            $c_operacion .= " || enrenta='Si'";
        }
    }
    if ($endiaria == 'Si') {
        if ($c_operacion == '') {
            $c_operacion .= " && (endiaria='Si'";
        } else {
            $c_operacion .= " || endiaria='Si'";
        }
    }
    if ($entraspaso == 'Si') {
        if ($c_operacion == '') {
            $c_operacion .= " && (entraspaso='Si'";
        } else {
            $c_operacion .= " || entraspaso='Si'";
        }
    }
    if ($c_operacion != '') {
        $c_operacion .= ')';
    } else {
        $c_operacion = " && enventa='No' && enrenta='No' && endiaria='No' && entraspaso='No'";
    }

    // Que colonias recuperar
    $c_colonia = '';					// Hasta aquí despliega todas las colonias
    if ($colonia[0] != '') {			// En caso de ser colonias específicas
        foreach ($colonia as $tmp) {
            if ($c_colonia == '') {
                $c_colonia .= " && (colonia='$tmp'";
            } else {
                $c_colonia .= " || colonia='$tmp'";
            }
        }
        $c_colonia .= ')';
    }

    // Que tipo de contratos recuperar
    $c_fin_contrato = '';
    if ($ver_contrataciones == 'vigentes') {
        $c_fin_contrato .= " && fin_contrato>='$hoy'";
    } elseif ($ver_contrataciones == 'caducados') {
        $c_fin_contrato .= " && fin_contrato<'$hoy'";
    }

    // Si se especifica rango de precio
    if ($precio1 != '') {
        $c_desde = " && $t_precio>='$precio1'";
    }
    if ($precio2 != '') {
        $c_hasta = " && $t_precio<='$precio2'";
    }
}

// Trato el asesor a mostrar
if ($asesor != '') {
    $c_asesor = "&& asesor='$asesor'";
}

if (empty($ordenar_por)) {
    $ordenar_por = 'fecha_modificaciones';
    $t_orden = 'DESC';
}

// Trato el órden de los resultados
$c_ordenar_por = " ORDER BY $ordenar_por";
$c_t_orden = $t_orden;

// Recupero los datos de las sucursales registradas
$consulta1 = mysqlQuery("SELECT id, nombre FROM sucursales WHERE (contrato='$config[contrato]')", $db_link);
$sucursales = array();
while ($sucursal = mysql_fetch_array($consulta1)) {
    if ($sucursal['id'] > 0) {
        $sucursales[$sucursal['id']] = $sucursal['nombre'];
    }
}

if ($tipo_listado != '') {
    $tipo_listado = urlencode($tipo_listado);
}
$claves_props = urlencode($claves_props);

if (is_array($colonia)) {
    $ccolonia = '';
    foreach ($colonia as $tmp) {
        $ccolonia .= '&colonia[]=' . urlencode($tmp);
    }
}
if (is_array($tipo)) {
    $ctipo = '';
    foreach ($tipo as $tmp) {
        $ctipo .= "&tipo[]=$tmp";
    }
}

$add_filter = array();

/* Al estar pidiendo el orden por precio, también filtro para que únicamente
se listen inmuebles que están en ese tipo de promoción */
$por_precio = array(
    'precio_venta_mxp' => "enventa='Si'",
    'precio_renta_mxp' => "enrenta='Si'",
    'precio_diaria_mxp' => "endiaria='Si'",
    'precio_traspaso_mxp' => "entraspaso='Si'",
    'fecha_expiracion' => "fecha_expiracion!='0000-00-00'",
);
if (in_array($_REQUEST['ordenar_por'], array_keys($por_precio))) {
    $add_filter[] = "AND {$por_precio[$_REQUEST['ordenar_por']]}";
}

// if (empty($limit)) $limit = 2500;

/* Armo el query sin order ni limit, ya que esta consulta será únicamente para
recuperar el número total de propiedades */
$query = sprintf(
    'SELECT * FROM propiedades WHERE contrato=%1$d %2$s %3$s',
    $config['contrato'],
    "{$c_claves} {$c_tipo} {$c_status_web} {$c_operacion} {$c_colonia} {$c_fin_contrato} {$c_desde} {$c_hasta} {$c_asesor}",
    implode(' ', $add_filter)
);

$cn = $sql->query($query);
$total_props = $cn->num_rows;

if ($oact !== $ordenar_por && in_array($oact, array_keys($por_precio))) {
    $limit = 0;
}

/* Aquí ya integro el orden y limit, ya que es la que hará el render del listado */
$c_ordenar_por = "{$c_ordenar_por} {$c_t_orden}";
if (strpos($c_ordenar_por, 'fecha_modificaciones') === false) {
    $c_ordenar_por .= ', fecha_modificaciones DESC';
}

// $limit = (int)$_REQUEST['limit'];
$limit = $limit ? ($limit > $total_props ? $total_props : $limit) : 250;
$limit > $total_props and $limit = $total_props;
$query .= "{$c_ordenar_por} LIMIT {$limit}";

// die($query);

$consulta1 = mysqlQuery($query, $db_link);

?>
<script>
    function abre(pag, ventana) {
        window.open(pag, ventana, "toolbar=yes,scrollbars=yes,resizable=yes,width=777,height=500");
        return;
    }
</script>

<main mk="mk001">
    <header>
        <h1 mk="mk004">Listando mis inmuebles<!--mk004--></h1>
        <cite class="px-1 left" mk="mk017"><?= number_format($total_props, 0, '.', ',') ?> inmuebles en total<!--mk017--></cite>
    </header>
    <div class="m-1 contenedor min-350" mk="mk020">
        <div class="input-with-complement-right-1 w-100 max-300 center">
            <span id="counter"><?= $limit ?></span>
            <input type="text" placeholder="Filtrar del listado" id="que_buscas" class="form-control" autocomplete="off" />
        </div>
        <form method="GET">
            <div class="flex">
                <div class="input-with-complement-right-1 w-100 max-150">
                    <span>inm</span>
                    <input name="limit" type="number" maxlength="4" class="form-control px-025" value="<?= $limit ?>">
                </div>
                <select name="ordenar_por" class="form-control">
                    <?php
                    $selected = array(true => ' selected', false => '');

foreach (array( // OPCIONES DE ORDEN Y NÚMERO DE INMUEBLES EN EL LISTADO
    'claveprop' => 'Clave',
    'tipo' => 'Tipo de inmueble',
    'precio_venta_mxp' => 'Venta',
    'precio_renta_mxp' => 'Renta/mes',
    'precio_diaria_mxp' => 'Renta/día',
    'precio_diaria_mxp' => 'Renta/día',
    'fecha_ingreso' => 'Fecha de ingreso',
    'fecha_modificaciones' => 'Últimas actualizaciones',
    'fecha_expiracion' => 'Expiración de contrato',
) as $value => $text) : ?>
                    <option value="<?= $value ?>" <?= $selected[$value == $ordenar_por] ?>><?= $text ?></option>
                    <?php endforeach ?>
                </select>
                <select name="t_orden" class="form-control">
                    <?php
?>
                    <option value="ASC" <?= $t_orden === 'ASC' ? ' selected' : '' ?>>Ascendente</option>
                    <option value="DESC" <?= $t_orden === 'DESC' ? ' selected' : '' ?>>Descendente</option>
                </select>
                <input type="submit" value="..." class="btn secondary" />
            </div>
            <input type="hidden" name="oact" value="<?= $ordenar_por ?>">
        </form>
        <!--mk020-->
    </div>
    <form class="mx-05" method="POST" name="Formulario" action="imprimir_claves.php" target="_blank">

        <section class="px-0" mk="mk002">
            <div class="td" mk="mk003">
                <!--mk003-->
            </div>
            <div class="tr" mk="mk018">
                <div class="contenedor min-300 gap-05" mk="mk019">
                    <!-- LISTADO DE LOS INMUEBLES -->

                    <?php

$tprops = array();
while ($propiedad = mysql_fetch_array($consulta1)) {

    // Recupero las fotos
    $consulta2 = mysqlQuery("SELECT foto_num, archivo FROM f_fotos WHERE (clave_sistema='$propiedad[clave_sistema]') ORDER BY orden ASC, id ASC", $db_link);
    if (($num_fotos = mysql_num_rows($consulta2)) && ($row = mysql_fetch_array($consulta2)) && ($file = $row['archivo'] ? $row['archivo'] : "{$propiedad['clave_sistema']}__{$row['foto_num']}.jpg")) {
        $foto = sprintf(
            '<img src="/static/img/placeholder.jpg" data-src="%1$s" alt="%2$s fotos" title="%2$s fotos" class="lazy" />',
            "/photos/propiedades/{$config['contrato']}/peque/{$file}",
            $num_fotos
        );
    }
    // $foto = "<IMG SRC=\"https://$server_photos/propiedades/$config[contrato]/peque/$file\" HEIGHT=\"50\" BORDER=\"0\" ALT=\"$num_fotos foto(s)\" TITLE=\"$num_fotos foto(s)\">";
    else {
        $foto = '<img src="/static/img/sin_imagen.jpg" />';
    }

    array_push($tprops, "p$propiedad[clave_sistema]");

    $claveprop = urlencode($propiedad[claveprop]);

    $fechas = '';
    $prop_date = array();
    if ($propiedad['fecha_ingreso'] != '0000-00-00 00:00:00') {
        $agno = substr($propiedad['fecha_ingreso'], 0, 4);
        $mes = substr($propiedad['fecha_ingreso'], 5, 2);
        $mes = $mes * 1;
        $mes = $meses[$mes];
        $dia = substr($propiedad['fecha_ingreso'], 8, 2);
        $fechas .= ($prop_date['registered'] = "{$dia}-{$mes}-{$agno}") . ' (ingreso)';
    }

    if ($propiedad['fecha_modificaciones'] != '0000-00-00 00:00:00') {
        $agno = substr($propiedad['fecha_modificaciones'], 0, 4);
        $mes = substr($propiedad['fecha_modificaciones'], 5, 2);
        $mes = $mes * 1;
        $mes = $meses[$mes];
        $dia = substr($propiedad['fecha_modificaciones'], 8, 2);
        if ($fechas != '') {
            $fechas .= '<br>';
        }
        $fechas .= ($prop_date['updated'] = "{$dia}-{$mes}-{$agno}") . ' (modificado)';
    }

    if ($propiedad['fecha_expiracion'] != '0000-00-00') {
        $agno = substr($propiedad['fecha_expiracion'], 0, 4);
        $mes = substr($propiedad['fecha_expiracion'], 5, 2);
        $mes = $mes * 1;
        $mes = $meses[$mes];
        $dia = substr($propiedad['fecha_expiracion'], 8, 2);
        if ($fechas != '') {
            $fechas .= '<br>';
        }

        $fecha1 = HOY;
        $fecha2 = $propiedad['fecha_expiracion'];

        $fecha_unix1 = strtotime($fecha1);
        $fecha_unix2 = strtotime($fecha2);

        $diferencia = $fecha_unix2 - $fecha_unix1;
        $dias = $diferencia / 86400;

        $adias = abs($dias);
        $fkk = $adias > 365 ? floor($adias / 365) : ($adias > 30 ? floor($adias / 30) : $adias);
        $fque = $adias > 365 ? ($fkk > 1 ? 'años' : 'año') : ($adias > 30 ? ($fkk > 1 ? 'meses' : 'mes') : ($fkk > 1 ? 'días' : 'día'));
        // $fkk = $dias < 30 ? $adias : ($adias < 365 ? max($adias/12) : max($adias/365));

        $falta = $dias <= 0 ? "hace {$fkk} {$fque}" : "{$fkk} {$fque}";

        // $d_meses = $dias / 30;
        // if ($dias <= 0) $falta = "hace ".abs($dias)." días";
        // else if ($d_meses < 1) $falta = ($dias === 1 ? '1 día' : "{$dias} días");
        // else $falta = ($d_meses = round($d_meses)) === 1 ? '1 mes' : "{$d_meses} meses";

        $fechas .= ($prop_date['end_promotion'] = "{$dia}-{$mes}-{$agno}") . " (fin de contrato en {$falta})";
    }

    // Inmueble sin publicar
    if ($propiedad['status_web'] === 'sin publicar') {
        $bgcolor = 'bg-color-5';
    } //
    // Inmueble expirado
    elseif (
        $propiedad['fecha_expiracion'] != '0000-00-00' &&
        strtotime($propiedad['fecha_expiracion']) < strtotime(HOY)
    ) {
        $bgcolor = 'bg-color-7';
    } //
    // Inmueble que comparte comisión en la MBI
    elseif ($propiedad['comparto_comision'] > 0) {
        $bgcolor = 'bg-color-9';
    } //
    // Inmueble activo
    else {
        $bgcolor = 'bg-white';
    }
    // $bgcolor = $bgcolor === 'bg-white' ? 'bg-soft' : 'bg-white';

    // Permisos para editar inmuebles de otro asesor
    if (($aspermisos['p_edita_todos_los_inmuebles'] == 'No' && $propiedad['aid'] != $_COOKIE['cookie_asesor']) || $aspermisos['p_registra_inmuebles'] == 'No') {
        $linkEditar = '';
        $cierraEditar = '';
        $cEditar = '';
    } else {
        $linkEditar = '<a href="minmueble.php?claveprop=' . $claveprop . '&paso=1">';
        $cierraEditar = '</a>';
        $cEditar = sprintf(
            '<div class="text-14">%s<ion-icon name="create-outline"></ion-icon>%s</div>',
            $linkEditar,
            $cierraEditar
        );
        // $cEditar = '<div class="text-14">' . $linkEditar . "<img src="./imagessi/create-outline.svg" alt='Editar inmueble'>{$cierraEditar}</div>";
    }	// if	// if

    ?>
                    <article class="px-025 py-05 <?= $bgcolor ?> shadow rounded-2" mk="mk039">
                        <div class="d-flex top">
                            <div class="d-flex w-100" mk="mk041">
                                <div class="td" mk="mk040">
                                    <input type="checkbox" name="props_imp[]" id="p<?= $propiedad['clave_sistema'] ?>" value="<?php echo $propiedad['clave_sistema']; ?>"><label for="p<?= $propiedad['clave_sistema'] ?>"></label>
                                    <!--mk040-->
                                </div>
                                <div class="w-100">
                                    <div class="center" mk="mk042">
                                        <div class="td" mk="mk043">
                                            <span style="font-weight: 700; font-family: Verdana;"><?php echo ($propiedad['en_resumen'] == 'Si' ? '* ' : '') . "$linkEditar$propiedad[claveprop]$cierraEditar"; ?></span>
                                            <?php

                                if ($propiedad['sucursal'] > 0) {
                                    echo '<br><span class="observaciones" style="color: #339900; font-weight: 700">' . $sucursales[$propiedad['sucursal']] . '</span>';
                                }

    ?>
                                            <!--mk043-->
                                        </div>
                                        <!--mk042-->
                                    </div>
                                    <div class="center" mk="mk044">
                                        <div class="center container-img border-color-4" id="mk045"><?= "{$linkEditar}{$foto}{$cierraEditar}" ?>
                                            <!--mk045-->
                                        </div>
                                        <!--mk044-->
                                    </div>
                                    <div class="w-100 center" mk="mk050">
                                        <div class="bold text-9"><?= $propiedad['colonia'] ?></div>
                                        <div class="text-8"><?= $propiedad['ciudad'] ?></div>
                                        <!--mk050-->
                                    </div>
                                    <div class="flex max-100 center between" mk="mk046">
                                        <div class="td" mk="mk047">
                                            <div class="text-14"><a HREF="javascript:abre('detallesnew.php?clave=<?php echo $propiedad[clave_sistema]; ?>','Clave<?php echo $propiedad[clave_sistema]; ?>');">
                                                    <ion-icon name="globe-outline"></ion-icon>
                                                </a></div><!--mk047-->
                                        </div>
                                        <div class="td" mk="mk048"><?= $cEditar ?><!--mk048--></div>
                                        <div class="td" mk="mk049">
                                            <div class="text-14"><a HREF="javascript:abre('imprimir_claves.php?props_imp%5B%5D=<?php echo $propiedad[clave_sistema]; ?>&imprimir=No','Clave<?php echo $propiedad[clave_sistema]; ?>');">
                                                    <ion-icon name="information-circle-outline"></ion-icon>
                                                </a></div><!--mk049-->
                                        </div>
                                        <!--mk046-->
                                    </div>
                                </div>
                                <!--mk041-->
                            </div>
                            <div class="w-100" mk="mk052">
                                <div class="underline center mb-025 text-9"><?= $tipo_inmuebles[$propiedad['tipo']] ?></div>

                                <?php $f = '<div class="text-8 center">%s</div><div class="text-9 bold pb-025 center">$%s%s %s</div>' ?>
                                <div><?= $propiedad['precio_venta']
    ? sprintf(
        $f,
        'Venta',
        number_format($propiedad['precio_venta'], 0, '.', ','),
        $propiedad['precio_por_metro'] === 'Si' ? '/m²' : '',
        $propiedad['moneda']
    ) : ''
    ?></div>
                                <div><?= $propiedad['precio_renta']
        ? sprintf(
            $f,
            'Renta mensual',
            number_format($propiedad['precio_renta'], 0, '.', ','),
            $propiedad['precio_por_metro'] === 'Si' ? '/m²' : '',
            $propiedad['moneda']
        ) : ''
    ?></div>
                                <div><?= $propiedad['precio_diaria']
        ? sprintf(
            $f,
            'Eventual',
            number_format($propiedad['precio_diaria'], 0, '.', ','),
            '',
            $propiedad['moneda']
        ) : ''
    ?></div>
                                <div><?= $propiedad['precio_traspaso']
        ? sprintf(
            $f,
            'Traspaso',
            number_format($propiedad['precio_traspaso'], 0, '.', ','),
            '',
            $propiedad['moneda']
        ) : ''
    ?></div>
                                <!--mk052-->
                            </div>
                            <div class="w-100 center" mk="mk053">
                                <div class="text-9 pb-025"><span class="text-8">Ingresado</span><br />
                                    <strong><?= $prop_date['registered'] ?></strong>
                                </div>
                                <div class="text-9 pb-025"><span class="text-8">Modificado</span><br />
                                    <strong><?= $prop_date['updated'] ?></strong>
                                </div>
                                <?php if (isset($prop_date['end_promotion'])) : ?>
                                <div class="text-9"><span class="text-8">Exp. <?= $falta ?></span><br />
                                    <strong><?= $prop_date['end_promotion'] ?></strong>
                                </div>
                                <?php endif ?>
                                <!--mk053-->
                            </div>
                        </div>
                        <div class="hidden pabuscar"><?php
                    $pabuscar = array();

    $pabuscar[] = $propiedad['claveprop'];
    $propiedad['enventa'] === 'Si' and ($pabuscar[] = 'venta') and
        !empty($propiedad['precio_venta_mxp']) and $pabuscar[] = round($propiedad['precio_venta_mxp']);
    $propiedad['enrenta'] === 'Si' and ($pabuscar[] = 'renta mensual') and
        !empty($propiedad['precio_renta_mxp']) and $pabuscar[] = round($propiedad['precio_renta_mxp']);
    $propiedad['endiaria'] === 'Si' and ($pabuscar[] = 'vacacional eventual diaria') and
        !empty($propiedad['precio_diaria_mxp']) and $pabuscar[] = round($propiedad['precio_diaria_mxp']);
    $propiedad['entraspaso'] === 'Si' and ($pabuscar[] = 'traspaso') and
        !empty($propiedad['precio_traspaso_mxp']) and $pabuscar[] = round($propiedad['precio_traspaso_mxp']);
    $propiedad['precio_por_metro'] === 'Si' and $pabuscar[] = 'metro cuadrado';
    $pabuscar[] = $propiedad['moneda'];
    $pabuscar[] = $propiedad['colonia'];
    $pabuscar[] = $propiedad['ciudad'];
    $pabuscar[] = $propiedad['provincia'];
    $pabuscar[] = $tipo_inmuebles[$propiedad['tipo']];
    $pabuscar[] = implode(' ', array_map(function ($i) {
        return str_replace('-', '', $i);
    }, $prop_date));
    $pabuscar[] = implode(' ', $prop_date);

    echo mb_strtolower(implode(' ', $pabuscar));

    ?></div>
                        <!--mk039-->
                    </article>
                    <?php

}

?>
                    <div class="tr" mk="mk054">
                        <div class="td" mk="mk055">
                            <script language="javascript">
                                <!--
                                function sel_quita(campo, theForm) {
                                    var ValVar;
                                    if (campo.name == 'selecciona_todos' && campo.checked == true) {
                                        theForm.quita_todos.checked = false;
                                        ValVar = true;
                                    }
                                    if (campo.name == 'quita_todos' && campo.checked == true) {
                                        theForm.selecciona_todos.checked = false;
                                        ValVar = false;
                                    }
                                    <?php
                foreach ($tprops as $tmp) {
                    echo "		document.getElementById('$tmp').checked = ValVar;\n";
                }
?>
                                }

                                function EnWeb(Que) {
                                    if (confirm('¿ Estás seguro(a) que deseas ' + Que + ' de tu página web el(los) inmueble(s) seleccionado(s) ?')) {
                                        document.Formulario.QueHace.value = Que;
                                        document.Formulario.target = "_self";
                                        document.Formulario.action = "<?php echo $PHP_SELF; ?>";
                                        document.Formulario.submit();
                                    } // if
                                    return;
                                }
                                -->
                            </script>
                            <?php
                            if ($aspermisos['p_registra_inmuebles'] == 'Si') {
                                ?>
                            <input TYPE="hidden" NAME="mop" VALUE="act">
                            <input TYPE="hidden" NAME="QueHace" VALUE="">

                            <div class="tr" mk="mk056">
                                <div class="td" mk="mk057"><a HREF="javascript:EnWeb('Publicar');">Publicar en web</a><!--mk057--></div>
                                <div class="td" mk="mk058"><a HREF="javascript:EnWeb('Quitar');">Quitar del web</a><!--mk058--></div>
                                <!--mk056-->
                            </div>

                            <?php
                            }	// if
?>

                            <div class="tr" mk="mk059">
                                <div class="td" mk="mk060"><input type="checkbox" name="selecciona_todos" value="Si" onClick="sel_quita(this, this.form);"><span class="observaciones">Seleccionar todos los inmuebles</span><!--mk060--></div>
                                <div class="td" mk="mk061"><input type="checkbox" name="quita_todos" value="Si" onClick="sel_quita(this, this.form);"><span class="observaciones">Quitar selecci&oacute;n a todos los inmuebles</span><!--mk061--></div>
                                <!--mk059-->
                            </div>
                            <div class="tr" mk="mk062">
                                <div class="td" mk="mk063"><input type="checkbox" name="imprimir_sin_fotos" value="Si"><span class="observaciones">Imprimir sin fotos</span><!--mk063--></div>
                                <div class="td" mk="mk064"><input type="checkbox" name="paginar" value="Si"><span class="observaciones">Paginar resultados con <input TYPE="text" NAME="prop_por_pag" value="2" size="3" class="form_peque"> inmuebles por p&aacute;gina</span><!--mk064--></div>
                                <!--mk062-->
                            </div>

                            <div class="p" mk="mk065"><input type="submit" value="Imprimir inmuebles seleccionados ..."><!--mk065--></div><!--mk055-->
                        </div>
                        <!--mk054-->
                    </div>

                    <!-- /LISTADO DE LOS INMUEBLES -->
                    <!--mk019-->
                </div>
                <!--mk018-->
            </div>

            <input type="hidden" name="t_orden" value="<?php echo $ort_orden; ?>">
            <input type="hidden" name="ordenar_por" value="<?php echo $ordenar_por; ?>">


            <!--mk002-->
        </section>
    </form>
    <!--mk001-->
</main>
<?php

// Desconectamos de la base de datos
mysql_close($db_link);

// Despliego el pie de página y termino la ejecución del programa
echo $m->render($plantilla['pie'], $render);
exit;

?>