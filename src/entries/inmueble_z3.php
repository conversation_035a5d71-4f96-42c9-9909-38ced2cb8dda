<?php

if (!isset($plantilla)) {
    die('No se puede entrar a este programa directamente');
}

// Recupero $propiedad[clave_sistema] de la propiedad
$consulta1 = mysqlQuery("SELECT clave_sistema, fecha_ingreso, fecha_modificaciones, aid FROM propiedades WHERE (contrato='$config[contrato]' && claveprop='$claveprop')", $db_link);
$propiedad = mysql_fetch_array($consulta1);

// Si no existe la propiedad, salimos
if (!$propiedad) {
    die('La propiedad no existe');
}

// Permisos de ejecución de este programa
if ($propiedad['aid'] != $_COOKIE['cookie_asesor']) {
    cm_permiso('p_edita_todos_los_inmuebles');
} else {
    cm_permiso('p_registra_inmuebles');
}

// Recupero el número de reportes de visita que tiene esta propiedad
$v1 = '';
$consulta1 = mysqlQuery("SELECT clave_sistema FROM reportes_visitas WHERE (clave_sistema='$propiedad[clave_sistema]' && completada='Si')", $db_link);
$v2 = mysql_num_rows($consulta1);
if ($v2 > 0) {
    $v1 = "Este inmueble tiene $v2 reporte(s) de visitas registrado(s),<br>se recomienda que imprima estos reportes para archivarlos<br>ya que se eliminarán los reportes de visita del inmueble.<br><br>Si desea imprimir los reportes de visita haga <A HREF=\"reportevisita.php?clave=$propiedad[clave_sistema]&imprimir=SI&paso=5&prop=NO\" target=\"_blank\">clic aquí</A><br><br><br>";
}


/**
 * Primer pantalla de confirmación
 */
if ($_POST['confirmacion'] !== 'SI') {

    ?>
<div align="center">
    <table class="t1">
        <tr>
            <td>
                <?php echo local_menu_propiedad(); ?>
            </td>
        </tr>
        <tr>
            <th>Eliminando inmueble</th>
        </tr>
        <tr>
            <td>
                <table class="t4" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <p align="center">
                                <font size="2" face="Arial"><?php echo $v1; ?>¿ Estás seguro(a) que deseas eliminar permanentemente el inmueble con<br>
                                    clave: <b><A HREF="#" onclick='window.open("detallesnew.php?clave=<?= $propiedad['clave_sistema']; ?>","info<?= $propiedad['clave_sistema']; ?>","toolbar=no,scrollbars=yes,resizable=yes,width=777,height=500");'><?php echo $claveprop; ?></a></b> ?</font>
                            </p>
                            <!-- FORMULARIO DE ELIMINACIÓN DE INMUEBLE -->
                            <form method="POST">
                                <div class="contenedor min-200">
                                    <input type="submit" value="Si, estoy seguro!" class="btn btn-primary">
                                    <input type="button" value="No, ya me arrepentí" OnClick="window.open('inmueble.php', '_top')" class="btn btn-primary">
                                </div>
                                <input type="hidden" name="paso" value="5">
                                <input type="hidden" name="confirmacion" value="SI">
                                <input type="hidden" name="claveprop" value="<?= $claveprop ?>">
                                <input type="hidden" name="clave_sistema" value="<?= $propiedad['clave_sistema'] ?>">

                                <input type="hidden" name="clv0" value="<?php echo str_replace(':', 'sisisii', cm_encripta("$config[contrato] --- $propiedad[clave_sistema] --- " . strftime("%Y-%m-%d", time()) . " --- $config[servicio_contratado]")); ?>">
                                <input type="hidden" name="clv1" value="<?= $claveprop ?>">
                                <input type="hidden" name="clv2" value="<?= $config['contrato'] ?>">
                                <input type="hidden" name="clv3" value="<?= $config['servicio_contratado'] ?>">
                                <input type="hidden" name="clv4" value="<?= $propiedad['clave_sistema'] ?>">
                                <input type="hidden" name="clv5" value="borrado">
                                <input type="hidden" name="clv6" value="<?php echo "$SERVER_NAME$PHP_SELF"; ?>">
                            </form>
                            <!-- /	FORMULARIO DE ELIMINACIÓN DE INMUEBLE -->
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
<?php
        /**/

        /**
         * Eliminación del inmueble incluyendo fotografías y panorámicas
         */
} else {

    $query = sprintf(
        "SELECT token 
        FROM api_tokens 
        WHERE contrato_id='%s' AND (expires_at > NOW() OR expires_at IS NULL)
        LIMIT 1",
        $config['contrato']
    );
    $api_token = $sql->row($query);

    $photos_service = getenv('PROXY_SERVICE_PHOTOS');
    $url = sprintf('http://%s/', $photos_service);

    // Datos para enviar por POST
    $postData = array(
        'j' => 'borraInmuebleSI4',
        'idi' => $propiedad['clave_sistema'],
        'contrato' => $config['contrato'],
        'token' => $api_token['token']
    );

    // Inicializar cURL
    $ch = curl_init();
    if ($ch === false) {
        die('Error: No se pudo inicializar cURL');
    }

    // Configurar opciones de cURL para POST
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Sistema Inmobiliario/1.0');

    // Ejecutar la petición
    $response = curl_exec($ch);

    // Verificar errores de cURL
    if ($response === false) {
        $error = curl_error($ch);
        curl_close($ch);
        die('Error de conexión: ' . $error);
    }

    // Verificar código de respuesta HTTP
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        die('Error HTTP: Código ' . $httpCode . ' - No se pudo conectar al servicio de imágenes');
    }

    // Verificar que la respuesta no esté vacía
    if (empty($response)) {
        die('Error: Respuesta vacía del servicio de imágenes');
    }

    // Decodificar JSON
    $rImages = json_decode($response, true);

    // Verificar errores de JSON
    if (json_last_error() !== JSON_ERROR_NONE) {
        die('Error al procesar respuesta: ' . json_last_error_msg());
    }

    if (empty($rImages['idProp'])) {
        die('Ocurrió un error al eliminar el inmueble !!!');
    }

    // Recupero la información de la propiedad y la grabo en el historial
    mysqlQuery("INSERT INTO props_eliminadas (contrato, usuario, clave_sistema, claveprop, ingresado, modificado, eliminado) VALUES ('$config[contrato]', '$cookie_s_usuario', '$propiedad[clave_sistema]', '$claveprop', '$propiedad[fecha_ingreso]', '$propiedad[fecha_modificaciones]', '$ahorita')", $db_link);

    // Borro de la base de datos la propiedad
    mysqlQuery("DELETE FROM propiedades WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema') LIMIT 1", $db_link);
    $num1 = mysql_affected_rows($db_link);

    // En caso de que si se haya eliminado un inmueble de la BD
    if ($num1 == 1) {

        // 🚀 INTEGRACIÓN CON METEOR - ELIMINAR POSTS INMOBILIARIOS
        try {
            require_once __DIR__ . '/clases/MeteorPost.class.php';
            $meteorPost = new Mulbin\MeteorPost($config['contrato']);

            // Eliminar todos los posts relacionados con este inmueble por externalId
            $meteorResponse = $meteorPost->deletePostsByExternalId($clave_sistema);

            if ($meteorResponse['success']) {
                $deletedCount = isset($meteorResponse['data']->totalDeleted) ? $meteorResponse['data']->totalDeleted : 0;
                $totalFound = isset($meteorResponse['data']->totalFound) ? $meteorResponse['data']->totalFound : 0;

                error_log("✅ Eliminación sincronizada Meteor - Inmueble: {$clave_sistema}, Posts encontrados: {$totalFound}, Posts eliminados: {$deletedCount}");

                // Guardamos info para mostrar al usuario
                $meteor_deletion_info = array(
                    'success' => true,
                    'posts_found' => $totalFound,
                    'posts_deleted' => $deletedCount
                );
            } else {
                $errorMsg = isset($meteorResponse['message']) ? $meteorResponse['message'] : 'Error desconocido';
                error_log("⚠️ Error en eliminación Meteor para inmueble {$clave_sistema}: {$errorMsg}");

                $meteor_deletion_info = array(
                    'success' => false,
                    'error' => $errorMsg
                );
            }

        } catch (\Exception $e) {
            // Log del error pero continuar con el flujo normal del sistema padre
            error_log("❌ Excepción en eliminación Meteor para inmueble {$clave_sistema}: " . $e->getMessage());

            $meteor_deletion_info = array(
                'success' => false,
                'error' => 'Excepción: ' . $e->getMessage()
            );
        }

        //	Inserto el registro en el bypass de movimientos de propiedades
        mysqlQuery("INSERT INTO movpropiedades (contrato, clave_sistema, movimiento) VALUES ('$config[contrato]', '$clave_sistema', 'DELETE')", $db_link);

        // Borro de la base de datos de comercializacion AMPI
        mysqlQuery("DELETE FROM ampi_comercializacion WHERE (clave_sistema='$clave_sistema')", $db_link);

        // Borro de la base de datos los campos que perteneces a esta propiedad
        mysqlQuery("DELETE FROM valores_campos WHERE (clave_sistema='$clave_sistema')", $db_link);

        // Borro de la base de datos las citas de la propiedad
        mysqlQuery("DELETE FROM citas WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema')", $db_link);
        $num_citas = mysql_affected_rows($db_link);

        // Borro de la base de datos las preguntas de la propiedad
        mysqlQuery("DELETE FROM preguntas WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema')", $db_link);
        $num_preguntas = mysql_affected_rows($db_link);

        // Borro de la base de datos los reportes de visita de la propiedad
        mysqlQuery("DELETE FROM documentos WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema')", $db_link);
        $num_contratos = mysql_affected_rows($db_link);

        // Borro de la base de datos los reportes de visita de la propiedad
        mysqlQuery("DELETE FROM reportes_visitas WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema')", $db_link);
        $num_reportes = mysql_affected_rows($db_link);

        // Borro las relaciones que tiene este inmueble con desarrollos registrados
        mysqlQuery("DELETE FROM rel_prop_des WHERE (prop_clave_sistema='$clave_sistema')", $db_link);
        $num_rel_prop_des = mysql_affected_rows($db_link);

        // Borro las publicaciones de este inmueble en otros sitios
        mysqlQuery("DELETE FROM casas_compartidas WHERE (clave_sistema='$clave_sistema')", $db_link);
        $num_casas_compartidas = mysql_affected_rows($db_link);
    }

    ?>
<p align="center">&nbsp;</p>
<p align="center">
    <font size="2" face="Arial"><?php echo $num1 ?> inmueble con clave <b>
            <?php echo $claveprop; ?></b> ha sido eliminado de la base de datos con éxito</font>
</p>

<p align="center">
    <font size="2" face="Arial">Se eliminaron <b><?= $rImages['fotos'] ?></b> fotografía(s) del inmueble</font>
</p>
<p align="center">
    <font size="2" face="Arial">Se eliminaron <b><?= $rImages['panos'] ?></b> vista(s) panorámica(s) del inmueble</font>
</p>
<p align="center">
    <font size="2" face="Arial">Se eliminaron <b><?php echo $num_citas; ?></b> citas registradas del inmueble</font>
</p>
<p align="center">
    <font size="2" face="Arial">Se eliminaron <b><?php echo $num_preguntas; ?></b> preguntas registradas del inmueble</font>
</p>
<p align="center">
    <font size="2" face="Arial">Se eliminaron <b><?php echo $num_contratos; ?></b> documentos registrados del inmueble</font>
</p>
<p align="center">
    <font size="2" face="Arial">Se eliminaron <b><?php echo $num_reportes; ?></b> reportes de visitas del inmueble</font>
</p>
<p align="center">
    <font size="2" face="Arial">Se eliminaron <b><?php echo $num_rel_prop_des; ?></b> registros que enlazaban el inmueble con desarrollos publicados</font>
</p>
<p align="center">
    <font size="2" face="Arial">Se eliminaron <b><?php echo $num_casas_compartidas; ?></b> registros de publicación de este inmueble en sitios web de socios registrados</font>
</p>

<?php
// 🆕 MOSTRAR INFORMACIÓN DE ELIMINACIÓN EN METEOR
if (isset($meteor_deletion_info)) {
    if ($meteor_deletion_info['success']) {
        $posts_found = $meteor_deletion_info['posts_found'];
        $posts_deleted = $meteor_deletion_info['posts_deleted'];

        if ($posts_found > 0) {
            echo '<p align="center">';
            echo '<font size="2" face="Arial" color="#008000">Se eliminaron <b>' . $posts_deleted . '</b> post(s) del Feed de Multibolsa Inmobiliaria</font>';
            echo '</p>';

            if ($posts_deleted < $posts_found) {
                $posts_no_autorizados = $posts_found - $posts_deleted;
                echo '<p align="center">';
                echo '<font size="2" face="Arial" color="#FFA500"><b>' . $posts_no_autorizados . '</b> post(s) no pudieron eliminarse (permisos insuficientes)</font>';
                echo '</p>';
            }
        } else {
            echo '<p align="center">';
            echo '<font size="2" face="Arial" color="#666666">No se encontraron posts relacionados en el Feed de Multibolsa Inmobiliaria</font>';
            echo '</p>';
        }
    } else {
        echo '<p align="center">';
        echo '<font size="2" face="Arial" color="#FF0000">⚠️ Error al eliminar posts del Feed: ' . htmlspecialchars($meteor_deletion_info['error']) . '</font>';
        echo '</p>';
    }
}
    ?>

<p align="center">&nbsp;</p>
<?php

}
?>