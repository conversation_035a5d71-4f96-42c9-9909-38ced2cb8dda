<?php

/*
Paso 2, es donde se graba la información en la base de datos
*/
require_once './clases/StringMethods.php';
$str = new \Estate\StringMethods();
$tag_destacados = 1;

/**
 * [cm_verifica_campo description].
 */
function cm_verifica_campo($variable, $campo, $prohibidos)
{
    global $plantilla, $db_link, $mustache;
    foreach ($prohibidos as $tmp) {
        if ($variable == $tmp) {
            $m_file = __DIR__ . '/inmueble/templates/error_verificacion_campo.st.mustache';
            $m_template = file_get_contents($m_file);
            $m_data = array('campo' => $campo);
            echo $mustache->render($m_template, $m_data);

            // Cierro BD y termino la ejecución del programa
            mysql_close($db_link);
            echo $plantilla[pie];
            exit;
        }
    }
}
// ///////////////////////////////////////////////////////////

/*
Cuerpo del programa
*/
if (!isset($plantilla)) {
    exit('No se puede entrar a este programa directamente');
}

// ////////////////////////////////////////////////
// Expiración o si se publica o no
if ('si expira' == $expiracion && $exp_dia > 0 && $exp_mes > 0 && $exp_agno > 0) {
    $status_web = 'publicado';
    $fecha_expiracion = "$exp_agno-$exp_mes-$exp_dia";
} else {
    if ('si expira' == $expiracion) {
        $expiracion = 'publicado';
    }
    $status_web = $expiracion;
    $fecha_expiracion = '0000-00-00';
}
// ////////////////////////////////////////////////

// ////////////////////////////////////////////////
// Trato los campos de selección, en caso de no estar definidos lo pongo como negativo
if ('Si' != $enventa) {
    $enventa = 'No';
}
if ('Si' != $enrenta) {
    $enrenta = 'No';
}
if ('Si' != $endiaria) {
    $endiaria = 'No';
}
if ('Si' != $entraspaso) {
    $entraspaso = 'No';
}
if ('Si' != $precio_por_metro) {
    $precio_por_metro = 'No';
}
if ('Si' != $en_resumen) {
    $en_resumen = 'No';
}
// ////////////////////////////////////////////////

// ////////////////////////////////////////////////
// Limpio todas las cadenas antes de guardarlas
$claveprop = trim($claveprop);
$pais = trim($pais);
$provincia = trim($provincia);
$ciudad = trim($ciudad);
$colonia = trim($colonia);
$muestra_colonia = trim($muestra_colonia);
$zona = trim($zona);
$tipo = trim($tipo);
$precio_venta = (int)preg_replace('/[^0-9]/', '', $precio_venta);
$precio_renta = (int)preg_replace('/[^0-9]/', '', $precio_renta);
$precio_diaria = (int)preg_replace('/[^0-9]/', '', $precio_diaria);
$precio_traspaso = (int)preg_replace('/[^0-9]/', '', $precio_traspaso);
$moneda = trim($moneda);
$anuncio_esp = trim($anuncio_esp);
$anuncio_ing = trim($anuncio_ing);
$anuncio_fra = trim($anuncio_fra);
$caract_esp = trim($caract_esp);
$caract_ing = trim($caract_ing);
$caract_fra = trim($caract_fra);
$keywords_esp = trim($keywords_esp);
$keywords_ing = trim($keywords_ing);
$keywords_fra = trim($keywords_fra);
$fecha_ingreso = $hoy;
$fecha_modificaciones = $ahorita;
$html_especial = trim($_POST['html_especial']);
// $link = mysqli_connect('db001-server', 'sistemainmo', 'sipubliweb777', "sistemainmobiliario");
// $html_especial = mysqli_real_escape_string($link, $_POST['html_especial']);
// ////////////////////////////////////////////////

// ////////////////////////////////////////////////
// Establezco los precios para el registro
if ('MXP' != $moneda) {
    $consulta1 = mysqlQuery("SELECT equiv_mxp FROM monedas WHERE (siglas='$moneda')", $db_link);
    $row = mysql_fetch_array($consulta1);
    $precio_venta_mxp = ((int) $precio_venta * $row['equiv_mxp']) ?: 'null';
    $precio_renta_mxp = ((int) $precio_renta * $row['equiv_mxp']) ?: 'null';
    $precio_diaria_mxp = ((int) $precio_diaria * $row['equiv_mxp']) ?: 'null';
    $precio_traspaso_mxp = ((int) $precio_traspaso * $row['equiv_mxp']) ?: 'null';
} else {
    $precio_venta_mxp = (int) $precio_venta ?: 'null';
    $precio_renta_mxp = (int) $precio_renta ?: 'null';
    $precio_diaria_mxp = (int) $precio_diaria ?: 'null';
    $precio_traspaso_mxp = (int) $precio_traspaso ?: 'null';
}
// ////////////////////////////////////////////////

// ////////////////////////////////////////////////
// Estas lineas son por si en el inter de la captura otro usuario ha borrado o ya ha registrado el inmueble
$consulta1 = mysqlQuery("SELECT clave_sistema FROM propiedades WHERE (contrato='$config[contrato]' && claveprop='$claveprop')", $db_link);
$num_props = mysql_num_rows($consulta1);

if (empty($codigo_postal)) {
    $codigo_postal = fieldDefault('propiedades', 'codigo_postal');
}
$aid = empty($cookie_asesor) ? fieldDefault('propiedades', 'aid') : $cookie_asesor;

// Si es nuevo inmueble pero otro usuario lo acaba de registrar
if ($num_props > 0 && '' == $clave_sistema) {
    $m_file = __DIR__ . '/inmueble/templates/error_clave_existente.st.mustache';
    $m_template = file_get_contents($m_file);
    $m_data = array('claveprop' => $claveprop);
    echo $mustache->render($m_template, $m_data);

    // En caso de estar editando el inmueble pero otro usuario lo acaba de borrar
} elseif (0 == $num_props && '' != $clave_sistema) {
    $m_file = __DIR__ . '/inmueble/templates/error_inmueble_no_existe.st.mustache';
    $m_template = file_get_contents($m_file);
    $m_data = array('claveprop' => $claveprop);
    echo $mustache->render($m_template, $m_data);
    // ////////////////////////////////////////////////

    // ////////////////////////////////////////////////
    // En caso de ser un alta de inmueble
} elseif (0 == $num_props && '' == $clave_sistema && 'POST' == $REQUEST_METHOD && !eregi(' ', $claveprop)) {
    // Si es una nueva colonia la grabo en la BD y obtengo su Id
    if (0 == $id_colonia) {
        $ciu = $sql->row("SELECT ciudades.slug FROM ciudades WHERE id={$id_ciudad} LIMIT 1");
        $col_slug = $str->slugify("{$colonia} {$ciu['slug']} {$config['contrato']}");
        $query = "
			SELECT id, colonia
			FROM new_colonias 
			WHERE (contrato IS NULL OR contrato={$config['contrato']}) AND 
				id_ciudad={$id_ciudad} AND (colonia='{$colonia}' OR slug='{$col_slug}')
			";
        if (!$r = $sql->row($query)) {
            $id_colonia = null;
            $sql->insert(
                'new_colonias',
                array(
                    'contrato' => $config['contrato'],
                    'id_ciudad' => $id_ciudad,
                    'colonia' => $colonia,
                    'slug' => $col_slug,
                ),
                true,
                function ($id) use (&$id_colonia) {
                    $id_colonia = $id;
                }
            );
        } else {
            $id_colonia = $r['id'];
            $colonia = $r['colonia'];
        }
    }   // if

    if ('Si' == $aspermisos['p_edita_todos_los_inmuebles']) {
        $t_asesor = 0;
    } else {
        $t_asesor = $cookie_asesor;
    }

    // Si el precio es por metro cuadrado calculo el precio total
    if ($_POST['ci_terreno'] > 0 && $precio_venta_mxp > 0 && 'Si' == $_POST['precio_por_metro']) {
        $precio_vta_total_mxp = $_POST['ci_terreno'] * $precio_venta_mxp;
    } elseif ($precio_venta_mxp > 0) {
        $precio_vta_total_mxp = $precio_venta_mxp;
    } else {
        $precio_vta_total_mxp = 'null';
    }

    // Verifico si el contrato tiene sucursales y si es así tomo la primera como la que promueve el inmueble
    if (!empty($_SESSION['sucursal'])) {
        $sucursal = $_SESSION['sucursal'];
    } elseif ($sucursal = $sql->row(
        "SELECT id FROM sucursales 
		WHERE (contrato='{$config['contrato']}')
		ORDER BY matriz DESC
		LIMIT 1"
    )) {
        $sucursal = $sucursal['id'];
    } else {
        $sucursal = 'null';
    }

    if (empty($residencial)) {
        $residencial = fieldDefault('propiedades', 'residencial');
    }
    if (empty($comercial)) {
        $comercial = fieldDefault('propiedades', 'comercial');
    }
    if (empty($industrial)) {
        $industrial = fieldDefault('propiedades', 'industrial');
    }
    if (empty($vacacional)) {
        $vacacional = fieldDefault('propiedades', 'vacacional');
    }

    $key_str = $str->generateKeyStr(7, $sql);

    // Grabo primero en la BD de propiedades los datos básicos del inmueble
    $query = sprintf(
        "INSERT INTO propiedades 
		(contrato, claveprop, nombreprop, tipo, residencial, comercial, industrial, vacacional, precio_venta, precio_renta, precio_diaria, precio_traspaso, precio_venta_mxp, precio_vta_total_mxp, precio_renta_mxp, precio_diaria_mxp, precio_traspaso_mxp, id_colonia, colonia, muestra_colonia, zona, codigo_postal, anuncio_esp, anuncio_ing, anuncio_fra, caract_esp, caract_ing, caract_fra, ciudad, provincia, pais, moneda, enventa, enrenta, endiaria, entraspaso, precio_por_metro, fecha_modificaciones, fecha_expiracion, keywords_esp, keywords_ing, keywords_fra, en_resumen, status_web, asesor, aid, html_especial, sucursal, key_str)
		VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', %s, %s, %s, %s, %s, '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', %s, '%s')",
        $config['contrato'],
        $claveprop,
        $nombreprop,
        $tipo,
        $residencial,
        $comercial,
        $industrial,
        $vacacional,
        $precio_venta,
        $precio_renta,
        $precio_diaria,
        $precio_traspaso,
        $precio_venta_mxp,
        $precio_vta_total_mxp,
        $precio_renta_mxp,
        $precio_diaria_mxp,
        $precio_traspaso_mxp,
        $id_colonia,
        $colonia,
        $muestra_colonia,
        $zona,
        $codigo_postal,
        $sql->real_escape_string($anuncio_esp),
        $sql->real_escape_string($anuncio_ing),
        $sql->real_escape_string($anuncio_fra),
        $sql->real_escape_string($caract_esp),
        $sql->real_escape_string($caract_ing),
        $sql->real_escape_string($caract_fra),
        $ciudad,
        $provincia,
        $pais,
        $moneda,
        $enventa,
        $enrenta,
        $endiaria,
        $entraspaso,
        $precio_por_metro,
        $fecha_modificaciones,
        $fecha_expiracion,
        $keywords_esp,
        $keywords_ing,
        $keywords_fra,
        $en_resumen,
        $status_web,
        $t_asesor,
        $aid,
        $html_especial,
        $sucursal,
        $key_str
    );

    if (!$sql->query($query)) {
        // mail('<EMAIL>', 'Error al insertar un inmueble', "Error al ejecutar la siguiente consulta:\n\n$query\n\n" . $sql->error() . "\n\n" . __FILE__ . "\n\n" . __LINE__);
        $m_file = __DIR__ . '/inmueble/templates/error_guardar_datos.st.mustache';
        $m_template = file_get_contents($m_file);
        $m_data = array();
        $m_data['query'] = $query;
        $m_data['error'] = $sql->error;
        $m_data['file'] = __FILE__;
        $m_data['line'] = __LINE__;
        $m_data['debug'] = getenv('DEBUG_MODE') == 'true' ? true : false;
        echo $mustache->render($m_template, $m_data);
    } //

    // Se insertó el nuevo inmueble con éxito en la DB
    else {
        $op_exitosa = 'Si';
        $query = sprintf(
            "SELECT clave_sistema FROM propiedades WHERE contrato='%s' AND claveprop='%s'",
            $config['contrato'],
            $claveprop
        );
        $consulta1 = $sql->query($query);
        $clave_sistema = $consulta1->fetch_assoc();
        $clave_sistema = $clave_sistema['clave_sistema'];

        // Hago el registro de la relación de tipo de inmueble
        $sql->insert(
            'tipo_inm_pivot',
            array(
                'tipo_id' => $tipo,
                'prop_id' => $clave_sistema
            )
        );

        // Cuando en_resumen === 'Si' se agrega el inmueble a la lista de destacados
        // por medio de tags_pivot
        if ('Si' == $en_resumen) {
            $sql->insert(
                'tags_pivot',
                array(
                    'tag_id' => $tag_destacados,
                    'prop_id' => $clave_sistema
                )
            );
        }

        // //////////////////////////////
        //  Registro el movimiento en el bypass para la actualización de wordpress
        $sql->query("INSERT INTO movpropiedades (contrato, clave_sistema, movimiento) VALUES ('$config[contrato]', '$clave_sistema', 'INSERT')");

        // Ahora grabo la información de los campos
        $consulta1 = $sql->query("SELECT * FROM campos_inmuebles WHERE ((contrato IS NULL || contrato='$config[contrato]') && uso='publico')");
        while ($row = $consulta1->fetch_assoc()) {
            $val_variable = ${$row['variable']};
            $valor_esp = '';
            $valor_ing = '';
            if ('selector' == $row['tipo']) {
                // En caso de haber especificado valores
                if ('-1' == $val_variable) {
                    $d_val_variable_esp = ${"d_$row[variable]_esp"};
                    $d_val_variable_ing = ${"d_$row[variable]_ing"};
                    if ('*** en español ***' == $d_val_variable_esp) {
                        $d_val_variable_esp = '';
                    }
                    if ('*** in english ***' == $d_val_variable_ing) {
                        $d_val_variable_ing = '';
                    }

                    $valor_esp = ucfirst(mb_strtolower(str_replace('  ', ' ', trim($d_val_variable_esp))));
                    $valor_ing = ucfirst(mb_strtolower(str_replace('  ', ' ', trim($d_val_variable_ing))));

                    // /////////////////////////////////////////
                    // Grabo los valores en los aprendidos
                    if ('' != $valor_esp || '' != $valor_ing) {
                        $consulta2 = $sql->query("
						SELECT * FROM valores_campos_aprendidos 
						WHERE ((contrato IS NULL OR contrato='{$config['contrato']}') AND variable='{$row['variable']}' AND 
							valor_esp='{$valor_esp}' AND valor_ing='{$valor_ing}')
						");

                        // Al cumplirse esto debo quiere decir que no hay registrado algo exactamente igual en español e inglés pero aún existe la posibilidad de que haya cualquiera de las 2 probabilidades registradas
                        if (0 == $consulta2->num_rows) {
                            // Esta consulta me dirá si lo que voy a hacer es sobreescribir un valor ya existente
                            $consulta2 = $sql->query("SELECT * FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp='')))");
                            $num_res = $consulta2->num_rows;

                            // Al cumplirse lo siguiente es que hay 2 valores para la misma entrada, borro lo que hay y hago una entrada para los 2 campos
                            if ($num_res >= 2) {
                                $sql->query("DELETE FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp='')))");
                                $sql->query("INSERT INTO valores_campos_aprendidos (contrato, variable, valor_esp, valor_ing) VALUES ('$config[contrato]', '$row[variable]', '$valor_esp', '$valor_ing')");

                                // En caso de que haya un valor ya sea en esp o ing pero su contraparte esté vacía sustituyo por los valores ya completos
                            } elseif (1 == $num_res) {
                                $sql->query("UPDATE valores_campos_aprendidos SET valor_esp='$valor_esp', valor_ing='$valor_ing' WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp=''))");

                                // Al cumplirse esto es que es 0 y no hay entrada bajo ningun valor por lo que inserto
                            } else {
                                $sql->query("INSERT INTO valores_campos_aprendidos (contrato, variable, valor_esp, valor_ing) VALUES ('$config[contrato]', '$row[variable]', '$valor_esp', '$valor_ing')");
                            }
                        }
                    }

                    // En caso de haber seleccionado un valor aprendido
                } elseif ('' != $val_variable) {
                    $consulta2 = $sql->query("SELECT * FROM valores_campos_aprendidos WHERE (id='$val_variable')");
                    $v0 = $consulta2->fetch_assoc();
                    $valor_esp = $v0['valor_esp'];
                    $valor_ing = $v0['valor_ing'];
                }
            } else {
                $valor_esp = $val_variable;
            }

            // En caso de tener información en esp o ing hago la entrada en la BD
            if ('' != $valor_esp || '' != $valor_ing) {
                if ('numerico' == $row['tipo'] && '' == $valor_ing) {
                    $valor_ing = $valor_esp;
                }

                $sql->query("
                    INSERT INTO valores_campos 
                    (contrato, clave_sistema, variable_id, variable, valor_esp, valor_ing) 
                    VALUES 
                    ('{$config['contrato']}', '{$clave_sistema}', '{$row['id']}', '{$row['variable']}', '{$valor_esp}', '{$valor_ing}')
                ");
            }
        }

        // Grabo o actualizo en la base para búsquedas
        cm_rec_datos_prop($clave_sistema, $config['contrato']);

        $nuevo_inmueble = true;
        $paso = 3;
    }
} //


// ////////////////////////////////////////////////
// En caso de estar editando el inmueble
elseif (
    1 == $num_props && '' != $clave_sistema &&
    $clave_sistema > 0 && 'POST' == $REQUEST_METHOD &&
    !preg_match('/\s/', $nclaveprop)) {
    if ('' == $nclaveprop) {
        $nclaveprop = $claveprop;
    }

    // Si es una nueva colonia la grabo en la BD y obtengo su Id
    if (0 == $id_colonia) {
        $ciu = $sql->row("SELECT ciudades.slug FROM ciudades WHERE id={$id_ciudad} LIMIT 1");
        $col_slug = $str->slugify("{$colonia} {$ciu['slug']} {$config['contrato']}");
        $query = "
			SELECT id, colonia
			FROM new_colonias 
			WHERE (contrato IS NULL OR contrato={$config['contrato']}) AND 
				id_ciudad={$id_ciudad} AND (colonia='{$colonia}' OR slug='{$col_slug}')
			";
        if (!$r = $sql->row($query)) {
            $id_colonia = null;
            $sql->insert('new_colonias', array(
                'contrato' => $config['contrato'],
                'id_ciudad' => $id_ciudad,
                'colonia' => $colonia,
                'slug' => $col_slug,
            ), true, function ($id) use (&$id_colonia) {
                $id_colonia = $id;
            });
        } else {
            $id_colonia = $r['id'];
            $colonia = $r['colonia'];
        }
    }   // if

    // Si el precio es por metro cuadrado calculo el precio total
    if ($_POST['ci_terreno'] > 0 && $precio_venta_mxp > 0 && 'Si' == $_POST['precio_por_metro']) {
        $precio_vta_total_mxp = $_POST['ci_terreno'] * $precio_venta_mxp;
    } elseif ($precio_venta_mxp > 0) {
        $precio_vta_total_mxp = $precio_venta_mxp;
    } else {
        $precio_vta_total_mxp = 'null';
    }

    $residencial = 'Si' == $_POST['residencial'] ? 'Si' : 'No';
    $comercial = 'Si' == $_POST['comercial'] ? 'Si' : 'No';
    $industrial = 'Si' == $_POST['industrial'] ? 'Si' : 'No';
    $vacacional = 'Si' == $_POST['vacacional'] ? 'Si' : 'No';

    // Genero un nuevo key_str solo si actualmente el key_str es null
    $propiedad = $sql->row("SELECT clave_sistema, key_str FROM propiedades WHERE clave_sistema = '{$clave_sistema}' LIMIT 1");
    if (null == $propiedad['key_str']) {
        $key_str = $str->generateKeyStr(7, $sql);
    } else {
        $key_str = $propiedad['key_str'];
    }

    // Considero el campo status_id directamente relacionado a status_web con la relación:
    // 1 = disponible, 2 = reservado, 3 = vendido, 4 = rentado, 6 = sin publicar
    // TODO: Falta la implementación completa de esta característica
    $status_relation = array(
        'publicado' => 1,
        'sin publicar' => 6,
    );
    $status_id = $status_relation[$status_web];

    // Grabo primero en la BD de propiedades los datos públicos del inmueble
    $query = "
		UPDATE propiedades 
		SET claveprop='{$nclaveprop}', nombreprop='{$nombreprop}', tipo='{$tipo}', residencial='{$residencial}', 
			comercial='{$comercial}', industrial='{$industrial}', vacacional='{$vacacional}',
			status_id='{$status_id}', precio_venta='{$precio_venta}',
			precio_renta='{$precio_renta}', precio_diaria='{$precio_diaria}', precio_traspaso='{$precio_traspaso}', 
			precio_venta_mxp={$precio_venta_mxp}, precio_vta_total_mxp={$precio_vta_total_mxp}, 
			precio_renta_mxp={$precio_renta_mxp}, precio_diaria_mxp={$precio_diaria_mxp}, 
			precio_traspaso_mxp={$precio_traspaso_mxp}, id_colonia='{$id_colonia}', colonia='{$colonia}', 
			muestra_colonia='{$muestra_colonia}', zona='{$zona}', codigo_postal='{$codigo_postal}', anuncio_esp='‹-›s', 
			anuncio_ing='‹-›s', anuncio_fra='‹-›s', caract_esp='‹-›s', caract_ing='‹-›s', 
			caract_fra='‹-›s', ciudad='{$ciudad}', provincia='{$provincia}', pais='{$pais}', moneda='{$moneda}', 
			enventa='{$enventa}', enrenta='{$enrenta}', endiaria='{$endiaria}', entraspaso='{$entraspaso}', 
			precio_por_metro='{$precio_por_metro}', fecha_modificaciones='{$fecha_modificaciones}', 
			fecha_expiracion='{$fecha_expiracion}', keywords_esp='{$keywords_esp}', keywords_ing='{$keywords_ing}', 
			keywords_fra='{$keywords_fra}', operacion_hecha='{$operacion_hecha}', en_resumen='{$en_resumen}', 
			status_web='{$status_web}', html_especial='{$html_especial}', CamposMC='{$CamposMC}', 
			title_seo='‹-›s', desc_seo='‹-›s', generated_by='‹-›s', key_str='{$key_str}'
		WHERE contrato='{$config['contrato']}' AND clave_sistema='{$clave_sistema}'
		LIMIT 1
	";
    $query = str_replace(array('%', '‹-›'), array('%%', '%'), $query);
    $query = sprintf(
        $query,
        $sql->real_escape_string($anuncio_esp),
        $sql->real_escape_string($anuncio_ing),
        $sql->real_escape_string($anuncio_fra),
        $sql->real_escape_string($caract_esp),
        $sql->real_escape_string($caract_ing),
        $sql->real_escape_string($caract_fra),
        $sql->real_escape_string(json_encode(array_map(function ($i) {
            return stripslashes($i);
        }, $_REQUEST['title_seo']))),
        $sql->real_escape_string(json_encode(array_map(function ($i) {
            return stripslashes($i);
        }, $_REQUEST['desc_seo']))),
        $sql->real_escape_string($_REQUEST['generated_by'])
    );

    if (!$sql->query($query)) {
        // mail('<EMAIL>', 'Error al actualizar Datos Publicos de un inmueble', "Error al ejecutar la siguiente consulta:\n\n$query\n\n" . $sql->error() . "\n\n" . __FILE__ . "\n\n" . __LINE__);
        $m_file = __DIR__ . '/inmueble/templates/error_guardar_datos.st.mustache';
        $m_template = file_get_contents($m_file);
        $m_data = array();
        $m_data['query'] = $query;
        $m_data['error'] = $sql->error;
        $m_data['file'] = __FILE__;
        $m_data['line'] = __LINE__;
        $m_data['debug'] = getenv('DEBUG_MODE') == 'true' ? true : false;
        echo $mustache->render($m_template, $m_data);
    } else {
        /**
         * Bloque que controla la tag para inmuebles destacados.
         */
        if ('Si' == $en_resumen) {
            $query = "
				SELECT * FROM tags_pivot
				WHERE tag_id = {$tag_destacados} AND prop_id = '{$clave_sistema}'
			";
            $result = $sql->query($query);
            if (0 == $result->num_rows) {
                $query = "
					INSERT INTO tags_pivot (tag_id, prop_id)
					VALUES ({$tag_destacados}, '{$clave_sistema}')
				";
                $sql->query($query);
            }
        } else {
            $query = "
				DELETE FROM tags_pivot
				WHERE tag_id = {$tag_destacados} AND prop_id = '{$clave_sistema}'
			";
            $sql->query($query);
        }
        /* - */

        // Aquí modifico el tipo de inmueble en la tabla pivot
        $sql->update_reg(
            'tipo_inm_pivot',
            array(
                'tipo_id' => $tipo,
            ),
            "prop_id='{$clave_sistema}'"
        );

        // //////////////////////////////
        //  Inserto el registro en el bypass de movimientos de propiedades
        $sql->query("INSERT INTO movpropiedades (contrato, clave_sistema, movimiento) VALUES ('$config[contrato]', '$clave_sistema', 'UPDATE')");

        $claveprop = $nclaveprop;

        $op_exitosa = 'Si';

        // Ahora grabo la información de los campos
        $consulta1 = $sql->query("SELECT * FROM campos_inmuebles WHERE ((contrato IS NULL || contrato='$config[contrato]') && uso='publico')");
        while ($row = $consulta1->fetch_assoc()) {
            $val_variable = ${$row['variable']};
            $valor_esp = '';
            $valor_ing = '';
            if ('selector' == $row['tipo']) {
                // En caso de haber especificado valores
                if ('-1' == $val_variable) {
                    $d_val_variable_esp = ${"d_$row[variable]_esp"};
                    $d_val_variable_ing = ${"d_$row[variable]_ing"};
                    if ('*** en español ***' == $d_val_variable_esp) {
                        $d_val_variable_esp = '';
                    }
                    if ('*** in english ***' == $d_val_variable_ing) {
                        $d_val_variable_ing = '';
                    }

                    $valor_esp = ucfirst(mb_strtolower(str_replace('  ', ' ', trim((string) $d_val_variable_esp)), 'UTF-8'));
                    $valor_ing = ucfirst(mb_strtolower(str_replace('  ', ' ', trim($d_val_variable_ing)), 'UTF-8'));

                    // /////////////////////////////////////////
                    // Grabo los valores en los aprendidos
                    if ('' != $valor_esp || '' != $valor_ing) {
                        $consulta2 = $sql->query("SELECT * FROM valores_campos_aprendidos WHERE ((contrato IS NULL OR contrato='{$config['contrato']}') AND variable='{$row['variable']}' AND valor_esp='{$valor_esp}' AND valor_ing='{$valor_ing}')");

                        // Al cumplirse esto quiere decir que no hay registrado algo exactamente igual en español e inglés pero aún existe la posibilidad de que haya cualquiera de las 2 probabilidades registradas
                        if (0 == $consulta2->num_rows) {
                            // Esta consulta me dirá si lo que voy a hacer es sobreescribir un valor ya existente
                            $consulta2 = $sql->query("SELECT * FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp='')))");
                            $num_res = $consulta2->num_rows;

                            // Al cumplirse lo siguiente es que hay 2 valores para la misma entrada, borro lo que hay y hago una entrada para los 2 campos
                            if ($num_res >= 2) {
                                $sql->query("DELETE FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp='')))");
                                $sql->query("INSERT INTO valores_campos_aprendidos (contrato, variable, valor_esp, valor_ing) VALUES ('$config[contrato]', '$row[variable]', '$valor_esp', '$valor_ing')");

                                // En caso de que haya un valor ya sea en esp o ing pero su contraparte esté vacía sustituyo por los valores ya completos
                            } elseif (1 == $num_res) {
                                $sql->query("UPDATE valores_campos_aprendidos SET valor_esp='$valor_esp', valor_ing='$valor_ing' WHERE (contrato='$config[contrato]' && variable='$row[variable]' && ((valor_esp='$valor_esp' && valor_ing='') || (valor_ing='$valor_ing' && valor_esp='')))");

                                // Al cumplirse esto es que es 0 y no hay entrada bajo ningun valor por lo que inserto
                            } else {
                                $sql->query("INSERT INTO valores_campos_aprendidos (contrato, variable, valor_esp, valor_ing) VALUES ('$config[contrato]', '$row[variable]', '$valor_esp', '$valor_ing')");
                            }
                        }
                    }

                    // En caso de haber seleccionado un valor aprendido
                } elseif ('' != $val_variable) {
                    $consulta2 = $sql->query("SELECT * FROM valores_campos_aprendidos WHERE (id='$val_variable')");
                    $v0 = $consulta2->fetch_assoc();
                    $valor_esp = $v0['valor_esp'];
                    $valor_ing = $v0['valor_ing'];
                }
            } else {
                $valor_esp = $val_variable;
            }

            // Borro el contenido del campo antes de insertar el nuevo
            $sql->query("DELETE FROM valores_campos WHERE (clave_sistema='$clave_sistema' && variable='$row[variable]') LIMIT 1");

            // En caso de tener información en esp o ing hago la entrada en la BD
            if ('' != $valor_esp || '' != $valor_ing) {
                if ('numerico' == $row['tipo'] && '' == $valor_ing) {
                    $valor_ing = $valor_esp;
                }

                $sql->query("
                    INSERT INTO valores_campos 
                    (contrato, clave_sistema, variable_id, variable, valor_esp, valor_ing)
                    VALUES
                    ('{$config['contrato']}', '{$clave_sistema}', '{$row['id']}', '{$row['variable']}', '{$valor_esp}', '{$valor_ing}')
                ");
            }
        }

        // Grabo o actualizo en la base para búsquedas
        cm_rec_datos_prop($clave_sistema, $config['contrato']);

        $_REQUEST['paso'] = $paso = 3;
    }
}

// ////////////////////////////////////////////////
// Me encargo del area de los desarrollos
if ('Si' == $desarrollo && 'Si' == $op_exitosa) {
    $consulta1 = $sql->query("SELECT clave_sistema FROM desarrollos WHERE (contrato='$config[contrato]')");
    while ($row = $consulta1->fetch_assoc()) {
        if ('Si' == ${"d_$row[clave_sistema]"}) {
            $consulta2 = $sql->query("SELECT * FROM rel_prop_des WHERE (prop_clave_sistema='$clave_sistema' && des_clave_sistema='$row[clave_sistema]')");
            if (0 == $consulta2->num_rows) {
                $sql->query("INSERT INTO rel_prop_des (prop_clave_sistema, des_clave_sistema) VALUES ('$clave_sistema', '$row[clave_sistema]')");
            }
        } else {
            $sql->query("DELETE FROM rel_prop_des WHERE(prop_clave_sistema='$clave_sistema' && des_clave_sistema='$row[clave_sistema]')");
        }
    }

    // Al no cumplirse significa que el inmueble no pertenece a ningún desarrollo y lo doy de baja de los que pueda estar
} else {
    $sql->query("DELETE FROM rel_prop_des WHERE (prop_clave_sistema='$clave_sistema')");
}
// ////////////////////////////////////////////////
