<?php

setlocale(LC_ALL, "es_MX");

/*
A este punto tenemos disponibles:
    $sql -> objeto de conexión a la base de datos
    $m -> Mustache_Engine
    $config -> array de configuración
    $cliente -> array de cliente
 */

// Incluir los modelos necesarios
require_once __DIR__ . '/../clases/Models/SICasaCompartida.php';
require_once __DIR__ . '/../clases/Models/SIInmueble.php';
require_once __DIR__ . '/../clases/Models/PWCliente.php';
require_once __DIR__ . '/../clases/Models/SIFoto.php';

use <PERSON><PERSON>bin\Models\SICasaCompartida;
use <PERSON><PERSON>bin\Models\SIInmueble;
use Mu<PERSON>bin\Models\PWCliente;
use Mulbin\Models\SIFoto;

$layout = file_get_contents('/var/www/templates/dist/guest.html');

$token = $_REQUEST['token'];
$action = isset($_POST['action']) ? $_POST['action'] : '';

$render = array();

// Buscar la solicitud por token
$solicitud = SICasaCompartida::buscarPorToken($token);

if (!$solicitud || // No existe la solicitud
    // Solo el socio solicitado puede gestionar la solicitud
    (
        (int)$solicitud->quien_solicita === (int)$solicitud->contrato &&
        (int)CONTRATO !== (int)$solicitud->getInmueble()->contrato
    )
) {
    $template = file_get_contents(__DIR__ . '/../templates/shared-properties-error.mustache');
    $render['contenido_resultado'] = $m->render($template, [
        'mensaje' => 'Token inválido o solicitud no encontrada.'
    ]);
} elseif ($solicitud->isProcessed()) {
    $template = file_get_contents(__DIR__ . '/../templates/shared-properties-success.mustache');
    $render['contenido_resultado'] = $m->render($template, [
        'mensaje' => 'Esta solicitud ya fue procesada anteriormente.',
        'autorizada' => $solicitud->isAutorizado()
    ]);
} else {
    // Procesar acciones
    if ($action === 'autorizar') {
        if ($solicitud->aceptarConexion()) {
            $template = file_get_contents(__DIR__ . '/../templates/shared-properties-success.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Publicación autorizada exitosamente. El inmueble se publicará automáticamente en tu sitio web.',
                'autorizada' => true
            ]);
        } else {
            $template = file_get_contents(__DIR__ . '/../templates/shared-properties-error.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Error al autorizar la publicación. Por favor, intenta nuevamente.'
            ]);
        }
    } elseif ($action === 'rechazar') {
        if ($solicitud->rechazarConexion()) {
            $template = file_get_contents(__DIR__ . '/../templates/shared-properties-success.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Solicitud rechazada. El inmueble no se publicará en tu sitio web.',
                'autorizada' => false
            ]);
        } else {
            $template = file_get_contents(__DIR__ . '/../templates/shared-properties-error.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Error al rechazar la solicitud. Por favor, intenta nuevamente.'
            ]);
        }
    } else {
        // Mostrar formulario de decisión
        $inmueble = $solicitud->getInmueble();
        $solicitante = $solicitud->getSolicitante();
        $socioDestino = $solicitud->getContratoSocio();

        if (!$inmueble || !$solicitante) {
            $template = file_get_contents(__DIR__ . '/../templates/shared-properties-error.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Error: No se pudo obtener la información del inmueble o del solicitante.'
            ]);
        } else {
            // Obtener datos completos del inmueble y solicitante
            $datosInmueble = $inmueble->getDatosCompletos();
            $datosSolicitante = obtenerDatosCompletosSocio($solicitante);

            $template = file_get_contents(__DIR__ . '/../templates/shared-properties-propietario-request.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'token' => $token,
                'fecha_solicitud' => $solicitud->getFechaSolicitudFormateada('largo'),
                'inmueble' => $datosInmueble,
                'solicitante' => $datosSolicitante,
                // 'metodo' => $solicitud->metodo
                'metodo' => false // TODO: agregar el metodo de la solicitud
            ]);
        }
    }
}

echo $m->render($layout, $render);

/**
 * Obtiene las iniciales de un nombre de usuario
 *
 * @param string $usuario
 * @return string
 */
function obtenerIniciales($usuario)
{
    // Limpiar el usuario y dividir por espacios o guiones
    $palabras = preg_split('/[\s\-_]+/', trim($usuario));

    $iniciales = '';
    foreach ($palabras as $palabra) {
        if (!empty($palabra)) {
            $iniciales .= strtoupper(substr($palabra, 0, 1));
        }
    }

    // Si no hay iniciales, usar las primeras 2 letras del usuario
    if (empty($iniciales)) {
        $iniciales = strtoupper(substr($usuario, 0, 2));
    }

    // Limitar a máximo 2 caracteres
    return substr($iniciales, 0, 2);
}

/**
 * Obtiene los datos completos de un socio incluyendo avatar
 *
 * @param object $socio Objeto SIConfig del socio
 * @return array
 */
function obtenerDatosCompletosSocio($socio)
{
    // Buscar información del cliente en PWCliente
    $cliente = PWCliente::buscarPorUsuario($socio->usuario);

    $avatar = null;
    $empresa = 'N/A';
    $estado = 'N/A';
    $iniciales = obtenerIniciales($socio->usuario);

    if ($cliente) {
        // Obtener avatar
        if (!empty($cliente->avatar)) {
            $avatar = getenv('MULBIN_URL') . $cliente->avatar;
        }

        // Obtener empresa desde clientes
        if (!empty($cliente->empresa)) {
            $empresa = $cliente->empresa;
        }

        // Obtener estado desde clientes
        if (!empty($cliente->estado)) {
            $estado = $cliente->estado;
        }

        // Obtener iniciales desde el nombre completo del cliente
        if ($cliente->getIniciales()) {
            $iniciales = $cliente->getIniciales();
        }
    }

    return [
        'usuario' => $socio->usuario,
        'empresa' => $empresa,
        'contrato' => $socio->contrato,
        'estado' => $estado,
        'avatar' => $avatar,
        'iniciales' => $iniciales,
        'nombre' => $cliente ? $cliente->nombre : 'N/A',
        'apellidos' => $cliente ? $cliente->apellidos : '',
        'ciudad' => $cliente ? $cliente->ciudad : 'N/A',
        'estado' => $cliente ? $cliente->estado : $estado,
        'pais' => $cliente ? $cliente->pais : 'N/A',
        'telefono' => $cliente ? $cliente->getTelefonoPrincipal() : null,
        'email' => $cliente ? ($cliente->email ?: $cliente->logmail) : null
    ];
}
