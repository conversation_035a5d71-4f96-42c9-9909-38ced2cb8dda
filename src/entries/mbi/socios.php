<?php

setlocale(LC_ALL, "es_MX");

/*
A este punto tenemos disponibles:
    $sql -> objeto de conexión a la base de datos
    $m -> Mustache_Engine
    $config -> array de configuración
    $cliente -> array de cliente
 */

// Incluir el modelo SISocios
require_once __DIR__ . '/../clases/Models/SISocios.php';
require_once __DIR__ . '/../clases/Models/PWCliente.php';

use Mulbin\Models\SISocios;
use Mulbin\Models\PWCliente;

// // Configurar el loader de partials para Mustache
// $m->setPartials(new Mustache_Loader_FilesystemLoader(__DIR__ . '/../templates/partials'));
// dd($m);

$layout = file_get_contents('/var/www/templates/dist/guest.html');

$token = $_REQUEST['token'];
$action = isset($_POST['action']) ? $_POST['action'] : '';

$render = array();

// Buscar la solicitud por token
$solicitud = SISocios::buscarPorToken($token);

if (!$solicitud) {
    $template = file_get_contents(__DIR__ . '/../templates/socios-error.mustache');
    $render['contenido_resultado'] = $m->render($template, [
        'mensaje' => 'Token inválido o solicitud no encontrada.'
    ]);
} elseif ($solicitud->isTokenExpired()) {
    $template = file_get_contents(__DIR__ . '/../templates/socios-error.mustache');
    $render['contenido_resultado'] = $m->render($template, [
        'mensaje' => 'El enlace ha expirado. Por favor, solicita un nuevo enlace.'
    ]);
} elseif ($solicitud->isAceptada()) {
    $template = file_get_contents(__DIR__ . '/../templates/socios-success.mustache');
    $render['contenido_resultado'] = $m->render($template, [
        'mensaje' => 'Esta solicitud ya fue aceptada anteriormente.'
    ]);
} else {
    // Procesar acciones
    if ($action === 'aceptar') {
        if ($solicitud->aceptarConexion()) {
            $template = file_get_contents(__DIR__ . '/../templates/socios-success.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Solicitud aceptada exitosamente. Ahora puedes compartir inmuebles con este socio.'
            ]);
        } else {
            $template = file_get_contents(__DIR__ . '/../templates/socios-error.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Error al aceptar la solicitud. Por favor, intenta nuevamente.'
            ]);
        }
    } elseif ($action === 'rechazar') {
        if ($solicitud->rechazarConexion()) {
            $template = file_get_contents(__DIR__ . '/../templates/socios-success.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Solicitud rechazada.'
            ]);
        } else {
            $template = file_get_contents(__DIR__ . '/../templates/socios-error.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Error al rechazar la solicitud. Por favor, intenta nuevamente.'
            ]);
        }
    } else {
        // Mostrar formulario de decisión
        $solicitante = $solicitud->getSolicitante();
        $solicitado = $solicitud->getSolicitado();

        if (!$solicitante || !$solicitado) {
            $template = file_get_contents(__DIR__ . '/../templates/socios-error.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'mensaje' => 'Error: No se pudo obtener la información de los socios.'
            ]);
        } else {
            // Obtener información completa de los socios incluyendo avatares
            $datosSolicitante = obtenerDatosCompletosSocio($solicitante);
            $datosSolicitado = obtenerDatosCompletosSocio($solicitado);

            $template = file_get_contents(__DIR__ . '/../templates/socios-request.mustache');
            $render['contenido_resultado'] = $m->render($template, [
                'token' => $token,
                'fecha_solicitud' => $solicitud->getFechaSolicitudFormateada('largo'),
                'solicitante' => $datosSolicitante,
                'solicitado' => $datosSolicitado
            ]);
        }
    }
}

echo $m->render($layout, $render);

/**
 * Obtiene las iniciales de un nombre de usuario
 *
 * @param string $usuario
 * @return string
 */
function obtenerIniciales($usuario)
{
    // Limpiar el usuario y dividir por espacios o guiones
    $palabras = preg_split('/[\s\-_]+/', trim($usuario));

    $iniciales = '';
    foreach ($palabras as $palabra) {
        if (!empty($palabra)) {
            $iniciales .= strtoupper(substr($palabra, 0, 1));
        }
    }

    // Si no hay iniciales, usar las primeras 2 letras del usuario
    if (empty($iniciales)) {
        $iniciales = strtoupper(substr($usuario, 0, 2));
    }

    // Limitar a máximo 2 caracteres
    return substr($iniciales, 0, 2);
}

/**
 * Obtiene los datos completos de un socio incluyendo avatar
 *
 * @param object $socio Objeto SIConfig del socio
 * @return array
 */
function obtenerDatosCompletosSocio($socio)
{
    // Buscar información del cliente en PWCliente
    $cliente = PWCliente::buscarPorUsuario($socio->usuario);

    $avatar = null;
    $empresa = 'N/A';
    $estado = 'N/A';
    $iniciales = obtenerIniciales($socio->usuario);

    if ($cliente) {
        // Obtener avatar
        if (!empty($cliente->avatar)) {
            $avatar = getenv('MULBIN_URL') . $cliente->avatar;
        }

        // Obtener empresa desde clientes
        if (!empty($cliente->empresa)) {
            $empresa = $cliente->empresa;
        }

        // Obtener estado desde clientes
        if (!empty($cliente->estado)) {
            $estado = $cliente->estado;
        }

        // Obtener iniciales desde el nombre completo del cliente
        if ($cliente->getIniciales()) {
            $iniciales = $cliente->getIniciales();
        }
    }

    return [
        'usuario' => $socio->usuario,
        'empresa' => $empresa,
        'contrato' => $socio->contrato,
        'estado' => $estado,
        'avatar' => $avatar,
        'iniciales' => $iniciales,
        'nombre' => $cliente->nombre,
        'apellidos' => $cliente->apellidos,
        'ciudad' => $cliente->ciudad,
        'estado' => $cliente->estado,
        'pais' => $cliente->pais
    ];
}
