<?php

// Recupero desde la tabla 'api_tokens' el token de la API
$token = $sql->row("
	SELECT token FROM api_tokens 
	WHERE contrato_id = {$config['contrato']} AND name = 'SI.v4:ControlPanel' AND 
		(expires_at >= NOW() OR expires_at IS NULL) 
	LIMIT 1");
if (!$token) {
    $token = array(
        'token' => 'tk_' . md5(uniqid(rand().time(), true))
    );
    $sql->insert("api_tokens", array(
        "contrato_id" => $config['contrato'],
        "name" => "SI.v4:ControlPanel",
        "token" => $token['token']
    ));
}

?>
<div id="questions-table" data-token="<?= 'tkk_' . md5(uniqid(rand().time(), true)) ?>" data-orden="fecha_hora_p" data-tipo-orden="desc"></div>
<link rel="stylesheet" href="../msi-v5/dist/assets/questionsTable_panel/questionsTable_panel.css" />
<script type="module" src="../msi-v5/dist/assets/questionsTable_panel/questionsTable_panel.js"></script>