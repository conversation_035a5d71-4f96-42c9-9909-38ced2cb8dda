<script language="javascript">
	<!-- //
	function abre(pag, ventana) {
		window.open(pag, ventana, "toolbar=yes,scrollbars=yes,resizable=yes,width=580,height=350");
		return;
	}

	function verifica() {
		if (document.Formulario1.que_hacer.value == 'borrar') {
			if (confirm('Está seguro que desea eliminar permanentemente esta pregunta ?')) {
				return (true);
			} else {
				return (false);
			}
		}
		return (true);
	}

	// 
	-->
</script>

<div align="center">
	<center>
		<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#000080" width="770" id="AutoNumber1" bgcolor="#E6E6FF">
			<tr>
				<td width="100%">
					<p align="center"><b>
							<font face="Arial">Pregunta realizada</font>
						</b></p>
					<table border="1" width="100%" id="table1" cellpadding="5" bgcolor="#FFFFFF" style="border-collapse: collapse">
						<tr>
							<td width="100%" align="left" height="25" bgcolor="#000080">
								<font color="#FFFFFF" size="2" face="Arial"><b>Pregunta y datos del prospecto:</b></font>
							</td>
						</tr>
						<tr>
							<td width="100%" align="left" height="25" bgcolor="#FFFFFF">
								<table border="0" width="100%" id="table4" cellspacing="0" cellpadding="0">
									<tr>
										<td>
											<font face="Arial" size="2"><? echo $pregunta[pregunta]; ?></font>
										</td>
										<td width="182" align="center" valign="top">
											<a href="detallesnew.php?clave=<? echo $pregunta[clave_sistema]; ?>" target="_blank"><b>
													<font size="2" face="Arial">
														<span style="text-decoration: none">Clave: <? echo $propiedad[claveprop]; ?></span>
													</font><br>
												</b><?

													// Primero recupero las fotos de los inmuebles a mostrar
													$ar_fotos = $images->primer_foto(array($pregunta['clave_sistema']), 'inmueble');
													echo $ar_fotos[$pregunta['clave_sistema']]['img'];

													?></a>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td width="100%" align="left" height="25" bgcolor="#FFFFFF">
								<table border="0" width="100%" id="table5" cellspacing="0" cellpadding="0" bgcolor="#F0F0F0">
									<tr>
										<td width="182" align="right">
											<font size="2" face="Arial">Nombre completo:&nbsp;&nbsp;&nbsp;&nbsp;
											</font>
										</td>
										<td width="426"><b>
												<font size="2" face="Arial"><? echo $nombre_completo; ?></font>
											</b></td>
										<td rowspan="3" align="right" valign="top">
											<?

											$consulta1 = mysqlQuery("SELECT id FROM contactos WHERE (contrato='$config[contrato]' && nombre='$nombre' && apellidos='$apellidos' && telefono='$telefono' && email='$email')", $db_link);

											if (mysql_num_rows($consulta1) == 0) {
												echo '<font size="2" face="Arial"><a href="contactos.php?op=Nuevo&nombre=' . urlencode($nombre) . '&apellidos=' . urlencode($apellidos) . '&email=' . urlencode($email) . '&telefono=' . urlencode($telefono) . '&fuera=Si&id=&tipo=Clientes">Agregar a contactos</a></font>';
												$cnt = 'No';
											} else {
												$contacto = mysql_fetch_array($consulta1);
												echo "<font size=\"2\" face=\"Arial\"><a href=\"javascript:abre('contactos.php?contacto=$contacto[id]&tiempo=" . time() . "&op=Ver');\">Ver datos del contacto</a></font>";
												$cnt = 'Si';
											}

											?>
										</td>
									</tr>
									<tr>
										<td width="182" align="right">
											<font size="2" face="Arial">Teléfono:&nbsp;&nbsp;&nbsp;&nbsp;
											</font>
										</td>
										<td width="426"><b>
												<font size="2" face="Arial"><? echo $telefono; ?></font>
											</b></td>
									</tr>
									<tr>
										<td width="182" align="right">
											<font size="2" face="Arial">Correo electrónico:&nbsp;&nbsp;&nbsp;&nbsp;
											</font>
										</td>
										<td width="426"><b>
												<font size="2" face="Arial"><? echo $email; ?></font>
											</b></td>
									</tr>
									<tr>
										<td width="182" align="right">
											<font size="2" face="Arial">Cuando se recibió:&nbsp;&nbsp;&nbsp;&nbsp;
											</font>
										</td>
										<td width="426"><b>
												<font size="2" face="Arial"><? echo cm_formato_fecha($pregunta[fecha_hora_p], 3); ?></font>
											</b></td>
									</tr>
									<?

									// En caso de haber recibido copia un asesor
									if ($pregunta['cc_asesor'] > 0) {
										$consulta2 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (id='$pregunta[cc_asesor]')", $db_link);
										if (mysql_num_rows($consulta2) == 1) {
											$asesor = mysql_fetch_array($consulta2);

									?>
											<tr>
												<td width="182" align="right">
													<font size="2" face="Arial">Con copia a:&nbsp;&nbsp;&nbsp;&nbsp;
													</font>
												</td>
												<td width="426"><b>
														<font size="2" face="Arial"><? echo "$asesor[nombre] $asesor[apellidos]"; ?></font>
													</b></td>
											</tr>
									<?

										}
									}	// if

									?>
								</table>
							</td>
						</tr>
						<tr>
							<td width="100%" align="left" height="25" bgcolor="#FFFFFF">