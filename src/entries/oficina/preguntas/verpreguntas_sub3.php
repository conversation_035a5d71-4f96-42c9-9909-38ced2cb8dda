	<div id="overDiv" style="POSITION: absolute; Z-INDEX: 1"></div>
	<script language="JavaScript" src="jsc/overlib.js"></script>

	<div align="center">
		<center>
			<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#000080" width="770" id="AutoNumber1" bgcolor="#E6E6FF">
				<tr>
					<td width="100%">
						<p align="center"><b>
								<font face="Arial">
									<?php

									if ($contestadas == 'Si') {
										echo "Preguntas ya contestadas";
									} else {
										echo "Preguntas pendientes de respuesta";
									}

									?></font>
							</b></p>
						<table border="1" width="100%" id="table1" cellpadding="3" bgcolor="#FFFFFF" style="border-collapse: collapse" cellspacing="0">
							<tr>
								<td width="16%" align="center" bgcolor="#000080">
									<b>
										<font color="#FFFFFF" size="2" face="Arial"><A class="blanco" HREF="<?php echo "$PHP_SELF?orden=clave&contestadas=$contestadas"; ?>">Clave del inmueble</A></font>
									</b>
								</td>
								<td width="42%" align="center" bgcolor="#000080">
									<b>
										<font face="Arial" size="2" color="#FFFFFF"><A class="blanco" HREF="<?php echo "$PHP_SELF?orden=fecha&contestadas=$contestadas"; ?>">Fecha y hora de la pregunta</A></font>
									</b>
								</td>
								<td width="29%" align="center" bgcolor="#000080">
									<b>
										<font face="Arial" size="2" color="#FFFFFF"><A class="blanco" HREF="<?php echo "$PHP_SELF?orden=nombre&contestadas=$contestadas"; ?>">Quién hizo la pregunta</A></font>
									</b>
								</td>
								<td width="13%" align="center" bgcolor="#000080">
									&nbsp;</td>
							</tr>
							<?php

							if ($orden == '' || $orden == 'fecha') {
								$orden = 'fecha_hora_p DESC';
							} elseif ($orden == 'nombre') {
								$orden = 'usuario, nombre, apellidos';
							} elseif ($orden == 'clave') {
								$orden = 'claveprop';
							}

							if ($contestadas == 'Si') {
								$v1 = '!=';
							} else {
								$v1 = '=';
							}

							$consulta1 = mysqlQuery("SELECT preguntas.numero, preguntas.clave_sistema, preguntas.pregunta, preguntas.usuario, preguntas.nombre, preguntas.apellidos, preguntas.fecha_hora_p, preguntas.cc_asesor, propiedades.claveprop, propiedades.sucursal FROM preguntas, propiedades WHERE (preguntas.contrato='$config[contrato]' && preguntas.fecha_hora_r $v1 '0000-00-00 00:00:00' && preguntas.clave_sistema=propiedades.clave_sistema) ORDER BY $orden, fecha_hora_p ASC", $db_link);

							$bgcolor = "#FFFFFF";
							while ($pregunta = mysql_fetch_array($consulta1)) {

								// Datos del usuario
								if ($pregunta[usuario] != '') {
									@mysql_select_db(DB_PW);
									$consulta3 = mysqlQuery("SELECT nombre, apellidos FROM clientes WHERE (usuario='$pregunta[usuario]')", $db_link);
									$visitante = mysql_fetch_array($consulta3);
									@mysql_select_db(DB_SI);
									$nombre = $visitante[nombre];
									$apellidos = $visitante[apellidos];
								} else {
									$nombre = $pregunta[nombre];
									$apellidos = $pregunta[apellidos];
								}

								$pregunta[pregunta] = cm_quita_saltos($pregunta[pregunta]);
								$pregunta[pregunta] = str_replace("'", '´', $pregunta[pregunta]);

							?>
								<tr bgcolor="<?php echo $bgcolor; ?>" onmouseout="nd(); return true;" onmouseover="drs('<?php echo $pregunta[pregunta]; ?>'); return true;">
									<td width="16%" align="center">
										<font face="Arial" size="2"><a href="detallesnew.php?clave=<?php echo $pregunta[clave_sistema]; ?>" target="_blank"><?php echo $pregunta[claveprop]; ?></a></font>
										<?php

										if ($pregunta[sucursal] > 0) {
											echo '<br><font face="Arial" size="1" color="#339900">' . $sucursales[$pregunta[sucursal]] . '</font>';
										}

										?>
									</td>
									<td width="42%" align="center">
										<font face="Arial" size="2"><?php echo cm_formato_fecha($pregunta[fecha_hora_p], 3); ?></font>
										<?php

										// En caso de haber recibido copia un asesor
										if ($pregunta['cc_asesor'] > 0) {
											$consulta2 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (id='$pregunta[cc_asesor]')", $db_link);
											$asesor = mysql_fetch_array($consulta2);
											if (mysql_num_rows($consulta2) == 1) echo "<br><span class=\"observaciones\">CC: <B>$asesor[nombre] $asesor[apellidos]</B></span>";
										}	// if

										?>
									</td>
									<td width="29%" align="center">
										<font face="Arial" size="2"><?php echo "$nombre $apellidos"; ?></font>
									</td>
									<td width="13%" align="center">
										<font face="Arial" size="2"><a href="<?php echo "$PHP_SELF?id=$pregunta[numero]&sucursal=$pregunta[sucursal]"; ?>">Ver pregunta</a></font>
									</td>
								</tr>
							<?php

								if ($bgcolor == '#FFFFFF') {
									$bgcolor = '#F0F0F0';
								} else {
									$bgcolor = '#FFFFFF';
								}
							}

							?>
						</table>
					</td>
				</tr>
				<tr>
					<td width="100%" bgcolor="#FFFFFF" align="center">
						<form method="POST" action="<?php echo $PHP_SELF; ?>">
							<p>
								<font size="2" face="Arial">Ver historial de preguntas del inmueble con clave: </font>
								<select size="1" name="clave">
									<?php

									$consulta1 = mysqlQuery("SELECT DISTINCT preguntas.clave_sistema, propiedades.claveprop, propiedades.sucursal FROM preguntas, propiedades WHERE (preguntas.contrato='$config[contrato]' && propiedades.clave_sistema=preguntas.clave_sistema) ORDER BY propiedades.claveprop", $db_link);
									while ($pregunta = mysql_fetch_array($consulta1)) {
										$v1 = "$pregunta[clave_sistema]---z24charly---$pregunta[claveprop]---z24charly---$pregunta[sucursal]";
										echo "			<option value=\"$v1\">$pregunta[claveprop]</option>\n";
									}

									?>
								</select><input type="submit" value="Continuar -&gt;">
							</p>
						</form>
					</td>
				</tr>
				<tr>
					<td width="100%" bgcolor="#FFFFFF">
						<p align="center">
							<?php

							if ($contestadas == 'Si') {
								echo "<font face=\"Arial\" size=\"2\"><B><A HREF=\"$PHP_SELF?contestadas=No\">Ver preguntas pendientes de respuesta</A></B></font>";
							} else {
								echo "<font face=\"Arial\" size=\"2\"><B><A HREF=\"$PHP_SELF?contestadas=Si\">Ver preguntas ya contestadas</A></B></font>";
							}

							?>
						</p>
					</td>
				</tr>
			</table>
		</center>
	</div>