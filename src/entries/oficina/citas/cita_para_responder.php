<?php

if (!isset($ids)) {
    $ids = explode(',', $id);
}

// Compruebo que no haya mas citas de este misma sesión para la hora solicitada
if (count($ids) == 1 && $solouno != 'Si' && trim($id) != '') {
    $consulta1 = mysqlQuery("SELECT citas.*, propiedades.sucursal FROM citas, propiedades WHERE (citas.contrato='$config[contrato]' && citas.id='$id' && citas.clave_sistema=propiedades.clave_sistema)", $db_link);
    $cita = mysql_fetch_array($consulta1);
    $consulta1 = mysqlQuery("SELECT citas.id, propiedades.sucursal FROM citas, propiedades WHERE (citas.contrato='$config[contrato]' && citas.usuario='$cita[usuario]' && citas.nombre='$cita[nombre]' && citas.apellidos='$cita[apellidos]' && citas.email='$cita[email]' && citas.telefono='$cita[telefono]' && citas.celular='$cita[celular]' && citas.nextel_tel='$cita[nextel_tel]' && citas.nextel_radio='$cita[nextel_radio]' && citas.ocupacion='$cita[ocupacion]' && citas.empresa='$cita[empresa]' && citas.ciudad='$cita[ciudad]' && citas.estado='$cita[estado]' && citas.pais='$cita[pais]' && citas.fecha_hora_solicitada='$cita[fecha_hora_solicitada]' && citas.fecha_hora_cita='$cita[fecha_hora_cita]' && citas.cancelada='$cita[cancelada]' && citas.sesion='$cita[sesion]' && citas.tipo_respuesta='$cita[tipo_respuesta]' && citas.lugar_cita='$cita[lugar_cita]' && citas.asesor='$cita[asesor]' && propiedades.sucursal='$cita[sucursal]' && citas.clave_sistema=propiedades.clave_sistema)", $db_link);

    $ids = array();
    while ($cita = mysql_fetch_array($consulta1)) {
        array_push($ids, $cita[id]);
    }
}

$v1 = '';
// Hago la cadena para la consulta
for ($i = 0; $i < count($ids); $i++) {
    if ($i == 0) {
        $v1 = "&& (id = $ids[0]";
    } else {
        $v1 .= " || id = " . $ids[$i];
    }
}
if ($v1 != '') {
    $v1 = "$v1)";
} else {
    echo '<CENTER><B>Cita inexistente</B></CENTER>';

    //////////////
    // Desconectamos de la base de datos
    mysql_close($db_link);

    //////////////
    // Despliego el pie de página y termino la ejecución del programa
    echo $plantilla[pie];
    exit;
}

// Recupero los datos de las citas solicitadas
$consulta1 = mysqlQuery("SELECT citas.*, propiedades.claveprop FROM citas, propiedades WHERE (citas.contrato='$config[contrato]' $v1 && citas.clave_sistema=propiedades.clave_sistema)", $db_link);

// Primero recupero las fotos de los inmuebles a mostrar
$ar_fotos = array();
while ($propiedad = mysql_fetch_array($consulta1)) {
    array_push($ar_fotos, $propiedad['clave_sistema']);
}
$ar_fotos = $images->primer_foto($ar_fotos, 'inmueble');
if (mysql_num_rows($consulta1) > 0) {
    mysql_data_seek($consulta1, 0);
}

$propiedades = '';
$v1 = '';
$v2 = 0;

while ($cita = mysql_fetch_array($consulta1)) {

    // Datos del usuario cuando se toman desde un usuario registrado
    if ($cita[usuario] != '' && $v1 != 'Pollo') {
        @mysql_select_db(DB_PW);
        $consulta3 = mysqlQuery("SELECT * FROM clientes WHERE (usuario='$cita[usuario]')", $db_link);
        $visitante = mysql_fetch_array($consulta3);
        @mysql_select_db(DB_SI);
        $cita[nombre] = $visitante[nombre];
        $cita[apellidos] = $visitante[apellidos];
        $cita[ocupacion] = $visitante[ocupacion];
        $cita[empresa] = $visitante[empresa];
        $cita[ciudad] = $visitante[ciudad];
        $cita[estado] = $visitante[estado];
        $cita[pais] = $visitante[pais];
        $cita[telefono] = $visitante[telefono];
        $cita[celular] = $visitante[celular];
        $cita[nextel_tel] = $visitante[nextel_tel];
        $cita[nextel_radio] = $visitante[nextel_radio];
        $cita[email] = $visitante[email];
    }

    if ($v1 != 'Pollo') {
        $nombre = $cita[nombre];
        $apellidos = $cita[apellidos];
        $ocupacion = $cita[ocupacion];
        $empresa = $cita[empresa];
        $ciudad = $cita[ciudad];
        $estado = $cita[estado];
        $pais = $cita[pais];
        $telefono = $cita[telefono];
        $celular = $cita[celular];
        $nextel_tel = $cita[nextel_tel];
        $nextel_radio = $cita[nextel_radio];
        $email = $cita[email];
        $fecha_hora_solicitada = $cita[fecha_hora_solicitada];
        $fecha_hora_cita = $cita[fecha_hora_cita];
        $tipo_respuesta = $cita[tipo_respuesta];
        $cancelada = $cita[cancelada];
        $asesor = $cita[asesor];
        $lugar_cita = $cita[lugar_cita];
        $como_llegar = $cita[como_llegar];
        $respuesta = $cita[respuesta];
        $cita_por = $cita[cita_por];
        $confirmo_cliente = $cita[confirmo_cliente];
        $cc_asesor = $cita[cc_asesor];
    }

    $v1 = 'Pollo';

    // Armo las claves a seleccionar
    $v2++;
    if (trim($cita[observaciones]) != '') {
        $comentario = "<font size=\"2\" face=\"Arial\"><u><i>Observaciones del cliente:</i></u><br><b>$cita[observaciones]</b></font>";
    } else {
        $comentario = '';
    }
    $propiedades .= "
							<tr>
								<td>
								<table border=\"0\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#FFFF00\" id=\"tbl$v2\">
									<tr>
										<td>
										<input type=\"checkbox\" name=\"ids[]\" value=\"$cita[id]\" onclick=\"colores(this, '$v2');\" checked><font size=\"2\" face=\"Arial\">Seleccionar 
										esta clave</font></td>
									</tr>
									<tr>
										<td align=\"center\">
										<a href=\"detallesnew.php?clave=$cita[clave_sistema]\" target=\"_blank\">
										<b>
										<font size=\"2\" face=\"Arial\">Clave: $cita[claveprop]</font><br>
						</b>" . $ar_fotos[$cita[clave_sistema]]['img'] . "</a></td>
									</tr>
									<tr>
										<td>$comentario</td>
									</tr>
									<tr>
										<td align=\"right\">
										<font size=\"2\" face=\"Arial\" color=\"#808080\">" . cm_formato_fecha($cita[solicitud], 4, '/') . "</font></td>
									</tr>
								</table>
								</td>
							</tr>
		";
}

?>
<script language="javascript">
    function abre(pag, ventana) {
        window.open(pag, ventana, "toolbar=yes,scrollbars=yes,resizable=yes,width=580,height=350");
        return;
    }

    function colores(valor, tabla) {
        tabla = 'tbl' + tabla;

        if (valor.checked == true) {
            document.getElementById(tabla).style.background = "#FFFF00";
        } else {
            document.getElementById(tabla).style.background = "#C0C0C0";
        }
    }

    function verifica(theForm) {
        var1 = 0;
        var2 = 0;
        while (theForm.elements[var1].name != 'email') {
            if (theForm.elements[var1].name == 'que_hacer' && theForm.elements[var1].value == 'cancelar') {
                if (!confirm("Se cancelará la cita con el cliente para conocer los inmuebles seleccionados\n¿ Desea continuar ?")) {
                    return (false);
                }
            }
            if (theForm.elements[var1].name == 'ids[]' && theForm.elements[var1].checked) {
                var2 = 1;
            }
            var1++;
        }

        // En caso de no haber seleccionado ningun cobro
        if (var2 == 0) {
            alert("No hay claves seleccionadas, por favor selecciona al menos una clave para continuar");
            return (false);
        }

        <?php

        if ($que_hacer == 'cambio' || $que_hacer == 'cambiar' || $que_hacer == 'confirmar') {

            ?>

        // En caso de que no se especifique la fecha y hora de la cita
        if (document.Formulario1.fecha_cita.value == "") {
            alert("Especifica la fecha que deseas enviar al cliente para esta cita.");
            return (false);
        }
        if (document.Formulario1.t_hora.value == "") {
            alert("Especifica la hora que deseas enviar al cliente para esta cita.");
            return (false);
        }

        <?php

            if ($que_hacer == 'confirmar') {

                ?>

        // En caso de no haber seleccionado un asesor para la cita
        if (document.Formulario1.asesor.value == 0) {
            alert("Selecciona el asesor que atenderá al cliente en la cita.");
            document.Formulario1.asesor.focus();
            return (false);
        }

        <?php

            }
        } else {

            ?>

        if (document.Formulario1.que_hacer.value == "borrar") {
            if (!confirm("Se eliminará permanentemente la cita para las claves seleccionadas\n¿ Deseas continuar ?")) {
                return (false);
            }
        }
        <?php

        }

?>

        return (true);
    }

</script>

<div align="center">
    <center>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#000080" width="770" id="AutoNumber1" bgcolor="#E6E6FF">
            <tr>
                <td width="100%">
                    <p align="center"><b>
                            <font face="Arial">Detalles de la cita</font>
                        </b></p>
                    <form method="<?php if ($que_hacer == 'confirmar' || $que_hacer == 'cambio' || $que_hacer == 'cambiar') {
                        echo 'POST';
                    } else {
                        echo 'GET';
                    } ?>" action="<?php echo $PHP_SELF; ?>" onsubmit="return verifica(this);" name="Formulario1">
                        <table border="1" width="100%" id="table1" cellpadding="5" bgcolor="#FFFFFF" style="border-collapse: collapse">
                            <tr>
                                <td width="100%" align="left" height="25" bgcolor="#000080">
                                    <font color="#FFFFFF" size="2" face="Arial"><b>Datos del prospecto:</b></font>
                                </td>
                            </tr>
                            <tr>
                                <td width="100%" align="left" height="25" bgcolor="#FFFFFF">
                                    <table border="0" width="100%" id="table4" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td valign="top">
                                                <table border="0" width="100%" id="table7" cellspacing="0" bgcolor="#F0F0F0">
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Nombre completo:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td width="51%"><b>
                                                                <font size="2" face="Arial"><?php echo "$nombre $apellidos"; ?></font>
                                                            </b></td>
                                                        <td align="center" valign="top" rowspan="2" width="22%">
                                                            <?php

                                            $consulta1 = mysqlQuery("SELECT id FROM contactos WHERE (contrato='$config[contrato]' && nombre='$nombre' && apellidos='$apellidos' && telefono='$telefono' && email='$email')", $db_link);

if (mysql_num_rows($consulta1) == 0) {
    echo '<font size="2" face="Arial"><a href="contactos.php?op=Nuevo&nombre=' . urlencode($nombre) . '&apellidos=' . urlencode($apellidos) . '&email=' . urlencode($email) . '&telefono=' . urlencode($telefono) . '&celular=' . urlencode($celular) . '&nextel_tel=' . urlencode($nextel_tel) . '&nextel_radio=' . urlencode($nextel_radio) . '&ocupacion=' . urlencode($ocupacion) . '&empresa=' . urlencode($empresa) . '&ciudad=' . urlencode($ciudad) . '&estado=' . urlencode($estado) . '&pais=' . urlencode($pais) . '&fuera=Si&id=&tipo=Clientes">Agregar a contactos</a></font>';
    $cnt = 'No';
} else {
    $contacto = mysql_fetch_array($consulta1);
    echo "<font size=\"2\" face=\"Arial\"><a href=\"javascript:abre('contactos.php?contacto=$contacto[id]&tiempo=" . time() . "&op=Ver');\">Ver datos del contacto</a></font>";
    $cnt = 'Si';
}

?>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                    if ($telefono != '') {
                                                        ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Teléfono:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td width="51%"><b>
                                                                <font size="2" face="Arial"><?php echo $telefono; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
                                                    }
if ($celular != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Celular:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php echo $celular; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($nextel_tel != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Tel. Nextel:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php echo $nextel_tel; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($nextel_radio != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Radio Nextel:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php echo $nextel_radio; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($email != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Correo electrónico:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php
                                                if ($tipo_respuesta == 'mail') {
                                                    echo "<A HREF=\"mailto:$email\">$email</A>";
                                                } else {
                                                    echo $email;
                                                }
    ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($ocupacion != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Ocupación:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php echo $ocupacion; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($empresa != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Empresa:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php echo $empresa; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($ciudad != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Ciudad:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php echo $ciudad; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($estado != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Estado:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php echo $estado; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($pais != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">País:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2"><b>
                                                                <font size="2" face="Arial"><?php echo $pais; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php
}
if ($cita_por != '') {
    ?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Cita hecha por:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2">
                                                            <font face="Arial" size="2"><b><?php echo $cita_por; ?></b></font>
                                                        </td>
                                                    </tr>
                                                    <?php
}
?>
                                                    <tr>
                                                        <td width="27%" align="right">
                                                            <font size="2" face="Arial">Cuando quiere la cita:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td colspan="2">
                                                            <font face="Arial"><b><?php echo cm_formato_fecha($fecha_hora_solicitada, 3); ?></b></font>
                                                        </td>
                                                    </tr>
                                                    <?php

// En caso de haber recibido copia un asesor
if ($cc_asesor > 0) {
    $consulta2 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (id='$cc_asesor')", $db_link);
    if (mysql_num_rows($consulta2) == 1) {
        $asesor = mysql_fetch_array($consulta2);

        ?>
                                                    <tr>
                                                        <td align="right">
                                                            <font size="2" face="Arial">Con copia a:&nbsp;&nbsp;&nbsp;&nbsp;
                                                            </font>
                                                        </td>
                                                        <td><b>
                                                                <font size="2" face="Arial"><?php echo "$asesor[nombre] $asesor[apellidos]"; ?></font>
                                                            </b></td>
                                                    </tr>
                                                    <?php

    }
}	// if

?>
                                                    <tr>
                                                        <td bgcolor="#FFFFFF" colspan="3">
                                                            <?php

        if ($fecha_hora_cita != '0000-00-00 00:00:00' && $fecha_hora_cita != $fecha_hora_solicitada) {
            if ($confirmo_cliente == 'Si') {
                echo '<font face="Arial" size="2">El cliente YA CONFIRMÓ para el día: <b>' . cm_formato_fecha($fecha_hora_cita, 3) . '' . '<br><i><font color="#339900">FALTA QUE CONFIRMES LA CITA para indicarle donde será la cita y quién lo atenderá.</font></i></b></font>';
            } else {
                echo '<font face="Arial" size="2">Solicité al cliente cambio en la cita al día: <b>' . cm_formato_fecha($fecha_hora_cita, 3) . '</b></font>';
            }
        }

?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td bgcolor="#FFFFFF" colspan="3">

                                                            <?php

// Si es la pantalla de confirmación despliego los datos para confirmar
if ($que_hacer == 'confirmar') {

    ?>
                                                            <script language="Javascript1.2">
                                                                <!-- // load htmlarea
                                                                _editor_url = "jsc/"; // URL to htmlarea files
                                                                var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
                                                                if (navigator.userAgent.indexOf('Mac') >= 0) {
                                                                    win_ie_ver = 0;
                                                                }
                                                                if (navigator.userAgent.indexOf('Windows CE') >= 0) {
                                                                    win_ie_ver = 0;
                                                                }
                                                                if (navigator.userAgent.indexOf('Opera') >= 0) {
                                                                    win_ie_ver = 0;
                                                                }
                                                                if (win_ie_ver >= 5.5) {
                                                                    document.write('<scr' + 'ipt src="' + _editor_url + 'editor.js"');
                                                                    document.write(' language="Javascript1.2"></scr' + 'ipt>');
                                                                } else {
                                                                    document.write('<scr' + 'ipt>function editor_generate() { return false; }</scr' + 'ipt>');
                                                                }
                                                                // 
                                                                -->
                                                            </script>

                                                            <script language="JavaScript" src="calendario/javascripts.js"></script>
                                                            &nbsp;<br>
                                                            <center>
                                                                <font face="Arial" size="2">Al confirmar la cita el cliente y tu quedan de acuerdo en la fecha y hora de la misma.</font>
                                                            </center>
                                                            <table border="0" cellpadding="2" style="border-collapse: collapse" width="100%" id="table9" bgcolor="#FFFFFF" cellspacing="2">
                                                                <tr>
                                                                    <td width="17%" align="right" valign="top" bgcolor="#E1E1E1">
                                                                        <font face="Arial" size="2" color="#000000">
                                                                            * Fecha y hora de la cita:&nbsp;&nbsp; </font>
                                                                    </td>
                                                                    <td width="80%" align="left" valign="top" bgcolor="#E1E1E1">
                                                                        <font size="2" face="Arial" color="#000000">
                                                                            <?php

                        // Armo los campos de fecha y hora
                        if ($fecha_hora_cita != '0000-00-00 00:00:00') {
                            $fecha_a_tratar = $fecha_hora_cita;
                        } else {
                            $fecha_a_tratar = $fecha_hora_solicitada;
                        }

    $t_hora = substr($fecha_a_tratar, 11, 2);
    $t_minuto = substr($fecha_a_tratar, 14, 2);

    // Armo las cadenas para los campos de hora y minuto
    $cad_t_hora = '';
    $cad_t_minuto = '';
    for ($i = 8; $i <= 20; $i++) {
        if ($i < 10) {
            $j = "0$i";
        } else {
            $j = $i;
        }
        if ($t_hora == $i) {
            $cad_t_hora .= "<option value=\"$i\" selected>$j</option>\n";
        } else {
            $cad_t_hora .= "<option value=\"$i\">$j</option>\n";
        }
    }

    for ($i = 0; $i <= 59; $i++) {
        if ($i < 10) {
            $j = "0$i";
        } else {
            $j = $i;
        }
        if ($t_minuto == $i) {
            $cad_t_minuto .= "<option value=\"$i\" selected>$j</option>\n";
        } else {
            $cad_t_minuto .= "<option value=\"$i\">$j</option>\n";
        }
    }

    $cad_t_fecha = cm_formato_fecha(substr($fecha_a_tratar, 0, 10), 2, '/');

    ?>
                                                                            <INPUT name="fecha_cita" size="20" onfocus="this.blur(this.form);" value="<?php echo $cad_t_fecha; ?>"><input type=button value="Seleccionar fecha" onclick="muestraCalendario('','Formulario1','fecha_cita')"><br>
                                                                        </font>
                                                                        <font face="Arial">
                                                                            <select size="1" name="t_hora">
                                                                                <?php echo $cad_t_hora; ?>
                                                                            </select>
                                                                        </font><b>
                                                                            <font size="2" face="Arial">:</font>
                                                                        </b>
                                                                        <font face="Arial">
                                                                            <select size="1" name="t_minuto">
                                                                                <?php echo $cad_t_minuto; ?>
                                                                            </select>
                                                                            <font size="2" color="#000000">hrs</font>
                                                                        </font>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="17%" align="right" valign="top" bgcolor="#E1E1E1">
                                                                        <font size="2" face="Arial">Lugar de la cita:&nbsp;&nbsp; </font>
                                                                    </td>
                                                                    <td width="80%" align="left" valign="top" bgcolor="#E1E1E1">
                                                                        <?php

                                                                            // Armo las sucursales
                                                                            $cad_suc01 = '';
    $cad_suc02 = '';
    $consulta1 = mysqlQuery("SELECT * FROM sucursales WHERE (contrato='$config[contrato]')", $db_link);
    while ($sucursal = mysql_fetch_array($consulta1)) {

        $cad_suc02 .= "<option value=\"$sucursal[id]\">$sucursal[nombre]</option>\n";

        $temp = "$sucursal[calle_numero]<br>Col. $sucursal[colonia], C.P. $sucursal[codigo_postal]<br>$sucursal[ciudad], $sucursal[estado], $sucursal[pais]<br>Tel.: $sucursal[telefono]<br>---------<br>$sucursal[como_llegar]";
        $temp = strtr($temp, array("'" => '´'));

        $cad_suc01 .= "
	}else if (valor.value == $sucursal[id]) {
		document.getElementById('lugar').innerHTML = '<font size=\"2\" face=\"Arial\">$temp</font>';
			";
    }

    ?>
                                                                        <script language="javascript">
                                                                            <!-- //
                                                                            function checa_lugar(valor) {
                                                                                if (valor.value == -1) {
                                                                                    document.getElementById('lugar').innerHTML = '<font size="2" face="Arial">Especifica el lugar donde te quedarás de ver con el cliente lo más claro posible, así como tu vestimenta y el auto en que llegarás al lugar de la cita.</font><br><textarea rows="3" name="como_llegar" cols="50"></textarea>';
                                                                                    document.Formulario1.como_llegar.focus();
                                                                                } else if (valor.value == 0) {
                                                                                    document.getElementById('lugar').innerHTML = '<font size="2" face="Arial"><?php
                                                                                            $temp = "$cliente[calle_numero]<br>Col. $cliente[colonia], C.P. $cliente[codigo_postal]<br>$cliente[ciudad], $cliente[estado], $cliente[pais]<br>Tel.: $cliente[telefono]<br>---------<br>$cliente[como_llegar]";
    $temp = strtr($temp, array("'" => '´'));
    echo $temp;
    ?></font>';
                                                                                    <?php echo $cad_suc01; ?>

                                                                                }
                                                                            }
                                                                            // 
                                                                            -->
                                                                        </script>
                                                                        <font face="Arial">
                                                                            <select size="1" name="lugar_cita" onchange="checa_lugar(this);">
                                                                                <option value="0">Oficina matriz</option>
                                                                                <?php echo $cad_suc02; ?>
                                                                                <option value="-1">Fuera de oficinas</option>
                                                                            </select>
                                                                        </font>
                                                                        <div id="lugar">
                                                                            <font size="2" face="Arial"><?php echo $temp; ?></font>
                                                                            <div>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="17%" align="right" valign="top" bgcolor="#E1E1E1">
                                                                        <font face="Arial" size="2">Asesor que atenderá al cliente:&nbsp;&nbsp;&nbsp;</font>
                                                                    </td>
                                                                    <td width="80%" align="left" valign="top" bgcolor="#E1E1E1">
                                                                        <?php

                                                                            if ($cookie_asesor > 0) {
                                                                                $consulta1 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]' && id='$cookie_asesor')", $db_link);
                                                                                $asesor = mysql_fetch_array($consulta1);

                                                                                ?>
                                                                        <INPUT TYPE="hidden" name="asesor" value="<?php echo $cookie_asesor ?>"><B><?php echo "$asesor[nombre] $asesor[apellidos]"; ?></B>
                                                                        <?php

                                                                            } else {

                                                                                ?>
                                                                        <select size="1" name="asesor">
                                                                            <option value="0">seleccione un asesor ...</option>
                                                                            <?php

                                                                                        $consulta1 = mysqlQuery("SELECT id, nombre, apellidos FROM asesores WHERE (contrato = '$config[contrato]') ORDER BY nombre, apellidos", $db_link);

                                                                                while ($asesor = mysql_fetch_array($consulta1)) {
                                                                                    echo "			<option value=\"$asesor[id]\">$asesor[nombre] $asesor[apellidos]</option>\n";
                                                                                }

                                                                                ?>
                                                                        </select>
                                                                        <?php

                                                                            }

    ?>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="17%" align="right" valign="top" bgcolor="#E1E1E1">
                                                                        <font face="Arial" size="2" color="#000000">
                                                                            Comentario:&nbsp;&nbsp; <br>
                                                                            (opcional)&nbsp;&nbsp;&nbsp; </font>
                                                                    </td>
                                                                    <td width="80%" align="left" valign="top" bgcolor="#E1E1E1">
                                                                        <textarea rows="8" name="respuesta" cols="54"></textarea>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="17%" align="right" valign="top" bgcolor="#E1E1E1">
                                                                        &nbsp;</td>
                                                                    <td width="80%" align="left" valign="top" bgcolor="#E1E1E1">
                                                                        <input type="submit" value="Confirmar cita">
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <script language="javascript1.2">
                                                                <!-- //
                                                                editor_generate('respuesta');
                                                                // 
                                                                -->
                                                            </script>

                                                            <?php

                                                                /////
                                                                // En caso de solicitar cambiar la fecha u hora de la cita al cliente
} elseif ($que_hacer == 'cambio' || $que_hacer == 'cambiar') {

    ?>

                                                            <script language="JavaScript" src="calendario/javascripts.js"></script>
                                                            &nbsp;<br>
                                                            <center>
                                                                <font face="Arial" size="2"><?php

                                        if ($que_hacer == 'cambiar') {
                                            echo 'Especifica la fecha y hora para realizar el cambio en esta cita.';
                                            echo '<INPUT TYPE="hidden" name="paso" value="cambiar">';
                                        } else {
                                            echo 'Espefica la fecha y hora que deseas solicitar al cliente para esta cita.';
                                        }

    ?></font>
                                                            </center>
                                                            <table border="0" cellpadding="2" style="border-collapse: collapse" width="100%" id="table9" bgcolor="#FFFFFF" cellspacing="2">
                                                                <tr>
                                                                    <td width="17%" align="right" valign="top" bgcolor="#E1E1E1">
                                                                        <font face="Arial" size="2" color="#000000">
                                                                            * Fecha y hora de la cita:&nbsp;&nbsp; </font>
                                                                    </td>
                                                                    <td width="80%" align="left" valign="top" bgcolor="#E1E1E1">
                                                                        <font size="2" face="Arial" color="#000000">
                                                                            <INPUT name="fecha_cita" size="20" onfocus="this.blur(this.form);" value=""><input type=button value="Seleccionar fecha" onclick="muestraCalendario('','Formulario1','fecha_cita')"><br>
                                                                        </font>
                                                                        <font face="Arial">
                                                                            <select size="1" name="t_hora">
                                                                                <option value="">--</option>
                                                                                <option value="8">08</option>
                                                                                <option value="9">09</option>
                                                                                <option value="10">10</option>
                                                                                <option value="11">11</option>
                                                                                <option value="12">12</option>
                                                                                <option value="13">13</option>
                                                                                <option value="14">14</option>
                                                                                <option value="15">15</option>
                                                                                <option value="16">16</option>
                                                                                <option value="17">17</option>
                                                                                <option value="18">18</option>
                                                                                <option value="19">19</option>
                                                                                <option value="20">20</option>
                                                                            </select>
                                                                        </font><b>
                                                                            <font size="2" face="Arial">:</font>
                                                                        </b>
                                                                        <font face="Arial">
                                                                            <select size="1" name="t_minuto">
                                                                                <option value="0">00</option>
                                                                                <option value="1">01</option>
                                                                                <option value="2">02</option>
                                                                                <option value="3">03</option>
                                                                                <option value="4">04</option>
                                                                                <option value="5">05</option>
                                                                                <option value="6">06</option>
                                                                                <option value="7">07</option>
                                                                                <option value="8">08</option>
                                                                                <option value="9">09</option>
                                                                                <option value="10">10</option>
                                                                                <option value="11">11</option>
                                                                                <option value="12">12</option>
                                                                                <option value="13">13</option>
                                                                                <option value="14">14</option>
                                                                                <option value="15">15</option>
                                                                                <option value="16">16</option>
                                                                                <option value="17">17</option>
                                                                                <option value="18">18</option>
                                                                                <option value="19">19</option>
                                                                                <option value="20">20</option>
                                                                                <option value="21">21</option>
                                                                                <option value="22">22</option>
                                                                                <option value="23">23</option>
                                                                                <option value="24">24</option>
                                                                                <option value="25">25</option>
                                                                                <option value="26">26</option>
                                                                                <option value="27">27</option>
                                                                                <option value="28">28</option>
                                                                                <option value="29">29</option>
                                                                                <option value="30">30</option>
                                                                                <option value="31">31</option>
                                                                                <option value="32">32</option>
                                                                                <option value="33">33</option>
                                                                                <option value="34">34</option>
                                                                                <option value="35">35</option>
                                                                                <option value="36">36</option>
                                                                                <option value="37">37</option>
                                                                                <option value="38">38</option>
                                                                                <option value="39">39</option>
                                                                                <option value="40">40</option>
                                                                                <option value="41">41</option>
                                                                                <option value="42">42</option>
                                                                                <option value="43">43</option>
                                                                                <option value="44">44</option>
                                                                                <option value="45">45</option>
                                                                                <option value="46">46</option>
                                                                                <option value="47">47</option>
                                                                                <option value="48">48</option>
                                                                                <option value="49">49</option>
                                                                                <option value="50">50</option>
                                                                                <option value="51">51</option>
                                                                                <option value="52">52</option>
                                                                                <option value="53">53</option>
                                                                                <option value="54">54</option>
                                                                                <option value="55">55</option>
                                                                                <option value="56">56</option>
                                                                                <option value="57">57</option>
                                                                                <option value="58">58</option>
                                                                                <option value="59">59</option>
                                                                            </select>
                                                                            <font size="2" color="#000000">hrs</font>
                                                                        </font>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="17%" align="right" valign="top" bgcolor="#E1E1E1">
                                                                        &nbsp;</td>
                                                                    <td width="80%" align="left" valign="top" bgcolor="#E1E1E1">
                                                                        <input type="submit" value="Realizar cambio">
                                                                    </td>
                                                                </tr>
                                                            </table>

                                                            <?php


                                                                /////
                                                                // En caso de presentar la selección de opciones a una cita no contestada ni confirmada
} elseif ($fecha_hora_solicitada != $fecha_hora_cita && $cancelada != 'Si') {

    if ($tipo_respuesta == 'mail') {
        echo '<p align="center"><font face="Arial" size="3" color="#CC6600"><b>&nbsp;<br>';
        echo "Se respondió a esta cita por medio de un e-mail externo";
        echo "</b></font></p>";
    }

    ?>

                                                            <table border="0" width="100%" id="table11" cellspacing="0" cellpadding="7">
                                                                <tr>
                                                                    <td>
                                                                        <p>
                                                                            <font size="2" face="Arial">&nbsp; <br>
                                                                                Que deseas hacer:</font><br>
                                                                            <select size="1" name="que_hacer">
                                                                                <option value="confirmar">CONFIRMAR CITA DE LA(S) CLAVE(S) SELECCIONADA(S)</option>
                                                                                <option value="cambio">Solicitar al cliente cambio en la fecha u hora en la cita de la(s) clave(s) seleccionada(s)</option>
                                                                                <?php

                            if ($tipo_respuesta != 'mail') {
                                echo '							<option value="mail">Enviar un e-mail y registrar como contestada la cita de la(s) clave(s) seleccionada(s)</option>';
                            }

    ?>
                                                                                <option value="borrar">Eliminar solicitud de cita de la(s) clave(s) seleccionada(s) sin contestar</option>
                                                                            </select><br>
                                                                            <input type="submit" value="Continuar -&gt;">
                                                                        </p>

                                                                    </td>
                                                                </tr>
                                                            </table>

                                                            <?php

} else {

    if ($cancelada == 'Si') {
        echo '<p align="center"><font face="Arial" size="3" color="#CC6600"><b>&nbsp;<br>';
        echo "C I T A<br>C A N C E L A D A";
        echo "</b></font></p>";
    } elseif ($tipo_respuesta == 'sistema' && $fecha_hora_cita == $fecha_hora_solicitada) {

        $consulta1 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]' && id='$asesor')", $db_link);
        $asesor = mysql_fetch_array($consulta1);

        // En caso de haber hecho cita en la matriz
        if ($lugar_cita == 0) {
            $como_llegar = 'Oficina principal';

            // En caso de haberla hecho en sucursales
        } elseif ($lugar_cita > 0) {
            $consulta1 = mysqlQuery("SELECT nombre FROM sucursales WHERE (contrato='$config[contrato]' && id='$lugar_cita')", $db_link);
            $como_llegar = mysql_fetch_array($consulta1);
            $como_llegar = $como_llegar[nombre];
        }

        ?>

                                                            <table border="0" width="100%" cellspacing="0" bgcolor="#FFEB9B">
                                                                <tr>
                                                                    <td width="149" align="right">
                                                                        <font size="2" face="Arial">Asesor asignado:&nbsp;&nbsp;
                                                                        </font>
                                                                    </td>
                                                                    <td><b>
                                                                            <font size="2" face="Arial"><?php echo "$asesor[nombre] $asesor[apellidos]"; ?></font>
                                                                        </b></td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="149" align="right">
                                                                        <font size="2" face="Arial">Lugar de la cita:&nbsp;&nbsp;
                                                                        </font>
                                                                    </td>
                                                                    <td><b>
                                                                            <font size="2" face="Arial"><?php echo $como_llegar; ?></font>
                                                                        </b></td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="149" align="right" valign="top">
                                                                        <font face="Arial" size="2">Comentario enviado:&nbsp;&nbsp;
                                                                        </font>
                                                                    </td>
                                                                    <td>
                                                                        <font size="2" face="Arial"><?php echo $respuesta; ?></font>
                                                                    </td>
                                                                </tr>
                                                            </table>

                                                            <p align="center">
                                                                <font face="Arial" size="3" color="#CC6600"><b>&nbsp;<br>C I T A &nbsp;&nbsp;&nbsp; C O N F I R M A D A</b></font>
                                                            </p>
                                                            <table border="0" width="100%" id="table11" cellspacing="0" cellpadding="7">
                                                                <tr>
                                                                    <td>
                                                                        <p>
                                                                            <font size="2" face="Arial">Que deseas hacer:</font><br>
                                                                            <select size="1" name="que_hacer">
                                                                                <option value="cambiar">Cambiar fecha y/o hora en la cita de la(s) clave(s) seleccionada(s)</option>
                                                                                <option value="cancelar">Cancelar cita de la(s) clave(s) seleccionada(s)</option>
                                                                            </select><br>
                                                                            <input type="submit" value="Continuar -&gt;">
                                                                        </p>

                                                                    </td>
                                                                </tr>
                                                            </table>

                                                            <?php

    }
}

?>

                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                            <td width="182" align="center" valign="top">
                                                <table border="1" width="100%" id="table8" style="border-collapse: collapse" cellpadding="5" cellspacing="0" bordercolor="#000000">
                                                    <tr>
                                                        <td align="center" bgcolor="#808080"><b>
                                                                <font size="2" face="Arial" color="#FFFFFF">Claves solicitadas</font>
                                                            </b></td>
                                                    </tr>
                                                    <?php echo $propiedades; ?>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                        <INPUT TYPE="hidden" name="email" value="<?php echo $email; ?>">
                    </form>
                </td>
            </tr>
        </table>
    </center>
</div>
