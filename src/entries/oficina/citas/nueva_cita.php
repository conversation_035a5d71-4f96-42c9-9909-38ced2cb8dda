<?php

$nombre_campo = 'visitante';
$mostrar = 'cliente';
include_once('VerificadorContactoJS.php');

?>
<script src="/jsc/ajax.js"></script>
<script>
    function termina() {

        if (!verificaContacto(document.Formulario1)) {
            return (false);
        } // if

        if (document.Formulario1.visitante.value == -1) {
            alert("Selecciona un cliente o captura uno nuevo.");
            document.Formulario1.visitante.focus();
            return false;
        }

        if (document.Formulario1.fecha_cita.value == '') {
            alert("Selecciona una fecha para la cita");
            document.Formulario1.fecha_cita.focus();
            return false;
        }

        if (document.Formulario1.t_hora.value == '') {
            alert("Selecciona una hora para la cita");
            document.Formulario1.t_hora.focus();
            return false;
        }

        if (document.Formulario1.asesor.value == '0') {
            alert("Selecciona el asesor que atenderá esta cita");
            document.Formulario1.asesor.focus();
            return false;
        }

        // Verifico que se seleccione al menos una propiedad a visitar
        var1 = 0;
        var2 = 0;
        while (document.Formulario1.elements[var1].name != 'paso') {
            if (document.Formulario1.elements[var1].name == 'claves_sistema[]' && document.Formulario1.elements[var1].checked) {
                var2 = 1;
            }
            var1++;
        }
        if (var2 == 0) {
            alert("No hay claves seleccionadas, por favor selecciona al menos una clave para continuar");
            return (false);
        }


        // Al llegar aquí es que todo es válido
        document.getElementById('botones').innerHTML = 'Registrando cita';
        return (true);
    }
</script>

<form method="POST" action="<?php echo $PHP_SELF; ?>" name="Formulario1" onsubmit="return termina()">

    <div align="center">
        <center>
            <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#000080" width="770" id="table1" bgcolor="#E6E6FF">
                <tr>
                    <td width="100%">
                        <p align="center">
                            <font face="Arial"><b>Concertando cita para llevar cliente</b></font>
                        </p>
                        <div align="center">
                            <table border="1" width="100%" cellspacing="4" style="border-collapse: collapse" cellpadding="4" id="table30" bordercolor="#000080" bgcolor="#FFFFFF">
                                <tr>
                                    <td align="left" width="100%" valign="top">
                                        <table border="0" width="100%" id="table31" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td><b>
                                                        <font size="2" face="Arial">Información requerida:</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <script language="JavaScript" src="calendario/javascripts.js"></script>
                                                    <table border="0" width="100%" cellspacing="0" cellpadding="3">

                                                        <tr bgcolor="#F0F0F0">
                                                            <td width="249" align="right" valign="top">
                                                                <font size="2" face="Arial">Cliente:</font>
                                                            </td>
                                                            <td>
                                                                <?php

                                                                    $databases->rec_contactos($nombre_campo, -1, $mostrar);

?>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td width="249" align="right" valign="top">
                                                                <font face="Arial" size="2" color="#000000">Fecha y hora de la cita:&nbsp;&nbsp; </font>
                                                            </td>
                                                            <td>
                                                                <font size="2" face="Arial" color="#000000">
                                                                    <INPUT name="fecha_cita" size="20" onfocus="this.blur(this.form);" value="" class="form_peque"><input type=button value="Seleccionar fecha" onclick="muestraCalendario('','Formulario1','fecha_cita')" class="form_peque"><br>
                                                                </font>
                                                                <font face="Arial">
                                                                    <select size="1" name="t_hora" class="form_peque">
                                                                        <option value="">--</option>
                                                                        <?php

        for ($i = 7; $i <= 21; $i++) {
            if ($i < 10) {
                $tmp = "0$i";
            } else {
                $tmp = $i;
            }
            echo "<option value=\"$i\">$tmp</option>\n";
        }	// for

?>
                                                                    </select>
                                                                </font><b>
                                                                    <font size="2" face="Arial">:</font>
                                                                </b>
                                                                <font face="Arial">
                                                                    <select size="1" name="t_minuto" class="form_peque">
                                                                        <?php

for ($i = 0; $i <= 59; $i++) {
    if ($i < 10) {
        $tmp = "0$i";
    } else {
        $tmp = $i;
    }
    echo "<option value=\"$i\">$tmp</option>\n";
}	// for

?>
                                                                    </select>
                                                                    <font size="2" color="#000000">hrs</font>
                                                                </font>
                                                            </td>
                                                        </tr>

                                                        <tr bgcolor="#F0F0F0">
                                                            <td width="249" align="right" valign="top">
                                                                <font size="2" face="Arial">Lugar de la cita:&nbsp;&nbsp; </font>
                                                            </td>
                                                            <td>
                                                                <?php

                                                                // Armo las sucursales
                                                                $cad_suc01 = '';
$cad_suc02 = '';
$consulta1 = mysqlQuery("SELECT * FROM sucursales WHERE (contrato='$config[contrato]')", $db_link);
while ($sucursal = mysql_fetch_array($consulta1)) {

    $cad_suc02 .= "<option value=\"$sucursal[id]\">$sucursal[nombre]</option>\n";

    $temp = "$sucursal[calle_numero]<br>Col. $sucursal[colonia], C.P. $sucursal[codigo_postal]<br>$sucursal[ciudad], $sucursal[estado], $sucursal[pais]<br>Tel.: $sucursal[telefono]<br>---------<br>$sucursal[como_llegar]";
    $temp = strtr($temp, array("'" => '´'));

    $cad_suc01 .= "
	}else if (valor.value == $sucursal[id]) {
		document.getElementById('lugar').innerHTML = '<span class=\"observaciones\">$temp</span>';
			";
}

?>
                                                                <script language="javascript">
                                                                    <!-- //
                                                                    function checa_lugar(valor) {
                                                                        if (valor.value == -1) {
                                                                            document.getElementById('lugar').innerHTML = '<span class="observaciones">Especifica el lugar donde te quedarás de ver con el cliente lo más claro posible, así como tu vestimenta y el auto en que llegarás al lugar de la cita.</span><br><textarea rows="3" name="como_llegar" cols="50"></textarea>';
                                                                            document.Formulario1.como_llegar.focus();
                                                                        } else if (valor.value == 0) {
                                                                            document.getElementById('lugar').innerHTML = '<span class="observaciones"><?php
                                                                                        $temp = "$cliente[calle_numero]<br>Col. $cliente[colonia], C.P. $cliente[codigo_postal]<br>$cliente[ciudad], $cliente[estado], $cliente[pais]<br>Tel.: $cliente[telefono]<br>---------<br>$cliente[como_llegar]";
$temp = strtr($temp, array("'" => '´'));
echo $temp;
?></span>';
                                                                            <?php echo $cad_suc01; ?>

                                                                        }
                                                                    }
                                                                    // 
                                                                    -->
                                                                </script>
                                                                <font face="Arial">
                                                                    <select size="1" name="lugar_cita" onchange="checa_lugar(this);" class="form_peque">
                                                                        <option value="0">Oficina matriz</option>
                                                                        <?php echo $cad_suc02; ?>
                                                                        <option value="-1">Fuera de oficinas</option>
                                                                    </select>
                                                                </font>
                                                                <div id="lugar"><span class="observaciones"><?php echo $temp; ?></span>
                                                                    <div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="249" align="right">
                                                                <font face="Arial" size="2">Asesor que mostrará los inmuebles:</font>
                                                            </td>
                                                            <td>
                                                                <?php

                                                                if ($cookie_asesor > 0) {
                                                                    $consulta1 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]' && id='$cookie_asesor')", $db_link);
                                                                    $asesor = mysql_fetch_array($consulta1);

                                                                    ?>
                                                                <INPUT TYPE="hidden" name="asesor" value="<?php echo $cookie_asesor ?>"><B><?php echo "$asesor[nombre] $asesor[apellidos]"; ?></B>
                                                                <?php

                                                                } else {

                                                                    $databases->rec_asesores();
                                                                }

?>
                                                            </td>
                                                        </tr>
                                                        <tr bgcolor="#F0F0F0">
                                                            <td width="249" align="right">
                                                                <font size="2" face="Arial">Se hizo la cita por::</font>
                                                            </td>
                                                            <td>
                                                                <select size="1" name="cita_por" class="form_peque">
                                                                    <option>Teléfono</option>
                                                                    <option>Visita del cliente</option>
                                                                    <option>Internet</option>
                                                                </select>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="249" align="right">
                                                                <font size="2" face="Arial">Medio por el cual se enteró de nosotros:</font>
                                                            </td>
                                                            <td>
                                                                <input type="text" name="como_se_entero" size="30" maxlength="50" class="form_peque">
                                                            </td>
                                                        </tr>
                                                        <tr bgcolor="#F0F0F0">
                                                            <td width="249" align="right" valign="top">
                                                                <font size="2" face="Arial">Observaciones adicionales:</font><br>
                                                                <font size="1" face="Arial">(Control interno, no se envian al cliente)</font>
                                                            </td>
                                                            <td>
                                                                <textarea rows="3" name="observaciones" cols="50" class="form_peque"></textarea>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center">
                                        <b>
                                            <font size="2" face="Arial">Seleccione a continuación él o los inmuebles a visitar:</font>
                                        </b>
                                        <?php
                                        include_once('ListaTodosInmuebles_ajax.php');
?>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" class="t2">
                                        <div id="hListadoInmuebles"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" width="100%">
                                        <div id="botones">
                                            <font face="Arial" size="4"><B><input type="button" value="&lt;- Regresar" OnClick="window.history.go(-1)">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="submit" value="Continuar -&gt;"></B></font>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </td>
                </tr>
            </table>
        </center>
    </div>
    <INPUT TYPE="hidden" name="paso" value="7">
</form>
<script language="javascript">
    hListado('ListaTodosInmuebles.php?editar_props=No');
</script>
