<?php

for ($i = 0; $i < count($ids); $i++) {
    if ($i == 0) {
        $v1 = "id = $ids[0]";
    } else {
        $v1 .= " || id = " . $ids[$i];
    }
}

mysqlQuery("UPDATE citas SET tipo_respuesta='mail' WHERE (contrato='$config[contrato]' && ($v1))", $db_link);

?>

<script>
    window.open('mailto:<?php echo $email; ?>');
</script>
<p align="center">&nbsp;</p>
<p align="center"><b>
        <font size="2" face="Arial">Se ha registrado como respondida por e-mail a la cita.<br><br>E-mail del cliente: <A HREF="mailto:<?php echo $email; ?>"><?php echo $email; ?></A></font>
    </b></p>
<p align="center">&nbsp;</p>
