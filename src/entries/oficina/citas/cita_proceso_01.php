<?php

// Defino algunas variables a utilizar
$foto = '';
$s_citas = '';

/////
// Confirmo que las citas no hayan sido confirmadas con anterioridad
for ($i = 0; $i < count($ids); $i++) {
    if ($i == 0) {
        $v1 = "citas.id = $ids[0]";
    } else {
        $v1 .= " || citas.id = " . $ids[$i];
    }
}
$consulta1 = mysqlQuery("SELECT citas.id, citas.clave_sistema, citas.usuario, citas.nombre, citas.apellidos, citas.email, citas.fecha_hora_solicitada, citas.fecha_hora_cita, citas.sesion, propiedades.claveprop, propiedades.sucursal FROM citas, propiedades WHERE (citas.contrato='$config[contrato]' && ($v1) && citas.clave_sistema=propiedades.clave_sistema)", $db_link);

// Primero recupero las fotos de los inmuebles a mostrar
$ar_fotos = array();
while ($propiedad = mysql_fetch_array($consulta1)) {
    array_push($ar_fotos, $propiedad['clave_sistema']);
}
$ar_fotos = $images->primer_foto($ar_fotos, 'inmueble');
if (mysql_num_rows($consulta1) > 0) {
    mysql_data_seek($consulta1, 0);
}

while ($cita = mysql_fetch_array($consulta1)) {

    if ($s_citas == '') {
        $s_citas = $cita[id];
    } else {
        $s_citas .= ",$cita[id]";
    }


    if ($cita[fecha_hora_solicitada] == $cita[fecha_hora_cita]) {

        $hoy = strftime("%Y-%m-%d 00:00:00", time());
        if ($cita[fecha_hora_cita] >= $hoy) {
            $v1 = 'ver=hoy';
        } else {
            $v1 = 'ver=pasadas';
        }

        ?>
<p align="center">&nbsp;</p>
<p align="center"><b>
        <font size="2" face="Arial">La cita para alguna de estas propiedades ya fue confirmada anteriormente,<br>puedes checar las confirmaciones de citas <a href="<?php echo "$PHP_SELF?$v1&t=" . time(); ?>">aquí</a></font>
    </b></p>
<p align="center">&nbsp;</p>
<?php

                mysql_close($db_link);
        echo $plantilla[pie];
        exit;
    }

    // Datos del usuario cuando se toman desde un usuario registrado
    if ($cita[usuario] != '' && $v2 != 'Pollo') {
        @mysql_select_db(DB_PW);
        $consulta3 = mysqlQuery("SELECT nombre, apellidos, email FROM clientes WHERE (usuario='$cita[usuario]')", $db_link);
        $visitante = mysql_fetch_array($consulta3);
        @mysql_select_db(DB_SI);
        $cita[nombre] = $visitante[nombre];
        $cita[apellidos] = $visitante[apellidos];
        $cita[email] = $visitante[email];
    }

    if ($v2 != 'Pollo') {
        $nombre = $cita[nombre];
        $apellidos = $cita[apellidos];
        $email = $cita[email];
        $sesion = $cita[sesion];
        $fecha_hora_solicitada = $cita[fecha_hora_solicitada];
        $dsucursal = $cita[sucursal];
    }

    $v2 = 'Pollo';

    // Armo las fotos a enviar en el e-mail
    $foto .= "<tr><td align=\"center\"><font size=\"2\" face=\"Arial\"><A HREF=\"http://$config[dominio]/detallesnew.php?clave=$cita[clave_sistema]&idioma=esp\"><b>$cita[claveprop]</b><br>" . $ar_fotos[$cita['clave_sistema']]['img'] . "</A></font></td></tr>\n";
}
$s_citas = urlencode($s_citas);


/////
// Grabo la información de la cita en la base de datos

// Convierto la información mandada a la que necesito para grabar en la BD
$num_mes = array('enero' => '01', 'febrero' => '02', 'marzo' => '03', 'abril' => '04', 'mayo' => '05', 'junio' => '06', 'julio' => '07', 'agosto' => '08', 'septiembre' => '09', 'octubre' => '10', 'noviembre' => '11', 'diciembre' => '12');
$fecha = explode('/', $fecha_cita);
if ($fecha[0] < 10) {
    $fecha[0] = '0' . ($fecha[0] * 1);
}
if ($t_hora < 10) {
    $t_hora = '0' . ($t_hora * 1);
}
if ($t_minuto < 10) {
    $t_minuto = '0' . ($t_minuto * 1);
}
$fecha_hora_cita = $fecha[2] . "-" . $num_mes[$fecha[1]] . "-" . $fecha[0] . " $t_hora:$t_minuto:00";


/////
// En caso de hacer la confirmación al cliente
if (trim($lugar_cita) != '') {

    // Si es en oficina matriz
    if ($lugar_cita == 0) {
        $como_llegar = "$cliente[calle_numero]<br>Col. $cliente[colonia], C.P. $cliente[codigo_postal]<br>$cliente[ciudad], $cliente[estado], $cliente[pais]<br>Tel.: $cliente[telefono]<br>---------<br>$cliente[como_llegar]";
        $como_llegar = strtr($como_llegar, array('"' => '\"', "'" => "\'"));
        // Si es en alguna sucursal
    } elseif ($lugar_cita > 0) {
        $consulta1 = mysqlQuery("SELECT * FROM sucursales WHERE (contrato='$config[contrato]' && id='$lugar_cita')", $db_link);
        $sucursal = mysql_fetch_array($consulta1);
        $como_llegar = "$sucursal[calle_numero]<br>Col. $sucursal[colonia], C.P. $sucursal[codigo_postal]<br>$sucursal[ciudad], $sucursal[estado], $sucursal[pais]<br>Tel.: $sucursal[telefono]<br>---------<br>$sucursal[como_llegar]";
        $como_llegar = strtr($como_llegar, array('"' => '\"', "'" => "\'"));
    }

    $como_llegar = nl2br($como_llegar);

    // Grabo la información en la BD
    mysqlQuery("UPDATE citas SET fecha_hora_solicitada='$fecha_hora_cita', fecha_hora_cita='$fecha_hora_cita', lugar_cita='$lugar_cita', como_llegar='$como_llegar', respuesta='$respuesta', tipo_respuesta='sistema', asesor='$asesor' WHERE (contrato='$config[contrato]' && ($v1))", $db_link);

    $como_llegar = strtr($como_llegar, array('\"' => '"', "\'" => "'"));
    $respuesta = strtr($respuesta, array('\"' => '"', "\'" => "'"));

    // Recupero los datos del asesor
    $consulta1 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]' && id='$asesor')", $db_link);
    $asesor = mysql_fetch_array($consulta1);

    $pagina = 'envio_de_cita_cliente_2';
    $asunto = "CONFIRMACION DE CITA EN $cliente[empresa]";
    $tmp = 'Se ha enviado la confirmación de cita con éxito.';


    /////
    // En caso de solo solicitar cambio en la cita al cliente
} else {

    // Grabo la información en la BD
    mysqlQuery("UPDATE citas SET fecha_hora_cita='$fecha_hora_cita', confirmo_cliente='No' WHERE (contrato='$config[contrato]' && ($v1))", $db_link);

    $pagina = 'envio_de_cita_cliente_3';
    $asunto = "Solicitamos cambio de su cita en $cliente[empresa]";
    $tmp = 'Se ha enviado la solicitud de cambio en la cita al cliente.';
}


/////
// Mando por e-mail la confirmación de cita al cliente

$mensaje = cm_lee_archivo("$rutasi3/plantillas/$pagina.htm");
$sust = array(
    '$FOTO' => $foto,
    '$LUGAR_CITA' => $como_llegar,
    '$RESPUESTA' => $respuesta,
    '$ASESOR' => "$asesor[nombre] $asesor[apellidos]",
    '$NOMBRE_COMPLETO' => "$nombre $apellidos",
    '$FECHA_HORA_SOLICITADA' => cm_formato_fecha($fecha_hora_solicitada, 3),
    '$FECHA_HORA_CITA' => cm_formato_fecha($fecha_hora_cita, 3),
    '$SESION' => urlencode($sesion),
    '$CITA[ID]' => $s_citas,
);
$mensaje = strtr($mensaje, $sust);
$mensaje = cm_pone_datos_empresa($mensaje);

//para el envío en formato HTML
$headers = "MIME-Version: 1.0\n";
$headers .= "Content-type: text/html; charset=iso-8859-1\n";

//dirección del remitente
if ($dsucursal > 0) {
    $consulta1 = mysqlQuery("SELECT email FROM sucursales WHERE (contrato='$config[contrato]' && id='$dsucursal')", $db_link);
    $row = mysql_fetch_array($consulta1);
    if (trim($row[email]) != '') {
        $config[correo_ventas] = $row[email];
    }
}
if ($config[correo_ventas] == '') {
    $headers .= "From: $cliente[email] ($cliente[empresa])\n";
} else {
    $headers .= "From: $config[correo_ventas] ($cliente[empresa])\n";
}

// Envio el mensaje
$sust = array(
    "\n" => "\r\n"
);
$mensaje = strtr($mensaje, $sust);
cm_pmail($email, $asunto, $mensaje, $headers);
//echo "$headers<br><br>Para: $email<br><br>Asunto: $asunto<br><br>$mensaje";

?>
<p align="center">&nbsp;</p>
<p align="center"><b>
        <font size="2" face="Arial"><?php echo $tmp; ?></font>
    </b></p>
<p align="center">&nbsp;</p>
