<?php

// Tratamiento de las fechas
$meses = array('enero' => '01', 'febrero' => '02', 'marzo' => '03', 'abril' => '04', 'mayo' => '05', 'junio' => '06', 'julio' => '07', 'agosto' => '08', 'septiembre' => '09', 'octubre' => '10', 'noviembre' => '11', 'diciembre' => '12');
$fecha_cita = explode('/', $fecha_cita);
$fecha_cita = "$fecha_cita[2]-" . $meses[$fecha_cita[1]] . "-$fecha_cita[0] $t_hora:$t_minuto:00";
$hoy = strftime("%Y-%m-%d %H:%M:%S", time());
$foto = '';

// Registro el cliente en la BD si así lo solicita
if ($visitante == 0 && $registrar_contacto == 'Si' && (trim($c_nombre) != '' || trim($c_apellidos) != '' || trim($c_telefono) != '' || trim($c_email) != '')) {
    $consulta1 = mysqlQuery("SELECT id FROM contactos WHERE (contrato = '$config[contrato]' && nombre = '$c_nombre' && apellidos = '$c_apellidos' && telefono = '$c_telefono' && email = '$c_email')", $db_link);
    if (mysql_num_rows($consulta1) == 0) {
        $c_sexo = empty($c_sexo) ? 'hombre' : $c_sexo;
        mysqlQuery("INSERT INTO contactos (contrato, nombre, apellidos, sexo, telefono, email, tipo) VALUES ('$config[contrato]', '$c_nombre', '$c_apellidos', '$c_sexo', '$c_telefono', '$c_email', 'Clientes')", $db_link);
    }

    // Recupero los datos del cliente en caso de ya estar registrado en la lista de contactos
} elseif ($visitante > 0) {
    $consulta1 = mysqlQuery("SELECT nombre, apellidos, email, telefono, ocupacion, empresa, ciudad, estado, pais FROM contactos WHERE (contrato='$config[contrato]' && id='$visitante')", $db_link);
    $contacto = mysql_fetch_array($consulta1);
    $c_nombre = $contacto[nombre];
    $c_apellidos = $contacto[apellidos];
    $c_email = $contacto[email];
    $c_telefono = $contacto[telefono];
    $c_celular = $contacto[celular];
    $c_nextel_tel = $contacto[nextel_tel];
    $c_nextel_radio = $contacto[nextel_radio];
    $c_ocupacion = $contacto[ocupacion];
    $c_empresa = $contacto[empresa];
    $c_ciudad = $contacto[ciudad];
    $c_estado = $contacto[estado];
    $c_pais = $contacto[pais];
}

// Grabo en la BD la cita
$sesion = "$config[contrato]" . time();

// Si es en oficina matriz
if ($lugar_cita == 0) {
    $como_llegar = "$cliente[calle_numero]<br>Col. $cliente[colonia], C.P. $cliente[codigo_postal]<br>$cliente[ciudad], $cliente[estado], $cliente[pais]<br>Tel.: $cliente[telefono]<br>---------<br>$cliente[como_llegar]";
    $como_llegar = strtr($como_llegar, array('"' => '\"', "'" => "\'"));

    // Si es en alguna sucursal
} elseif ($lugar_cita > 0) {
    $consulta1 = mysqlQuery("SELECT * FROM sucursales WHERE (contrato='$config[contrato]' && id='$lugar_cita')", $db_link);
    $sucursal = mysql_fetch_array($consulta1);
    $como_llegar = "$sucursal[calle_numero]<br>Col. $sucursal[colonia], C.P. $sucursal[codigo_postal]<br>$sucursal[ciudad], $sucursal[estado], $sucursal[pais]<br>Tel.: $sucursal[telefono]<br>---------<br>$sucursal[como_llegar]";
    $como_llegar = strtr($como_llegar, array('"' => '\"', "'" => "\'"));
}
if ($lugar_cita >= 0) {
    $cl_bd = '';
} else {
    $cl_bd = nl2br($como_llegar);
}
$como_llegar = nl2br($como_llegar);

// Primero recupero las fotos de los inmuebles a mostrar
$ar_fotos = array();
foreach ($claves_sistema as $clave_sistema) {
    array_push($ar_fotos, $clave_sistema);
}
$ar_fotos = $images->primer_foto($ar_fotos, 'inmueble');

foreach ($claves_sistema as $clave_sistema) {
    mysqlQuery("INSERT INTO citas (contrato, clave_sistema, nombre, apellidos, email, telefono, celular, nextel_tel, nextel_radio, ocupacion, empresa, ciudad, estado, pais, observaciones, fecha_hora_solicitada, fecha_hora_cita, solicitud, sesion, tipo_respuesta, lugar_cita, como_llegar, asesor, como_se_entero, cita_por) VALUES ('$config[contrato]', '$clave_sistema', '$c_nombre', '$c_apellidos', '$c_email', '$c_telefono', '$c_celular', '$c_nextel_tel', '$c_nextel_radio', '$c_ocupacion', '$c_empresa', '$c_ciudad', '$c_estado', '$c_pais', '$observaciones', '$fecha_cita', '$fecha_cita', '$hoy', '$sesion', 'sistema', '$lugar_cita', '$cl_bd', '$asesor', '$como_se_entero', '$cita_por')", $db_link);

    $consulta1 = mysqlQuery("SELECT claveprop, sucursal FROM propiedades WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema')", $db_link);
    $propiedad = mysql_fetch_array($consulta1);

    // Armo las fotos a enviar en el e-mail
    $foto .= "<tr><td align=\"center\"><font size=\"2\" face=\"Arial\"><A HREF=\"http://$config[dominio]/detallesnew.php?clave=$clave_sistema&idioma=esp\"><b>$propiedad[claveprop]</b><br>" . $ar_fotos[$clave_sistema]['img'] . "</A></font></td></tr>\n";
}
$dsucursal = $propiedad[sucursal];
$como_llegar = strtr($como_llegar, array('\"' => '"', "\'" => "'"));

$consulta1 = mysqlQuery("SELECT id, fecha_hora_cita FROM citas WHERE (contrato='$config[contrato]' && nombre='$c_nombre' && apellidos='$c_apellidos' && sesion='$sesion')", $db_link);
$cita = mysql_fetch_array($consulta1);

// Si está definida la cuenta de correo del cliente le envio la confirmación por e-mail
if (trim($c_email) != '') {

    // Recupero los datos del asesor
    $consulta1 = mysqlQuery("SELECT nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]' && id='$asesor')", $db_link);
    $asesor = mysql_fetch_array($consulta1);

    $asunto = "CONFIRMACION DE CITA EN $cliente[empresa]";
    $mensaje = cm_lee_archivo("$rutasi3/plantillas/envio_de_cita_cliente_2.htm");
    $sust = array(
        '$FOTO' => $foto,
        '$LUGAR_CITA' => $como_llegar,
        '$RESPUESTA' => '',
        '$ASESOR' => "$asesor[nombre] $asesor[apellidos]",
        '$NOMBRE_COMPLETO' => "$c_nombre $c_apellidos",
        '$FECHA_HORA_SOLICITADA' => cm_formato_fecha($cita[fecha_hora_cita], 3),
        '$FECHA_HORA_CITA' => cm_formato_fecha($cita[fecha_hora_cita], 3),
        '$SESION' => $sesion,
        '$CITA[ID]' => $cita[id],
    );
    $mensaje = strtr($mensaje, $sust);
    $mensaje = cm_pone_datos_empresa($mensaje);

    // para el envío en formato HTML
    $headers = "MIME-Version: 1.0\n";
    $headers .= "Content-type: text/html; charset=iso-8859-1\n";

    //dirección del remitente
    if ($dsucursal > 0) {
        $consulta1 = mysqlQuery("SELECT email FROM sucursales WHERE (contrato='$config[contrato]' && id='$dsucursal')", $db_link);
        $row = mysql_fetch_array($consulta1);
        if (trim($row[email]) != '') {
            $config[correo_ventas] = $row[email];
        }
    }
    if ($config[correo_ventas] == '') {
        $headers .= "From: $cliente[email] ($cliente[empresa])\n";
    } else {
        $headers .= "From: $config[correo_ventas] ($cliente[empresa])\n";
    }

    // Envio el mensaje
    $sust = array(
        "\n" => "\r\n"
    );
    $mensaje = strtr($mensaje, $sust);
    cm_pmail($c_email, $asunto, $mensaje, $headers);
    //echo "$headers<br><br>Para: $email<br><br>Asunto: $asunto<br><br>$mensaje";
}

?>
<p align="center">&nbsp;</p>
<p align="center"><b>
        <font size="2" face="Arial">Se ha programado la cita con el cliente.</font>
    </b></p>
<p align="center">&nbsp;</p>
