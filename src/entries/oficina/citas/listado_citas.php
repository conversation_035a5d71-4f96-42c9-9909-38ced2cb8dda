<?php

// Preparar datos para el template Mustache
$hoy = date("Y-m-d H:i:s", time());

// Determinar filtros de consulta
if ($ver == 'hoy') {
    $v1 = 'citas.fecha_hora_solicitada = citas.fecha_hora_cita';
    $v2 = "citas.tipo_respuesta != 'mail'";
    $v3 = "citas.fecha_hora_solicitada >= '$hoy'";
} elseif ($ver == 'pasadas') {
    $v1 = 'citas.fecha_hora_solicitada = citas.fecha_hora_cita';
    $v2 = "citas.tipo_respuesta != 'mail'";
    $v3 = "citas.fecha_hora_solicitada < '$hoy'";
} else {
    $v1 = 'citas.fecha_hora_solicitada != citas.fecha_hora_cita';
    if ($por_email == 'SiHoy') {
        $v2 = "citas.tipo_respuesta = 'mail' && citas.fecha_hora_solicitada >= '$hoy'";
    } elseif ($por_email == 'SiPasadas') {
        $v2 = "citas.tipo_respuesta = 'mail' && citas.fecha_hora_solicitada < '$hoy'";
    } elseif ($por_email == 'Si') {
        $v2 = "citas.tipo_respuesta = 'mail'";
    } else {
        $v2 = "citas.tipo_respuesta != 'mail'";
    }
    $v3 = "citas.fecha_hora_solicitada >= '0000-00-00 00:00:00'";
}

$query = "SELECT citas.id, citas.clave_sistema, citas.usuario, citas.nombre, citas.apellidos, citas.telefono, citas.celular, citas.nextel_tel, citas.nextel_radio, citas.email, citas.fecha_hora_solicitada, citas.fecha_hora_cita, citas.solicitud, citas.cancelada, citas.asesor, citas.confirmo_cliente, citas.cc_asesor, propiedades.claveprop, propiedades.sucursal 
FROM citas, propiedades 
WHERE (citas.contrato='{$config['contrato']}' AND {$v1} AND {$v2} AND {$v3} AND citas.clave_sistema=propiedades.clave_sistema) 
ORDER BY fecha_hora_solicitada, sesion, propiedades.sucursal, cancelada ASC";
$consulta1 = $sql->query($query);

// Preparar datos para el template
$templateData = [];
$templateData['phpSelf'] = $PHP_SELF;
$templateData['timestamp'] = time();

// Determinar títulos según el tipo de vista
if ($ver == 'hoy') {
    $templateData['titulo'] = 'Citas hechas para hoy y a futuro';
} elseif ($ver == 'pasadas') {
    $templateData['titulo'] = 'Citas pasadas';
} elseif (isset($por_email)) {
    $templateData['titulo'] = 'Citas que fueron contestadas por e-mail externo al sistema';
    if ($por_email == 'SiHoy') {
        $templateData['subtitulo'] = 'Solicitudes para hoy y a futuro';
    } elseif ($por_email == 'SiPasadas') {
        $templateData['subtitulo'] = 'Solicitudes pasadas';
    }
    $templateData['mostrarNavegacionEmail'] = true;
} else {
    $templateData['titulo'] = 'Citas pendientes de confirmación';
    $templateData['subtitulo'] = 'Las citas marcadas en color <IMG SRC="imagessi/rojo.gif" BORDER="0"> no se respondieron a tiempo y siguen pendientes de confirmación';
}

// Procesar citas si hay registros
if ($consulta1->num_rows > 0) {
    $templateData['hasCitas'] = true;
    $templateData['citas'] = [];

    $bgcolor = "#FFFFFF";
    $arreglo_todas = array();
    $i = -1;

    while ($cita = $consulta1->fetch_assoc()) {
        // Datos del usuario cuando se toman desde un usuario registrado
        if ($cita['usuario'] != '') {
            $consulta3 = $sql->query("SELECT * FROM clientes WHERE (usuario='{$cita['usuario']}')");
            if ($consulta3->num_rows > 0) {
                $visitante = $consulta3->fetch_assoc();
                $cita['nombre'] = $visitante['nombre'];
                $cita['apellidos'] = $visitante['apellidos'];
                $cita['ocupacion'] = $visitante['ocupacion'];
                $cita['empresa'] = $visitante['empresa'];
                $cita['ciudad'] = $visitante['ciudad'];
                $cita['estado'] = $visitante['estado'];
                $cita['pais'] = $visitante['pais'];
                $cita['telefono'] = $visitante['telefono'];
                $cita['celular'] = $visitante['celular'];
                $cita['nextel_tel'] = $visitante['nextel_tel'];
                $cita['nextel_radio'] = $visitante['nextel_radio'];
                $cita['email'] = $visitante['email'];
            }
        }

        // Verificar si es una nueva cita o una propiedad adicional para la misma cita
        if ($cita['fecha_hora_solicitada'] == $fecha_hora_solicitada && $cita['sesion'] == $sesion &&
            $cita['cancelada'] == $cancelada && $cita['nombre'] == $nombre && $cita['apellidos'] == $apellidos &&
            $cita['email'] == $email && $cita['telefono'] == $telefono && $cita['celular'] == $celular &&
            $cita['nextel_tel'] == $nextel_tel && $cita['nextel_radio'] == $nextel_radio &&
            $cita['ocupacion'] == $ocupacion && $cita['empresa'] == $empresa && $cita['ciudad'] == $ciudad &&
            $cita['estado'] == $estado && $cita['pais'] == $pais && $cita['sucursal'] == $sucursal) {

            // Agregar a las claves a mostrar
            $arreglo_todas[$i]['claveprop'] .= ", <a href=\"detallesnew.php?clave={$cita['clave_sistema']}\" target=\"_blank\">{$cita['claveprop']}</a>";
            $p_claves .= "&claves_sistema[]={$cita['clave_sistema']}";
            $arreglo_todas[$i]['ver_cita'] .= urlencode(',') . $cita['id'];

        } else {
            if ($i >= 0) {
                $sust = array('--z24charly-clavesprop--' => $p_claves);
                $arreglo_todas[$i]['papeleta'] = strtr($arreglo_todas[$i]['papeleta'], $sust);

                if ($sucursal > 0) {
                    $arreglo_todas[$i]['claveprop'] .= '<br><font size="1" color="#339900" face="Arial">' . $sucursales[$sucursal] . '</font>';
                }
            }

            $i++;

            $agno = substr($cita['fecha_hora_solicitada'], 0, 4) * 1;
            $mes = substr($cita['fecha_hora_solicitada'], 5, 2) * 1;
            $dia = substr($cita['fecha_hora_solicitada'], 8, 2) * 1;
            $hora = substr($cita['fecha_hora_solicitada'], 11, 2) * 1;
            $minuto = substr($cita['fecha_hora_solicitada'], 14, 2) * 1;
            $v1 = mktime($hora, $minuto, 0, $mes, $dia, $agno);

            // Determinar color y papeleta según el estado de la cita
            if ($v1 <= time() && $ver != 'hoy' && $ver != 'pasadas' && substr($por_email, 0, 2) != 'Si') {
                $arreglo_todas[$i]['color'] = '#FF0000';
                if ($ver == 'hoy' || $ver == 'pasadas') {
                    $consulta2 = $sql->query("SELECT id FROM asesores WHERE (contrato='{$config['contrato']}' && id='{$cita['asesor']}')");
                    $asesor = $consulta2->fetch_assoc();
                    $p_nombre = urlencode($cita['nombre']);
                    $p_apellidos = urlencode($cita['apellidos']);
                    $p_telefono = urlencode($cita['telefono']);
                    $p_email = urlencode($cita['email']);
                    $p_fecha = urlencode(substr($cita['fecha_hora_solicitada'], 0, 10));
                    $p_ciudad = urlencode($cliente['ciudad']);
                    $p_estado = urlencode($cliente['estado']);

                    $arreglo_todas[$i]['papeleta'] = "<br><br><A HREF=\"reportevisita.php?asesor={$asesor['id']}&visitante=0&c_nombre={$p_nombre}&c_apellidos={$p_apellidos}&c_telefono={$p_telefono}&c_email={$p_email}&fecha={$p_fecha}&ciudad={$p_ciudad}&estado={$p_estado}&espacios=0&paso=2&--z24charly-clavesprop--\" target=\"_blank\">Imprimir reporte de visita</A>";
                } else {
                    $arreglo_todas[$i]['papeleta'] = '';
                }
            } elseif ($cita['cancelada'] == 'Si') {
                $arreglo_todas[$i]['color'] = '#C0C0C0';
                $arreglo_todas[$i]['papeleta'] = '';
            } else {
                $arreglo_todas[$i]['color'] = $bgcolor;
                if ($ver == 'hoy' || $ver == 'pasadas') {
                    $consulta2 = $sql->query("SELECT id FROM asesores WHERE (contrato='{$config['contrato']}' && id='{$cita['asesor']}')");
                    $asesor = $consulta2->fetch_assoc();
                    $p_nombre = urlencode($cita['nombre']);
                    $p_apellidos = urlencode($cita['apellidos']);
                    $p_telefono = urlencode($cita['telefono']);
                    $p_email = urlencode($cita['email']);
                    $p_fecha = urlencode(substr($cita['fecha_hora_solicitada'], 0, 10));
                    $p_ciudad = urlencode($cliente['ciudad']);
                    $p_estado = urlencode($cliente['estado']);

                    $arreglo_todas[$i]['papeleta'] = "<br><br><A HREF=\"reportevisita.php?asesor={$asesor['id']}&visitante=0&c_nombre={$p_nombre}&c_apellidos={$p_apellidos}&c_telefono={$p_telefono}&c_email={$p_email}&fecha={$p_fecha}&ciudad={$p_ciudad}&estado={$p_estado}&espacios=0&paso=2&--z24charly-clavesprop--\" target=\"_blank\">Imprimir reporte de visita</A>";
                } else {
                    $arreglo_todas[$i]['papeleta'] = '';
                }
            }

            // Agregar la información al arreglo
            $arreglo_todas[$i]['claveprop'] = "<a href=\"detallesnew.php?clave={$cita['clave_sistema']}\" target=\"_blank\">{$cita['claveprop']}</a>";
            $p_claves = "claves_sistema[]={$cita['clave_sistema']}";
            $arreglo_todas[$i]['fecha_hora_solicitada'] = cm_formato_fecha($cita['fecha_hora_solicitada'], 3);

            if ($cita['fecha_hora_solicitada'] != $cita['fecha_hora_cita'] && $cita['fecha_hora_cita'] != '0000-00-00 00:00:00') {
                if ($cita['confirmo_cliente'] == 'Si') {
                    $arreglo_todas[$i]['fecha_hora_solicitada'] .= '<br><font face="Arial" size="1">El cliente YA CONFIRMÓ para el ' . cm_formato_fecha($cita['fecha_hora_cita'], 4, '/') . '<br><font color="#339900">Confirma la cita para indicar lugar y persona que lo atenderá</font>';
                } else {
                    $arreglo_todas[$i]['fecha_hora_solicitada'] .= '<br><font face="Arial" size="1">Esperando confirmación del cliente al ' . cm_formato_fecha($cita['fecha_hora_cita'], 4, '/');
                }
            }

            // En caso de haber recibido copia un asesor
            if ($cita['cc_asesor'] > 0) {
                $consulta2 = $sql->query("SELECT nombre, apellidos FROM asesores WHERE (id='{$cita['cc_asesor']}')");
                if ($consulta2->num_rows == 1) {
                    $asesor = $consulta2->fetch_assoc();
                    $arreglo_todas[$i]['fecha_hora_solicitada'] .= "<br><span class=\"observaciones\"><B>{$asesor['nombre']} {$asesor['apellidos']}</B></span>";
                }
            }

            $arreglo_todas[$i]['cliente'] = "{$cita['nombre']} {$cita['apellidos']}";
            $arreglo_todas[$i]['ver_cita'] = "{$PHP_SELF}?id={$cita['id']}";

            // Variables para la siguiente iteración
            $fecha_hora_solicitada = $cita['fecha_hora_solicitada'];
            $sesion = $cita['sesion'];
            $cancelada = $cita['cancelada'];
            $nombre = $cita['nombre'];
            $apellidos = $cita['apellidos'];
            $ocupacion = $cita['ocupacion'];
            $empresa = $cita['empresa'];
            $ciudad = $cita['ciudad'];
            $estado = $cita['estado'];
            $pais = $cita['pais'];
            $telefono = $cita['telefono'];
            $celular = $cita['celular'];
            $nextel_tel = $cita['nextel_tel'];
            $nextel_radio = $cita['nextel_radio'];
            $email = $cita['email'];
            $sucursal = $cita['sucursal'];

            if ($bgcolor == '#FFFFFF') {
                $bgcolor = '#F0F0F0';
            } else {
                $bgcolor = '#FFFFFF';
            }
        }
    }

    // Procesar último elemento
    if ($i >= 0) {
        $sust = array('--z24charly-clavesprop--' => $p_claves);
        $arreglo_todas[$i]['papeleta'] = strtr($arreglo_todas[$i]['papeleta'], $sust);

        if ($sucursal > 0) {
            $arreglo_todas[$i]['claveprop'] .= '<br><font size="1" color="#339900" face="Arial">' . $sucursales[$sucursal] . '</font>';
        }
    }

    // Convertir arreglo a formato para template
    $templateData['citas'] = $arreglo_todas;
}

// Procesar segunda consulta para mostrar citas de hoy si no hay citas principales
if (!isset($t)) {
    $ver_hoy = 'hoy';

    $hoy = strftime("%Y-%m-%d 00:00:00", time());
    $v1 = 'citas.fecha_hora_solicitada = citas.fecha_hora_cita';
    $v2 = "citas.tipo_respuesta != 'mail'";
    $v3 = "citas.fecha_hora_solicitada >= '$hoy'";

    $consulta1 = $sql->query("SELECT citas.id, citas.clave_sistema, citas.usuario, citas.nombre, citas.apellidos, citas.telefono, citas.email, citas.fecha_hora_solicitada, citas.fecha_hora_cita, citas.solicitud, citas.cancelada, citas.asesor, propiedades.claveprop, propiedades.sucursal FROM citas, propiedades WHERE (citas.contrato='$config[contrato]' && $v1 && $v2 && $v3 && citas.clave_sistema=propiedades.clave_sistema) ORDER BY fecha_hora_solicitada, sesion, propiedades.sucursal, cancelada ASC");

    if ($consulta1->num_rows > 0) {
        $templateData['mostrarCitasHoy'] = true;
        $templateData['citasHoy'] = [];

        $bgcolor = "#FFFFFF";
        $arreglo_todas_hoy = array();
        $i = -1;

        while ($cita = $consulta1->fetch_assoc()) {
            // Procesar citas de hoy (código similar al anterior pero más simple)
            if ($cita['usuario'] != '') {
                $consulta3 = $sql->query("SELECT * FROM clientes WHERE (usuario='$cita[usuario]')");
                $visitante = $consulta3->fetch_assoc();
                $cita['nombre'] = $visitante['nombre'];
                $cita['apellidos'] = $visitante['apellidos'];
            }

            $i++;
            $arreglo_todas_hoy[$i]['claveprop'] = "<a href=\"detallesnew.php?clave={$cita['clave_sistema']}\" target=\"_blank\">{$cita['claveprop']}</a>";
            $arreglo_todas_hoy[$i]['fecha_hora_solicitada'] = cm_formato_fecha($cita['fecha_hora_solicitada'], 3);
            $arreglo_todas_hoy[$i]['cliente'] = "{$cita['nombre']} {$cita['apellidos']}";
            $arreglo_todas_hoy[$i]['ver_cita'] = "{$PHP_SELF}?id={$cita['id']}";
            $arreglo_todas_hoy[$i]['color'] = $bgcolor;

            if ($bgcolor == '#FFFFFF') {
                $bgcolor = '#F0F0F0';
            } else {
                $bgcolor = '#FFFFFF';
            }
        }

        $templateData['citasHoy'] = $arreglo_todas_hoy;
    }
    $ver = '';
}

// Configurar enlaces de navegación
if ($ver == 'hoy') {
    $templateData['linkCitasPendientes'] = true;
    $templateData['textoCitasPendientes'] = 'Ver citas pendientes de confirmación';
} else {
    $templateData['linkCitasHoy'] = true;
    $templateData['textoCitasHoy'] = 'Ver citas hechas para hoy y a futuro';
}

if ($ver == 'pasadas') {
    $templateData['linkCitasPendientes'] = true;
    $templateData['textoCitasPendientes'] = 'Ver citas pendientes de confirmación';
} else {
    $templateData['linkCitasPasadas'] = true;
    $templateData['textoCitasPasadas'] = 'Ver citas pasadas';
}

// Generar opciones del select para historial de citas
$templateData['opcionesClave'] = [];
$consulta1 = $sql->query("SELECT DISTINCT citas.clave_sistema, propiedades.claveprop, propiedades.sucursal FROM citas, propiedades WHERE (citas.contrato='$config[contrato]' && propiedades.clave_sistema=citas.clave_sistema) ORDER BY propiedades.claveprop");
while ($cita = $consulta1->fetch_assoc()) {
    $v1 = "$cita[clave_sistema]---z24charly---$cita[claveprop]---z24charly---$cita[sucursal]";
    $templateData['opcionesClave'][] = [
        'value' => $v1,
        'text' => $cita['claveprop']
    ];
}

// Renderizar con Mustache
echo $m->render(file_get_contents(__DIR__ . '/listado_citas.mustache'), $templateData);
