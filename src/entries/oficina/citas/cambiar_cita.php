<?php

// Hago la cadena para la consulta
for ($i = 0; $i < count($ids); $i++) {
    if ($i == 0) {
        $v1 = "id = $ids[0]";
    } else {
        $v1 .= " || id = " . $ids[$i];
    }
}

// Tratamiento de las fechas
$meses = array('enero' => '01', 'febrero' => '02', 'marzo' => '03', 'abril' => '04', 'mayo' => '05', 'junio' => '06', 'julio' => '07', 'agosto' => '08', 'septiembre' => '09', 'octubre' => '10', 'noviembre' => '11', 'diciembre' => '12');
$fecha_cita = explode('/', $fecha_cita);
$fecha_cita = "$fecha_cita[2]-" . $meses[$fecha_cita[1]] . "-$fecha_cita[0] $t_hora:$t_minuto:00";
$foto = '';

$consulta1 = mysqlQuery("SELECT fecha_hora_cita FROM citas WHERE (contrato='$config[contrato]' && ($v1))", $db_link);
$anterior = mysql_fetch_array($consulta1);

// Grabo la información en la BD
mysqlQuery("UPDATE citas SET fecha_hora_solicitada='$fecha_cita', fecha_hora_cita='$fecha_cita' WHERE (contrato='$config[contrato]' && ($v1))", $db_link);

$consulta1 = mysqlQuery("SELECT * FROM citas WHERE (contrato='$config[contrato]' && ($v1))", $db_link);

// Primero recupero las fotos de los inmuebles a mostrar
$ar_fotos = array();
while ($propiedad = mysql_fetch_array($consulta1)) {
    array_push($ar_fotos, $propiedad['clave_sistema']);
}
$ar_fotos = $images->primer_foto($ar_fotos, 'inmueble');
if (mysql_num_rows($consulta1) > 0) {
    mysql_data_seek($consulta1, 0);
}

$v1 = 0;
while ($cita = mysql_fetch_array($consulta1)) {
    if ($cita[usuario] != '' && $v1 == 0) {
        @mysql_select_db(DB_PW);
        $consulta3 = mysqlQuery("SELECT nombre, apellidos, email FROM clientes WHERE (usuario='$cita[usuario]')", $db_link);
        $visitante = mysql_fetch_array($consulta3);
        @mysql_select_db(DB_SI);
        $nombre_completo = "$visitante[nombre] $visitante[apellidos]";
        $email = $visitante[email];
        $cita_nueva = $cita[fecha_hora_cita];
        $sesion = $cita[sesion];
        $id = $cita[id];
        $v1 = 1;
    } elseif ($v1 == 0) {
        $nombre_completo = "$cita[nombre] $cita[apellidos]";
        $email = $cita[email];
        $cita_nueva = $cita[fecha_hora_cita];
        $sesion = $cita[sesion];
        $id = $cita[id];
        $v1 = 1;
    }

    $consulta2 = mysqlQuery("SELECT claveprop, sucursal FROM propiedades WHERE (contrato='$config[contrato]' && clave_sistema='$cita[clave_sistema]')", $db_link);
    $propiedad = mysql_fetch_array($consulta2);

    // Armo las fotos a enviar en el e-mail
    $foto .= "<tr><td align=\"center\"><font size=\"2\" face=\"Arial\"><A HREF=\"http://$config[dominio]/detallesnew.php?clave=$cita[clave_sistema]&idioma=esp\"><b>$propiedad[claveprop]</b><br>" . $ar_fotos[$cita['clave_sistema']]['img'] . "</A></font></td></tr>\n";
}

$dsucursal = $propiedad[sucursal];

// Si el cliente tiene cuenta de e-mail se le manda el correo notificandole el cambio en la cita
if (trim($email) != '') {

    $asunto = "Cambio en la cita de $cliente[empresa]";
    $mensaje = cm_lee_archivo("$rutasi3/plantillas/envio_de_cita_cliente_5.htm");
    $sust = array(
        '$FOTO' => $foto,
        '$NOMBRE_COMPLETO' => $nombre_completo,
        '$CITA_ANTERIOR' => cm_formato_fecha($anterior[fecha_hora_cita], 3),
        '$CITA_NUEVA' => cm_formato_fecha($cita_nueva, 3),
        '$SESION' => $sesion,
        '$CITA[ID]' => $id,
    );
    $mensaje = strtr($mensaje, $sust);
    $mensaje = cm_pone_datos_empresa($mensaje);

    // para el envío en formato HTML
    $headers = "MIME-Version: 1.0\n";
    $headers .= "Content-type: text/html; charset=iso-8859-1\n";

    //dirección del remitente
    if ($dsucursal > 0) {
        $consulta1 = mysqlQuery("SELECT email FROM sucursales WHERE (contrato='$config[contrato]' && id='$dsucursal')", $db_link);
        $row = mysql_fetch_array($consulta1);
        if (trim($row[email]) != '') {
            $config[correo_ventas] = $row[email];
        }
    }
    if ($config[correo_ventas] == '') {
        $headers .= "From: $cliente[email] ($cliente[empresa])\n";
    } else {
        $headers .= "From: $config[correo_ventas] ($cliente[empresa])\n";
    }

    // Envio el mensaje
    $sust = array(
        "\n" => "\r\n"
    );
    $mensaje = strtr($mensaje, $sust);
    cm_pmail($email, $asunto, $mensaje, $headers);
    //echo "$headers<br><br>Para: $email<br><br>Asunto: $asunto<br><br>$mensaje";

}

?>
<p align="center">&nbsp;</p>
<p align="center"><b>
        <font size="2" face="Arial">Se ha realizado el cambio en la cita.</font>
    </b></p>
<p align="center">&nbsp;</p>
