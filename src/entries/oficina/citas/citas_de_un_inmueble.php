<?php

// Parsear la clave
$v1 = explode('---z24charly---', $clave);
$clave_sistema = $v1[0];
$claveprop = $v1[1];
$clave_encoded = urlencode($clave);

// Datos de la sucursal
$sucursal = $v1[2];
$sucursal_nombre = '';
if ($sucursal > 0) {
    $consulta1 = $sql->query("SELECT nombre FROM sucursales WHERE (contrato='$config[contrato]' && id='$sucursal')");
    $dsucursal = $consulta1->fetch_assoc();
    if ($dsucursal) {
        $sucursal_nombre = $dsucursal['nombre'];
    }
}

// Determinar orden de consulta
if ($orden == 'nombre') {
    $orden_sql = 'usuario, nombre, apellidos';
} elseif ($orden == 'fecha' || $orden == '') {
    $orden_sql = 'fecha_hora_solicitada';
} elseif ($orden == 'estado') {
    $orden_sql = 'tipo_respuesta, cancelada';
} else {
    $orden_sql = 'fecha_hora_solicitada';
}

// Obtener citas
$consulta1 = $sql->query("SELECT * FROM citas WHERE (contrato='$config[contrato]' && clave_sistema=$clave_sistema) ORDER BY $orden_sql, fecha_hora_solicitada ASC");

$citas = array();
$bgcolor = "#FFFFFF";
while ($cita = $consulta1->fetch_assoc()) {

    // Datos del usuario
    if ($cita['usuario'] != '') {
        $consulta3 = $sql->query("SELECT nombre, apellidos FROM ".DB_PW."clientes WHERE (usuario='$cita[usuario]')");
        $visitante = $consulta3->fetch_assoc();
        $nombre = $visitante ? $visitante['nombre'] : '';
        $apellidos = $visitante ? $visitante['apellidos'] : '';
    } else {
        $nombre = $cita['nombre'];
        $apellidos = $cita['apellidos'];
    }

    // Estado de la cita
    $estado_cita = '';
    if ($cita['cancelada'] == 'Si') {
        $estado_cita = '<i>CANCELADA</i>';
    } elseif ($cita['fecha_hora_solicitada'] == $cita['fecha_hora_cita'] && $cita['tipo_respuesta'] == 'sistema') {
        $estado_cita = 'CONFIRMADA';
    } elseif ($cita['fecha_hora_solicitada'] != $cita['fecha_hora_cita'] && $cita['tipo_respuesta'] != 'mail') {
        $estado_cita = 'SIN CONFIRMAR';
    } elseif ($cita['tipo_respuesta'] == 'mail') {
        $estado_cita = 'Respondida por email';
    }

    // Fecha formateada
    $fecha_formateada = cm_formato_fecha($cita['fecha_hora_solicitada'], 3);
    $fecha_confirmacion = '';
    if ($cita['fecha_hora_solicitada'] != $cita['fecha_hora_cita'] && $cita['fecha_hora_cita'] != '0000-00-00 00:00:00') {
        $fecha_confirmacion = '<br><font face="Arial" size="1">Esperando confirmación del cliente al ' . cm_formato_fecha($cita['fecha_hora_cita'], 4, '/') . '</font>';
    }

    // Datos del asesor CC
    $asesor_cc = '';
    if ($cita['cc_asesor'] > 0) {
        $consulta2 = $sql->query("SELECT nombre, apellidos FROM ".DB_PW."asesores WHERE (id='$cita[cc_asesor]')");
        if ($consulta2->num_rows == 1) {
            $asesor = $consulta2->fetch_assoc();
            $asesor_cc = "<br><span class=\"observaciones\">CC: <B>$asesor[nombre] $asesor[apellidos]</B></span>";
        }
    }

    $citas[] = array(
        'id' => $cita['id'],
        'bgcolor' => $bgcolor,
        'estado' => $estado_cita,
        'fecha' => $fecha_formateada,
        'fecha_confirmacion' => $fecha_confirmacion,
        'nombre_completo' => trim($nombre . ' ' . $apellidos),
        'asesor_cc' => $asesor_cc,
        'ver_cita_url' => $PHP_SELF . '?id=' . $cita['id']
    );

    // Alternar color de fondo
    $bgcolor = ($bgcolor == '#FFFFFF') ? '#F0F0F0' : '#FFFFFF';
}

// Obtener lista de inmuebles con citas para el dropdown
$inmuebles_opciones = array();
$consulta1 = $sql->query("SELECT DISTINCT citas.clave_sistema, propiedades.claveprop FROM citas, propiedades WHERE (citas.contrato='$config[contrato]' && propiedades.clave_sistema=citas.clave_sistema) ORDER BY propiedades.claveprop");
while ($inmueble = $consulta1->fetch_assoc()) {
    $valor = $inmueble['clave_sistema'] . '---z24charly---' . $inmueble['claveprop'];
    $inmuebles_opciones[] = array(
        'value' => $valor,
        'text' => $inmueble['claveprop']
    );
}

// Datos para el template
$template_data = array(
    'clave_sistema' => $clave_sistema,
    'claveprop' => $claveprop,
    'clave_encoded' => $clave_encoded,
    'sucursal_nombre' => $sucursal_nombre,
    'mostrar_sucursal' => $sucursal > 0,
    'PHP_SELF' => $PHP_SELF,
    'citas' => $citas,
    'inmuebles_opciones' => $inmuebles_opciones,
    'timestamp' => time()
);


// Renderizar con Mustache
echo $m->render(file_get_contents(__DIR__ . '/citas_de_un_inmueble.mustache'), $template_data);
