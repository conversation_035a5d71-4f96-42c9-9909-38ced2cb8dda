<?php

/**
 * Clase Curl para consumir servicios y APIs
 * Compatible con PHP 5.6
 *
 * <AUTHOR>
 * @version 1.0
 */
class Curl
{
    private $ch;
    private $url;
    private $headers;
    private $timeout;
    private $followLocation;
    private $maxRedirects;
    private $userAgent;
    private $cookies;
    private $sslVerify;
    private $response;
    private $httpCode;
    private $error;

    /**
     * Constructor de la clase
     */
    public function __construct()
    {
        $this->ch = curl_init();
        $this->headers = array();
        $this->timeout = 30;
        $this->followLocation = true;
        $this->maxRedirects = 5;
        $this->userAgent = 'MulbinComponents-Curl/1.0';
        $this->cookies = '';
        $this->sslVerify = false;
        $this->response = null;
        $this->httpCode = 0;
        $this->error = null;

        // Configuración básica por defecto
        $this->setDefaultOptions();
    }

    /**
     * Establece las opciones por defecto de cURL
     */
    private function setDefaultOptions()
    {
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->ch, CURLOPT_HEADER, false);
        curl_setopt($this->ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, $this->followLocation);
        curl_setopt($this->ch, CURLOPT_MAXREDIRS, $this->maxRedirects);
        curl_setopt($this->ch, CURLOPT_USERAGENT, $this->userAgent);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, $this->sslVerify);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, $this->sslVerify ? 2 : 0);
    }

    /**
     * Establece la URL de destino
     *
     * @param string $url URL de destino
     * @return Curl
     */
    public function setUrl($url)
    {
        $this->url = $url;
        curl_setopt($this->ch, CURLOPT_URL, $url);
        return $this;
    }

    /**
     * Establece headers personalizados
     *
     * @param array $headers Array de headers
     * @return Curl
     */
    public function setHeaders($headers)
    {
        // Verifico si el array es asociativo (clave => valor) y lo convierto a formato de headers
        if (!empty($headers) && array_keys($headers) !== range(0, count($headers) - 1)) {
            // Es un array asociativo, convertir a formato "Clave: Valor"
            $formattedHeaders = array();
            foreach ($headers as $key => $value) {
                $formattedHeaders[] = $key . ': ' . $value;
            }
            $headers = $formattedHeaders;
        }
        $this->headers = array_merge($this->headers, $headers);
        curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
        return $this;
    }

    /**
     * Añade un header individual
     *
     * @param string $header Header a añadir
     * @return Curl
     */
    public function addHeader($header)
    {
        $this->headers[] = $header;
        curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
        return $this;
    }

    /**
     * Establece el timeout de la petición
     *
     * @param int $timeout Timeout en segundos
     * @return Curl
     */
    public function setTimeout($timeout)
    {
        $this->timeout = $timeout;
        curl_setopt($this->ch, CURLOPT_TIMEOUT, $timeout);
        return $this;
    }

    /**
     * Establece el User Agent
     *
     * @param string $userAgent User Agent string
     * @return Curl
     */
    public function setUserAgent($userAgent)
    {
        $this->userAgent = $userAgent;
        curl_setopt($this->ch, CURLOPT_USERAGENT, $userAgent);
        return $this;
    }

    /**
     * Establece cookies para la petición
     *
     * @param string $cookies String de cookies
     * @return Curl
     */
    public function setCookies($cookies)
    {
        $this->cookies = $cookies;
        curl_setopt($this->ch, CURLOPT_COOKIE, $cookies);
        return $this;
    }

    /**
     * Habilita o deshabilita la verificación SSL
     *
     * @param bool $verify True para verificar SSL, false para no verificar
     * @return Curl
     */
    public function setSslVerify($verify)
    {
        $this->sslVerify = $verify;
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, $verify);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, $verify ? 2 : 0);
        return $this;
    }

    /**
     * Establece autenticación básica
     *
     * @param string $username Usuario
     * @param string $password Contraseña
     * @return Curl
     */
    public function setBasicAuth($username, $password)
    {
        curl_setopt($this->ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($this->ch, CURLOPT_USERPWD, $username . ':' . $password);
        return $this;
    }

    /**
     * Establece token Bearer para autenticación
     *
     * @param string $token Token de autorización
     * @return Curl
     */
    public function setBearerToken($token)
    {
        $this->addHeader('Authorization: Bearer ' . $token);
        return $this;
    }

    /**
     * Realiza una petición GET
     *
     * @param string $url URL opcional (si no se estableció previamente)
     * @param array $params Parámetros GET opcionales
     * @return Curl
     */
    public function get($url = null, $params = array())
    {
        if ($url !== null) {
            $this->setUrl($url);
        }

        if (!empty($params)) {
            $queryString = http_build_query($params);
            $separator = strpos($this->url, '?') !== false ? '&' : '?';
            $this->setUrl($this->url . $separator . $queryString);
        }

        curl_setopt($this->ch, CURLOPT_HTTPGET, true);
        return $this->execute();
    }

    /**
     * Realiza una petición POST
     *
     * @param string $url URL opcional
     * @param mixed $data Datos a enviar (array, string, JSON)
     * @param string $contentType Tipo de contenido
     * @return Curl
     */
    public function post($url = null, $data = null, $contentType = 'application/x-www-form-urlencoded')
    {
        if ($url !== null) {
            $this->setUrl($url);
        }

        curl_setopt($this->ch, CURLOPT_POST, true);

        if ($data !== null) {
            $this->setPostData($data, $contentType);
        }

        return $this->execute();
    }

    /**
     * Realiza una petición PUT
     *
     * @param string $url URL opcional
     * @param mixed $data Datos a enviar
     * @param string $contentType Tipo de contenido
     * @return Curl
     */
    public function put($url = null, $data = null, $contentType = 'application/json')
    {
        if ($url !== null) {
            $this->setUrl($url);
        }

        curl_setopt($this->ch, CURLOPT_CUSTOMREQUEST, 'PUT');

        if ($data !== null) {
            $this->setPostData($data, $contentType);
        }

        return $this->execute();
    }

    /**
     * Realiza una petición DELETE
     *
     * @param string $url URL opcional
     * @param mixed $data Datos a enviar (opcional)
     * @param string $contentType Tipo de contenido
     * @return Curl
     */
    public function delete($url = null, $data = null, $contentType = 'application/json')
    {
        if ($url !== null) {
            $this->setUrl($url);
        }

        curl_setopt($this->ch, CURLOPT_CUSTOMREQUEST, 'DELETE');

        if ($data !== null) {
            $this->setPostData($data, $contentType);
        }

        return $this->execute();
    }

    /**
     * Realiza una petición PATCH
     *
     * @param string $url URL opcional
     * @param mixed $data Datos a enviar
     * @param string $contentType Tipo de contenido
     * @return Curl
     */
    public function patch($url = null, $data = null, $contentType = 'application/json')
    {
        if ($url !== null) {
            $this->setUrl($url);
        }

        curl_setopt($this->ch, CURLOPT_CUSTOMREQUEST, 'PATCH');

        if ($data !== null) {
            $this->setPostData($data, $contentType);
        }

        return $this->execute();
    }

    /**
     * Establece los datos para peticiones POST/PUT/PATCH/DELETE
     *
     * @param mixed $data Datos a enviar
     * @param string $contentType Tipo de contenido
     */
    private function setPostData($data, $contentType)
    {
        switch ($contentType) {
            case 'application/json':
                if (is_array($data) || is_object($data)) {
                    $data = json_encode($data);
                }
                $this->addHeader('Content-Type: application/json');
                break;

            case 'application/xml':
                $this->addHeader('Content-Type: application/xml');
                break;

            case 'multipart/form-data':
                // Para archivos, no establecer Content-Type manualmente
                break;

            default:
                if (is_array($data)) {
                    $data = http_build_query($data);
                }
                $this->addHeader('Content-Type: application/x-www-form-urlencoded');
                break;
        }

        curl_setopt($this->ch, CURLOPT_POSTFIELDS, $data);
    }

    /**
     * Ejecuta la petición cURL
     *
     * @return Curl
     */
    private function execute()
    {
        $this->response = curl_exec($this->ch);
        $this->httpCode = curl_getinfo($this->ch, CURLINFO_HTTP_CODE);

        if ($this->response === false) {
            $this->error = curl_error($this->ch);
        }

        return $this;
    }

    /**
     * Obtiene la respuesta de la petición
     *
     * @return string|false
     */
    public function getResponse()
    {
        return $this->response;
    }

    /**
     * Obtiene la respuesta como array JSON decodificado
     *
     * @param bool $assoc True para array asociativo, false para objeto
     * @return mixed
     */
    public function getJsonResponse($assoc = true)
    {
        return json_decode($this->response, $assoc);
    }

    /**
     * Obtiene el código de estado HTTP
     *
     * @return int
     */
    public function getHttpCode()
    {
        return $this->httpCode;
    }

    /**
     * Obtiene información detallada de la petición
     *
     * @return array
     */
    public function getInfo()
    {
        return curl_getinfo($this->ch);
    }

    /**
     * Obtiene el error de cURL si existe
     *
     * @return string|null
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * Verifica si la petición fue exitosa
     *
     * @return bool
     */
    public function isSuccess()
    {
        return $this->httpCode >= 200 && $this->httpCode < 300 && $this->error === null;
    }

    /**
     * Verifica si hubo un error
     *
     * @return bool
     */
    public function hasError()
    {
        return $this->error !== null || $this->httpCode >= 400;
    }

    /**
     * Resetea la instancia para una nueva petición
     *
     * @return Curl
     */
    public function reset()
    {
        curl_close($this->ch);
        $this->ch = curl_init();
        $this->headers = array();
        $this->response = null;
        $this->httpCode = 0;
        $this->error = null;
        $this->setDefaultOptions();
        return $this;
    }

    /**
     * Descarga un archivo desde una URL
     *
     * @param string $url URL del archivo
     * @param string $filePath Ruta donde guardar el archivo
     * @return bool
     */
    public function downloadFile($url, $filePath)
    {
        $this->setUrl($url);

        $fp = fopen($filePath, 'w+');
        if ($fp === false) {
            $this->error = 'No se pudo crear el archivo: ' . $filePath;
            return false;
        }

        curl_setopt($this->ch, CURLOPT_FILE, $fp);
        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, true);

        $result = curl_exec($this->ch);
        $this->httpCode = curl_getinfo($this->ch, CURLINFO_HTTP_CODE);

        fclose($fp);

        if ($result === false) {
            $this->error = curl_error($this->ch);
            unlink($filePath); // Eliminar archivo parcial en caso de error
            return false;
        }

        return $this->isSuccess();
    }

    /**
     * Sube un archivo vía POST
     *
     * @param string $url URL de destino
     * @param string $filePath Ruta del archivo a subir
     * @param string $fieldName Nombre del campo del formulario
     * @param array $additionalFields Campos adicionales del formulario
     * @return Curl
     */
    public function uploadFile($url, $filePath, $fieldName = 'file', $additionalFields = array())
    {
        $this->setUrl($url);

        if (!file_exists($filePath)) {
            $this->error = 'El archivo no existe: ' . $filePath;
            return $this;
        }

        $postData = $additionalFields;

        // PHP 5.6 compatible
        if (class_exists('CURLFile')) {
            $postData[$fieldName] = new CURLFile($filePath);
        } else {
            $postData[$fieldName] = '@' . $filePath;
        }

        curl_setopt($this->ch, CURLOPT_POST, true);
        curl_setopt($this->ch, CURLOPT_POSTFIELDS, $postData);

        return $this->execute();
    }

    /**
     * Destructor de la clase
     */
    public function __destruct()
    {
        if (is_resource($this->ch)) {
            curl_close($this->ch);
        }
    }
}
