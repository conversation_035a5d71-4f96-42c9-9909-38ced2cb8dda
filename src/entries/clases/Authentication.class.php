<?php

namespace Auth;

require_once __DIR__ . '/LaravelPasswordManager.class.php';
require_once __DIR__ . '/User.php';
require_once __DIR__ . '/Models/SIConfig.php';

use <PERSON><PERSON><PERSON>\User;
use <PERSON><PERSON><PERSON>\Models\SIConfig;

class Authentication
{
    protected $sql;

    private $db_pw;
    private $db_si;
    private $access = array('username' => null, 'password' => null);
    private $password = null;
    private $pw_admin = false;
    private $si_user = false;
    private $si_logmail = false;
    private $si_asesor = false;
    private $sucursal = false;
    private $contrato;
    private $logged = false;
    private $user_type = false;
    private $passwordManager;
    private $auth_by_token = false;
    // private $user;

    /**
     * ‹‹‹ __construct ›››
     */
    public function __construct()
    {
        global $sql;
        $this->sql = $sql;
        $this->passwordManager = new LaravelPasswordManager();
        // Si no está definido el contrato, termino con un error
        if (!defined('CONTRATO')) {
            throw new \Exception('CONTRATO no está definido');
        }
        $this->contrato = CONTRATO;
        $this->db_pw = DB_PW;
        $this->db_si = DB_SI;
    }

    /**
     * ‹‹‹ login ›››
     */
    public function login(
        $username = '',
        $password = '',
        $superadmin = false,
        $auth_by_token = false
    ) {
        if ($superadmin) {
            return true;
        }

        // Sanitize $username and $password
        $this->access['username'] = $this->sql->real_escape_string(strtolower(trim($username)));
        $this->access['password'] = $this->sql->real_escape_string(trim($password));

        if ($auth_by_token) {
            $this->auth_by_token = $auth_by_token;
            $this->access['username'] = $this->auth_by_token['usuario'];
        }

        // dd([
        //     'username' => $this->access['username'],
        //     'password' => $this->access['password'],
        //     'auth_by_token' => $this->auth_by_token,
        // ]);

        // Acceso de SuperAdmin o Asesor
        if (
            // Verifico si se está entrando como superadmin
            (
                $this->validateSuperAdmin($this->access['username'], $this->access['password']) ||
                // Verifico si se está entrando como asesor
                $this->validateAsesor($this->access['username'], $this->access['password'])
            ) && $this->validateSIUser()
        ) {
            return true;
        } //
        // Acceso por usuario y contraseña clásico
        elseif (
            ($vu = $this->validateSIUser($this->access['password'])) &&
            ($vu['usuario'] === $this->access['username'] || $vu['logmail'] === $this->access['username'])
        ) {
            return true;
        } //
        // Acceso por token
        elseif ($this->auth_by_token) {
            $this->password = $this->auth_by_token['password'];
            $this->si_user = $this->auth_by_token['usuario'];
            $this->user_type = 'owner';
            $this->logged = true;
            return true;
        } //
        // Acceso denegado
        else {
            return false;
        }
    }

    /**
     * ‹‹‹ validateSuperAdmin ›››
     */
    private function validateSuperAdmin($username, $password)
    {
        $row = $this->sql->row($query = sprintf("
            SELECT * 
            FROM %s.admin 
            WHERE usuario='%s' AND en_oficina='Si' AND admin_si='Si'
        ", $this->db_pw, $username));
        if (
            $row &&
            $this->cm_encripta($password, $row['password']) == $row['password']
        ) {
            $this->user_type = 'superadmin';
            $this->pw_admin = $row['usuario'];
            return true;
        } else {
            return false;
        }
    }

    /**
     * ‹‹‹ validateAsesor ›››
     */
    public function validateAsesor($username, $password)
    {
        $aid = substr($username, 0, 4) === 'aid-' ? (int)substr($username, 4) : 0;

        // Recupero la data del asesor
        $row = $this->sql->row($query = sprintf("
            SELECT ase.id, ase.password, suc.id AS sucursal_id
            FROM %1\$s.asesores AS ase
            LEFT JOIN %1\$s.sucursales AS suc ON ase.sucursal_id=suc.id
            WHERE (ase.id=%2\$d OR ase.usuario='%4\$s') AND ase.contrato=%3\$d AND ase.status='activo'
            LIMIT 1
        ", $this->db_si, $aid, $this->contrato, $username));
        if (
            $row &&
            ($this->cm_encripta($password, $row['password']) == $row['password'] ||
                $password === $row['password'])
        ) {
            $this->user_type = 'asesor';
            $this->si_asesor = $row['id'];
            $this->sucursal = $row['sucursal_id'];
            $this->password = $row['password'];
            return true;
        } else {
            return false;
        }
    }

    /**
     * Valida las credenciales de un usuario del Sistema Inmobiliario (propietario de contrato)
     *
     * @param string|null $password Contraseña a validar (opcional)
     * @return array|false Datos del usuario si es válido, false en caso contrario.
     *                    Retorna array con datos del usuario si es válido:
     *                    - 'usuario': nombre de usuario
     *                    - 'logmail': email del usuario
     *                    Retorna false si la validación falla
     */
    public function validateSIUser($password = null)
    {
        $row = $this->sql->row($query = sprintf(
            "SELECT clientes.logmail, clientes.usuario, clientes.password
            FROM %1\$s.clientes
            INNER JOIN %1\$s.contratos ON clientes.usuario=contratos.usuario
            WHERE contratos.numero=%2\$d AND clientes.activo='Si'
            LIMIT 1",
            $this->db_pw,
            $this->contrato
        ));
        if (!$row) {
            return false;
        }

        // Mando al log el resultado de la validación
        error_log("Validación de usuario - Contrato: " . $this->contrato . ", Usuario: " . $row['usuario'] . ", Logmail: " . $row['logmail'] . ", Password DB: " . $row['password'] . ", Password: " . $password);

        if (
            // Ya había validado como superadmin o asesor
            $this->pw_admin ||
            $this->si_asesor ||
            // Válido con formato Laravel
            $this->passwordManager->verify($password, $row['password']) ||
            // Es válido su acceso como usuario del Sistema Inmobiliario
            (
                $this->cm_encripta($password, $row['password']) == $row['password'] &&
                ($passwordUpdated = $this->updatePassword($row['usuario'], $password))
            ) ||
            $password === $row['password']
        ) {
            error_log("Usuario válido - Contrato: " . $this->contrato . ", Usuario: " . $row['usuario'] . ", Logmail: " . $row['logmail'] . ", Password DB: " . $row['password'] . ", Password: " . $password);

            $this->logged = true;
            $this->si_user = $row['usuario'];
            $this->si_logmail = $row['logmail'];
            // Si logmail es NULL o vacío, intentamos establecerlo con el email del cliente
            $newLogmail = $this->ensureLogmailOnLogin($this->si_user);
            if ($newLogmail) {
                $this->si_logmail = $newLogmail;
            }
            if (!$this->user_type) {
                $this->user_type = 'owner';
            }
            if (!$this->password) {
                if (!$passwordUpdated) {
                    $this->password = $row['password'];
                } else {
                    $this->password = $passwordUpdated;
                }
            }
            $this->userInMeteor();
            return [
                'usuario' => $this->si_user,
                'logmail' => $this->si_logmail,
            ];
        } else {
            return false;
        }
    }

    private function userInMeteor()
    {
        $user = new User($this->contrato);

        try {
            $userInMeteor = $user->userExistsInMeteor();
        } catch (\Exception $e) {
            // Error de servidor en Meteor - saltar creación de usuario
            error_log("Error al verificar usuario en Meteor, saltando creación - Contrato: " . $this->contrato . ", Error: " . $e->getMessage());
            return; // Continuar con autenticación normal sin Meteor
        }

        // El usuario no existe en Meteor (false), lo creamos
        if ($userInMeteor === false) {
            error_log("Usuario no existe en Meteor, lo creamos - Contrato: " . $this->contrato . ", Username: " . $user->getUsername());

            try {
                $creationResult = $user->createMeteorUser();

                if ($creationResult->success) {
                    // Usuario creado exitosamente
                    error_log("Usuario creado en Meteor exitosamente - Contrato: " . $this->contrato . ", Username: " . $user->getUsername() . ", UserID: " . $creationResult->userId);
                    // Actualizo el usuario en la base de datos SIConfig
                    $config = SIConfig::where('contrato', $this->contrato)->first();
                    $config->meteor_id = $creationResult->userId;
                    $config->save();

                    // Guardo el token de autenticación en la sesión
                    $_SESSION['meteor_user_id'] = $creationResult->userId;
                    $_SESSION['meteor_auth_token'] = $creationResult->authToken;
                } else {
                    // Error al crear usuario, pero continuamos con la autenticación normal
                    error_log("Error al crear usuario en Meteor - Contrato: " . $this->contrato . ", Username: " . $user->getUsername() . ", Error: " . $creationResult->error);
                }
            } catch (\Exception $e) {
                // Capturar cualquier excepción y continuar con la autenticación normal
                error_log("Excepción al crear usuario en Meteor - Contrato: " . $this->contrato . ", Error: " . $e->getMessage());
            }
        }
        // El usuario ya existe en Meteor, recupero el token de autenticación
        else {
            error_log("Usuario ya existe en Meteor, recupero el token de autenticación - Contrato: " . $this->contrato . ", Username: " . $user->getUsername());

            try {
                // Verificar que tenemos los datos del usuario correctamente estructurados
                if (!isset($userInMeteor->user) || !isset($userInMeteor->user->id)) {
                    error_log("Datos de usuario incompletos en respuesta de Meteor - Contrato: " . $this->contrato . ", Username: " . $user->getUsername());
                    return;
                }

                $loginResult = $user->makeMeteorRequest('/auth/generate-token', 'POST', array(
                    'userId' => $userInMeteor->user->id,
                ));

                $data = $loginResult['data'];

                // Verificar que la respuesta del token es válida
                if (isset($data->userId) && isset($data->authToken)) {
                    // Guardo el token de autenticación en la sesión
                    $_SESSION['meteor_user_id'] = $data->userId;
                    $_SESSION['meteor_auth_token'] = $data->authToken;

                    error_log("Token de autenticación generado exitosamente - Contrato: " . $this->contrato . ", UserID: " . $data->userId);
                } else {
                    error_log("Respuesta de token inválida de Meteor - Contrato: " . $this->contrato . ", Username: " . $user->getUsername());
                }

            } catch (\Exception $e) {
                error_log("Error al generar token de autenticación en Meteor - Contrato: " . $this->contrato . ", Error: " . $e->getMessage());
            }
        }
    }

    private function updatePassword($username, $password)
    {
        $to_update = [
            'password' => $newPassword = $this->passwordManager->hash($password)
        ];

        error_log("Actualizando contraseña - Contrato: " . $this->contrato . ", Username: " . $username . ", Password: " . $password . ", Password Manager: " . $newPassword);

        // Recupero el registro del cliente
        $row = $this->sql->row(sprintf("
            SELECT * 
            FROM %1\$s.clientes 
            WHERE usuario = '%2\$s'
            LIMIT 1
        ", $this->db_pw, $username));
        // Si logmail es NULL le pongo el email
        if (!$row['logmail']) {
            // Verifico si otro cliente tiene el mismo email como logmail, si ya existe entonces no se incluye en el update
            $other_client = $this->sql->row(sprintf(
                "SELECT * 
                FROM %1\$s.clientes 
                WHERE logmail = '%2\$s'
                LIMIT 1",
                $this->db_pw,
                $row['email']
            ));
            if (!$other_client) {
                $to_update['logmail'] = $row['email'];
            }
        }

        if (!$this->sql->update_reg(
            $this->db_pw . '.clientes',
            $to_update,
            "usuario = '$username'"
        )) {
            error_log("Error al actualizar contraseña - Contrato: " . $this->contrato . ", Username: " . $username . ", Password: " . $password . ", Password Manager: " . $newPassword);

            return false;
        }

        error_log("Contraseña actualizada - Contrato: " . $this->contrato . ", Username: " . $username . ", Password: " . $password . ", Password Manager: " . $newPassword);

        return $newPassword;
    }

    /**
     * Asegura que el campo logmail esté establecido al momento del login.
     * Si está NULL o vacío, intenta usar el email del cliente.
     * Devuelve el nuevo logmail si se actualizó, o NULL si no hubo cambios.
     */
    private function ensureLogmailOnLogin($username)
    {
        try {
            // Recuperar el registro del cliente
            $row = $this->sql->row(sprintf(
                "SELECT email, logmail
                FROM %1\$s.clientes
                WHERE usuario = '%2\$s'
                LIMIT 1",
                $this->db_pw,
                $username
            ));

            // Si no hay registro o ya tiene logmail o no tiene email, no hacemos nada
            if (!$row || !empty($row['logmail']) || empty($row['email'])) {
                return null;
            }

            // Evitar duplicados: verificar que ningún otro cliente use ya ese logmail
            $other_client = $this->sql->row(sprintf(
                "SELECT 1
                FROM %1\$s.clientes
                WHERE logmail = '%2\$s'
                LIMIT 1",
                $this->db_pw,
                $row['email']
            ));

            if ($other_client) {
                return null;
            }

            // Actualizar logmail
            $updated = $this->sql->update_reg(
                $this->db_pw . '.clientes',
                array('logmail' => $row['email']),
                "usuario = '$username'"
            );

            if ($updated) {
                return $row['email'];
            }

            return null;
        } catch (\Exception $e) {
            error_log("Error actualizando logmail en login - Contrato: " . $this->contrato . ", Usuario: " . $username . ", Error: " . $e->getMessage());
            return null;
        }
    }

    private function cm_encripta($str, $salt = null)
    {
        if (!$salt) {
            $salt = md5(uniqid(rand(), 1));
        }
        $salt = substr($salt, 0, 8);
        return $salt . ":" . md5($salt . $str);
    }

    public function getValidToken($name = 'SI.v4:ControlPanel')
    {
        global $config;
        $hoy = date("Y-m-d 00:00:00", time());

        $query = sprintf("SELECT token 
            FROM api_tokens 
            WHERE contrato_id='%s' AND 
                name='%s' AND
                (expires_at>='%s' OR expires_at IS NULL)
            ORDER BY created_at DESC
            LIMIT 1", $this->contrato, $name, $hoy);

        if (!$token = $this->sql->row($query)) {
            // Generar un nuevo token encriptado
            $token = array(
                'token' => str_replace(':', '', $this->cm_encripta(uniqid(rand().time(), true)))
            );

            // Insertar el nuevo token en la base de datos
            $this->sql->insert("api_tokens", array(
                "contrato_id" => $this->contrato,
                "name" => $name,
                "token" => $token['token']
            ));
        }

        return 'Bearer ' . $token['token'];
    }

    public function getLogged()
    {
        return $this->logged;
    }

    public function getContrato()
    {
        return $this->contrato;
    }

    public function getSIUser()
    {
        return $this->si_user;
    }
    public function getSILogmail()
    {
        return $this->si_logmail;
    }
    public function getSIAsesor()
    {
        return $this->si_asesor;
    }
    public function getSucursal()
    {
        return $this->sucursal;
    }
    public function getPWAdmin()
    {
        return $this->pw_admin;
    }
    public function getUserType()
    {
        return $this->user_type;
    }
    public function getUserAccess()
    {
        return $this->access['username'];
    }
    public function getPasswordAccess()
    {
        return $this->access['password'];
    }
    public function getPassword()
    {
        return $this->password;
    }
}
