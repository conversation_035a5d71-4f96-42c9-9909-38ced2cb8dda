<?php

namespace Mulbin;

require_once __DIR__ . '/User.php';

/**
 * Clase para integración con API de Posts Inmobiliarios de Meteor
 * Permite crear y actualizar posts inmobiliarios desde el sistema padre (PHP)
 *
 * <AUTHOR>
 * @version 2.0
 * @date 2025-01-07
 */
class MeteorPost
{
    private $meteorBaseUrl;
    private $meteorApiKey;

    public function __construct($contrato)
    {
        $this->meteorBaseUrl = getenv('API_URL_METEOR') ? getenv('API_URL_METEOR') : 'http://localhost:3000/api';
        $this->meteorApiKey = getenv('API_KEY_METEOR') ? getenv('API_KEY_METEOR') : 'c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb';
    }

    /**
     * Crea un post inmobiliario en Meteor
     * @param object $apiInmueble Datos del inmueble desde la API
     * @param array $formData Datos del formulario ($_POST)
     * @return array Respuesta de la API de Meteor
     */
    public function createPost($apiInmueble, $formData)
    {
        // Validar que el usuario tiene meteor_id
        if (!isset($formData['author_id']) || empty($formData['author_id'])) {
            throw new \RuntimeException('No se encontró el ID del autor para crear el post en Meteor');
        }

        // Verificar si el usuario habilitó la publicación en el Feed
        $usarMultibolsa = isset($formData['usar_multibolsa']) && $formData['usar_multibolsa'] === 'Si';

        // Si el usuario deshabilitó "Publicar en el Feed", no publicar
        if (!$usarMultibolsa) {
            return array(
                'status' => 200,
                'data' => (object) array(
                    'message' => 'Post no creado - usuario deshabilitó publicación en Feed',
                    'skipped' => true
                )
            );
        }

        // Verificar si se debe publicar (solo si comparte comisión)
        $compartirVenta = isset($formData['comparto_comision']) && floatval($formData['comparto_comision']) > 0;
        $compartirRenta = isset($formData['rta_comparto_comision']) && floatval($formData['rta_comparto_comision']) > 0;

        // Si no comparte comisión de ningún tipo simplemente no se publica en Feed
        if (!$compartirVenta && !$compartirRenta) {
            return array(
                'status' => 200,
                'data' => (object) array(
                    'message' => 'Post no creado - no se comparte comisión',
                    'skipped' => true
                )
            );
        }

        // Preparar datos del post
        $postData = $this->preparePostData($apiInmueble, $formData);

        // Procesar destinatarios (socios específicos)
        $targetUserIds = $this->extractTargetUserIds($formData);
        if (!empty($targetUserIds)) {
            $postData['targetUserIds'] = $targetUserIds;
        }

        // dd($postData);

        try {
            // Log del intento de creación
            error_log("🔄 Iniciando creación de post en Meteor: " . json_encode(array(
                'inmueble_id' => isset($apiInmueble->id) ? $apiInmueble->id : 'N/A',
                'titulo' => isset($postData['title']) ? $postData['title'] : 'N/A',
                'precio' => isset($postData['price']) ? $postData['price'] : 'N/A',
                'tipo' => isset($postData['type']) ? $postData['type'] : 'N/A',
                'location' => isset($postData['location']) ? $postData['location'] : 'N/A',
                'authorId' => isset($postData['authorId']) ? $postData['authorId'] : 'N/A',
                'targetUserIds' => $targetUserIds
            )));

            $response = $this->makeMeteorRequest('/posts', 'POST', $postData);

            if ($response['status'] === 201) {
                $postId = isset($response['data']->post->_id) ? $response['data']->post->_id : 'N/A';
                error_log("✅ Post creado exitosamente en Meteor - ID: " . $postId . ", Inmueble: " . $apiInmueble->id);
            }

            return $response;

        } catch (\Exception $e) {
            error_log("❌ Error al crear post en Meteor: " . $e->getMessage());
            return array(
                'status' => 500,
                'data' => (object) array('error' => $e->getMessage())
            );
        }
    }

    /**
     * Extrae IDs de usuarios destinatarios del formulario
     * @param array $formData Datos del formulario
     * @return array Array de IDs de usuarios destinatarios
     */
    private function extractTargetUserIds($formData)
    {
        $targetUserIds = array();

        // Verificar si hay socios seleccionados en el formulario
        if (isset($formData['socios_seleccionados']) && !empty($formData['socios_seleccionados'])) {
            $targetUserIds = $this->parseUserIds($formData['socios_seleccionados']);
        }
        // En este caso se seleccionan todos los socios que vienen en `lista_socios`,
        elseif (
            $formData['usar_multibolsa'] === 'Si' && $formData['publicacion_publica'] === 'Si' &&
            (!isset($formData['socios_seleccionados']) || empty($formData['socios_seleccionados'])) &&
            isset($formData['lista_socios']) && !empty($formData['lista_socios'])
        ) {
            $targetUserIds = $this->parseUserIds($formData['lista_socios']);
        }

        // Limpiar y validar IDs
        $cleanTargetUserIds = array();
        foreach ($targetUserIds as $userId) {
            $userId = trim($userId);
            if (!empty($userId) && is_string($userId)) {
                $cleanTargetUserIds[] = $userId;
            }
        }

        return $cleanTargetUserIds;
    }

    /**
     * Parsea IDs de usuarios desde diferentes formatos (array, JSON, comma-separated)
     * @param mixed $userIds Datos de IDs en cualquier formato
     * @return array Array de IDs parseados
     */
    private function parseUserIds($userIds)
    {
        $parsedIds = array();

        if (is_array($userIds)) {
            // Si viene como array, usarlo directamente
            $parsedIds = $userIds;
        } elseif (is_string($userIds)) {
            // Si viene como string (JSON o comma-separated), parsearlo
            $decoded = json_decode($userIds, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $parsedIds = $decoded;
            } else {
                // Intentar split por comas
                $parsedIds = explode(',', $userIds);
                $parsedIds = array_map('trim', $parsedIds);
                $parsedIds = array_filter($parsedIds); // Remover elementos vacíos
            }
        }

        return $parsedIds;
    }

    /**
     * Prepara los datos del post para la API de Meteor
     * @param object $apiInmueble Datos del inmueble
     * @param array $formData Datos del formulario
     * @return array Datos estructurados para la API
     */
    private function preparePostData($apiInmueble, $formData)
    {
        // Determinar tipo de post (priorizar venta)
        $type = 'venta';
        $price = $apiInmueble->precios->venta;

        if ($apiInmueble->disponibilidad->renta && !$apiInmueble->disponibilidad->venta) {
            $type = 'renta';
            $price = $apiInmueble->precios->renta;
        }

        // Extraer recámaras y baños de campos personalizados
        $bedrooms = $this->extractCustomField($apiInmueble->campos_personalizados, 'ci_recamaras', 0);
        $bathrooms = $this->extractCustomField($apiInmueble->campos_personalizados, 'ci_banos', 0);
        $area = $this->extractCustomField($apiInmueble->campos_personalizados, 'ci_terreno', 0);

        // Convertir área de "500.00 m²" a número
        if (is_string($area)) {
            $area = (float) preg_replace('/[^0-9.]/', '', $area);
        }

        // Convertir baños de "4 ½" a número
        if (is_string($bathrooms)) {
            $bathrooms = (float) str_replace(
                array('½', '¼', '¾'),
                array('.5', '.25', '.75'),
                preg_replace('/[^0-9½¼¾.]/', '', $bathrooms)
            );
        }

        // Generar título
        $title = $this->generateTitle($apiInmueble, $type);

        // Generar descripción limpia
        $description = $this->generateDescription($apiInmueble);

        // Mapear ubicación a zones conocidas
        $location = $this->defineLocation($apiInmueble->ubicacion);

        // Extraer características adicionales
        $features = $this->extractFeatures($apiInmueble->campos_personalizados);

        $postData = array(
            'type' => $type,
            'title' => $title,
            'description' => $description,
            'price' => (float) $price,
            'location' => $location,
            'propertyType' => $this->determinePropertyType($apiInmueble),
            'bedrooms' => (int) $bedrooms,
            'bathrooms' => (float) $bathrooms,
            'area' => (float) $area,
            'features' => $features,
            'authorId' => $formData['author_id'],
            'contactPreference' => 'phone', // Valor por defecto
            'urgency' => 'media',
            'images' => $this->extractImages($apiInmueble),

            // Metadatos del sistema padre
            'externalId' => (string) $apiInmueble->id,
            'externalKey' => $apiInmueble->clave_interna,
            'currency' => isset($apiInmueble->precios->moneda) ? $apiInmueble->precios->moneda : 'MXP',

            // Datos de comisión
            'commissionData' => $this->prepareCommissionData($formData, $type)
        );

        return $postData;
    }

    /**
     * Extrae las imágenes del inmueble desde la API
     */
    private function extractImages($apiInmueble)
    {
        if (isset($apiInmueble->fotos) && is_array($apiInmueble->fotos)) {
            return $apiInmueble->fotos;
        }
        return array();
    }

    /**
     * Extrae valor de un campo personalizado
     */
    private function extractCustomField($campos, $variable, $default = null)
    {
        foreach ($campos as $campo) {
            if ($campo->variable === $variable) {
                return $campo->valor;
            }
        }
        return $default;
    }

    /**
     * Genera título atractivo para el post
     */
    private function generateTitle($apiInmueble, $type)
    {
        $tipoPropiedad = $this->determinePropertyType($apiInmueble);
        // $ciudad = $apiInmueble->ubicacion->ciudad;
        $colonia = $apiInmueble->ubicacion->colonia;
        $clave = $apiInmueble->clave_interna;

        $actionText = $type === 'venta' ? 'en venta' : 'en renta';

        return sprintf(
            "((%s %s)) › ((%s)) › ((%s))",
            ucfirst($tipoPropiedad),
            $actionText,
            $colonia,
            $clave
        );
    }

    /**
     * Genera descripción limpia del HTML
     */
    private function generateDescription($apiInmueble)
    {
        if (empty($apiInmueble->descripcion->anuncio)) {
            return "Excelente propiedad ubicada en {$apiInmueble->ubicacion->colonia}, {$apiInmueble->ubicacion->ciudad}.";
        }

        // Limpiar HTML y extraer texto
        $description = strip_tags($apiInmueble->descripcion->anuncio);
        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
        $description = preg_replace('/\s+/', ' ', trim($description));

        // Limitar longitud
        if (strlen($description) > 500) {
            $description = substr($description, 0, 497) . '...';
        }

        return $description;
    }

    /**
     * Define la ubicación del inmueble
     */
    private function defineLocation($ubicacion)
    {
        return $ubicacion->colonia . ', ' . $ubicacion->ciudad;
    }

    /**
     * Determina el tipo de propiedad
     */
    private function determinePropertyType($apiInmueble)
    {
        return $apiInmueble->tipo->nombre;
    }

    /**
     * Extrae características de campos personalizados
     */
    private function extractFeatures($campos)
    {
        $features = array();

        foreach ($campos as $campo) {
            switch ($campo->variable) {
                case 'ci_alberca':
                    if (strtolower($campo->valor) === 'si' ||
                        strpos(strtolower($campo->valor), 'equipada') !== false) {
                        $features[] = 'alberca';
                    }
                    break;
                case 'ci_estacionamiento':
                    if (strpos(strtolower($campo->valor), 'auto') !== false) {
                        $features[] = 'estacionamiento';
                    }
                    break;
                case 'ci_gas_estacionario':
                    if (strtolower($campo->valor) === 'si') {
                        $features[] = 'gas_estacionario';
                    }
                    break;
                case 'ci_cisterna':
                    if (strtolower($campo->valor) === 'si') {
                        $features[] = 'cisterna';
                    }
                    break;
            }
        }

        return $features;
    }

    /**
     * Prepara datos de comisión
     */
    private function prepareCommissionData($formData, $type)
    {
        $commissionData = array();

        if ($type === 'venta' && isset($formData['comparto_comision'])) {
            $commissionData['venta'] = array(
                'porcentaje' => (float) $formData['comparto_comision'],
                'tipo' => isset($formData['t_comparto_comision']) ? $formData['t_comparto_comision'] : 'sobre precio'
            );
        }

        if ($type === 'renta' && isset($formData['rta_comparto_comision'])) {
            $commissionData['renta'] = array(
                'porcentaje' => (float) $formData['rta_comparto_comision'],
                'tipo' => 'sobre comision'
            );
        }

        return $commissionData;
    }

    /**
     * Realiza petición a la API de Meteor
     */
    private function makeMeteorRequest($endpoint, $method = 'GET', $data = null)
    {
        $url = $this->meteorBaseUrl . $endpoint;

        $headers = array(
            'Authorization: ApiKey ' . $this->meteorApiKey,
            'Content-Type: application/json'
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        if ($data && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return array(
            'status' => $httpCode,
            'data' => json_decode($response)
        );
    }

    /**
     * Actualiza un post existente en Meteor
     * @param string $postId ID del post en Meteor
     * @param object $apiInmueble Datos del inmueble actualizados
     * @param array $formData Datos del formulario
     * @return array Respuesta de la API
     */
    public function updatePost($postId, $apiInmueble, $formData)
    {
        $postData = $this->preparePostData($apiInmueble, $formData);

        // 🆕 NUEVO: Procesar destinatarios para actualización
        $targetUserIds = $this->extractTargetUserIds($formData);
        if (!empty($targetUserIds)) {
            $postData['targetUserIds'] = $targetUserIds;
        }

        try {
            $response = $this->makeMeteorRequest('/posts/' . $postId, 'PUT', $postData);

            if ($response['status'] === 200) {
                error_log("✅ Post actualizado exitosamente en Meteor - ID: " . $postId);
            }

            return $response;

        } catch (\Exception $e) {
            error_log("❌ Error al actualizar post en Meteor: " . $e->getMessage());
            return array(
                'status' => 500,
                'data' => (object) array('error' => $e->getMessage())
            );
        }
    }

    /**
     * Busca un post existente por external ID
     * @param string $externalId ID del inmueble en el sistema padre
     * @return array|false Datos del post si existe, false si no
     */
    public function findPostByExternalId($externalId)
    {
        try {
            $response = $this->makeMeteorRequest('/posts?externalId=' . urlencode($externalId), 'GET');

            if ($response['status'] === 200 && isset($response['data']->posts) && count($response['data']->posts) > 0) {
                return $response['data']->posts[0]; // Retornar el primer post encontrado
            }

            return false;

        } catch (\Exception $e) {
            error_log("❌ Error al buscar post por ID externo: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Valida si los datos son suficientes para crear un post
     * @param object $apiInmueble
     * @param array $formData
     * @return array Array con 'valid' (bool) y 'errors' (array)
     */
    public function validatePostData($apiInmueble, $formData)
    {
        $errors = array();

        // Validar autor
        if (!isset($formData['author_id']) || empty($formData['author_id'])) {
            $errors[] = 'ID del autor (meteor_user_id) es requerido';
        }

        // Validar datos básicos del inmueble
        if (empty($apiInmueble->id)) {
            $errors[] = 'ID del inmueble es requerido';
        }

        if (empty($apiInmueble->ubicacion->ciudad)) {
            $errors[] = 'Ciudad del inmueble es requerida';
        }

        // Validar que al menos un precio esté disponible
        $hasPrice = false;
        if ($apiInmueble->disponibilidad->venta && $apiInmueble->precios->venta > 0) {
            $hasPrice = true;
        }
        if ($apiInmueble->disponibilidad->renta && $apiInmueble->precios->renta > 0) {
            $hasPrice = true;
        }

        if (!$hasPrice) {
            $errors[] = 'El inmueble debe tener al menos un precio válido (venta o renta)';
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Obtiene un resumen de la integración para logging
     * @param object $apiInmueble
     * @param array $formData
     * @return string
     */
    public function getIntegrationSummary($apiInmueble, $formData)
    {
        $summary = array(
            'inmueble_id' => isset($apiInmueble->id) ? $apiInmueble->id : 'N/A',
            'clave' => isset($apiInmueble->clave_interna) ? $apiInmueble->clave_interna : 'N/A',
            'ciudad' => isset($apiInmueble->ubicacion->ciudad) ? $apiInmueble->ubicacion->ciudad : 'N/A',
            'precio_venta' => isset($apiInmueble->precios->venta) ? $apiInmueble->precios->venta : 0,
            'precio_renta' => isset($apiInmueble->precios->renta) ? $apiInmueble->precios->renta : 0,
            'usar_multibolsa' => isset($formData['usar_multibolsa']) ? $formData['usar_multibolsa'] : 'No',
            'comparte_venta' => isset($formData['comparto_comision']) && floatval($formData['comparto_comision']) > 0,
            'comparte_renta' => isset($formData['rta_comparto_comision']) && floatval($formData['rta_comparto_comision']) > 0,
            'author_id' => isset($formData['author_id']) ? $formData['author_id'] : 'N/A'
        );

        return json_encode($summary, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Elimina posts relacionados con un inmueble por su externalId
     * @param string $externalId ID del inmueble en el sistema padre
     * @return array Respuesta de la API con detalles de eliminación
     */
    public function deletePostsByExternalId($externalId)
    {
        try {
            // Log del intento de eliminación
            error_log("🔄 Iniciando eliminación de posts en Meteor para inmueble externalId: " . $externalId);

            $response = $this->makeMeteorRequest('/posts/by-external-id/' . urlencode($externalId), 'DELETE');

            if ($response['status'] === 200) {
                $deletedCount = isset($response['data']->totalDeleted) ? $response['data']->totalDeleted : 0;
                $totalFound = isset($response['data']->totalFound) ? $response['data']->totalFound : 0;

                error_log("✅ Posts eliminados exitosamente de Meteor - ExternalId: {$externalId}, Encontrados: {$totalFound}, Eliminados: {$deletedCount}");

                return array(
                    'status' => 200,
                    'success' => true,
                    'data' => $response['data'],
                    'message' => "Posts eliminados exitosamente de Meteor"
                );

            } elseif ($response['status'] === 404) {
                // No se encontraron posts, esto no es un error crítico
                error_log("ℹ️ No se encontraron posts en Meteor para inmueble externalId: {$externalId}");

                return array(
                    'status' => 200,
                    'success' => true,
                    'data' => (object) array(
                        'totalFound' => 0,
                        'totalDeleted' => 0,
                        'message' => 'No se encontraron posts para eliminar'
                    ),
                    'message' => "No se encontraron posts para eliminar"
                );

            } else {
                $errorDetail = isset($response['data']->error) ? $response['data']->error : 'Error desconocido';
                error_log("⚠️ Error al eliminar posts en Meteor para inmueble {$externalId}: {$errorDetail}");

                return array(
                    'status' => $response['status'],
                    'success' => false,
                    'data' => $response['data'],
                    'message' => "Error al eliminar posts: {$errorDetail}"
                );
            }

        } catch (\Exception $e) {
            error_log("❌ Excepción al eliminar posts en Meteor para inmueble {$externalId}: " . $e->getMessage());

            return array(
                'status' => 500,
                'success' => false,
                'data' => (object) array('error' => $e->getMessage()),
                'message' => "Excepción al eliminar posts: " . $e->getMessage()
            );
        }
    }

    /**
     * Elimina un post específico por su ID de Meteor
     * @param string $postId ID del post en Meteor
     * @return array Respuesta de la API
     */
    public function deletePostById($postId)
    {
        try {
            error_log("🔄 Eliminando post específico en Meteor - ID: " . $postId);

            $response = $this->makeMeteorRequest('/posts/' . urlencode($postId), 'DELETE');

            if ($response['status'] === 200) {
                error_log("✅ Post eliminado exitosamente de Meteor - ID: {$postId}");
                return array(
                    'status' => 200,
                    'success' => true,
                    'data' => $response['data'],
                    'message' => "Post eliminado exitosamente"
                );
            } else {
                $errorDetail = isset($response['data']->error) ? $response['data']->error : 'Error desconocido';
                error_log("⚠️ Error al eliminar post en Meteor - ID {$postId}: {$errorDetail}");

                return array(
                    'status' => $response['status'],
                    'success' => false,
                    'data' => $response['data'],
                    'message' => "Error al eliminar post: {$errorDetail}"
                );
            }

        } catch (\Exception $e) {
            error_log("❌ Excepción al eliminar post en Meteor - ID {$postId}: " . $e->getMessage());

            return array(
                'status' => 500,
                'success' => false,
                'data' => (object) array('error' => $e->getMessage()),
                'message' => "Excepción al eliminar post: " . $e->getMessage()
            );
        }
    }
}
