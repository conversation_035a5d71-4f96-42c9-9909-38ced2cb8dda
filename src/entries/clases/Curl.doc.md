# Clase Curl - Documentación

## Descripción

La clase `Curl` es una wrapper completa para la librería cURL de PHP, diseñada para facilitar el consumo de servicios web y APIs. Es compatible con PHP 5.6 y proporciona una interfaz fluida y fácil de usar para realizar peticiones HTTP.

## Características

- ✅ Compatible con PHP 5.6+
- ✅ Soporte para métodos HTTP: GET, POST, PUT, DELETE, PATCH
- ✅ Autenticación básica y Bearer Token
- ✅ Manejo de headers personalizados
- ✅ Soporte para JSON, XML y form-data
- ✅ Descarga y subida de archivos
- ✅ Manejo de cookies y SSL
- ✅ Interfaz fluida (method chaining)
- ✅ Manejo de errores robusto

## Instalación

Simplemente incluye la clase en tu proyecto:

```php
require_once 'clases/Curl.php';
```

## Uso Básico

### Crear una instancia

```php
$curl = new Curl();
```

### Petición GET simple

```php
$curl = new Curl();
$response = $curl->get('https://api.ejemplo.com/users')
                 ->getResponse();

if ($curl->isSuccess()) {
    echo "Respuesta: " . $response;
} else {
    echo "Error: " . $curl->getError();
}
```

### Petición POST con datos JSON

```php
$curl = new Curl();
$data = array(
    'nombre' => 'Juan',
    'email' => '<EMAIL>'
);

$response = $curl->post('https://api.ejemplo.com/users', $data, 'application/json')
                 ->getJsonResponse();

if ($curl->isSuccess()) {
    print_r($response);
}
```

## Métodos Principales

### Configuración

#### `setUrl($url)`
Establece la URL de destino.

```php
$curl->setUrl('https://api.ejemplo.com/endpoint');
```

#### `setTimeout($timeout)`
Establece el timeout en segundos (por defecto: 30).

```php
$curl->setTimeout(60); // 60 segundos
```

#### `setHeaders($headers)`
Establece múltiples headers.

```php
$curl->setHeaders(array(
    'Content-Type: application/json',
    'Accept: application/json',
    'X-API-Key: tu-api-key'
));
```

#### `addHeader($header)`
Añade un header individual.

```php
$curl->addHeader('Authorization: Bearer token123');
```

#### `setSslVerify($verify)`
Habilita/deshabilita verificación SSL.

```php
$curl->setSslVerify(false); // Deshabilitar para desarrollo
```

### Autenticación

#### `setBasicAuth($username, $password)`
Configura autenticación básica.

```php
$curl->setBasicAuth('usuario', 'contraseña');
```

#### `setBearerToken($token)`
Configura token Bearer.

```php
$curl->setBearerToken('tu-bearer-token');
```

### Métodos HTTP

#### `get($url, $params)`
Realiza petición GET.

```php
// GET simple
$curl->get('https://api.ejemplo.com/users');

// GET con parámetros
$curl->get('https://api.ejemplo.com/users', array(
    'page' => 1,
    'limit' => 10
));
```

#### `post($url, $data, $contentType)`
Realiza petición POST.

```php
// POST con form-data
$curl->post('https://api.ejemplo.com/users', array(
    'nombre' => 'Juan',
    'email' => '<EMAIL>'
));

// POST con JSON
$curl->post('https://api.ejemplo.com/users', $data, 'application/json');
```

#### `put($url, $data, $contentType)`
Realiza petición PUT.

```php
$curl->put('https://api.ejemplo.com/users/1', $data, 'application/json');
```

#### `delete($url, $data, $contentType)`
Realiza petición DELETE.

```php
$curl->delete('https://api.ejemplo.com/users/1');
```

#### `patch($url, $data, $contentType)`
Realiza petición PATCH.

```php
$curl->patch('https://api.ejemplo.com/users/1', $data, 'application/json');
```

### Obtener Respuestas

#### `getResponse()`
Obtiene la respuesta raw.

```php
$response = $curl->getResponse();
```

#### `getJsonResponse($assoc)`
Obtiene la respuesta como JSON decodificado.

```php
$data = $curl->getJsonResponse(); // Array asociativo
$data = $curl->getJsonResponse(false); // Objeto
```

#### `getHttpCode()`
Obtiene el código de estado HTTP.

```php
$code = $curl->getHttpCode(); // 200, 404, 500, etc.
```

#### `getInfo()`
Obtiene información detallada de la petición.

```php
$info = $curl->getInfo();
echo "Tiempo total: " . $info['total_time'];
```

### Verificación de Estado

#### `isSuccess()`
Verifica si la petición fue exitosa (código 200-299).

```php
if ($curl->isSuccess()) {
    echo "¡Éxito!";
}
```

#### `hasError()`
Verifica si hubo algún error.

```php
if ($curl->hasError()) {
    echo "Error: " . $curl->getError();
}
```

### Manejo de Archivos

#### `downloadFile($url, $filePath)`
Descarga un archivo.

```php
$success = $curl->downloadFile(
    'https://ejemplo.com/archivo.pdf',
    '/ruta/local/archivo.pdf'
);

if ($success) {
    echo "Archivo descargado exitosamente";
}
```

#### `uploadFile($url, $filePath, $fieldName, $additionalFields)`
Sube un archivo.

```php
$curl->uploadFile(
    'https://api.ejemplo.com/upload',
    '/ruta/al/archivo.jpg',
    'imagen',
    array('descripcion' => 'Mi imagen')
);
```

## Ejemplos Avanzados

### API REST completa

```php
class ApiClient {
    private $curl;
    private $baseUrl;
    private $apiKey;
    
    public function __construct($baseUrl, $apiKey) {
        $this->curl = new Curl();
        $this->baseUrl = $baseUrl;
        $this->apiKey = $apiKey;
        
        // Configuración común
        $this->curl->setTimeout(30)
                   ->setSslVerify(false)
                   ->addHeader('X-API-Key: ' . $this->apiKey)
                   ->addHeader('Content-Type: application/json');
    }
    
    public function getUsers($page = 1) {
        return $this->curl->get($this->baseUrl . '/users', array('page' => $page))
                          ->getJsonResponse();
    }
    
    public function createUser($userData) {
        return $this->curl->post($this->baseUrl . '/users', $userData, 'application/json')
                          ->getJsonResponse();
    }
    
    public function updateUser($id, $userData) {
        return $this->curl->put($this->baseUrl . '/users/' . $id, $userData, 'application/json')
                          ->getJsonResponse();
    }
    
    public function deleteUser($id) {
        $this->curl->delete($this->baseUrl . '/users/' . $id);
        return $this->curl->isSuccess();
    }
}

// Uso
$api = new ApiClient('https://api.ejemplo.com', 'tu-api-key');

// Obtener usuarios
$users = $api->getUsers(1);

// Crear usuario
$newUser = $api->createUser(array(
    'nombre' => 'Juan Pérez',
    'email' => '<EMAIL>'
));
```

### Consumo de servicios con autenticación

```php
// Servicio con Bearer Token
$curl = new Curl();
$token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...';

$response = $curl->setBearerToken($token)
                 ->get('https://api.protegida.com/datos')
                 ->getJsonResponse();

// Servicio con autenticación básica
$curl->reset() // Resetear para nueva petición
     ->setBasicAuth('usuario', 'contraseña')
     ->get('https://api.basica.com/datos');
```

### Manejo de errores robusto

```php
function consumirAPI($url, $data = null) {
    $curl = new Curl();
    $curl->setTimeout(10)
         ->setSslVerify(false);
    
    try {
        if ($data) {
            $curl->post($url, $data, 'application/json');
        } else {
            $curl->get($url);
        }
        
        if ($curl->hasError()) {
            throw new Exception('Error cURL: ' . $curl->getError());
        }
        
        $httpCode = $curl->getHttpCode();
        if ($httpCode >= 400) {
            throw new Exception('Error HTTP: ' . $httpCode);
        }
        
        return $curl->getJsonResponse();
        
    } catch (Exception $e) {
        error_log('Error en API: ' . $e->getMessage());
        return false;
    }
}

// Uso
$resultado = consumirAPI('https://api.ejemplo.com/datos');
if ($resultado !== false) {
    // Procesar datos
    print_r($resultado);
}
```

### Subida de múltiples archivos

```php
function subirArchivos($archivos, $url) {
    $curl = new Curl();
    $resultados = array();
    
    foreach ($archivos as $archivo) {
        if (file_exists($archivo)) {
            $curl->reset()
                 ->uploadFile($url, $archivo, 'archivo');
            
            $resultados[] = array(
                'archivo' => $archivo,
                'exitoso' => $curl->isSuccess(),
                'respuesta' => $curl->getResponse(),
                'codigo' => $curl->getHttpCode()
            );
        }
    }
    
    return $resultados;
}

// Uso
$archivos = array(
    '/ruta/imagen1.jpg',
    '/ruta/imagen2.png',
    '/ruta/documento.pdf'
);

$resultados = subirArchivos($archivos, 'https://api.ejemplo.com/upload');
```

## Tipos de Contenido Soportados

- `application/json` - Para APIs REST
- `application/xml` - Para servicios XML
- `application/x-www-form-urlencoded` - Para formularios web
- `multipart/form-data` - Para subida de archivos

## Códigos de Estado HTTP Comunes

- **200**: OK - Petición exitosa
- **201**: Created - Recurso creado exitosamente
- **400**: Bad Request - Petición malformada
- **401**: Unauthorized - No autorizado
- **403**: Forbidden - Prohibido
- **404**: Not Found - Recurso no encontrado
- **500**: Internal Server Error - Error del servidor

## Mejores Prácticas

1. **Siempre verificar errores**: Usar `isSuccess()` y `hasError()`
2. **Establecer timeouts apropiados**: Para evitar bloqueos
3. **Manejar SSL correctamente**: Verificar en producción, deshabilitar solo en desarrollo
4. **Usar headers apropiados**: Especialmente Content-Type
5. **Resetear la instancia**: Usar `reset()` para peticiones múltiples
6. **Logging de errores**: Registrar errores para debugging

## Limitaciones

- Compatible con PHP 5.6+
- Requiere extensión cURL habilitada
- Para archivos grandes, considerar límites de memoria PHP
- Timeouts pueden necesitar ajuste según el servicio

## Soporte

Para reportar bugs o solicitar nuevas características, contacta al equipo de MulbinComponents.
