<?php

namespace Auth;

class LaravelPasswordManager
{
    /**
     * Verifica si la contraseña coincide con el hash de Laravel
     * 
     * @param string $password La contraseña en texto plano
     * @param string $hashedPassword El hash de la contraseña almacenado en la base de datos
     * @return bool Verdadero si la contraseña coincide, falso en caso contrario
     */
    public function verify($password, $hashedPassword)
    {
        // Laravel utiliza password_verify() de PHP para verificar contraseñas
        if (password_verify($password, $hashedPassword)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Crea un hash de contraseña compatible con Laravel
     * 
     * @param string $password La contraseña en texto plano
     * @return string El hash de la contraseña
     */
    public function hash($password)
    {
        // Laravel utiliza bcrypt por defecto (BCRYPT, cost=10)
        return password_hash($password, PASSWORD_BCRYPT);
    }
    
    /**
     * Verifica si el hash necesita ser actualizado (rehash)
     * 
     * @param string $hashedPassword El hash de la contraseña almacenado
     * @return bool Verdadero si el hash necesita ser actualizado
     */
    public function needsRehash($hashedPassword)
    {
        return password_needs_rehash($hashedPassword, PASSWORD_BCRYPT);
    }
    
    /**
     * Método compatible con el antiguo sistema
     * Intenta verificar usando el nuevo método y si falla, intenta con el antiguo
     * 
     * @param string $password La contraseña en texto plano
     * @param string $hashedPassword El hash almacenado
     * @param callable $legacyVerify Función del método antiguo de verificación
     * @return bool Verdadero si alguno de los métodos verifica correctamente
     */
    public function verifyWithLegacyFallback($password, $hashedPassword, $legacyVerify = null)
    {
        // Primero intentamos con el método nuevo de Laravel
        if ($this->verify($password, $hashedPassword)) {
            return true;
        }
        
        // // Si existe un método legacy y el hash no parece ser de Laravel
        // if ($legacyVerify && !$this->isLaravelHash($hashedPassword)) {
        //     return $legacyVerify($password, $hashedPassword);
        // }
        
        return false;
    }
    
    /**
     * Verifica si un hash parece ser de formato Laravel
     * 
     * @param string $hash El hash a verificar
     * @return bool Verdadero si parece un hash de Laravel
     */
    private function isLaravelHash($hash)
    {
        // Los hashes de Laravel comienzan con $2y$ (bcrypt)
        return strpos($hash, '$2y$') === 0;
    }

    private function cm_encripta($str, $salt = null)
    {
        if (!$salt)
            $salt = md5(uniqid(rand(), 1));
        $salt = substr($salt, 0, 8);
        return $salt . ":" . md5($salt . $str);
    }

} 