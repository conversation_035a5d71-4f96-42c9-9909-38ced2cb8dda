<?php

namespace Mu<PERSON>bin\Models;

use Mulbin\Models\Model;

class SISocios extends Model
{
    protected $table = 'bolsa_inmobiliaria';
    protected $primaryKey = 'id';

    /**
     * Los atributos que son asignables masivamente.
     *
     * @var array
     */
    protected $fillable = [
        'contrato_solicitante',
        'contrato_solicitado',
        'conexion_aceptada',
        'bak_solicitante',
        'bak_solicitado',
        'token',
        'token_expires_at',
        'fecha_aceptacion',
        'authorized_at',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Obtiene el socio solicitante.
     */
    public function getSolicitante()
    {
        if (!$this->contrato_solicitante) {
            return null;
        }

        return SIConfig::find($this->contrato_solicitante);
    }

    /**
     * Obtiene el socio solicitado.
     */
    public function getSolicitado()
    {
        if (!$this->contrato_solicitado) {
            return null;
        }

        return SIConfig::find($this->contrato_solicitado);
    }

    /**
     * Verifica si la conexión está aceptada.
     *
     * @return bool
     */
    public function isAceptada()
    {
        return $this->conexion_aceptada === 'Si';
    }

    /**
     * Verifica si el token ha expirado.
     *
     * @return bool
     */
    public function isTokenExpired()
    {
        if (!$this->token_expires_at) {
            return true;
        }

        $expiresAt = new \DateTime($this->token_expires_at);
        $now = new \DateTime();

        return $expiresAt < $now;
    }

    /**
     * Verifica si la solicitud está autorizada.
     *
     * @return bool
     */
    public function isAuthorized()
    {
        return $this->authorized_at !== null;
    }

    /**
     * Marca la conexión como aceptada.
     *
     * @return bool
     */
    public function aceptarConexion()
    {
        $this->conexion_aceptada = 'Si';
        $this->fecha_aceptacion = date('Y-m-d H:i:s');
        $this->authorized_at = date('Y-m-d H:i:s');
        $this->token = null;

        return $this->save();
    }

    /**
     * Marca la conexión como rechazada.
     *
     * @return bool
     */
    public function rechazarConexion()
    {
        $this->contrato_solicitante = null;
        $this->contrato_solicitado = null;
        $this->conexion_aceptada = 'No';
        $this->fecha_aceptacion = date('Y-m-d H:i:s');
        $this->authorized_at = date('Y-m-d H:i:s');
        $this->deleted_at = date('Y-m-d H:i:s');
        $this->token = null;

        return $this->save();
    }

    /**
     * Genera un nuevo token de seguridad.
     *
     * @param int $expirationHours Horas de expiración del token
     * @return string
     */
    public function generarToken($expirationHours = 24)
    {
        // Compatible con PHP 5.6
        $this->token = bin2hex(openssl_random_pseudo_bytes(32));
        $this->token_expires_at = date('Y-m-d H:i:s', strtotime("+{$expirationHours} hours"));

        $this->save();

        return $this->token;
    }

    /**
     * Invalida el token actual.
     *
     * @return bool
     */
    public function invalidarToken()
    {
        $this->token = null;
        $this->token_expires_at = null;

        return $this->save();
    }

    /**
     * Busca solicitudes pendientes por contrato.
     *
     * @param int $contrato
     * @return array
     */
    public static function buscarSolicitudesPendientes($contrato)
    {
        $query = static::where('contrato_solicitado', $contrato);
        $query = $query->where('conexion_aceptada', 'No');
        return $query->get();
    }

    /**
     * Busca conexiones aceptadas por contrato.
     *
     * @param int $contrato
     * @return array
     */
    public static function buscarConexionesAceptadas($contrato)
    {
        // Buscar como solicitante
        $query1 = static::where('conexion_aceptada', 'Si');
        $query1 = $query1->where('contrato_solicitante', $contrato);
        $solicitante = $query1->get();

        // Buscar como solicitado
        $query2 = static::where('conexion_aceptada', 'Si');
        $query2 = $query2->where('contrato_solicitado', $contrato);
        $solicitado = $query2->get();

        // Combinar resultados
        return array_merge($solicitante, $solicitado);
    }

    /**
     * Verifica si existe una solicitud entre dos contratos.
     *
     * @param int $contrato1
     * @param int $contrato2
     * @return bool
     */
    public static function existeSolicitud($contrato1, $contrato2)
    {
        $query1 = static::where('contrato_solicitante', $contrato1);
        $query1 = $query1->where('contrato_solicitado', $contrato2);
        $solicitud = $query1->first();

        if (!$solicitud) {
            $query2 = static::where('contrato_solicitante', $contrato2);
            $query2 = $query2->where('contrato_solicitado', $contrato1);
            $solicitud = $query2->first();
        }

        return $solicitud !== null;
    }

    /**
     * Crea una nueva solicitud de conexión.
     *
     * @param int $contratoSolicitante
     * @param int $contratoSolicitado
     * @param int $bakSolicitante
     * @param int $bakSolicitado
     * @return SISocios
     */
    public static function crearSolicitud($contratoSolicitante, $contratoSolicitado, $bakSolicitante, $bakSolicitado)
    {
        // Compatible con PHP 5.6 - crear instancia y luego llenar
        $solicitud = new static();
        $solicitud->fill([
            'contrato_solicitante' => $contratoSolicitante,
            'contrato_solicitado' => $contratoSolicitado,
            'conexion_aceptada' => 'No',
            'bak_solicitante' => $bakSolicitante,
            'bak_solicitado' => $bakSolicitado,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $solicitud->save();

        return $solicitud;
    }

    /**
     * Busca solicitudes por token.
     *
     * @param string $token
     * @return SISocios|null
     */
    public static function buscarPorToken($token)
    {
        return static::where('token', $token)->first();
    }

    /**
     * Obtiene el estado de la solicitud como texto.
     *
     * @return string
     */
    public function getEstadoTexto()
    {
        if ($this->conexion_aceptada === 'Si') {
            return 'Aceptada';
        } elseif ($this->conexion_aceptada === 'No') {
            return 'Pendiente';
        }

        return 'Desconocido';
    }

    /**
     * Obtiene la fecha de solicitud formateada.
     *
     * @return string
     */
    public function getFechaSolicitudFormateada($formato = 'corto')
    {
        if (!$this->created_at) {
            return '';
        }

        $timestamp = strtotime($this->created_at);

        switch ($formato) {
            case 'largo':
                // Definir nombres de días y meses en español
                $dias = array('Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado');
                $meses = array('', 'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
                              'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre');

                $diaSemana = $dias[date('w', $timestamp)];
                $dia = date('j', $timestamp);
                $mes = $meses[date('n', $timestamp)];
                $anio = date('Y', $timestamp);

                return $diaSemana . ', ' . $dia . ' de ' . $mes . ' de ' . $anio;

            case 'medio':
                return date('j/n/Y \a \l\a\s H:i', $timestamp);

            case 'corto':
            default:
                return date('d/m/Y H:i', $timestamp);
        }
    }

    /**
     * Obtiene la fecha de aceptación formateada.
     *
     * @return string
     */
    public function getFechaAceptacionFormateada()
    {
        if (!$this->fecha_aceptacion) {
            return '';
        }

        return date('d/m/Y H:i', strtotime($this->fecha_aceptacion));
    }
}
