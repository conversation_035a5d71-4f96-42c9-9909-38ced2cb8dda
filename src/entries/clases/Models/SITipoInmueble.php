<?php

namespace Mu<PERSON>bin\Models;

use Mulbin\Models\Model;

class SITipoInmueble extends Model
{
    protected $table = 'tipo_inmuebles';
    protected $primaryKey = 'clave';
    public $timestamps = false;

    /**
     * Los atributos que son asignables masivamente.
     *
     * @var array
     */
    protected $fillable = [
        'clave',
        'id_string',
        'ES',
        'EN',
        'tipo',
        'p_tipo',
        'tipo_esp',
        'tipo_ing',
        'tipo_fra',
        'p_tipo_esp',
        'p_tipo_ing',
        'p_tipo_fra',
        'visitas',
        'slug'
    ];

    /**
     * Busca un tipo de inmueble por su clave.
     *
     * @param int $clave
     * @return SITipoInmueble|null
     */
    public static function buscarPorClave($clave)
    {
        return static::find($clave);
    }

    /**
     * Busca un tipo de inmueble por su id_string.
     *
     * @param string $id_string
     * @return SITipoInmueble|null
     */
    public static function buscarPorIdString($id_string)
    {
        return static::where('id_string', $id_string)->first();
    }

    /**
     * Obtiene el nombre del tipo en español.
     *
     * @return string
     */
    public function getNombreEspanol()
    {
        return !empty($this->tipo_esp) ? $this->tipo_esp : $this->ES;
    }

    /**
     * Obtiene el nombre del tipo en inglés.
     *
     * @return string
     */
    public function getNombreIngles()
    {
        return !empty($this->tipo_ing) ? $this->tipo_ing : $this->EN;
    }

    /**
     * Obtiene el nombre del tipo en francés.
     *
     * @return string
     */
    public function getNombreFrances()
    {
        return !empty($this->tipo_fra) ? $this->tipo_fra : $this->tipo;
    }

    /**
     * Obtiene el nombre del plural en español.
     *
     * @return string
     */
    public function getPluralEspanol()
    {
        return !empty($this->p_tipo_esp) ? $this->p_tipo_esp : $this->p_tipo;
    }

    /**
     * Obtiene el nombre del plural en inglés.
     *
     * @return string
     */
    public function getPluralIngles()
    {
        return !empty($this->p_tipo_ing) ? $this->p_tipo_ing : $this->p_tipo;
    }

    /**
     * Obtiene el nombre del plural en francés.
     *
     * @return string
     */
    public function getPluralFrances()
    {
        return !empty($this->p_tipo_fra) ? $this->p_tipo_fra : $this->p_tipo;
    }

    /**
     * Obtiene el nombre según el idioma especificado.
     *
     * @param string $idioma 'es', 'en', 'fr'
     * @param bool $plural Si se quiere el nombre en plural
     * @return string
     */
    public function getNombrePorIdioma($idioma = 'es', $plural = false)
    {
        switch (strtolower($idioma)) {
            case 'en':
                return $plural ? $this->getPluralIngles() : $this->getNombreIngles();
            case 'fr':
                return $plural ? $this->getPluralFrances() : $this->getNombreFrances();
            case 'es':
            default:
                return $plural ? $this->getPluralEspanol() : $this->getNombreEspanol();
        }
    }

    /**
     * Incrementa el contador de visitas.
     *
     * @return bool
     */
    public function incrementarVisitas()
    {
        $this->visitas = intval($this->visitas) + 1;
        return $this->save();
    }

    /**
     * Obtiene todos los tipos de inmuebles ordenados por nombre en español.
     *
     * @return array
     */
    public static function obtenerTodosOrdenados()
    {
        $instance = static::newInstance();
        return $instance->newQuery()->orderBy('tipo_esp')->get();
    }

    /**
     * Obtiene los tipos más visitados.
     *
     * @param int $limite
     * @return array
     */
    public static function obtenerMasVisitados($limite = 10)
    {
        $instance = static::newInstance();
        return $instance->newQuery()->orderBy('visitas', 'DESC')->limit($limite)->get();
    }
}
