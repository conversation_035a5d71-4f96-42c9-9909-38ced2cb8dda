<?php

namespace Mu<PERSON>bin\Models;

use <PERSON><PERSON>bin\Models\Model;

class SICasaCompartida extends Model
{
    protected $table = 'casas_compartidas';
    protected $primaryKey = 'id';

    /**
     * Los atributos que son asignables masivamente.
     *
     * @var array
     */
    protected $fillable = [
        'clave_sistema',
        'contrato',
        'autorizado',
        'quien_solicita',
        'metodo',
        'token',
        'created_at',
        'updated_at',
    ];

    /**
     * Busca una solicitud por su token
     *
     * @param string $token
     * @return SICasaCompartida|null
     */
    public static function buscarPorToken($token)
    {
        return static::where('token', $token)->first();
    }

    /**
     * Verifica si la solicitud ya fue procesada
     *
     * @return bool
     */
    public function isProcessed()
    {
        return empty($this->token);
    }

    /**
     * Obtiene el inmueble.
     */
    public function getInmueble()
    {
        if (!$this->clave_sistema) {
            return null;
        }

        return SIInmueble::find($this->clave_sistema);
    }

    /**
     * Obtiene el solicitante (propietario del inmueble)
     */
    public function getSolicitante()
    {
        if (!$this->quien_solicita) {
            return null;
        }

        return SIConfig::find($this->quien_solicita);
    }

    /**
     * Obtiene el contrato del socio.
     */
    public function getContratoSocio()
    {
        if (!$this->contrato) {
            return null;
        }

        return SIConfig::find($this->contrato);
    }

    /**
     * Verifica si la casa está autorizada.
     *
     * @return bool
     */
    public function isAutorizado()
    {
        return $this->autorizado === 'Si';
    }

    /**
     * Autoriza la publicación del inmueble.
     *
     * @return bool
     */
    public function aceptarConexion()
    {
        $this->autorizado = 'Si';
        $this->token = null; // Invalidar token después de usar
        $this->updated_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * Rechaza la publicación del inmueble.
     *
     * @return bool
     */
    public function rechazarConexion()
    {
        $this->autorizado = 'No';
        $this->token = null; // Invalidar token después de usar
        $this->updated_at = date('Y-m-d H:i:s');

        return $this->save();
    }

    /**
     * Obtiene la fecha de solicitud formateada
     *
     * @param string $formato
     * @return string
     */
    public function getFechaSolicitudFormateada($formato = 'corto')
    {
        if (empty($this->created_at)) {
            return 'Fecha no disponible';
        }

        $fecha = new \DateTime($this->created_at);

        if ($formato === 'largo') {
            $meses = [
                1 => 'enero', 2 => 'febrero', 3 => 'marzo', 4 => 'abril',
                5 => 'mayo', 6 => 'junio', 7 => 'julio', 8 => 'agosto',
                9 => 'septiembre', 10 => 'octubre', 11 => 'noviembre', 12 => 'diciembre'
            ];

            $dia = $fecha->format('j');
            $mes = $meses[(int)$fecha->format('n')];
            $anio = $fecha->format('Y');
            $hora = $fecha->format('H:i');

            return "{$dia} de {$mes} de {$anio} a las {$hora}";
        }

        return $fecha->format('d/m/Y H:i');
    }
}
