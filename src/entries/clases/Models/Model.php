<?php

namespace Mulbin\Models;

abstract class Model
{
    protected $table;
    protected $sql;
    protected $primaryKey = 'id';
    protected $attributes = array();
    protected $original = array();

    public function __construct($attributes = array())
    {
        global $sql;
        $this->sql = $sql;

        if (!empty($attributes)) {
            $this->fill($attributes);
        }
    }

    /**
     * Obtener el nombre de la tabla
     */
    public function getTable()
    {
        return $this->table;
    }

    /**
     * Obtener la clave primaria
     */
    public function getPrimaryKey()
    {
        return $this->primaryKey;
    }

    /**
     * Crear una nueva instancia con conexión SQL inicializada
     */
    protected static function newInstance()
    {
        $instance = new static();

        // Asegurar conexión SQL
        if (!$instance->sql) {
            global $sql;
            if (!$sql) {
                throw new \RuntimeException('No hay conexión global a la base de datos disponible');
            }
            $instance->sql = $sql;
        }

        return $instance;
    }

    /**
     * Encuentra un registro por su clave primaria
     */
    public static function find($id)
    {
        $instance = static::newInstance();

        // Verificar configuración
        if (!$instance->getTable()) {
            throw new \RuntimeException('La propiedad $table no está definida en el modelo: ' . get_class($instance));
        }

        $query = "SELECT * FROM {$instance->getTable()} WHERE {$instance->getPrimaryKey()} = ? LIMIT 1";

        $stmt = $instance->sql->prepare($query);
        if ($stmt === false) {
            throw new \RuntimeException('Error preparando consulta SQL: ' . $instance->sql->error . ' - Query: ' . $query);
        }

        $stmt->bind_param(is_int($id) ? 'i' : 's', $id);

        if (!$stmt->execute()) {
            throw new \RuntimeException('Error ejecutando consulta SQL: ' . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($result === false) {
            throw new \RuntimeException('Error obteniendo resultado: ' . $stmt->error);
        }

        if ($row = $result->fetch_assoc()) {
            return $instance->newFromBuilder($row);
        }

        return null;
    }

    /**
     * Obtiene todos los registros
     */
    public static function all()
    {
        $instance = static::newInstance();
        $query = "SELECT * FROM {$instance->getTable()}";

        $result = $instance->sql->query($query);
        if ($result === false) {
            throw new \RuntimeException('Error ejecutando consulta SQL: ' . $instance->sql->error . ' - Query: ' . $query);
        }

        $models = array();

        while ($row = $result->fetch_assoc()) {
            $models[] = $instance->newFromBuilder($row);
        }

        return $models;
    }

    /**
     * Encuentra el primer registro que coincida con las condiciones
     */
    public static function where($column, $operator, $value = null)
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }

        $instance = static::newInstance();
        return $instance->newQuery()->where($column, $operator, $value);
    }

    /**
     * Crear join con otra tabla
     */
    public static function join($table, $first, $operator = null, $second = null, $type = 'INNER')
    {
        $instance = static::newInstance();
        return $instance->newQuery()->join($table, $first, $operator, $second, $type);
    }

    /**
     * Crear left join con otra tabla
     */
    public static function leftJoin($table, $first, $operator = null, $second = null)
    {
        return static::join($table, $first, $operator, $second, 'LEFT');
    }

    /**
     * Crear right join con otra tabla
     */
    public static function rightJoin($table, $first, $operator = null, $second = null)
    {
        return static::join($table, $first, $operator, $second, 'RIGHT');
    }

    /**
     * Crear un nuevo query builder básico
     */
    public function newQuery()
    {
        return new ModelQueryBuilder($this);
    }

    /**
     * Crear una nueva instancia desde datos de BD
     */
    public function newFromBuilder($attributes)
    {
        $instance = new static();
        $instance->attributes = $attributes;
        $instance->original = $attributes;
        return $instance;
    }

    /**
     * Llenar atributos masivamente
     */
    public function fill($attributes)
    {
        foreach ($attributes as $key => $value) {
            $this->attributes[$key] = $value;
        }
        return $this;
    }

    /**
     * Guardar el modelo
     */
    public function save()
    {
        if ($this->exists()) {
            return $this->update();
        } else {
            return $this->insert();
        }
    }

    /**
     * Insertar nuevo registro
     */
    protected function insert()
    {
        $columns = array_keys($this->attributes);
        $placeholders = str_repeat('?,', count($columns) - 1) . '?';

        $query = "INSERT INTO {$this->getTable()} (" . implode(',', $columns) . ") VALUES ({$placeholders})";

        $stmt = $this->sql->prepare($query);
        if ($stmt === false) {
            throw new \RuntimeException('Error preparando consulta INSERT: ' . $this->sql->error . ' - Query: ' . $query);
        }

        $types = str_repeat('s', count($this->attributes));
        $values = array_values($this->attributes);

        if (!$stmt->bind_param($types, ...$values)) {
            throw new \RuntimeException('Error vinculando parámetros: ' . $stmt->error);
        }

        if ($stmt->execute()) {
            $this->attributes[$this->getPrimaryKey()] = $this->sql->insert_id;
            $this->original = $this->attributes;
            return true;
        }

        throw new \RuntimeException('Error ejecutando INSERT: ' . $stmt->error);
    }

    /**
     * Actualizar registro existente
     */
    protected function update()
    {
        $updates = array();
        $values = array();

        foreach ($this->attributes as $key => $value) {
            if ($key !== $this->getPrimaryKey() && $value !== $this->original[$key]) {
                $updates[] = "{$key} = ?";
                $values[] = $value;
            }
        }

        if (empty($updates)) {
            return true; // No hay cambios
        }

        $values[] = $this->attributes[$this->getPrimaryKey()];
        $query = "UPDATE {$this->getTable()} SET " . implode(', ', $updates) . " WHERE {$this->getPrimaryKey()} = ?";

        $stmt = $this->sql->prepare($query);
        if ($stmt === false) {
            throw new \RuntimeException('Error preparando consulta UPDATE: ' . $this->sql->error . ' - Query: ' . $query);
        }

        $types = str_repeat('s', count($values));
        if (!$stmt->bind_param($types, ...$values)) {
            throw new \RuntimeException('Error vinculando parámetros UPDATE: ' . $stmt->error);
        }

        if ($stmt->execute()) {
            $this->original = $this->attributes;
            return true;
        }

        throw new \RuntimeException('Error ejecutando UPDATE: ' . $stmt->error);
    }

    /**
     * Eliminar el modelo
     */
    public function delete()
    {
        if (!$this->exists()) {
            return false;
        }

        $query = "DELETE FROM {$this->getTable()} WHERE {$this->getPrimaryKey()} = ?";
        $stmt = $this->sql->prepare($query);
        if ($stmt === false) {
            throw new \RuntimeException('Error preparando consulta DELETE: ' . $this->sql->error . ' - Query: ' . $query);
        }

        if (!$stmt->bind_param(is_int($this->attributes[$this->getPrimaryKey()]) ? 'i' : 's', $this->attributes[$this->getPrimaryKey()])) {
            throw new \RuntimeException('Error vinculando parámetros DELETE: ' . $stmt->error);
        }

        if (!$stmt->execute()) {
            throw new \RuntimeException('Error ejecutando DELETE: ' . $stmt->error);
        }

        return true;
    }

    /**
     * Verificar si el modelo existe en BD
     */
    public function exists()
    {
        return isset($this->attributes[$this->getPrimaryKey()]);
    }

    /**
     * Obtener atributo
     */
    public function __get($key)
    {
        return isset($this->attributes[$key]) ? $this->attributes[$key] : null;
    }

    /**
     * Establecer atributo
     */
    public function __set($key, $value)
    {
        $this->attributes[$key] = $value;
    }

    /**
     * Verificar si existe atributo
     */
    public function __isset($key)
    {
        return isset($this->attributes[$key]);
    }

    /**
     * Convertir a array
     */
    public function toArray()
    {
        return $this->attributes;
    }
}

/**
 * Query Builder básico para compatibilidad con PHP 5.6
 */
class ModelQueryBuilder
{
    protected $model;
    protected $wheres = array();
    protected $orders = array();
    protected $limits = null;
    protected $joins = array();
    protected $selectColumns = array();

    public function __construct($model)
    {
        $this->model = $model;

        // Asegurar que el modelo tenga conexión SQL
        $this->ensureSQLConnection();

        // Verificar que la tabla esté definida usando getter
        if (empty($this->model->getTable())) {
            throw new \RuntimeException('La propiedad $table está vacía en el modelo: ' . get_class($this->model));
        }

        $this->selectColumns = array($this->model->getTable() . '.*');
    }

    /**
     * Asegurar que el modelo tenga conexión SQL inicializada
     */
    protected function ensureSQLConnection()
    {
        if (!$this->model->sql) {
            global $sql;
            if (!$sql) {
                throw new \RuntimeException('No hay conexión global a la base de datos disponible');
            }
            $this->model->sql = $sql;
        }
    }

    public function join($table, $first, $operator = null, $second = null, $type = 'INNER')
    {
        if ($operator === null) {
            $operator = '=';
        }

        $this->joins[] = array(
            'type' => $type,
            'table' => $table,
            'first' => $first,
            'operator' => $operator,
            'second' => $second
        );

        return $this;
    }

    public function leftJoin($table, $first, $operator = null, $second = null)
    {
        return $this->join($table, $first, $operator, $second, 'LEFT');
    }

    public function rightJoin($table, $first, $operator = null, $second = null)
    {
        return $this->join($table, $first, $operator, $second, 'RIGHT');
    }

    public function select($columns = array())
    {
        if (!is_array($columns)) {
            $columns = func_get_args();
        }

        if (!empty($columns)) {
            $this->selectColumns = $columns;
        }

        return $this;
    }

    public function where($column, $operator, $value = null)
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }

        $this->wheres[] = array(
            'column' => $column,
            'operator' => $operator,
            'value' => $value
        );

        return $this;
    }

    public function orderBy($column, $direction = 'ASC')
    {
        $this->orders[] = "{$column} {$direction}";
        return $this;
    }

    public function limit($count)
    {
        $this->limits = $count;
        return $this;
    }

    public function first()
    {
        $this->limit(1);
        $results = $this->get();
        return !empty($results) ? $results[0] : null;
    }

    public function find($id)
    {
        $this->where($this->model->getTable() . '.' . $this->model->getPrimaryKey(), '=', $id);
        return $this->first();
    }

    /**
     * Obtener la consulta SQL sin ejecutarla
     */
    public function toSql()
    {
        $query = $this->buildQuery();

        if (!empty($this->wheres)) {
            $conditions = array();
            foreach ($this->wheres as $where) {
                $conditions[] = "{$where['column']} {$where['operator']} ?";
            }
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        if (!empty($this->orders)) {
            $query .= " ORDER BY " . implode(', ', $this->orders);
        }

        if ($this->limits) {
            $query .= " LIMIT {$this->limits}";
        }

        return $query;
    }

    /**
     * Obtener la consulta SQL con parámetros sustituidos (para debugging)
     */
    public function toRawSql()
    {
        $query = $this->buildQuery();
        $params = array();

        if (!empty($this->wheres)) {
            $conditions = array();
            foreach ($this->wheres as $where) {
                $value = is_string($where['value']) ? "'{$where['value']}'" : $where['value'];
                $conditions[] = "{$where['column']} {$where['operator']} {$value}";
                $params[] = $where['value'];
            }
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        if (!empty($this->orders)) {
            $query .= " ORDER BY " . implode(', ', $this->orders);
        }

        if ($this->limits) {
            $query .= " LIMIT {$this->limits}";
        }

        return $query;
    }

    /**
     * Debug: mostrar información completa de la consulta
     */
    public function dd()
    {
        echo "<pre>";
        echo "=== QUERY DEBUG ===\n";
        echo "SQL: " . $this->toSql() . "\n";
        echo "Raw SQL: " . $this->toRawSql() . "\n";
        echo "Parámetros WHERE:\n";
        foreach ($this->wheres as $i => $where) {
            echo "  [{$i}] {$where['column']} {$where['operator']} " . var_export($where['value'], true) . "\n";
        }
        if (!empty($this->joins)) {
            echo "JOINS:\n";
            foreach ($this->joins as $i => $join) {
                echo "  [{$i}] {$join['type']} JOIN {$join['table']} ON {$join['first']} {$join['operator']} {$join['second']}\n";
            }
        }
        echo "===================\n";
        echo "</pre>";
        die();
    }

    protected function buildQuery()
    {
        // Verificar que tengamos lo necesario
        if (empty($this->model->getTable())) {
            throw new \RuntimeException('La propiedad $table no está definida en el modelo: ' . get_class($this->model));
        }

        if (empty($this->selectColumns)) {
            $this->selectColumns = array($this->model->getTable() . '.*');
        }

        // SELECT clause
        $query = "SELECT " . implode(', ', $this->selectColumns);

        // FROM clause
        $query .= " FROM {$this->model->getTable()}";

        // JOIN clauses
        if (!empty($this->joins)) {
            foreach ($this->joins as $join) {
                $query .= " {$join['type']} JOIN {$join['table']} ON {$join['first']} {$join['operator']} {$join['second']}";
            }
        }

        return $query;
    }

    public function get()
    {
        // Asegurar conexión SQL antes de ejecutar
        $this->ensureSQLConnection();

        $query = $this->buildQuery();
        $params = array();
        $types = '';

        if (!empty($this->wheres)) {
            $conditions = array();
            foreach ($this->wheres as $where) {
                $conditions[] = "{$where['column']} {$where['operator']} ?";
                $params[] = $where['value'];
                $types .= is_int($where['value']) ? 'i' : 's';
            }
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        if (!empty($this->orders)) {
            $query .= " ORDER BY " . implode(', ', $this->orders);
        }

        if ($this->limits) {
            $query .= " LIMIT {$this->limits}";
        }

        $stmt = $this->model->sql->prepare($query);
        if ($stmt === false) {
            throw new \RuntimeException('Error preparando consulta SELECT: ' . $this->model->sql->error . ' - Query: ' . $query);
        }

        if (!empty($params)) {
            if (!$stmt->bind_param($types, ...$params)) {
                throw new \RuntimeException('Error vinculando parámetros SELECT: ' . $stmt->error);
            }
        }

        if (!$stmt->execute()) {
            throw new \RuntimeException('Error ejecutando consulta SELECT: ' . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($result === false) {
            throw new \RuntimeException('Error obteniendo resultado SELECT: ' . $stmt->error);
        }

        $models = array();
        while ($row = $result->fetch_assoc()) {
            $models[] = $this->model->newFromBuilder($row);
        }

        return $models;
    }
}
