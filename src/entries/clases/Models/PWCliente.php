<?php

namespace Mu<PERSON>bin\Models;

use <PERSON><PERSON>bin\Models\Model;

class PWCliente extends Model
{
    protected $table = DB_PW . '.clientes';

    /**
     * Los atributos que son asignables masivamente.
     *
     * @var array
     */
    protected $fillable = [
        'usuario',
        'logmail',
        'password',
        'remember_token',
        'google_id',
        'google_token',
        'google_refresh_token',
        'facebook_id',
        'facebook_token',
        'as_cnt',
        'name',
        'nombre',
        'apellidos',
        'ocupacion',
        'empresa',
        'calle_numero',
        'colonia',
        'codigo_postal',
        'ciudad',
        'estado',
        'pais',
        'code_alpha2',
        'como_llegar',
        'telefono',
        'phone_country_code',
        'phone_number',
        'celular',
        'nextel_tel',
        'nextel_radio',
        'fax',
        'email',
        'pending_email',
        'pending_email_at',
        'email_verified_at',
        'website_activated_at',
        'avatar',
        'email_sec',
        'sitio_web',
        'fact_nombre',
        'fact_domicilio',
        'fact_rfc',
        'activo',
        'observaciones',
        'quien_registro',
        'conekta_id',
        'pabusqueda',
        'pabuscarsocio',
        'created_at',
        'updated_at'
    ];

    public function __construct($attributes = array())
    {
        parent::__construct($attributes);

        $this->table = DB_PW . '.clientes';
    }

    /**
     * Busca un cliente por usuario.
     *
     * @param string $usuario
     * @return PWCliente|null
     */
    public static function buscarPorUsuario($usuario)
    {
        return static::where('usuario', $usuario)->first();
    }

    /**
     * Busca un cliente por email.
     *
     * @param string $email
     * @return PWCliente|null
     */
    public static function buscarPorEmail($email)
    {
        return static::where('logmail', $email)->first();
    }

    /**
     * Verifica si el cliente está activo.
     *
     * @return bool
     */
    public function estaActivo()
    {
        return $this->activo === 'Si';
    }

    /**
     * Obtiene el nombre completo del cliente.
     *
     * @return string
     */
    public function getNombreCompleto()
    {
        return trim($this->nombre . ' ' . $this->apellidos);
    }

    /**
     * Obtiene la dirección completa del cliente.
     *
     * @return string
     */
    public function getDireccionCompleta()
    {
        $partes = array_filter([
            $this->calle_numero,
            $this->colonia,
            $this->ciudad,
            $this->estado,
            $this->pais
        ]);

        return implode(', ', $partes);
    }

    /**
     * Obtiene el teléfono principal del cliente.
     *
     * @return string
     */
    public function getTelefonoPrincipal()
    {
        if (!empty($this->phone_number)) {
            $codigo = $this->phone_country_code ?: '+52';
            return $codigo . ' ' . $this->phone_number;
        }

        return $this->telefono ?: $this->celular;
    }

    /**
     * Verifica si el cliente tiene email verificado.
     *
     * @return bool
     */
    public function tieneEmailVerificado()
    {
        return $this->email_verified_at !== null;
    }

    /**
     * Obtiene la URL del avatar con fallback.
     *
     * @return string|null
     */
    public function getAvatarUrl()
    {
        if (!empty($this->avatar)) {
            return $this->avatar;
        }

        return null;
    }

    /**
     * Obtiene las iniciales del cliente para avatar por defecto.
     *
     * @return string
     */
    public function getIniciales()
    {
        $nombre = trim($this->nombre . ' ' . $this->apellidos);

        if (empty($nombre)) {
            return strtoupper(substr($this->usuario, 0, 2));
        }

        $palabras = preg_split('/[\s\-_]+/', $nombre);
        $iniciales = '';

        foreach ($palabras as $palabra) {
            if (!empty($palabra)) {
                $iniciales .= strtoupper(substr($palabra, 0, 1));
            }
        }

        return substr($iniciales, 0, 2);
    }
}
