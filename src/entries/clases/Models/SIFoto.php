<?php

namespace Mulbin\Models;

use Mulbin\Models\Model;

class SIFoto extends Model
{
    protected $table = 'f_fotos';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'id',
        'contrato',
        'clave_sistema',
        'fecha',
        'descripcion',
        'enlaces',
        'de_donde_esp',
        'de_donde_ing',
        'de_donde_fra',
        'foto_num',
        'foto_principal',
        'con_logo',
        'orden',
        'archivo',
        'sitio_web',
        'bolsa',
        'checado',
        'mig',
    ];

    /**
     * Busca la foto principal de un inmueble
     *
     * @param int $claveSystem
     * @return SIFoto|null
     */
    public static function buscarFotoPrincipal($claveSystem)
    {
        return static::where('clave_sistema', $claveSystem)
            ->where('sitio_web', 1)
            ->orderBy('foto_principal', 'DESC')
            ->orderBy('orden', 'ASC')
            ->orderBy('id', 'ASC')
            ->limit(1)
            ->first();
    }

    /**
     * Busca todas las fotos de un inmueble ordenadas
     *
     * @param int $claveSystem
     * @param bool $soloSitioWeb
     * @return array
     */
    public static function buscarFotosInmueble($claveSystem, $soloSitioWeb = true)
    {
        $query = static::where('clave_sistema', $claveSystem);

        if ($soloSitioWeb) {
            $query->where('sitio_web', 1);
        }

        return $query->orderBy('foto_principal', 'DESC')
                    ->orderBy('orden', 'ASC')
                    ->orderBy('id', 'ASC')
                    ->get();
    }

    /**
     * Busca fotos por contrato
     *
     * @param int $contrato
     * @return array
     */
    public static function buscarFotosPorContrato($contrato)
    {
        return static::where('contrato', $contrato)
            ->where('sitio_web', 1)
            ->orderBy('orden', 'ASC')
            ->get();
    }

    /**
     * Verifica si es la foto principal
     *
     * @return bool
     */
    public function esFotoPrincipal()
    {
        return $this->foto_principal === 'Si';
    }

    /**
     * Verifica si tiene logo
     *
     * @return bool
     */
    public function tieneLogo()
    {
        return $this->con_logo === 'Si';
    }

    /**
     * Obtiene la URL completa de la foto
     *
     * @param string $baseUrl URL base del servidor de imágenes
     * @return string|null
     */
    public function getUrlCompleta($baseUrl = null)
    {
        if (empty($this->archivo)) {
            return null;
        }

        // Si no se proporciona baseUrl, usar variable de entorno
        if (!$baseUrl) {
            $baseUrl = getenv('MULBIN_URL') ?: '';
        }

        // Construir la URL completa
        return rtrim($baseUrl, '/') . '/' . $this->archivo;
    }

    /**
     * Obtiene la descripción en un idioma específico
     *
     * @param string $idioma 'esp', 'ing', 'fra'
     * @return string
     */
    public function getDescripcionPorIdioma($idioma = 'esp')
    {
        switch (strtolower($idioma)) {
            case 'esp':
            case 'es':
                return $this->de_donde_esp ?: 'Foto del inmueble';
            case 'ing':
            case 'en':
                return $this->de_donde_ing ?: 'Property photo';
            case 'fra':
            case 'fr':
                return $this->de_donde_fra ?: 'Photo de la propriété';
            default:
                return $this->de_donde_esp ?: 'Foto del inmueble';
        }
    }

    /**
     * Obtiene la descripción JSON parseada
     *
     * @return array|null
     */
    public function getDescripcionJson()
    {
        if (empty($this->descripcion)) {
            return null;
        }

        return json_decode($this->descripcion, true);
    }

    /**
     * Obtiene los enlaces JSON parseados
     *
     * @return array|null
     */
    public function getEnlacesJson()
    {
        if (empty($this->enlaces)) {
            return null;
        }

        return json_decode($this->enlaces, true);
    }

    /**
     * Obtiene información completa de la foto para templates
     *
     * @param string $baseUrl URL base del servidor
     * @return array
     */
    public function getDatosCompletos($baseUrl = null)
    {
        return [
            'id' => $this->id,
            'url' => $this->getUrlCompleta($baseUrl),
            'archivo' => $this->archivo,
            'descripcion' => $this->getDescripcionPorIdioma('esp'),
            'descripcion_en' => $this->getDescripcionPorIdioma('en'),
            'descripcion_fr' => $this->getDescripcionPorIdioma('fr'),
            'es_principal' => $this->esFotoPrincipal(),
            'tiene_logo' => $this->tieneLogo(),
            'orden' => $this->orden,
            'foto_num' => $this->foto_num,
            'descripcion_json' => $this->getDescripcionJson(),
            'enlaces_json' => $this->getEnlacesJson(),
        ];
    }
}
