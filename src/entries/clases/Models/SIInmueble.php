<?php

namespace Mu<PERSON>bin\Models;

require_once __DIR__ . '/SITipoInmueble.php';
require_once __DIR__ . '/SIFoto.php';

use <PERSON><PERSON>bin\Models\Model;
use <PERSON><PERSON>bin\Models\SITipoInmueble;
use <PERSON><PERSON>bin\Models\SIFoto;

class SIInmueble extends Model
{
    protected $table = 'propiedades';
    protected $primaryKey = 'clave_sistema';
    public $timestamps = false;

    /**
     * Los atributos que son asignables masivamente.
     *
     * @var array
     */
    protected $fillable = [
        'clave_sistema',
        'contrato',
        'claveprop',
        'nombreprop',
        'tipo',
        'colonia',
        'sucursal',
        'precio_venta',
        'precio_renta',
        'precio_diaria',
        'moneda',
        'enventa',
        'enrenta',
        'endiaria',
        'entraspaso',
        'ciudad',
        'provincia',
        'pais',
        'intro_corta_esp',
        'anuncio_esp',
        'status_web',
        'map_lat',
        'map_lng',
    ];

    /**
     * Obtiene la configuración del contrato
     */
    public function getConfig()
    {
        if (!$this->contrato) {
            return null;
        }

        return SIConfig::find($this->contrato);
    }

    /**
     * Obtiene el tipo de inmueble desde la tabla tipo_inmuebles
     */
    public function getTipo($idioma = 'es', $plural = false)
    {
        if (!$this->tipo) {
            return 'Inmueble';
        }

        $tipoInmueble = SITipoInmueble::buscarPorClave($this->tipo);

        if (!$tipoInmueble) {
            return 'Inmueble';
        }

        return $tipoInmueble->getNombrePorIdioma($idioma, $plural);
    }

    /**
     * Obtiene la relación con el tipo de inmueble
     */
    public function tipoInmueble()
    {
        return SITipoInmueble::buscarPorClave($this->tipo);
    }

    /**
     * Obtiene la operación principal del inmueble
     */
    public function getOperacionPrincipal()
    {
        if ($this->enventa === 'Si' && $this->precio_venta > 0) {
            return 'venta';
        }

        if ($this->enrenta === 'Si' && $this->precio_renta > 0) {
            return 'renta';
        }

        if ($this->entraspaso === 'Si') {
            return 'traspaso';
        }

        return 'venta'; // Default
    }

    /**
     * Obtiene el precio principal del inmueble
     */
    public function getPrecioPrincipal()
    {
        $operacion = $this->getOperacionPrincipal();

        switch ($operacion) {
            case 'venta':
                return $this->precio_venta;
            case 'renta':
                return $this->precio_renta;
            case 'traspaso':
                return $this->precio_venta; // Asumiendo que traspaso usa precio_venta
            default:
                return $this->precio_venta;
        }
    }

    /**
     * Obtiene la ubicación completa
     */
    public function getUbicacionCompleta()
    {
        $ubicacion = [];

        if (!empty($this->colonia)) {
            $ubicacion[] = $this->colonia;
        }

        if (!empty($this->ciudad)) {
            $ubicacion[] = $this->ciudad;
        }

        if (!empty($this->provincia)) {
            $ubicacion[] = $this->provincia;
        }

        return implode(', ', array_filter($ubicacion));
    }

    /**
     * Obtiene la descripción del inmueble
     */
    public function getDescripcion()
    {
        if (!empty($this->anuncio_esp)) {
            return $this->anuncio_esp;
        }

        if (!empty($this->intro_corta_esp)) {
            return $this->intro_corta_esp;
        }

        return 'Sin descripción disponible';
    }

    /**
     * Obtiene las características básicas del inmueble
     * Esto podría venir de una tabla de campos personalizados
     */
    public function getCaracteristicas()
    {
        // Por ahora retornamos un array vacío
        // En el futuro se podría consultar la tabla de campos personalizados
        return [
            'recamaras' => null,
            'banos' => null,
            'area' => null,
        ];
    }

    /**
     * Obtiene la imagen principal del inmueble
     */
    public function getImagenPrincipal()
    {
        $foto = SIFoto::buscarFotoPrincipal($this->clave_sistema);

        if (!$foto) {
            return null;
        }

        return $foto->getUrlCompleta('/pics/' . $this->contrato . '/alta');
    }

    /**
     * Obtiene todas las fotos del inmueble
     */
    public function getFotos($soloSitioWeb = true)
    {
        return SIFoto::buscarFotosInmueble($this->clave_sistema, $soloSitioWeb);
    }

    /**
     * Obtiene la foto principal como objeto
     */
    public function getFotoPrincipal()
    {
        return SIFoto::buscarFotoPrincipal($this->clave_sistema);
    }

    /**
     * Obtiene los datos completos para mostrar en templates
     */
    public function getDatosCompletos()
    {
        $caracteristicas = $this->getCaracteristicas();

        return [
            'id' => $this->clave_sistema,
            'clave' => $this->claveprop,
            'titulo' => $this->nombreprop,
            'tipo' => $this->getTipo(),
            'operacion' => $this->getOperacionPrincipal(),
            'precio' => $this->getPrecioPrincipal(),
            'precio_formateado' => $this->getPrecioFormateado(),
            'moneda' => $this->moneda,
            'ubicacion' => $this->getUbicacionCompleta(),
            'descripcion' => $this->getDescripcion(),
            'recamaras' => $caracteristicas['recamaras'],
            'banos' => $caracteristicas['banos'],
            'area' => $caracteristicas['area'],
            'imagen_principal' => $this->getImagenPrincipal(),
            'coordenadas' => [
                'lat' => $this->map_lat,
                'lng' => $this->map_lng,
            ],
        ];
    }

    /**
     * Formatea el precio para mostrar
     */
    public function getPrecioFormateado()
    {
        $precio = $this->getPrecioPrincipal();

        if (!$precio || $precio <= 0) {
            return 'Precio a consultar';
        }

        // Formatear según la moneda
        $simbolo = '$';
        if ($this->moneda === 'USD') {
            $simbolo = 'USD $';
        } elseif ($this->moneda === 'EUR') {
            $simbolo = '€';
        }

        return $simbolo . number_format($precio, 0, '.', ',');
    }
}
