<?php

namespace Mulbin;

require_once __DIR__ . '/Models/Model.php';
require_once __DIR__ . '/Models/SIConfig.php';
require_once __DIR__ . '/CurlPost.class.php';

use Mulbin\Models\SIConfig;
use Http\CurlPost;

class User
{
    protected $sql;

    private $contrato;
    private $config;
    private $username;
    private $meteorBaseUrl;
    private $meteorApiKey;
    private $mulbinUrl;

    public function __construct($contrato)
    {
        global $sql;
        $this->sql = $sql;
        $this->contrato = $contrato;

        $this->meteorBaseUrl = getenv('API_URL_METEOR') ?: 'http://localhost:3000/api';
        $this->meteorApiKey = getenv('API_KEY_METEOR') ?: 'c9214e...';

        $this->mulbinUrl = getenv('MULBIN_URL') ?: 'http://mulbin.com';

        // Buscar configuración por contrato
        $config = DB_SI . '.config';
        $contratos = DB_PW . '.contratos';
        $clientes = DB_PW . '.clientes';
        $this->config = SIConfig::join($contratos, "{$contratos}.numero", '=', 'config.contrato')
            ->join($clientes, "{$clientes}.usuario", '=', "{$contratos}.usuario")
            ->where("{$config}.contrato", $this->contrato)
            ->select(
                // Config
                "{$config}.contrato",
                "{$config}.meteor_id",
                // Clientes
                "{$clientes}.usuario AS username",
                "{$clientes}.logmail",
                "{$clientes}.nombre",
                "{$clientes}.apellidos",
                "{$clientes}.phone_number",
                "{$clientes}.empresa",
                "{$clientes}.avatar"
            )->first();
        // dd($this->config->toSql());

        $this->username = $this->config->username;

        // dd($this->config->toArray());

        // if (!$this->config) {
        //     throw new \RuntimeException("No se encontró configuración para contrato: " . $this->contrato);
        // }
    }

    /**
     * Verifica si el usuario existe en Meteor
     * @return object|false Retorna object con datos del usuario si existe:
     *                     - 'exists': booleano que indica si el usuario existe
     *                     - 'message': mensaje descriptivo
     *                     - 'user': datos del usuario si existe
     *                     - 'username': nombre de usuario
     *                     Retorna false si el usuario no existe o hay error
     */
    public function userExistsInMeteor()
    {
        try {
            $response = $this->makeMeteorRequest('/users/check-username/' . urlencode($this->username), 'GET');

            // Validación básica de estructura de respuesta
            if (!isset($response['status']) || !isset($response['data'])) {
                error_log("Respuesta de Meteor con formato inválido - Contrato: {$this->contrato}, Username: {$this->username}");
                return false;
            }

            $httpCode = $response['status'];
            $data = $response['data'];

            // Manejo de errores HTTP críticos
            if ($httpCode >= 500) {
                // Errores de servidor - re-lanzar para que el código superior pueda manejar reintentos
                throw new \RuntimeException("Error del servidor Meteor (HTTP $httpCode)");
            }

            if ($httpCode === 404) {
                // Usuario definitivamente no existe
                return false;
            }

            if ($httpCode !== 200) {
                // Otros códigos HTTP - loggear pero continuar
                error_log("Código HTTP inesperado de Meteor - Contrato: {$this->contrato}, Username: {$this->username}, HTTP: $httpCode");
                return false;
            }

            // Validación de JSON (aunque makeMeteorRequest ya decodifica)
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \RuntimeException('Error al decodificar respuesta JSON de Meteor');
            }

            // Verificar estructura de respuesta según el endpoint
            if (!isset($data->exists)) {
                error_log("Respuesta de Meteor sin campo 'exists' - Contrato: {$this->contrato}, Username: {$this->username}");
                return false;
            }

            // Si el usuario existe, retornar los datos completos
            if ($data->exists === true) {
                return $data;
            }

            // Si el usuario no existe, retornar false
            return false;

        } catch (\Exception $e) {
            $httpCode = isset($response['status']) ? $response['status'] : 'N/A';
            error_log("Error al verificar usuario en Meteor - Contrato: {$this->contrato}, Username: {$this->username}, HTTP: $httpCode, Error: " . $e->getMessage());

            // Solo re-lanzar excepciones para errores de servidor (500+)
            if (isset($response['status']) && $response['status'] >= 500) {
                throw $e;
            }

            return false;
        }
    }

    /**
     * Crea un usuario en Meteor usando la API REST
     * @return object|false Retorna datos del usuario creado o false si hay error
     */
    public function createMeteorUser()
    {
        // Validar que tenemos los datos necesarios
        if (!$this->config || !$this->config->username || !$this->config->logmail) {
            throw new \RuntimeException('No se encontraron datos suficientes para crear el usuario en Meteor');
        }

        // Generar password temporal aleatorio
        $randomPassword = $this->generateRandomPassword();

        // Preparar datos del usuario para Meteor
        $userData = array(
            'email' => $this->config->logmail,
            'password' => $randomPassword,
            'username' => $this->config->username,
            'profile' => array(
                'avatar' => isset($this->config->avatar) ? $this->mulbinUrl . $this->config->avatar : $this->mulbinUrl . '/images/avatar.png',
                'firstName' => $this->config->nombre ?: '',
                'lastName' => $this->config->apellidos ?: '',
                'phone' => $this->config->phone_number ?: '',
                'company' => $this->config->empresa ?: '',
                'bio' => 'Usuario del Sistema Inmobiliario - Contrato #' . $this->contrato,
                'location' => 'centro', // Valor por defecto
                'specialties' => array('residential'),
                'verified' => true
            ),
            'roles' => array('user')
        );

        try {
            // Ejecutar la petición
            $response = $this->makeMeteorRequest('/users', 'POST', $userData);

            // Decodificar respuesta
            $data = $response['data'];

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \RuntimeException('Error al decodificar respuesta JSON de Meteor: ' . json_last_error_msg());
            }

            // Verificar si la creación fue exitosa
            if (isset($data->message) && strpos($data->message, 'exitosamente') !== false) {
                // Guardar información del usuario creado para logs
                error_log("Usuario creado en Meteor - Contrato: {$this->contrato}, Username: {$this->config->username}, UserID: {$data->userId}");

                return (object) array(
                    'success' => true,
                    'userId' => $data->userId,
                    'authToken' => $data->authToken,
                    'username' => $this->config->username,
                    'email' => $this->config->logmail,
                    'userData' => $data->user
                );
            } else {
                // Error en la creación
                $errorMsg = isset($data->error) ? $data->error : 'Error desconocido al crear usuario en Meteor';
                throw new \RuntimeException($errorMsg);
            }

        } catch (\RuntimeException $e) {
            // Log del error
            error_log("Error al crear usuario en Meteor - Contrato: {$this->contrato}, Username: {$this->config->username}, Error: " . $e->getMessage());

            return (object) array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Actualiza un usuario en Meteor usando la API REST
     * @return object|false Retorna datos del usuario actualizado o false si hay error
     */
    public function updateMeteorUserProfile()
    {
        // Configuración de la API de Meteor
        // Validar que tenemos los datos necesarios
        if (!$this->config || !$this->config->meteor_id) {
            throw new \RuntimeException('No se encontraron datos suficientes para actualizar el usuario en Meteor');
        }

        // Preparar datos del usuario para Meteor
        $data = array(
            'profile' => array(
                'avatar' => isset($this->config->avatar) ? $this->mulbinUrl . $this->config->avatar : $this->mulbinUrl . '/images/avatar.png',
                'firstName' => $this->config->nombre ?: '',
                'lastName' => $this->config->apellidos ?: '',
                'phone' => $this->config->phone_number ?: '',
                'company' => $this->config->empresa ?: ''
            )
        );

        $response = $this->makeMeteorRequest('/update-user/' . $this->config->meteor_id, 'PUT', $data);

        return $response;
    }

    /**
     * Realiza una petición a la API de Meteor
     * @param string $endpoint URL del endpoint a la API de Meteor
     * @param string $method Método HTTP a usar (GET, POST, PUT, PATCH)
     * @param array|null $data Datos a enviar en la petición
     * @return array Respuesta de la API de Meteor
     */
    public function makeMeteorRequest($endpoint, $method = 'GET', $data = null)
    {
        $url = $this->meteorBaseUrl . $endpoint;

        $headers = [
            'Authorization: ApiKey ' . $this->meteorApiKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return [
            'status' => $httpCode,
            'data' => json_decode($response)
        ];
    }

    /**
     * Genera una contraseña aleatoria segura
     * @return string
     */
    private function generateRandomPassword()
    {
        // Generar hash aleatorio seguro
        $randomBytes = '';

        // Intentar usar métodos seguros disponibles
        if (function_exists('random_bytes')) {
            $randomBytes = random_bytes(32);
        } elseif (function_exists('openssl_random_pseudo_bytes')) {
            $randomBytes = openssl_random_pseudo_bytes(32);
        } else {
            // Fallback para sistemas más antiguos
            $randomBytes = hash('sha256', uniqid(mt_rand(), true) . time() . $this->contrato, true);
        }

        // Crear hash final
        return hash('sha256', $randomBytes . $this->config->username . time());
    }

    public function getContrato()
    {
        return $this->contrato;
    }

    public function getConfig()
    {
        return $this->config;
    }

    public function getUsername()
    {
        return $this->username;
    }
}
