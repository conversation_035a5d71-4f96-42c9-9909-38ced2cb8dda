<?php

setlocale(LC_ALL, "es_MX");

include_once("languages/esp.php");
include_once("funciones.php");
$ancho_n = 300;
$alto_n = 100;
$ancho_p = 150;
$alto_p = 60;

function cm_optimizarimg($imagen, $tipo = null, $x_size = null, $y_size = null)
{
    global $ancho_n, $alto_n, $ancho_p, $alto_p;

    if ($tipo == 'LOGOPEQUE') {
        $ancho_maximo = $ancho_p;
        $alto_maximo = $alto_p;
    } else {
        $ancho_maximo = $ancho_n;
        $alto_maximo = $alto_n;
    }

    //////////////////////////////
    // * imagen a reducir $imagen * //
    //////////////////////////////
    $datos_imagen = GetImageSize($imagen);
    if ($datos_imagen[2] == 2) {
        $imgsrc = imagecreatefromjpeg($imagen);
    }
    if ($datos_imagen[2] == 3) {
        $imgsrc = imagecreatefrompng($imagen);
    }

    // se obtienen las dimensiones de la imagen
    $srcx = $x_size = $datos_imagen[0];
    $srcy = $y_size = $datos_imagen[1];

    if ($srcx > $ancho_maximo) {
        $x_size = $ancho_maximo;
        $y_size = round($srcy * $x_size / $srcx);
    }

    if ($y_size > $alto_maximo) {
        $x_size = round($alto_maximo * $x_size / $y_size);
        $y_size = $alto_maximo;
    }

    if ($srcx == $x_size && $srcy == $y_size) {
        return;
    }

    /////////////////////////////////
    // * plantilla en blanco imgtrg * //
    /////////////////////////////////

    $imgtrg = imagecreatetruecolor($x_size, $y_size);

    // Preservar transparencia para PNG
    if ($datos_imagen[2] == 3) {
        imagealphablending($imgtrg, false);
        imagesavealpha($imgtrg, true);
        $transparent = imagecolorallocatealpha($imgtrg, 255, 255, 255, 127);
        imagefill($imgtrg, 0, 0, $transparent);
    }

    imagecopyresampled($imgtrg, $imgsrc, 0, 0, 0, 0, $x_size, $y_size, $srcx, $srcy);
    if ($datos_imagen[2] == 2) {
        imagejpeg($imgtrg, "$imagen");
    }
    if ($datos_imagen[2] == 3) {
        imagepng($imgtrg, "$imagen");
    }

    imagedestroy($imgtrg);
    imagedestroy($imgsrc);
}
include_once("verifica_pago.php");

unset($_COOKIE['tema']);
unset($_SESSION['tema']);
setcookie('tema', '');

include_once("theme.php");
cm_valida_usuario('ADMIN');
cm_permiso('ADMIN');

unset($_COOKIE['tema']);
unset($_SESSION['tema']);
setcookie('tema', '');

//////////////
// Recupero el encabezado y lo despliego, también defino variables para socios AMPI
$plantilla = cm_extrae_plantilla_mto();
include_once("enc_mto.php");


//////////////
// Despliego la primer pantalla de presentación de información
if (trim($tipo_tema) == '') {
    $themeWithConf = array(
        'base' => array('cp' => "#2a1c72", 'cs' => "#007bff", 'ct' => "#6c757d", 'tp' => "#000000", 'ts' => "#696969"),
        'Default' => array('cp' => "#ffffff", 'cs' => "#ffffff", 'ct' => "#ffffff", 'tp' => "#000000", 'ts' => "#696969")
    );

    // Generar opciones de temas genéricos como array de objetos
    $temasGenericos = array();
    $direct = opendir('./themes');
    $fic = readdir($direct);
    while ($fic) {
        if (strpos($fic, '.') !== 0) {
            $temasGenericos[] = array(
                'value' => $fic,
                'label' => $fic,
                'selected' => ($config['tipo_tema'] == 'Generico' && $config['theme'] == $fic)
            );
        }
        $fic = readdir($direct);
    }

    // Generar opciones de temas personalizados como array de objetos
    $temasPersonalizados = array();
    $rthemes = "{$config['ruta']}www/themes";

    if (file_exists($rthemes)) {
        $direct = opendir($rthemes);
        $fic = readdir($direct);
        while ($fic) {
            if (strpos($fic, '.') !== 0) {
                $temasPersonalizados[] = array(
                    'value' => $fic,
                    'label' => $fic,
                    'selected' => ($config['tipo_tema'] == 'Personalizado' && $config['theme'] == $fic && is_dir("{$config['ruta']}www/themes/$fic"))
                );
            }
            $fic = readdir($direct);
        }
    }

    if (substr($config['theme'], 0, 9) == 'joomla---') {
        $tmp = explode('---', $config['theme']);
        $config['theme'] = $tmp[1];
    }

    if (file_exists("$config[ruta]www/joomla/templates")) {
        $direct = opendir("$config[ruta]www/joomla/templates");
        $fic = readdir($direct);
        while ($fic) {
            if ($fic != '.' && $fic != '..') {
                $temasPersonalizados[] = array(
                    'value' => "joomla---{$fic}",
                    'label' => "{$fic} (Joomla)",
                    'selected' => ($config['tipo_tema'] == 'Personalizado' && $config['theme'] == $fic && is_dir("{$config['ruta']}www/joomla/templates/{$fic}"))
                );
            }
            $fic = readdir($direct);
        }
    }

    // Hago chequeo de diseño de la versión 2 del sistema
    if (file_exists("$config[ruta]www/pgfuentes/plantilla_esp.htm")) {
        $temasPersonalizados[] = array(
            'value' => 'SI.v2',
            'label' => 'Diseño de versión 2 o anterior',
            'selected' => ($config['theme'] == 'SI.v2')
        );
    }

    // Verificar logotipos existentes
    $tiempo = time();
    $logoActual = '';
    $logopequeActual = '';

    if (file_exists("$rutasir/logos/$config[contrato]/logo.png")) {
        $logoActual = "/logos/$config[contrato]/logo.png?t=$tiempo";
    } elseif (file_exists("$rutasir/logos/$config[contrato]/logo.gif")) {
        $logoActual = "/logos/$config[contrato]/logo.gif?t=$tiempo";
    } elseif (file_exists("$rutasir/logos/$config[contrato]/logo.jpg")) {
        $logoActual = "/logos/$config[contrato]/logo.jpg?t=$tiempo";
    }

    if (file_exists("$rutasir/logos/$config[contrato]/logopeque.png")) {
        $logopequeActual = "/logos/$config[contrato]/logopeque.png?t=$tiempo";
    } elseif (file_exists("$rutasir/logos/$config[contrato]/logopeque.gif")) {
        $logopequeActual = "/logos/$config[contrato]/logopeque.gif?t=$tiempo";
    } elseif (file_exists("$rutasir/logos/$config[contrato]/logopeque.jpg")) {
        $logopequeActual = "/logos/$config[contrato]/logopeque.jpg?t=$tiempo";
    }

    // Preparar variables para el template
    $render = array(
        'TIPO_TEMA_GENERICO_CHECKED' => ($config['tipo_tema'] == 'Generico') ? 'checked' : '',
        'TIPO_TEMA_PERSONALIZADO_CHECKED' => ($config['tipo_tema'] == 'Personalizado') ? 'checked' : '',
        'TEMA_GENERICO_DISABLED' => ($config['tipo_tema'] == 'Personalizado') ? 'disabled' : '',
        'TEMA_PERSONALIZADO_DISABLED' => ($config['tipo_tema'] == 'Generico') ? 'disabled' : '',
        'TEMAS_GENERICOS' => $temasGenericos,
        'TEMAS_PERSONALIZADOS' => $temasPersonalizados,
        'SHOW_CUSTOM_THEMES' => (count($temasPersonalizados) > 0),
        'ANCHO_N' => $ancho_n,
        'ALTO_N' => $alto_n,
        'ANCHO_P' => $ancho_p,
        'ALTO_P' => $alto_p,
        'LOGO_ACTUAL' => $logoActual,
        'LOGOPEQUE_ACTUAL' => $logopequeActual,
        'THEME_CONFIG_LIST' => json_encode($themeWithConf)
    );

    $salida = cm_lee_archivo(__DIR__.'/templates/panel/sitio_web/temas.mustache');
    echo $mustache->render($salida, $render);


} //

//////////////
// En caso de guardar la información en la base de datos
else {

    ///////
    // Tratamiento del tema
    if ($tipo_tema == 'Personalizado') {
        $tema = $tema_personalizado;
    } else {
        $tema = $tema_generico;
    }

    // Actualizo la información del tema en la base de datos
    $query = "UPDATE config SET tipo_tema='{$tipo_tema}', theme='{$tema}' WHERE (contrato='{$config['contrato']}') LIMIT 1";
    $sql->query($query);
    $changed = $sql->affected_rows;

    ///////
    // Si no existen los directorios para los logotipos
    $ruta_logos = "$rutasir/logos";
    if (!file_exists($ruta_logos)) {
        if (!mkdir($ruta_logos)) {
            die('No es posible crear el directorio a usar de fotos');
        }
    }
    $ruta_logos = "$rutasir/logos/$config[contrato]";
    if (!file_exists($ruta_logos)) {
        if (!mkdir($ruta_logos)) {
            die('No es posible crear el directorio a usar de fotos');
        }
    }

    ///////
    // Tratamiento del logotipo grande
    if (trim($logo['tmp_name']) != '') {
        $datos_imagen = GetImageSize($logo['tmp_name']);

        if ($datos_imagen[2] == 1) {
            move_uploaded_file($logo['tmp_name'], "$ruta_logos/logo.gif");
            if ($datos_imagen[0] > $ancho_n || $datos_imagen[1] > $alto_n) {
                unlink("$ruta_logos/logo.gif");
                echo '<p align="center"><b><font size="2" face="Arial" color="#CC3300">Error: El tamaño de la imagen es mayor al recomendado, por lo que no se subió el archivo.</font></b><p>';
            } else {
                if (file_exists("$ruta_logos/logo.jpg")) {
                    unlink("$ruta_logos/logo.jpg");
                }
                if (file_exists("$ruta_logos/logo.png")) {
                    unlink("$ruta_logos/logo.png");
                }
            }
        } elseif ($datos_imagen[2] == 2) {
            move_uploaded_file($logo['tmp_name'], "$ruta_logos/logo.jpg");
            cm_optimizarimg("$ruta_logos/logo.jpg");
            if (file_exists("$ruta_logos/logo.gif")) {
                unlink("$ruta_logos/logo.gif");
            }
            if (file_exists("$ruta_logos/logo.png")) {
                unlink("$ruta_logos/logo.png");
            }
        } elseif ($datos_imagen[2] == 3) {
            move_uploaded_file($logo['tmp_name'], "$ruta_logos/logo.png");
            cm_optimizarimg("$ruta_logos/logo.png");
            if (file_exists("$ruta_logos/logo.jpg")) {
                unlink("$ruta_logos/logo.jpg");
            }
            if (file_exists("$ruta_logos/logo.gif")) {
                unlink("$ruta_logos/logo.gif");
            }
        } else {
            echo '<p align="center"><b><font size="2" face="Arial" color="#CC3300">Error: El formato de imagen del logotipo no es válido.</font></b><p>';
        }
    }


    ///////
    // Tratamiento del logotipo pequeño
    if (trim($logopeque['tmp_name']) != '') {
        $datos_imagen = GetImageSize($logopeque['tmp_name']);

        if ($datos_imagen[2] == 1) {
            move_uploaded_file($logopeque['tmp_name'], "$ruta_logos/logopeque.gif");
            if ($datos_imagen[0] > $ancho_p || $datos_imagen[1] > $alto_p) {
                unlink("$ruta_logos/logopeque.gif");
                echo '<p align="center"><b><font size="2" face="Arial" color="#CC3300">Error: El tamaño de la imagen es mayor al recomendado, por lo que no se subió el archivo.</font></b><p>';
            } else {
                if (file_exists("$ruta_logos/logopeque.jpg")) {
                    unlink("$ruta_logos/logopeque.jpg");
                }
                if (file_exists("$ruta_logos/logopeque.png")) {
                    unlink("$ruta_logos/logopeque.png");
                }
            }
        } elseif ($datos_imagen[2] == 2) {
            move_uploaded_file($logopeque['tmp_name'], "$ruta_logos/logopeque.jpg");
            cm_optimizarimg("$ruta_logos/logopeque.jpg", 'LOGOPEQUE');
            if (file_exists("$ruta_logos/logopeque.gif")) {
                unlink("$ruta_logos/logopeque.gif");
            }
            if (file_exists("$ruta_logos/logopeque.png")) {
                unlink("$ruta_logos/logopeque.png");
            }
        } elseif ($datos_imagen[2] == 3) {
            move_uploaded_file($logopeque['tmp_name'], "$ruta_logos/logopeque.png");
            cm_optimizarimg("$ruta_logos/logopeque.png", 'LOGOPEQUE');
            if (file_exists("$ruta_logos/logopeque.jpg")) {
                unlink("$ruta_logos/logopeque.jpg");
            }
            if (file_exists("$ruta_logos/logopeque.gif")) {
                unlink("$ruta_logos/logopeque.gif");
            }
        } else {
            echo '<p align="center"><b><font size="2" face="Arial" color="#CC3300">Error: El formato de imagen del logotipo miniatura no es válido.</font></b><p>';
        }
    }

    // Mostrar pantalla de éxito usando template específico
    $mensajeExito = 'La configuración de la apariencia ha sido actualizada correctamente.';

    $render = array(
        'MENSAJE_EXITO' => $mensajeExito,
        'CAMBIO_TEMA' => ($changed ? true : false)
    );

    $salida = cm_lee_archivo(__DIR__.'/templates/panel/sitio_web/temas_exito.mustache');
    echo $mustache->render($salida, $render);
}

// Despliego el pie de página y termino la ejecución del programa
echo $mustache->render($plantilla['pie'], $render);
