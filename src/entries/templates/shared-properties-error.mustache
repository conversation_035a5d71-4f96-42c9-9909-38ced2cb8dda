<div class="container px-4 py-8 mx-auto max-w-2xl">
    <div class="overflow-hidden bg-white rounded-lg shadow-xl">
        <!-- Header -->
        <div class="px-6 py-4 bg-gradient-to-r from-red-500 to-pink-600">
            <h2 class="flex items-center text-xl font-semibold text-white">
                <svg class="mr-3 w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                Ups, algo salió mal
            </h2>
        </div>

        <!-- Contenido -->
        <div class="p-8 text-center">
            <div class="mb-8">
                <!-- Icono de error -->
                <div class="flex justify-center mb-6">
                    <div class="flex justify-center items-center w-20 h-20 bg-red-100 rounded-full">
                        <svg class="w-12 h-12 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
                
                <h3 class="mb-4 text-2xl font-bold text-gray-900">¡Oops!</h3>
                <p class="mb-6 text-lg text-gray-700">{{mensaje}}</p>
                
                <!-- Información adicional de ayuda -->
                <div class="p-6 mb-6 bg-gradient-to-r from-red-50 to-pink-50 rounded-xl border border-red-200">
                    <div class="flex justify-center mb-4">
                        <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h4 class="mb-3 text-lg font-semibold text-red-800">Posibles causas del problema:</h4>
                    <div class="space-y-2 text-sm text-left text-red-700">
                        <div class="flex items-start">
                            <svg class="flex-shrink-0 mt-1 mr-2 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span>El enlace puede haber expirado o ser inválido</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="flex-shrink-0 mt-1 mr-2 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span>La solicitud ya pudo haber sido procesada anteriormente</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="flex-shrink-0 mt-1 mr-2 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span>Puede existir un problema técnico temporal</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sugerencias -->
            <div class="p-4 mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                <div class="flex justify-center mb-3">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h4 class="mb-3 text-lg font-semibold text-blue-800">¿Qué puedes hacer?</h4>
                <div class="space-y-2 text-sm text-left text-blue-700">
                    <div class="flex items-start">
                        <svg class="flex-shrink-0 mt-1 mr-2 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <span>Verifica que el enlace esté completo y correcto</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="flex-shrink-0 mt-1 mr-2 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <span>Contacta a tu socio para solicitar un nuevo enlace</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="flex-shrink-0 mt-1 mr-2 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <span>Intenta acceder más tarde si es un problema temporal</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="flex-shrink-0 mt-1 mr-2 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <span>Revisa tu panel de control para gestionar solicitudes</span>
                    </div>
                </div>
            </div>

            <!-- Botones de acción -->
            <div class="flex flex-col gap-4 justify-center sm:flex-row">
                <a href="/" class="inline-flex justify-center items-center px-8 py-3 font-semibold text-white bg-gradient-to-r rounded-lg shadow-lg transition-all duration-200 transform from-mulbin-500 to-mulbin-600 hover:from-mulbin-600 hover:to-mulbin-700 hover:shadow-xl hover:scale-105">
                    <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                    Ir al Panel Principal
                </a>
                
                <a href="/propiedades/solicitudes" class="inline-flex justify-center items-center px-8 py-3 font-semibold bg-white rounded-lg border-2 transition-all duration-200 transform text-mulbin-600 border-mulbin-500 hover:bg-mulbin-50 hover:scale-105">
                    <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a2 2 0 002 2h8a2 2 0 002-2V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                    </svg>
                    Ver Mis Solicitudes
                </a>
            </div>

            <!-- Contacto de soporte -->
            <div class="p-4 mt-8 text-xs text-center text-gray-500 bg-gray-50 rounded-lg">
                <p class="flex justify-center items-center mb-2">
                    <svg class="mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                    ¿Necesitas ayuda adicional?
                </p>
                <p>Contacta a nuestro equipo de soporte técnico o consulta la documentación del sistema.</p>
            </div>
        </div>
    </div>
</div>
