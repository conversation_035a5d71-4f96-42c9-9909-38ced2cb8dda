<link
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
  rel="stylesheet"
/>

<div class="container px-2 mx-auto sm:px-4 md:py-8">
  <!-- Header -->
  <div class="px-4 mb-4 text-center sm:mb-8">
    <h1
      class="mb-2 text-2xl font-bold leading-tight text-gray-800 sm:text-3xl lg:text-4xl"
    >
      Apariencia del Sitio Web
    </h1>
  </div>

  <!-- Main Card -->
  <div class="mx-auto max-w-4xl">
    <div
      class="overflow-hidden bg-white rounded-2xl border border-gray-100 shadow-xl"
    >
      <!-- Card Header -->
      <div class="px-6 py-4 bg-gradient-to-r to-indigo-600 from-mulbin-500">
        <div class="flex items-center space-x-3">
          <div
            class="flex justify-center items-center w-10 h-10 rounded-lg bg-white/20"
          >
            <i class="text-lg text-white fas fa-brush"></i>
          </div>
          <div>
            <h2 class="text-xl font-semibold text-white">Personalización</h2>
            <p class="text-sm text-blue-100">
              Configura la apariencia visual de tu sitio web
            </p>
          </div>
        </div>
      </div>

      <!-- Card Body -->
      <div class="p-6">
        <form method="POST" enctype="multipart/form-data" class="space-y-8">
          <!-- Theme Selection Section -->
          <div class="space-y-4">
            <h3 class="flex items-center text-lg font-semibold text-gray-800">
              <i class="mr-2 text-blue-500 fas fa-swatchbook"></i>
              Tema a utilizar
            </h3>
            
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
              <!-- Generic Themes -->
              <div class="p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center mb-3">
                  <input type="radio" value="Generico" name="tipo_tema" id="tipo_tema_generico" 
                         onclick="chequeo(this);" {{TIPO_TEMA_GENERICO_CHECKED}} 
                         class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500">
                  <label for="tipo_tema_generico" class="ml-2 text-sm font-medium text-gray-700">
                    Temas Genéricos
                  </label>
                </div>
                <select name="tema_generico" onchange="checkThemeConf();" {{TEMA_GENERICO_DISABLED}}
                        class="px-3 py-2 w-full text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                  {{#TEMAS_GENERICOS}}
                  <option value="{{value}}" {{#selected}}selected{{/selected}}>{{label}}</option>
                  {{/TEMAS_GENERICOS}}
                </select>
              </div>

              <!-- Custom Themes -->
              {{#SHOW_CUSTOM_THEMES}}
              <div class="p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center mb-3">
                  <input type="radio" name="tipo_tema" id="tipo_tema_personalizado" value="Personalizado" 
                         onclick="chequeo(this);" {{TIPO_TEMA_PERSONALIZADO_CHECKED}}
                         class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500">
                  <label for="tipo_tema_personalizado" class="ml-2 text-sm font-medium text-gray-700">
                    Temas Personalizados
                  </label>
                </div>
                <select name="tema_personalizado" onchange="checkThemeConf();" {{TEMA_PERSONALIZADO_DISABLED}}
                        class="px-3 py-2 w-full text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                  {{#TEMAS_PERSONALIZADOS}}
                  <option value="{{value}}" {{#selected}}selected{{/selected}}>{{label}}</option>
                  {{/TEMAS_PERSONALIZADOS}}
                </select>
              </div>
              {{/SHOW_CUSTOM_THEMES}}
              {{^SHOW_CUSTOM_THEMES}}
              <input type="hidden" name="tema_personalizado" value="Default">
              {{/SHOW_CUSTOM_THEMES}}
            </div>
          </div>

          <!-- Logo Section -->
          <div class="space-y-6">
            <h3 class="flex items-center text-lg font-semibold text-gray-800">
              <i class="mr-2 text-blue-500 fas fa-image"></i>
              Logotipos
            </h3>

            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
              <!-- Main Logo -->
              <div class="p-4 bg-gray-50 rounded-lg">
                <h4 class="mb-2 text-sm font-medium text-gray-700">
                  Logotipo Principal
                </h4>
                <p class="mb-3 text-xs text-gray-500">
                  Se recomienda formato PNG, GIF o JPG no mayor de {{ANCHO_N}} x {{ALTO_N}} píxeles
                </p>
                
                {{#LOGO_ACTUAL}}
                <div class="mb-3 text-center">
                  <img src="{{LOGO_ACTUAL}}" alt="Logo actual" class="max-w-full h-auto rounded border">
                </div>
                {{/LOGO_ACTUAL}}
                
                <input type="file" name="logo" accept="image/*"
                       class="px-3 py-2 w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
              </div>

              <!-- Mini Logo -->
              <div class="p-4 bg-gray-50 rounded-lg">
                <h4 class="mb-2 text-sm font-medium text-gray-700">
                  Logotipo Miniatura
                </h4>
                <p class="mb-3 text-xs text-gray-500">
                  Se recomienda formato PNG, GIF o JPG no mayor de {{ANCHO_P}} x {{ALTO_P}} píxeles
                </p>
                
                {{#LOGOPEQUE_ACTUAL}}
                <div class="mb-3 text-center">
                  <img src="{{LOGOPEQUE_ACTUAL}}" alt="Logo miniatura actual" class="max-w-full h-auto rounded border">
                </div>
                {{/LOGOPEQUE_ACTUAL}}
                
                <input type="file" name="logopeque" accept="image/*"
                       class="px-3 py-2 w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="pt-4">
            <button type="submit"
                    class="px-6 py-3 w-full font-semibold text-white bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:shadow-xl">
              <i class="mr-2 fas fa-save"></i>
              Guardar Configuración
            </button>
          </div>

          <input type="hidden" name="paso" value="2">
        </form>
      </div>
    </div>


    <!-- Error Messages -->
    {{#MENSAJE_ERROR}}
    <div class="p-6 mt-6 bg-red-50 rounded-xl border border-red-200">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <div class="flex justify-center items-center w-8 h-8 bg-red-100 rounded-lg">
            <i class="text-red-600 fas fa-exclamation-triangle"></i>
          </div>
        </div>
        <div>
          <h3 class="text-sm font-medium text-red-800">
            Error en la configuración
          </h3>
          <p class="text-sm text-red-600">
            {{MENSAJE_ERROR}}
          </p>
        </div>
      </div>
    </div>
    {{/MENSAJE_ERROR}}
  </div>
</div>

<script>
  var themeConfigList = {{{THEME_CONFIG_LIST}}};

  function checkThemeConf() {
    var tipoTema = document.querySelector('input[name="tipo_tema"]:checked');
    if (!tipoTema) return;
    
    var selectName = tipoTema.value === "Generico" ? 'tema_generico' : 'tema_personalizado';
    var selectedTheme = document.getElementsByName(selectName)[0].value;
    var ifThemeConfig = themeConfigList[selectedTheme];
    
    if (ifThemeConfig) {
      var editorTema = document.getElementById('trEditorTema');
      if (editorTema) {
        editorTema.removeAttribute('style');
        var form = document.getElementById('Formulario1');
        if (form.bgPrimario) form.bgPrimario.value = ifThemeConfig.cp;
        if (form.bgSecundario) form.bgSecundario.value = ifThemeConfig.cs;
        if (form.bgTerciario) form.bgTerciario.value = ifThemeConfig.ct;
        if (form.colorPrimario) form.colorPrimario.value = ifThemeConfig.tp;
        if (form.colorSecundario) form.colorSecundario.value = ifThemeConfig.ts;
      }
    } else {
      var editorTema = document.getElementById('trEditorTema');
      if (editorTema) {
        editorTema.setAttribute('style', 'display:none;');
      }
    }
  }

  function chequeo(vRadio) {
    var temasGenericos = document.getElementsByName('tema_generico')[0];
    var temasPersonalizados = document.getElementsByName('tema_personalizado')[0];
    
    if (vRadio.value == 'Generico') {
      if (temasGenericos) temasGenericos.disabled = false;
      if (temasPersonalizados) temasPersonalizados.disabled = true;
    } else {
      if (temasGenericos) temasGenericos.disabled = true;
      if (temasPersonalizados) temasPersonalizados.disabled = false;
    }
    checkThemeConf();
  }

  // Initialize on page load
  document.addEventListener('DOMContentLoaded', function() {
    var checkedRadio = document.querySelector('input[name="tipo_tema"]:checked');
    if (checkedRadio) {
      chequeo(checkedRadio);
    }
  });
</script>