<div class="container px-4 py-8 mx-auto max-w-5xl">
    <div class="overflow-hidden bg-white rounded-lg shadow-xl">
        <!-- Header -->
        <div class="px-6 py-4 bg-gradient-to-r from-mulbin-500 to-mulbin-600">
            <h2 class="flex items-center text-xl font-semibold text-white">
                <div class="overflow-hidden mr-3 w-12 h-12 bg-white bg-opacity-20 rounded-full">
                    <div class="flex justify-center items-center w-full h-full">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                        </svg>
                    </div>
                </div>
                Solicitud de Publicación de tu Inmueble
            </h2>
        </div>

        <!-- Párrafo explicativo -->
        <div class="p-6 bg-blue-50 border-b">
            <p class="leading-relaxed text-gray-700">
                Tu socio <strong>{{solicitante.nombre}} {{solicitante.apellidos}}</strong> de <strong>{{solicitante.empresa}}</strong> está solicitando publicar tu inmueble en su sitio web. Al aceptar esta solicitud, tu propiedad aparecerá en el listado de su sitio web y él podrá promocionarla a sus clientes.
            </p>
        </div>

        <div class="p-6">
            <!-- Fecha de la solicitud -->
            <div class="p-4 mb-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-r-lg border-l-4 border-blue-400">
                <div class="flex items-center">
                    <svg class="mr-3 w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <p class="font-medium text-blue-800">Fecha de solicitud</p>
                        <p class="text-sm text-blue-600">{{fecha_solicitud}}</p>
                    </div>
                </div>
            </div>

            <div class="grid gap-6 lg:grid-cols-2">
                <!-- Card de la Propiedad -->
                <div class="overflow-hidden bg-white rounded-xl border-2 border-gray-200 shadow-lg">
                    <div class="px-4 py-3 bg-gradient-to-r from-green-500 to-teal-600">
                        <h3 class="flex items-center font-semibold text-white">
                            <svg class="mr-3 w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                            </svg>
                            Clave del inmueble: {{inmueble.clave}}
                        </h3>
                    </div>
                    
                    {{#inmueble.imagen_principal}}
                    <div class="relative h-48 bg-gray-200">
                        <img src="{{inmueble.imagen_principal}}" alt="{{inmueble.titulo}}" class="object-cover w-full h-full">
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 text-xs font-semibold text-white bg-black bg-opacity-70 rounded-full">
                                {{inmueble.tipo}}
                            </span>
                        </div>
                    </div>
                    {{/inmueble.imagen_principal}}
                    {{^inmueble.imagen_principal}}
                    <div class="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200">
                        <div class="flex justify-center items-center w-full h-full">
                            <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 text-xs font-semibold text-green-800 whitespace-nowrap bg-green-100 rounded-full">
                                {{inmueble.tipo}}
                            </span>
                        </div>
                    </div>
                    {{/inmueble.imagen_principal}}
                    
                    <div class="p-4">
                        <h4 class="text-lg font-bold text-gray-900 line-clamp-2">{{inmueble.titulo}}</h4>
                        <div class="p-3 mb-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <svg class="mr-2 w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                    </svg>
                                    <div>
                                        <div class="flex items-baseline">
                                            <span class="text-2xl font-bold text-green-700">{{inmueble.precio_formateado}}</span>
                                            {{#inmueble.moneda}}
                                                <span class="ml-1 text-sm text-green-600">{{inmueble.moneda}}</span>
                                            {{/inmueble.moneda}}
                                        </div>
                                        <div class="flex items-center">
                                            <span class="text-sm font-medium text-green-600 uppercase">{{inmueble.operacion}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">
                                        🏠 {{inmueble.operacion}}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex items-center">
                                <svg class="mr-2 w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                </svg>
                                <span>{{inmueble.ubicacion}}</span>
                            </div>
                            {{#inmueble.recamaras}}
                            <div class="flex items-center">
                                <svg class="mr-2 w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                                </svg>
                                <span>{{inmueble.recamaras}} recámaras</span>
                                {{#inmueble.banos}}
                                <span class="mx-2">•</span>
                                <span>{{inmueble.banos}} baños</span>
                                {{/inmueble.banos}}
                            </div>
                            {{/inmueble.recamaras}}
                            {{#inmueble.area}}
                            <div class="flex items-center">
                                <svg class="mr-2 w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                </svg>
                                <span>{{inmueble.area}} m²</span>
                            </div>
                            {{/inmueble.area}}
                        </div>
                        
                        {{#inmueble.descripcion}}
                        <div class="pt-3 mt-3 border-t border-gray-200">
                            <p class="text-sm text-gray-600 line-clamp-3">{{inmueble.descripcion}}</p>
                        </div>
                        {{/inmueble.descripcion}}
                    </div>
                </div>

                <!-- Card del Solicitante -->
                <div class="overflow-hidden bg-white rounded-xl border-2 border-gray-200 shadow-lg">
                    <div class="px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600">
                        <h3 class="flex items-center font-semibold text-white">
                            <div class="overflow-hidden flex-shrink-0 mr-3 w-8 h-8 bg-white bg-opacity-20 rounded-full">
                                {{#solicitante.avatar}}
                                    <img src="{{solicitante.avatar}}" alt="Avatar de {{solicitante.usuario}}" class="object-cover w-full h-full">
                                {{/solicitante.avatar}}
                                {{^solicitante.avatar}}
                                    <div class="flex justify-center items-center w-full h-full">
                                        <span class="text-sm font-semibold text-white">{{solicitante.iniciales}}</span>
                                    </div>
                                {{/solicitante.avatar}}
                            </div>
                            Solicitante
                        </h3>
                    </div>
                    
                    <div class="p-4">
                        <h4 class="mb-3 text-lg font-semibold text-gray-900">{{solicitante.empresa}}</h4>
                        <div class="space-y-3 text-sm text-gray-600">
                            <div class="flex items-center">
                                <svg class="mr-2 w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                </svg>
                                <span>{{solicitante.nombre}} {{solicitante.apellidos}}</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="mr-2 w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                </svg>
                                <span>{{solicitante.ciudad}}, {{solicitante.estado}}</span>
                            </div>
                            {{#solicitante.telefono}}
                            <div class="flex items-center">
                                <svg class="mr-2 w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                </svg>
                                <span>{{solicitante.telefono}}</span>
                            </div>
                            {{/solicitante.telefono}}
                            {{#solicitante.email}}
                            <div class="flex items-center">
                                <svg class="mr-2 w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                                </svg>
                                <span>{{solicitante.email}}</span>
                            </div>
                            {{/solicitante.email}}
                        </div>
                        
                        <!-- Beneficios -->
                        <div class="p-3 mt-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                            <h5 class="mb-2 text-sm font-semibold text-indigo-800">💡 Beneficios de autorizar</h5>
                            <ul class="space-y-1 text-xs text-indigo-700">
                                <li class="flex items-center">
                                    <svg class="mr-1 w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                    Mayor exposición de la propiedad
                                </li>
                                <li class="flex items-center">
                                    <svg class="mr-1 w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                    Acceso a nueva base de clientes
                                </li>
                                <li class="flex items-center">
                                    <svg class="mr-1 w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                    Colaboración entre profesionales
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Información sobre el método -->
            {{#metodo}}
            <div class="p-4 mt-6 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-r-lg border-l-4 border-yellow-400">
                <div class="flex items-center">
                    <svg class="mr-3 w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <p class="font-medium text-yellow-800">Método de colaboración: <span class="uppercase">{{metodo}}</span></p>
                        <p class="text-sm text-yellow-700">Esta propiedad se publicará en tu sitio web siguiendo este método de colaboración.</p>
                    </div>
                </div>
            </div>
            {{/metodo}}

            <!-- Botones de acción -->
            <div class="flex flex-col gap-4 justify-center mt-8 sm:flex-row">
                <form method="POST" class="flex-1 sm:flex-none">
                    <input type="hidden" name="token" value="{{token}}">
                    <input type="hidden" name="action" value="autorizar">
                    <button type="submit" class="flex justify-center items-center px-8 py-4 w-full font-semibold text-white bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg transition-all duration-200 transform sm:w-auto hover:from-green-600 hover:to-green-700 hover:shadow-xl hover:scale-105">
                        <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Autorizar Publicación
                    </button>
                </form>
                
                <form method="POST" class="flex-1 sm:flex-none">
                    <input type="hidden" name="token" value="{{token}}">
                    <input type="hidden" name="action" value="rechazar">
                    <button type="submit" class="flex justify-center items-center px-8 py-4 w-full font-semibold text-white bg-gradient-to-r from-red-500 to-red-600 rounded-lg shadow-lg transition-all duration-200 transform sm:w-auto hover:from-red-600 hover:to-red-700 hover:shadow-xl hover:scale-105" 
                            onclick="return confirm('¿Estás seguro de que deseas rechazar esta solicitud de publicación?')">
                        <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                        Rechazar Solicitud
                    </button>
                </form>
            </div>

            <!-- Nota legal -->
            <div class="p-3 mt-6 text-xs text-center text-gray-500 bg-gray-50 rounded-lg">
                <p>Al autorizar esta solicitud, permites que la propiedad sea visible en el sitio web de tu socio. Puedes gestionar o revocar estos permisos desde tu panel de control.</p>
            </div>
        </div>
    </div>
</div>
