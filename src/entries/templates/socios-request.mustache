<div class="container px-4 py-8 mx-auto max-w-4xl">
    <div class="overflow-hidden bg-white rounded-lg shadow-lg">
        <!-- Header -->
        <div class="px-6 py-4 bg-mulbin-500">
            <h2 class="flex items-center text-xl font-semibold text-white">
                <div class="overflow-hidden mr-3 w-12 h-12 bg-gray-200 rounded-full">
                        {{#solicitado.avatar}}
                            <img src="{{solicitado.avatar}}" alt="Avatar de {{solicitado.usuario}}" class="object-cover w-full h-full">
                        {{/solicitado.avatar}}
                        {{^solicitado.avatar}}
                            <div class="flex justify-center items-center w-full h-full bg-mulbin-500">
                                <span class="text-lg font-semibold text-white">{{solicitado.iniciales}}</span>
                            </div>
                        {{/solicitado.avatar}}
                    </div>
                Solicitud de Conexión entre Socios
            </h2>
        </div>

        <div class="p-6">
            <!-- <PERSON><PERSON> de la solicitud -->
            <div class="p-4 mb-6 bg-blue-50 border-l-4 border-blue-400">
                <div class="flex items-center">
                    <svg class="mr-3 w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <p class="font-medium text-blue-800">Fecha de solicitud</p>
                        <p class="text-sm text-blue-600">{{fecha_solicitud}}</p>
                    </div>
                </div>
            </div>

            <!-- Card de socio solicitante -->
            <div class="flex justify-center mb-6">
                <!-- Socio Solicitante -->
                <div class="overflow-hidden w-full max-w-md bg-white rounded-lg border-2 border-blue-200">
                    <div class="px-4 py-3 bg-gray-50 border-b border-blue-200">
                        <h3 class="flex items-center font-medium text-gray-800">
                            <div class="overflow-hidden flex-shrink-0 mr-3 w-12 h-12 bg-gray-200 rounded-full">
                                {{#solicitante.avatar}}
                                    <img src="{{solicitante.avatar}}" alt="Avatar de {{solicitante.usuario}}" class="object-cover w-full h-full">
                                {{/solicitante.avatar}}
                                {{^solicitante.avatar}}
                                    <div class="flex justify-center items-center w-full h-full bg-blue-500">
                                        <span class="text-sm font-semibold text-white">{{solicitante.iniciales}}</span>
                                    </div>
                                {{/solicitante.avatar}}
                            </div>
                            Socio Solicitante
                        </h3>
                    </div>
                    <div class="p-4">
                        <h4 class="mb-3 text-lg font-semibold text-gray-900">{{solicitante.empresa}}</h4>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex justify-start">
                                <span>{{solicitante.nombre}} {{solicitante.apellidos}}</span>
                            </div>
                            <div class="flex justify-start">
                                <span>{{solicitante.ciudad}}, {{solicitante.estado}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Botones de acción -->
            <div class="flex flex-col gap-4 justify-center sm:flex-row">
                <form method="POST" class="flex-1 sm:flex-none">
                    <input type="hidden" name="token" value="{{token}}">
                    <input type="hidden" name="action" value="aceptar">
                    <button type="submit" class="flex justify-center items-center px-8 py-3 w-full font-semibold text-white bg-green-500 rounded-lg transition duration-200 sm:w-auto hover:bg-green-600">
                        <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Aceptar Solicitud
                    </button>
                </form>
                
                <form method="POST" class="flex-1 sm:flex-none">
                    <input type="hidden" name="token" value="{{token}}">
                    <input type="hidden" name="action" value="rechazar">
                    <button type="submit" class="flex justify-center items-center px-8 py-3 w-full font-semibold text-white bg-red-500 rounded-lg transition duration-200 sm:w-auto hover:bg-red-600" 
                            onclick="return confirm('¿Estás seguro de que deseas rechazar esta solicitud?')">
                        <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                        Rechazar
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
