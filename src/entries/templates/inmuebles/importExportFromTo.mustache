<link
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
  rel="stylesheet"
/>

<div class="container px-2 mx-auto sm:px-4 md:py-8">
  <!-- Header -->
  <div class="px-4 mb-4 text-center sm:mb-8">
    <h1
      class="mb-2 text-2xl font-bold leading-tight text-gray-800 sm:text-3xl lg:text-4xl"
    >
      Configuración de Plataformas
    </h1>
  </div>

  <!-- Main Card -->
  <div class="mx-auto max-w-2xl">
    <div
      class="overflow-hidden bg-white rounded-2xl border border-gray-100 shadow-xl"
    >
      <!-- Card Header -->
      <div class="px-6 py-4 bg-gradient-to-r to-indigo-600 from-mulbin-500">
        <div class="flex items-center space-x-3">
          <div
            class="flex justify-center items-center w-10 h-10 rounded-lg bg-white/20"
          >
            <i class="text-lg text-white fas fa-key"></i>
          </div>
          <div>
            <h2 class="text-xl font-semibold text-white">API Keys</h2>
            <p class="text-sm text-blue-100">
              Configura tus claves de API para sincronizar servicios externos
            </p>
          </div>
        </div>
      </div>

      <!-- Card Body -->
      <div class="p-6">
        <form method="POST" class="space-y-6">
          <!-- API Key Input -->
          <div class="space-y-2">
            <label
              for="api_key"
              class="block text-sm font-medium text-gray-700"
            >
              <i class="mr-2 text-blue-500 fas fa-key"></i>
              API Key de EasyBroker
            </label>
            <div class="relative">
              <input
                type="text"
                name="api_key"
                id="api_key"
                value="{{EASYBROKER_APIKEY}}"
                placeholder="Ingresa tu API Key de EasyBroker"
                class="px-4 py-3 w-full placeholder-gray-400 rounded-lg border border-gray-300 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                autocomplete="off"
              />
              <div class="flex absolute inset-y-0 right-0 items-center pr-3">
                <i class="text-gray-400 fas fa-shield-alt"></i>
              </div>
            </div>
            <p class="mt-1 text-xs text-gray-500">
              <i class="mr-1 text-blue-500 fas fa-info-circle"></i>
              Esta clave se utiliza para sincronizar propiedades desde
              EasyBroker
            </p>
          </div>

          <!-- Hidden Input -->
          <input type="hidden" name="external_provider" value="easybroker" />

          <!-- Submit Button -->
          <div class="pt-4">
            <button
              type="submit"
              class="px-6 py-3 w-full font-semibold text-white bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:shadow-xl"
            >
              <i class="mr-2 fas fa-save"></i>
              Guardar Configuración
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Additional Info Card -->
    <div class="p-6 mt-6 bg-white rounded-xl border border-gray-100 shadow-lg">
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
          <div
            class="flex justify-center items-center w-8 h-8 bg-blue-100 rounded-lg"
          >
            <i class="text-blue-600 fas fa-lightbulb"></i>
          </div>
        </div>
        <div>
          <h3 class="mb-1 text-sm font-medium text-gray-800">
            ¿Necesitas ayuda?
          </h3>
          <p class="text-sm text-gray-600">
            Para obtener tu API Key de EasyBroker, visita su sección de ayuda en
            <a
              href="https://dev.easybroker.com/docs/autenticaci%C3%B3n"
              target="_blank"
              class="text-blue-600 underline hover:text-blue-800"
            >
              API Reference
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Success Toast (opcional, se puede mostrar con JavaScript) -->
<div
  id="success-toast"
  class="hidden fixed top-4 right-4 px-6 py-3 text-white bg-green-500 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full"
>
  <div class="flex items-center space-x-2">
    <i class="fas fa-check-circle"></i>
    <span>Configuración guardada exitosamente</span>
  </div>
</div>
