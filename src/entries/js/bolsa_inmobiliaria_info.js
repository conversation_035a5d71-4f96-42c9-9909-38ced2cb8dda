/**
 * Muestra información sobre la Bolsa Inmobiliaria en un modal
 */
function mostrarInfoBolsaInmobiliaria() {
  // Crear el modal si no existe
  if (!document.getElementById("modalInfoBolsa")) {
    const modal = document.createElement("div");
    modal.id = "modalInfoBolsa";
    modal.className = "modalDialog";

    // Contenido del modal
    const contenido = `
      <img src="imagessi/times.png" />
      <div style="background-color: white; padding: 20px; overflow-y: auto;">
        <h2 style="color: #0077c9; margin-bottom: 15px; text-align: center;">¿Qué es la Bolsa Inmobiliaria?</h2>
        
        <p style="margin-bottom: 15px; line-height: 1.5;">La <strong>Bolsa Inmobiliaria</strong> es un sistema colaborativo que permite a los agentes inmobiliarios compartir propiedades y comisiones, maximizando las oportunidades de venta o renta de inmuebles.</p>
        
        <p style="margin-bottom: 15px; line-height: 1.5;">Al formar parte de una Bolsa Inmobiliaria, usted puede:</p>
        
        <ul style="margin-left: 20px; margin-bottom: 15px; line-height: 1.5;">
          <li>Aumentar su inventario de propiedades disponibles para mostrar a sus clientes</li>
          <li>Compartir comisiones con otros agentes de manera transparente y segura</li>
          <li>Reducir el tiempo de comercialización de sus propiedades</li>
          <li>Ampliar su red de contactos profesionales en el sector inmobiliario</li>
        </ul>
        
        <p style="margin-bottom: 15px; line-height: 1.5;">Para participar, simplemente indique el porcentaje de comisión que está dispuesto a compartir. Este porcentaje puede ser calculado sobre el valor total del inmueble o sobre su comisión.</p>
        
        <div style="background-color: #f0f8ff; padding: 10px; border-left: 4px solid #0077c9; margin-bottom: 15px;">
          <p style="font-style: italic;">Recuerde que una correcta estrategia de colaboración puede multiplicar sus posibilidades de éxito en las transacciones inmobiliarias.</p>
        </div>
      </div>
    `;

    modal.innerHTML = contenido;
    document.body.appendChild(modal);

    // Agregar evento para cerrar el modal
    modal.onclick = function (e) {
      if (e.target === modal || e.target.tagName === "IMG") {
        document.body.className = document.body.className.replace(
          " modalBolsaInfo",
          ""
        );
      }
    };
  }

  // Mostrar el modal
  document.body.className = document.body.className + " modalBolsaInfo";
}

// Exportar la función
export { mostrarInfoBolsaInmobiliaria };

// Hacerla disponible globalmente
window.mostrarInfoBolsaInmobiliaria = mostrarInfoBolsaInmobiliaria;
