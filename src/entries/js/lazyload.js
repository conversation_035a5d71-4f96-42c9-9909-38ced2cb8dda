/**
 * Lazyload de imágenes
 */
// Alternativa más eficiente usando Intersection Observer (recomendado)
if ("IntersectionObserver" in window) {
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove("lazy");
        observer.unobserve(img);
        // console.log("Imagen cargada con Intersection Observer:", img);
      }
    });
  });

  // Observa todas las imágenes lazy
  document.querySelectorAll(".lazy").forEach((img) => {
    imageObserver.observe(img);
  });
}
