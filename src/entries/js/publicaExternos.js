$(function() {
	$('.external-service').on('click', function(e) {
		e.preventDefault()
		let tag = $(this)
		let service = tag.attr('data-service')
		let clave_sistema = tag.attr('data-idsi')
		let colonia = $(`#colonia${clave_sistema}`).val()

		$.post(`./aj/${service}.php`, {
			"id_si": clave_sistema,
			"location": colonia
		}).done(function(data) {
			// DE ESTAR DEFINIDO NO EXISTE LA COLONIA EN 'EB' POR LO QUE DEBEREMOS PONER SELECCIONADOR
			if (data.localities) {
				let options = '';
				for (let colonia in data.localities) {
					options += `<option value="${data.localities[colonia].full_name}">${data.localities[colonia].name}</option>`
				}
				$(`#msg${clave_sistema}`).html(`<label>Selecciona la colonia equivalente en EasyBroker:<br /><select id="colonia${clave_sistema}">${options}</select></label><br /><input type="button" value="Publicar en EasyBroker" style="margin: 10px 0 5px 0;" class="external-service" data-service="easybroker" data-idsi="${clave_sistema}" data-rel="${service}${clave_sistema}" />`)
			} else if (data.status === 'OK') {
				$(`#msg${clave_sistema}`).html(`El inmueble se publicó en EasyBroker como <strong>${data.public_id}</strong>`)
			}
		})
	})
	$('div').on('click', 'input.external-service', function(e) {
		e.stopPropagation()
		let tag = $(this)
		$(`#${tag.attr('data-rel')}`).trigger('click')
	})
})