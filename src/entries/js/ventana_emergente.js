// Estos valores definen la posición de la ventana emergente
var Xoffset = -100;
var Yoffset = 16;
var yyy = -1000;

window.popup = function (msg) {
  var content =
    "<table cellpadding=0 cellspacing=0 class=ventana_emergente><tbody><tr><td class=ventana_emergente>" +
    msg +
    "</td></tr></tbody></table>";
  yyy = Yoffset;
  document.getElementById("dek").innerHTML = content;
  dek.style.display = "block";
};

window.windel = function () {
  yyy = -1000;
  dek.style.display = "none";
};

document.addEventListener("DOMContentLoaded", function () {
  var dek = document.createElement("div");
  document.body.insertBefore(dek, document.body.firstChild);
  dek.id = "dek";
  dek.style.display = "none";
  dek.style.zIndex = 200;
  dek.style.position = "absolute";

  document.addEventListener("mousemove", function (e) {
    let x = e.pageX;
    dek.style.left = x + Xoffset + "px";
    let y = e.pageY;
    dek.style.top = y + yyy + "px";
  });
});
