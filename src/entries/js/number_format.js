document.addEventListener("DOMContentLoaded", function () {
  // Obtener todos los elementos con la clase "number-format"
  var elementos = document.querySelectorAll(".number-format");

  // Iterar sobre los elementos y agregar listeners para algunos eventos
  elementos.forEach(function (elemento) {
    // Agregar un listener para el evento blur
    elemento.addEventListener("blur", function (e) {
      // Obtener el valor del input
      var value = elemento.value;

      // Remover cualquier caracter que no sea un dígito ni un punto
      value = value.replace(/[^0-9.]/g, "");

      // Obtener la configuración del atributo d-config
      var config = elemento.getAttribute("d-config");
      config = JSON.parse(config);

      // Aplicar la configuración para formatear el valor
      var formattedValue = Number(value).toLocaleString("es-MX", {
        minimumFractionDigits: config.decimals,
        maximumFractionDigits: config.decimals,
        useGrouping: true,
        currency: config.currency || undefined,
        currencyDisplay: config.currencyDisplay || undefined,
        currencySign: config.currencySign || undefined,
        notation: config.notation || undefined,
        compactDisplay: config.compactDisplay || undefined,
        signDisplay: config.signDisplay || undefined,
        style: config.style || undefined,
        unit: config.unit || undefined,
        unitDisplay: config.unitDisplay || undefined,
      });

      // Asignar el valor formateado al input
      if (elemento.value !== "") elemento.value = formattedValue;
    });

    // Agregar un listener para el evento focus
    elemento.addEventListener("focus", function (e) {
      // Obtener el valor del input
      var value = elemento.value;

      // Remover cualquier caracter que no sea un número o un punto
      value = value.replace(/[^\d\.]/g, "");

      // Asignar el valor limpio al input
      elemento.value = value;
    });

    // Agregar un listener para el evento input (cada teclazo)
    elemento.addEventListener("input", function (e) {
      // Obtener el valor del input
      var value = elemento.value;

      value = value.replace(/[^\d\.]/g, "");

      // Asignar el valor formateado al input
      elemento.value = value;
    });

    // Hago el trigger para formatear los elementos renderizados
    elemento.dispatchEvent(new Event("blur"));
  });
});
