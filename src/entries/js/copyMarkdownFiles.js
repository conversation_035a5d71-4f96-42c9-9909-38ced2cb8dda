/**
 * Script para copiar archivos Markdown a la carpeta pública durante el build
 */
const fs = require("fs");
const path = require("path");

const source = path.resolve(__dirname, "../md");
const destination = path.resolve(__dirname, "../../../public/md");

// Crear la carpeta de destino si no existe
if (!fs.existsSync(destination)) {
  fs.mkdirSync(destination, { recursive: true });
  console.log(`📁 Carpeta creada: ${destination}`);
}

// Leer todos los archivos de la carpeta md
try {
  const files = fs.readdirSync(source);

  files.forEach((file) => {
    if (file.endsWith(".md")) {
      const sourceFile = path.join(source, file);
      const destFile = path.join(destination, file);

      // Copiar el archivo
      fs.copyFileSync(sourceFile, destFile);
      console.log(`📄 Archivo copiado: ${file}`);
    }
  });

  console.log("✅ Archivos Markdown copiados con éxito.");
} catch (error) {
  console.error("❌ Error al copiar archivos Markdown:", error);
}
