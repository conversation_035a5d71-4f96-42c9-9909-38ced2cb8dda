/**
 * Abre un modal con la ayuda de la página indicada
 * @param {string} pag - La página a la que se desea acceder
 */
function ayuda(pag) {
  // Crear el modal si no existe
  if (!document.getElementById("modalPanel")) {
    const modal = document.createElement("div");
    modal.id = "modalPanel";
    modal.className = "modalDialog";
    modal.innerHTML = `
      <img src="imagessi/times.png" />
      <div>
        <iframe id="panelIFrame" src="" name="socialIFrame"></iframe>
      </div>
    `;
    document.body.appendChild(modal);

    // Agregar evento para cerrar el modal
    modal.onclick = function () {
      document.body.className = document.body.className.replace(
        " modalPanel",
        ""
      );
      document.getElementById("panelIFrame").src = "";
    };
  }

  // Mostrar el modal y cargar la página
  document.body.className = document.body.className + " modalPanel";
  document.getElementById("panelIFrame").src = pag;
  return;
}

/**
 * Muestra el corredor de la Bolsa Inmobiliaria
 * @param {string} quienBI - El corredor a mostrar
 * @param {string} Corredor - El corredor a mostrar
 * @param {string} Miembro - El miembro a mostrar
 */
function BImuestra(quienBI, Corredor, Miembro) {
  if (quienBI.substr(0, 5) == "quien") {
    cdn = "<br><i>promueve ...</i></center>";
  } else {
    cdn = "</center>";
  }
  document.getElementById(quienBI).innerHTML =
    "<center><a href=\"javascript:newWindow('bolsa_inmobiliaria.php?miembro=" +
    Miembro +
    "&paso=7', '_blank', 'toolbar=yes,scrollbars=yes,resizable=no,width=580,height=250')\">" +
    Corredor +
    "</a>" +
    cdn;
}

/**
 * Abre una nueva ventana con la página indicada
 * @param {string} pag - La página a la que se desea acceder
 * @param {string} ventana - La ventana en la que se desea abrir la página
 * @param {string} adicional - Los adicionales a la página
 */
function newWindow(pag, ventana, adicional) {
  window.open(pag, ventana, adicional);
}

// Exportar las funciones
export { ayuda, BImuestra, newWindow };
