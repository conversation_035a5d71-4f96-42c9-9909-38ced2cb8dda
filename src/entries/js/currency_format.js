document.addEventListener("DOMContentLoaded", function () {
  // Obtener todos los elementos con la clase "number-with-format"
  var elementos = document.querySelectorAll(".currency-format");

  // Iterar sobre los elementos y agregar un listener para el evento input
  elementos.forEach(function (elemento) {
    elemento.addEventListener("input", function (e) {
      // Obtener el valor del input
      var value = elemento.value;

      // Remover cualquier caracter que no sea un dígito
      value = value.replace(/\D/g, "");

      // Formatear el valor con separadores de miles
      value = Number(value).toLocaleString("es-MX");

      // Asignar el valor formateado al input
      if (elemento.value !== "") elemento.value = value;
    });

    // Hago el trigger para formatear los elementos renderizados
    elemento.dispatchEvent(new Event("input"));
  });
});
