/**
 * MarkdownModal - Componente para mostrar contenido de archivos Markdown en un modal
 */
import { marked } from "marked";

class MarkdownModal {
  constructor() {
    this.modalId = "markdownModal";
    this.modalClass = "modalDialog";
    this.modalContent = null;
    this.mdCache = {}; // Caché para archivos markdown ya cargados

    // Configuración de marked para procesar markdown
    marked.setOptions({
      gfm: true, // GitHub Flavored Markdown
      breaks: true, // Convertir saltos de línea en <br>
      headerIds: true, // Generar IDs para encabezados
      mangle: false, // No codificar caracteres especiales en IDs
    });

    // Estilos CSS para el contenido del modal
    this.contentStyles = {
      container: "background-color: white; padding: 20px; overflow-y: auto;",
      title: "color: #0077c9; margin-bottom: 15px; text-align: center;",
      paragraph: "margin-bottom: 15px; line-height: 1.5;",
      list: "margin-left: 20px; margin-bottom: 15px; line-height: 1.5;",
      quote:
        "background-color: #f0f8ff; padding: 10px; border-left: 4px solid #0077c9; margin-bottom: 15px; font-style: italic;",
    };

    // Crear una hoja de estilo para los elementos del markdown
    this.createStylesheet();
  }

  /**
   * Crea la hoja de estilos para el contenido markdown
   */
  createStylesheet() {
    const styleElement = document.createElement("style");
    styleElement.textContent = `
      .md-content {
        padding: 30px;
        max-height: 95vh;
        overflow-y: auto;
      }
      
      .md-content h1, .md-content h2, .md-content h3, .md-content h4, .md-content h5, .md-content h6 {
        color: #0077c9;
        margin-bottom: 18px;
      }
      
      .md-content h1 {
        text-align: center;
        font-size: 2rem;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eaeaea;
      }
      
      .md-content h2 {
        font-size: 1.5rem;
        margin-top: 25px;
      }
      
      .md-content h3 {
        font-size: 1.3rem;
      }
      
      .md-content p {
        margin-bottom: 16px;
        line-height: 1.6;
      }
      
      .md-content ul, .md-content ol {
        margin-left: 25px;
        margin-bottom: 20px;
        line-height: 1.6;
      }
      
      .md-content li {
        margin-bottom: 8px;
      }
      
      .md-content blockquote {
        background-color: #f0f8ff;
        padding: 15px;
        border-left: 4px solid #0077c9;
        margin: 25px 0;
        font-style: italic;
        border-radius: 0 4px 4px 0;
      }
      
      .md-content a {
        color: #0077c9;
        text-decoration: underline;
      }
      
      .md-content code {
        background-color: #f0f0f0;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: monospace;
      }
      
      .md-content table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 20px;
      }
      
      .md-content th, .md-content td {
        border: 1px solid #ddd;
        padding: 10px;
      }
      
      .md-content th {
        background-color: #f0f8ff;
        text-align: left;
      }
      
      .md-content tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      /* Estilos para el botón de cerrar */
      .modal-close {
        position: absolute;
        top: 15px;
        right: 15px;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid #ddd;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1001;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      .modal-close:hover {
        background: #fff;
        border-color: #0077c9;
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
      
      .modal-close:active {
        transform: scale(0.95);
      }
      
      .modal-close ion-icon {
        color: #666;
        transition: color 0.2s ease;
      }
      
      .modal-close:hover ion-icon {
        color: #0077c9;
      }
      
      @media (max-width: 768px) {
        .md-content {
          padding: 20px;
        }
        
        .md-content h1 {
          font-size: 1.8rem;
          margin-bottom: 20px;
          padding-bottom: 10px;
        }
        
        .modal-close {
          top: 10px;
          right: 10px;
          width: 36px;
          height: 36px;
        }
        
        .modal-close ion-icon {
          font-size: 20px !important;
        }
      }
    `;
    document.head.appendChild(styleElement);
  }

  /**
   * Carga y muestra el contenido de un archivo markdown
   * @param {string} mdFile - Ruta al archivo markdown
   * @param {string} title - Título opcional para el modal (se usará si el md no tiene título)
   */
  async show(mdFile, title = "") {
    try {
      // Comprobar si ya existe un modal, si no, crearlo
      let modal = document.getElementById(this.modalId);
      if (!modal) {
        modal = this.createModal();
      }

      // Mostrar indicador de carga
      const contentContainer = modal.querySelector(".md-content");
      contentContainer.innerHTML =
        '<div style="text-align: center; padding: 30px;"><p>Cargando...</p></div>';

      // Mostrar el modal mientras carga
      document.body.className =
        document.body.className + ` modal${this.modalId}`;

      // Cargar el contenido markdown
      let mdContent;
      if (this.mdCache[mdFile]) {
        mdContent = this.mdCache[mdFile];
      } else {
        const response = await fetch(mdFile);
        if (!response.ok) {
          throw new Error(`No se pudo cargar el archivo: ${mdFile}`);
        }
        mdContent = await response.text();
        this.mdCache[mdFile] = mdContent; // Guardar en caché
      }

      // Convertir markdown a HTML
      const htmlContent = marked.parse(mdContent);

      // Actualizar contenido del modal
      this.updateModalContent(modal, htmlContent, title);
    } catch (error) {
      console.error("Error al mostrar el modal markdown:", error);
      // Mostrar un mensaje de error en el modal
      const errorHtml = `
        <h1>Error</h1>
        <p>No se pudo cargar el contenido. ${error.message}</p>
      `;

      const modal = document.getElementById(this.modalId) || this.createModal();
      const contentContainer = modal.querySelector(".md-content");
      contentContainer.innerHTML = errorHtml;
    }
  }

  /**
   * Crea el elemento modal
   * @returns {HTMLElement} El elemento modal creado
   */
  createModal() {
    const modal = document.createElement("div");
    modal.id = this.modalId;
    modal.className = this.modalClass;

    // Contenedor principal del contenido
    const modalContainer = document.createElement("div");
    modalContainer.className = "modal-container";

    // Agregar el botón de cierre
    const closeButton = document.createElement("button");
    closeButton.innerHTML = `
      <ion-icon name="close-outline" style="font-size: 24px;"></ion-icon>
    `;
    closeButton.className = "modal-close";
    closeButton.setAttribute("aria-label", "Cerrar modal");
    closeButton.type = "button";
    modal.appendChild(closeButton);

    // Crear contenedor para el contenido
    const contentContainer = document.createElement("div");
    contentContainer.className = "md-content";

    // Agregar el contenedor de contenido al contenedor principal
    modalContainer.appendChild(contentContainer);

    // Agregar el contenedor principal al modal
    modal.appendChild(modalContainer);

    // Agregar al DOM
    document.body.appendChild(modal);

    // Eventos para cerrar el modal
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        this.hide();
      }
    });

    // Evento específico para el botón de cerrar
    closeButton.addEventListener("click", (e) => {
      e.stopPropagation();
      this.hide();
    });

    // También cerrar con ESC
    document.addEventListener("keydown", (e) => {
      if (
        e.key === "Escape" &&
        document.body.className.includes(`modal${this.modalId}`)
      ) {
        this.hide();
      }
    });

    return modal;
  }

  /**
   * Actualiza el contenido del modal
   * @param {HTMLElement} modal - El elemento modal
   * @param {string} htmlContent - El contenido HTML
   * @param {string} fallbackTitle - Título de respaldo si no hay uno en el contenido
   */
  updateModalContent(modal, htmlContent, fallbackTitle) {
    const contentContainer = modal.querySelector(".md-content");
    contentContainer.innerHTML = htmlContent;

    // Si no hay encabezado H1, agregar el título de respaldo
    if (fallbackTitle && !contentContainer.querySelector("h1")) {
      const titleElement = document.createElement("h1");
      titleElement.textContent = fallbackTitle;
      contentContainer.insertBefore(titleElement, contentContainer.firstChild);
    }
  }

  /**
   * Oculta el modal
   */
  hide() {
    document.body.className = document.body.className.replace(
      ` modal${this.modalId}`,
      ""
    );
  }
}

// Crear instancia y exportarla
const markdownModal = new MarkdownModal();
export default markdownModal;

// Hacer disponible globalmente
window.markdownModal = markdownModal;
