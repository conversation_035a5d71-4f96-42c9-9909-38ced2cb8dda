<?php

setlocale(LC_ALL, "es_MX");

include_once("languages/esp.php");
include_once("funciones.php");
include_once("verifica_pago.php");
include_once("theme.php");
cm_valida_usuario('ADMIN');

$config['external_connections'] = json_decode($config['external_connections'], true);
// if (empty($config['external_connections'])) $config['external_connections'] = array();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_REQUEST['external_provider'] === 'easybroker' && substr($_REQUEST['api_key'], 0, 10) !== '**********') {
    $api_key = trim($_REQUEST['api_key']);
    if (!empty($api_key)) {
        $config['external_connections']['easybroker'] = array(
            'api_key' => $api_key
        );
    } else {
        unset($config['external_connections']['easybroker']);
    }
    $sql->update_reg('config', array(
        'external_connections' => json_encode($config['external_connections'])
    ), "contrato={$config['contrato']}");
}

//////////////
// Recupero el encabezado y lo despliego, también defino variables para socios AMPI
$plantilla = cm_extrae_plantilla_mto();
include_once("enc_mto.php");

$ext_con = &$config['external_connections'];

$salida = cm_lee_archivo(__DIR__.'/templates/inmuebles/importExportFromTo.mustache');
$sust = array(
    '{{EASYBROKER_APIKEY}}' => !empty($ext_con['easybroker']['api_key'])
        ? '**************'.substr($ext_con['easybroker']['api_key'], -5) : ''
);
echo strtr($salida, $sust);


// Despliego el pie de página y termino la ejecución del programa
echo $mustache->render($plantilla['pie']);
exit;
