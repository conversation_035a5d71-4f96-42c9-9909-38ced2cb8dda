<?php
setlocale(LC_ALL, "es_MX");

include_once("languages/esp.php");
include_once("funciones.php");
include_once("verifica_pago.php");
include_once("theme.php");
@mysql_select_db(DB_SI);
cm_valida_usuario('ADMIN');


////////////////////////////////////////////////////////
// Recupero el encabezado y lo despliego, también defino variables para socios AMPI
$plantilla = cm_extrae_plantilla_mto();
include_once("enc_mto.php");

?>
<script language="javascript">
	<!--
	function lTrim(sStr) {
		while (sStr.charAt(0) == " ")
			sStr = sStr.substr(1, sStr.length - 1);
		return sStr;
	}

	function rTrim(sStr) {
		while (sStr.charAt(sStr.length - 1) == " ")
			sStr = sStr.substr(0, sStr.length - 1);
		return sStr;
	}

	function allTrim(sStr) {
		return rTrim(lTrim(sStr));
	}

	function val_numerico(campo, dec) {
		var checkStr = campo.value;
		var allValid = true;
		var allNum = "";
		if (dec > 0) {
			var checkOK = "0123456789.";
			var msg = 'Teclee sólo dígitos sin comas o deje el campo en blanco.';
		} else {
			var checkOK = "0123456789";
			var msg = 'Teclee sólo dígitos sin puntos ni comas o deje el campo en blanco.';
		}
		for (i = 0; i < checkStr.length; i++) {
			ch = checkStr.charAt(i);
			for (j = 0; j < checkOK.length; j++)
				if (ch == checkOK.charAt(j)) break;
			if (j == checkOK.length) {
				allValid = false;
				break;
			}
			allNum += ch;
		}
		if (!allValid) {
			alert(msg);
			campo.focus();
			campo.value = "";
			return (false);
		}
		return (true);
	}

	function confirma(campo, ncampo) {
		if (confirm("El campo \"" + ncampo + "\" será eliminado,\n¿ Deseas continuar ?")) {
			window.open('<?= $PHP_SELF; ?>?op=eliminar&campo=' + campo, '_self');
		}
		return;
	}
	-->
</script>
<?php


////////////////////////////////////////////////////////////
// En caso de no usar un capo original
if (($op == 'siuso' || $op == 'nouso') && trim($campo) != '') {
    $consulta1 = mysqlQuery("SELECT uso FROM campos_inmuebles WHERE (contrato IS NULL && variable='$campo')", $db_link);

    // En caso de haber encontrado la variable con el contrato 0
    if (mysql_num_rows($consulta1) > 0) {
        $consulta1 = mysqlQuery("SELECT * FROM campos_inhabilitados WHERE (contrato='$config[contrato]' && variable='$campo')", $db_link);

        // En caso de no estar ya registrada como inhabilitada
        if (mysql_num_rows($consulta1) == 0 && $op == 'nouso') {
            mysqlQuery("INSERT INTO campos_inhabilitados (contrato, variable) VALUES ('$config[contrato]', '$campo')", $db_link);
        } elseif (mysql_num_rows($consulta1) > 0 && $op == 'siuso') {
            mysqlQuery("DELETE FROM campos_inhabilitados WHERE (contrato='$config[contrato]' && variable='$campo') LIMIT 1", $db_link);
        }
    }


    ////////////////////////////////////////////////////////////
    // En caso de no usar un capo original
} elseif (($op == 'hab_ra' || $op == 'blq_ra') && trim($campo) != '') {
    $consulta1 = mysqlQuery("SELECT uso FROM campos_inmuebles WHERE ((contrato IS NULL || contrato='$config[contrato]') && variable='$campo')", $db_link);

    // En caso de haber encontrado la variable con el contrato 0
    if (mysql_num_rows($consulta1) > 0) {
        $consulta1 = mysqlQuery("SELECT * FROM campos_bloqueados WHERE (contrato='$config[contrato]' && variable='$campo')", $db_link);

        // En caso de no estar ya registrada como inhabilitada
        if (mysql_num_rows($consulta1) == 0 && $op == 'blq_ra') {
            mysqlQuery("INSERT INTO campos_bloqueados (contrato, variable) VALUES ('$config[contrato]', '$campo')", $db_link);
        } elseif (mysql_num_rows($consulta1) > 0 && $op == 'hab_ra') {
            mysqlQuery("DELETE FROM campos_bloqueados WHERE (contrato='$config[contrato]' && variable='$campo') LIMIT 1", $db_link);
        }
    }


    ////////////////////////////////////////////////////////////
    // En caso de eliminar un campo personalizado
} elseif ($op == 'eliminar' && trim($campo) != '') {
    $consulta1 = mysqlQuery("SELECT uso FROM campos_inmuebles WHERE (contrato='$config[contrato]' && variable='$campo')", $db_link);

    // En caso de haber encontrado la variable con el contrato del cliente primero la elimino de ba BD
    if (mysql_num_rows($consulta1) > 0) {
        mysqlQuery("DELETE FROM campos_inmuebles WHERE (contrato='$config[contrato]' && variable='$campo') LIMIT 1", $db_link);

        // Ahora elimino las respuestas aprendidas de esta variable
        mysqlQuery("DELETE FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$campo')", $db_link);

        // Ahora elimino los valores registrados para este campo
        mysqlQuery("DELETE FROM valores_campos WHERE (contrato='$config[contrato]' && variable='$campo')", $db_link);

        $consulta1 = mysqlQuery("SELECT clave_sistema FROM propiedades WHERE (contrato='$config[contrato]')", $db_link);
        // Grabo o actualizo en la base para búsquedas
        while ($propiedad = mysql_fetch_array($consulta1)) {
            cm_rec_datos_prop($propiedad['clave_sistema'], $config['contrato']);
        }
    }
    $op = '';

    ////////////////////////////////////////////////////////////
    // En caso de eliminar un valor aprendido de un campo
} elseif ($op == 'bvalor') {
    if (trim($campo) != '' && (trim($vesp) != '' || trim($ving) != '')) {
        $consulta1 = mysqlQuery("SELECT * FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$campo' && valor_esp='$vesp' && valor_ing='$ving')", $db_link);
        if (mysql_num_rows($consulta1) > 0) {
            mysqlQuery("DELETE FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$campo' && valor_esp='$vesp' && valor_ing='$ving')", $db_link);
        }
    }
    $op = 'respuestas';


    //////////////////////////////////////////////////////
    // En caso de grabar ya el nuevo campo
} elseif (($op == 'nuevo' || $op == 'edicion') && $paso == 2) {

    // Formateo nombre español e inglés
    $nombre_esp = ucfirst(strtolower($nombre_esp));
    $nombre_ing = ucfirst(strtolower($nombre_ing));

    // Primero hago el nombre de la variable de acuerod al nombre en español
    $variable = strtolower($nombre_esp);
    $sust = array(
        ' ' => '',
        '-' => '',
        '_' => '',
        '/' => '',
        '*' => '',
        '+' => '',
        ',' => '',
        '.' => '',
        ':' => '',
        ';' => '',
        'á' => 'a',
        'é' => 'e',
        'í' => 'i',
        'ó' => 'o',
        'ú' => 'u',
        'ä' => 'a',
        'ë' => 'e',
        'ï' => 'i',
        'ö' => 'o',
        'ü' => 'u',
        'ñ' => 'n',
    );
    $variable = trim(strtr($variable, $sust));

    if ($variable == '') {
        echo '<CENTER>Ha ocurrido un error al asignar nombre interno a tu campo ...</CENTER>';

        // Desconectamos de la base de datos
        mysql_close($db_link);

        // Despliego el pie de página y termino la ejecución del programa
        echo $plantilla['pie'];
        exit;
    }

    // Arreglo los campos antes de registrarlos
    if (($longitud == '' || $longitud == 0) && ($tipo == 'caracter' || $tipo == 'selector')) {
        $longitud = 35;
    } elseif (($longitud == '' || $longitud == 0) && $tipo == 'numerico') {
        $longitud = 7;
    }
    if ($decimales == '') {
        $decimales = 0;
    }

    // Al haber cambio en la variable verifico que la nueva variable no exista yá
    if (($variable != $_POST['variable'] && $op == 'edicion') || $op == 'nuevo') {
        $consulta1 = mysqlQuery("SELECT * FROM campos_inmuebles WHERE ((contrato='$config[contrato]' || contrato IS NULL) && variable='$variable')", $db_link);
        if (mysql_num_rows($consulta1) > 0) {
            echo '<CENTER>Al parecer ya existe un campo igual al que estás tratando de registrar, por favor veríficalo antes ...</CENTER>';

            // Desconectamos de la base de datos
            mysql_close($db_link);

            // Despliego el pie de página y termino la ejecución del programa
            echo $plantilla['pie'];
            exit;
        }	// if
    }	// if

    // En caso de rwegistar nuevo campo
    if ($op == 'nuevo') {

        $consulta1 = mysqlQuery("SELECT orden FROM campos_inmuebles WHERE (contrato='$config[contrato]') ORDER BY orden DESC", $db_link);
        $orden = mysql_fetch_array($consulta1);
        $orden = $orden['orden'] + 1;

        $query = "INSERT INTO campos_inmuebles 
			(contrato, grupo_id, uso, variable, nombre_esp, nombre_ing, tipo, longitud, decimales, orden) 
			VALUES ('{$config['contrato']}', {$grupo_id}, '{$uso}', '{$variable}', '{$nombre_esp}', '{$nombre_ing}', '{$tipo}', '{$longitud}', '{$decimales}', {$orden})
			";
        if (!mysqlQuery($query, $db_link)) {
            echo '<CENTER>Ocurrió un error y no se pudo registrar el nuevo campo ...</CENTER>';

            // Desconectamos de la base de datos
            mysql_close($db_link);

            // Despliego el pie de página y termino la ejecución del programa
            echo $plantilla['pie'];
            exit;
        } else {
            $op = '';
        }

        // En caso de editar un campo ya existente
    } elseif ($op == 'edicion') {

        // Hago el cambio en la información del campo
        $query = "UPDATE campos_inmuebles 
			SET uso='{$uso}', grupo_id='{$grupo_id}', variable='{$variable}', nombre_esp='{$nombre_esp}', 
				nombre_ing='{$nombre_ing}', tipo='{$tipo}', longitud='{$longitud}', decimales ='{$decimales}' 
			WHERE (contrato='{$config['contrato']}' AND variable='{$_POST['variable']}')";
        mysqlQuery($query, $db_link);

        // Hago el cambio en el nombre de variable en los valores del campo ya existente
        mysqlQuery("UPDATE valores_campos SET variable='$variable' WHERE (contrato='$config[contrato]' && variable='$_POST[variable]')", $db_link);

        // Hago el cambio en los valores aprendidos del campo
        mysqlQuery("UPDATE valores_campos_aprendidos SET variable='$variable' WHERE (contrato='$config[contrato]' && variable='$_POST[variable]')", $db_link);

        $op = '';
    }	// if	// if
}


////////////////////////////////////////////////////////////
// La primer pantalla del programa
if (empty($op)) {

    /*
        En caso de cambiar el órden
    */
    if ($orden == 'Subir' || $orden == 'Bajar') {

        // En caso de que la opción sea bajar de posición
        if ($orden == 'Bajar') {
            $query = "SELECT id, orden FROM campos_inmuebles WHERE (contrato='$config[contrato]') ORDER BY orden";

            // En caso de que la opción sea subir de posición
        } else {
            $query = "SELECT id, orden FROM campos_inmuebles WHERE (contrato='$config[contrato]') ORDER BY orden DESC";
        }

        $v1 = 0;
        $consulta1 = mysqlQuery($query, $db_link);
        while ($row = mysql_fetch_array($consulta1)) {

            if ($v1 == 1) {
                $sig_orden = $row['orden'];
                $sig_id = $row['id'];
                if ($sig_orden == $ant_orden && $orden == 'Bajar') {
                    $sig_orden++;
                }
                if ($sig_orden == $ant_orden && $orden == 'Subir') {
                    $ant_orden++;
                }
                break;
            }

            if ($row[id] == $id) {
                $v1 = 1;
                $ant_orden = $row['orden'];
            }
        }

        // Hago los cambios en el id a cambiar y en el id siguiente intercambiando sus numeros de orden respectivamente
        if (isset($sig_orden) && isset($sig_id) && isset($ant_orden) && isset($id)) {
            mysqlQuery("UPDATE campos_inmuebles SET orden='$sig_orden' WHERE (contrato='$config[contrato]' && id='$id')", $db_link);
            mysqlQuery("UPDATE campos_inmuebles SET orden='$ant_orden' WHERE (contrato='$config[contrato]' && id='$sig_id')", $db_link);
        }
    }
    /*
        / En caso de cambiar el órden
    */

    ?>

<main>
	<header>
		<h1>Personalizando ficha de mis inmuebles<!--mk3--></h1>
	</header>
	<div class="px-1" mk="mk4">
		<div class="td" mk="mk5">

			<div class="tr" mk="mk6">
				<section mk="mk7">

					<div class="tr" mk="mk8">
						<div class="td" mk="mk9">
							<h2 class="py-2 text-xl font-bold">Campos originales</h2>
						</div>
						<!--mk8-->
					</div>
					<div class="tr" mk="mk10">
						<div class="px-5 pb-4 text-sm td" mk="mk11">Puedes
							quitar de la ficha de captura de inmuebles cualquiera de
							los campos originales, as&iacute; tambi&eacute;n quitar respuestas
							aprendidas que no deseas que aparezcan a la hora de
							capturar o editar inmuebles.<br>
							<span class="observaciones">(Las respuestas aprendidas
								en color rojo no pueden ser eliminadas)</span><!--mk11-->
						</div>
						<!--mk10-->
					</div>
					<div class="mb-1" mk="mk12">
						<div class="td" mk="mk13"><span class="observaciones">Los campos marcados en gris
								est&aacute;n inhabilitados</span><!--mk13--></div>
						<!--mk12-->
					</div>
					<div class="tr" mk="mk14">
						<div class="contenedor min-300" mk="mk15">
							<?php

                                    // Antes recupero los campos inhabilitados
                                    $cin = array();
    $consulta1 = mysqlQuery("SELECT variable FROM campos_inhabilitados WHERE (contrato='$config[contrato]')", $db_link);
    while ($row = mysql_fetch_array($consulta1)) {
        array_push($cin, $row['variable']);
    }

    // Recupero también los campos en que los asesores no pueden poner respuestas aprendidas
    $cas = array();
    $consulta1 = mysqlQuery("SELECT variable FROM campos_bloqueados WHERE (contrato='$config[contrato]')", $db_link);
    while ($row = mysql_fetch_array($consulta1)) {
        array_push($cas, $row['variable']);
    }

    // Recupero los campos originales y los despliego
    $consulta1 = mysqlQuery("SELECT * FROM campos_inmuebles WHERE (contrato IS NULL && activo='Si') ORDER BY uso ASC, orden", $db_link);
    while ($row = mysql_fetch_array($consulta1)) {
        if (in_array($row['variable'], $cin)) {
            $adclass = 'bg-color-4';
            $cusa = "<a href=\"$PHP_SELF?op=siuso&campo=$row[variable]\">Sí uso este campo</a>";
        } else {
            $adclass = 'bg-white';
            if ($row['forzoso'] == 'No') {
                $cusa = "<a href=\"$PHP_SELF?op=nouso&campo=$row[variable]\">No uso este campo</a>";
            } else {
                $cusa = '<div>Campo forzoso</div>';
            }	// if
        }

        if ($row['tipo'] === 'selector') {
            if (in_array($row['variable'], $cas)) {
                $csa = "<a href=\"$PHP_SELF?op=hab_ra&campo=$row[variable]\">Habilitar resp. dinámicas p/asesores</a>";
            } else {
                $csa = "<a href=\"$PHP_SELF?op=blq_ra&campo=$row[variable]\">Bloquear resp. dinámicas p/asesores</a>";
            }
        } else {
            $csa = '';
        }

        ?>
							<article class="contenedor min-200 <?= $adclass ?> shadow rounded-1 p-1 mb-1" mk="mk20">
								<div class="d-grid" mk="mk22">
									<div class="py-1 w-full center bg-mulbin-100" mk="mk21">
										<div class="font-mono font-bold">‹<?= $row['tipo']; ?>›</div>
										<?php if ($row['uso'] == 'interno') : ?>
										<div class="mx-5 text-sm font-thin bg-green-300 rounded-1"><?= ucfirst($row['uso']) ?></div>
										<?php else : ?>
										<div class="mx-5 text-sm font-thin text-white rounded-1 bg-mulbin-300"><?= ucfirst($row['uso']) ?></div>
										<?php endif; ?>
									</div>
									<div class="tr" mk="mk23">
										<div class="td" align="center" mk="mk24">
											<span class="campos"><strong><?= $row['nombre_esp']; ?></strong></span><?php
                                                                                                    if ($row['nombre_ing'] != '') {
                                                                                                        echo "<br><span class=\"observaciones\" style=\"color: #C0C0C0;\">$row[nombre_ing]</span>";
                                                                                                    }
        ?><!--mk24-->
										</div>
										<div class="td" mk="mk25">
											<?php

                                                    // Recupero los valores de este campo en caso de ser selector
                                                    $mra = 0;
        if ($row['tipo'] == 'selector') {
            $mra = 1;
            echo "<select size=\"1\" name=\"$row[variable]\" class=\"form-control\">";
            $consulta2 = mysqlQuery("SELECT * FROM valores_campos_aprendidos WHERE ((contrato IS NULL || contrato='$config[contrato]') && variable='$row[variable]') ORDER BY contrato", $db_link);
            while ($valor = mysql_fetch_array($consulta2)) {
                if ($valor['contrato'] == 0) {
                    $color = '#FF0000';
                } else {
                    $color = '#000000';
                }
                if ($valor['valor_ing'] != '') {
                    $valor['valor_esp'] = "$valor[valor_esp] / $valor[valor_ing]";
                }
                echo "<option style=\"color: $color\">$valor[valor_esp]</option>\n";
            }
            echo '</select>';
        } elseif ($row['tipo'] == 'caracter') {
            echo "<INPUT TYPE=\"text\" NAME=\"$row[variable]\" size=\"17\" maxlength=\"$row[longitud]\" class=\"form-control\">";
        } elseif ($row['tipo'] == 'numerico') {
            echo "<input type=\"text\" name=\"$row[variable]\" size=\"9\" maxlength=\"$row[longitud]\" onchange=\"return val_numerico(this, $row[decimales]);\" class=\"form-control\">";
        }

        ?>
											<!--mk25-->
										</div>
										<!--mk23-->
									</div>
									<!--mk22-->
								</div>
								<div class="nav2" mk="mk26">
									<?= $cusa; ?>
									<?php
                                            if ($mra == 1 && (!in_array($row['variable'], $cas) || !$_COOKIE['cookie_asesor'])) {
                                                echo "<a href=\"$PHP_SELF?op=respuestas&campo=$row[variable]\">Respuestas aprendidas</a>";
                                            }
        ?>
									<?php if (!$_COOKIE['cookie_asesor']) {
									    echo $csa;
									} ?>
									<!--mk26-->
								</div>
								<!--mk20-->
							</article>
							<?php

    }

    ?>
							<!--mk15-->
						</div>
						<div class="tr" mk="mk28">
							<div class="td" mk="mk29">
								<h2 class="py-2 text-xl font-bold">Campos personalizados</h2><!--mk29-->
							</div>
							<!--mk28-->
						</div>
						<div class="tr" mk="mk30">
							<div class="px-5 pb-4 text-sm td" mk="mk31">Puedes
								agregar y/o quitar campos a la ficha de captura de
								inmuebles, as&iacute; tambi&eacute;n quitar respuestas
								aprendidas que no deseas que aparezcan a la hora de
								capturar o editar inmuebles.<!--mk31--></div>
							<!--mk30-->
						</div>
						<div class="tr" mk="mk32">
							<div class="py-1 center">
								<input type="submit" value="Agregar un nuevo campo ..." class="btn btn-primary">
							</div>
							<div class="contenedor min-300" mk="mk33">
								<?php

        // Recupero los campos originales y los despliego
        $consulta1 = mysqlQuery("SELECT * FROM campos_inmuebles WHERE (contrato='$config[contrato]') ORDER BY orden ASC", $db_link);
    $num_res = mysql_num_rows($consulta1);
    $v1 = 0;
    while ($row = mysql_fetch_array($consulta1)) {
        $v1++;

        ?>
								<article class="p-1 mb-1 shadow contenedor min-200 rounded-1 bg-soft" mk="mk39">

									<div class="d-grid" mk="mk41">
										<div class="center" mk="mk40"><span class="observaciones">&lt;<?= $row['tipo']; ?>&gt;
												<strong><?php

                                    if ($row['uso'] == 'interno') {
                                        echo '<span style="background-color: #FFE6BF">&nbsp;&nbsp;' . ucfirst($row['uso']) . '&nbsp;&nbsp;</span>';
                                    } else {
                                        echo '<span style="background-color: #E6F4FF">&nbsp;&nbsp;' . ucfirst($row['uso']) . '&nbsp;&nbsp;</span>';
                                    }

        ?></strong></span><!--mk40--></div>
										<div class="tr" mk="mk42">
											<div class="td" mk="mk43">
												<span class="campos"><strong><?= $row['nombre_esp']; ?></strong></span><?php
                                                                        if ($row['nombre_ing'] != '') {
                                                                            echo "<br><span class=\"observaciones\" style=\"color: #C0C0C0;\">$row[nombre_ing]</span>";
                                                                        }
        ?><!--mk43-->
											</div>
											<div class="td" mk="mk44">
												<?php

                                                        // Recupero los valores de este campo en caso de ser selector
                                                        $mra = 0;
        if ($row['tipo'] == 'selector') {
            $mra = 1;
            echo "<select size=\"1\" name=\"$row[variable]\" class=\"form-control\">";
            $consulta2 = mysqlQuery("SELECT * FROM valores_campos_aprendidos WHERE (contrato='$config[contrato]' && variable='$row[variable]')", $db_link);
            while ($valor = mysql_fetch_array($consulta2)) {
                if ($valor['valor_ing'] != '') {
                    $valor['valor_esp'] = "$valor[valor_esp] / $valor[valor_ing]";
                }
                echo "<option>$valor[valor_esp]</option>\n";
            }
            echo '</select>';
        } elseif ($row['tipo'] == 'caracter') {
            echo "<INPUT TYPE=\"text\" NAME=\"$row[variable]\" size=\"17\" maxlength=\"$row[longitud]\" class=\"form-control\">";
        } elseif ($row['tipo'] == 'numerico') {
            echo "<input type=\"text\" name=\"$row[variable]\" size=\"9\" maxlength=\"$row[longitud]\" onchange=\"return val_numerico(this, $row[decimales]);\" class=\"form-control\">";
        } elseif ($row['tipo'] == 'texto') {
            echo "<textarea NAME=\"$row[variable]\" cols=\"30\" rows=\"3\" class=\"form-control\"></textarea>";
        }	// if

        if ($row['tipo'] === 'selector') {
            if (in_array($row['variable'], $cas)) {
                $csa = "<a href=\"$PHP_SELF?op=hab_ra&campo=$row[variable]\">Habilitar resp. dinámicas p/asesores</a>";
            } else {
                $csa = "<a href=\"$PHP_SELF?op=blq_ra&campo=$row[variable]\">Bloquear resp. dinámicas p/asesores</a>";
            }
        } else {
            $csa = '';
        }

        ?>
												<!--mk44-->
											</div>
											<!--mk42-->
										</div>

										<!--mk41-->
									</div>
									<div class="d-flex">
										<div class="nav2" mk="mk45">
											<a href="<?= "$PHP_SELF?op=edicion&campo=$row[variable]"; ?>">Editar este campo</a>
											<a href="javascript:confirma(<?= "'$row[variable]', '" . str_replace("'", '´', $row['nombre_esp']) . "'"; ?>);">Eliminar este campo</a>
											<?php
                                                    if ($mra == 1 && (!in_array($row['variable'], $cas) || !$_COOKIE['cookie_asesor'])) {
                                                        echo "<a href=\"$PHP_SELF?op=respuestas&campo=$row[variable]\">Respuestas aprendidas</a>";
                                                    }
        ?>
											<?php if (!$_COOKIE['cookie_asesor']) {
											    echo $csa;
											} ?>
											<!--mk45-->
										</div>
										<div class="center" mk="mk46">

											<div class="d-flex" mk="mk47">
												<div class="td" mk="mk48">
													<?php
											                // Si no es el último registro despliego la flecha hacia abajo
											                if ($v1 < $num_res) {
											                    ?>
													<a HREF="<?= "$PHP_SELF?id=$row[id]&orden=Bajar&t=" . time(); ?>"><img border="0" src="imagessi/down.gif" width="11" height="14" alt="Bajar una posición" title="Subir una posición"></a>
													<?php
											                }
        ?>
													<!--mk48-->
												</div>
												<div class="td" mk="mk49">
													<?php
        // Si no es el primer registro despliego la flecha hacia arriba
        if ($v1 > 1) {
            ?>
													<a HREF="<?= "$PHP_SELF?id=$row[id]&orden=Subir&t=" . time(); ?>"><img border="0" src="imagessi/up.gif" width="11" height="14" alt="Subir una posición" title="Subir una posición"></a>
													<?php
        }
        ?>
													<!--mk49-->
												</div>
												<!--mk47-->
											</div>

											<!--mk46-->
										</div>
									</div>
									<!--mk39-->
								</article>
								<?php

    }

    ?>

								<!--mk33-->
							</div>
							<!--mk32-->
						</div>
						<div class="tr" mk="mk50">
							<div class="td" mk="mk51">
								<form method="POST" action="<?= $PHP_SELF; ?>">
									<div class="py-1 center" mk="mk52">
										<input type="submit" value="Agregar un nuevo campo ..." class="btn btn-primary"><!--mk52-->
									</div>
									<input type="hidden" name="op" value="nuevo">
								</form>
								<!--mk51-->
							</div>
							<!--mk50-->
						</div>

						<!--mk14-->
					</div>
					<!--mk7-->
				</section>

				<!--mk6-->
			</div>
			<!--mk5-->
		</div>

		<!--mk4-->
	</div>
</main>
<?php


    //////////////////////////////////////////////////////
    // En caso de listar respuestas personalizadas de un campo para borrar algunas
} elseif ($op == 'respuestas' && trim($campo) != '') {

    // En caso de registrar una nueva respuesta
    if ($paso == 2) {
        $nresp_esp = trim(ucfirst(mb_strtolower($nresp_esp)));
        $nresp_ing = trim(ucfirst(mb_strtolower($nresp_ing)));
        if ($nresp_esp != '' || $nresp_ing != '') {

            // Verifico si ya está registrada esta respuesta
            $consulta1 = mysqlQuery("SELECT * FROM valores_campos_aprendidos WHERE ((contrato='$config[contrato]' || contrato IS NULL) && variable='$campo' && valor_esp='$nresp_esp' && valor_ing='$nresp_ing')", $db_link);
            if (mysql_num_rows($consulta1) == 0) {
                mysqlQuery("
					INSERT INTO valores_campos_aprendidos
					(contrato, variable, valor_esp, valor_ing, complemento_al_valor) 
					VALUES ('$config[contrato]', '$campo', '$nresp_esp', '$nresp_ing', '$complemento_al_valor')
					", $db_link);
            }
        }
    } elseif ($imprime == 'Si' || $imprime == 'No') {
        mysqlQuery("UPDATE valores_campos_aprendidos SET para_impresion='$imprime' WHERE (contrato='$config[contrato]' && id='$resp' && variable='$campo')", $db_link);
    }

    // Recupero los datos del campo
    $consulta1 = mysqlQuery("SELECT * FROM campos_inmuebles WHERE ((contrato IS NULL || contrato='$config[contrato]') && variable='$campo')", $db_link);
    $ica = mysql_fetch_array($consulta1);

    ?>
<script language="javascript">
	<!--
	function vnc(theForm) {
		if (allTrim(theForm.nresp_esp.value) == '' && allTrim(theForm.nresp_ing.value) == '') {
			alert('No has introducido valor algunos para la nueva respuesta');
			theForm.nresp_esp.focus();
			return (false);
		}

		return (true);
	}

	function ir_a(valor) {
		var vresp = valor.value;
		if (valor.checked == true) {
			var liga = "<?= "$PHP_SELF?op=respuestas&campo=$campo&t=" . time(); ?>&resp=" + vresp + "&imprime=Si";
		} else {
			var liga = "<?= "$PHP_SELF?op=respuestas&campo=$campo&t=" . time(); ?>&resp=" + vresp + "&imprime=No";
		}
		window.location = liga;
	}
	-->
</script>

<div align="center" mk="mk53">

	<div class="tr" mk="mk54">
		<div class="td" mk="mk55">Respuestas aprendidas<!--mk55--></div>
		<!--mk54-->
	</div>
	<div class="tr" mk="mk56">
		<div class="td" mk="mk57">

			<div class="tr" mk="mk58">
				<div class="td" mk="mk59">

					<div class="tr" mk="mk60">
						<div class="td" mk="mk61">
							<h4>&lt; <?php

                                                echo $ica['nombre_esp'];
    if ($ica['nombre_ing'] != '') {
        echo " / $ica[nombre_ing]";
    }

    ?> &gt;</h4>
							<!--mk61-->
						</div>
						<!--mk60-->
					</div>
					<div class="tr" mk="mk62">
						<div class="td" mk="mk63">A continuaci&oacute;n se listas las respuestas aprendidas para el campo:<br><span class="observaciones">Las respuestas marcadas en rojo no se pueden eliminar al ser respuestas predeterminadas.</span><!--mk63--></div>
						<!--mk62-->
					</div>
					<div class="tr" mk="mk64">
						<div class="td" mk="mk65">

							<div class="tr" mk="mk66">
								<div class="td" mk="mk67">
									<span style="color: #FFFFFF; font-weight:700">
										<?php

    echo $ica['nombre_esp'];
    if ($ica['nombre_ing'] != '') {
        echo " / $ica[nombre_ing]";
    }

    ?></span><!--mk67-->
								</div>
								<!--mk66-->
							</div>
							<div class="tr" mk="mk68">
								<div class="td" mk="mk69">
									<span style="color: #FFFFFF">En espa&ntilde;ol</span><!--mk69-->
								</div>
								<div class="td" mk="mk70">
									<span style="color: #FFFFFF">In english</span><!--mk70-->
								</div>
								<div class="td" mk="mk71">
									&nbsp;<!--mk71--></div>
								<div class="td" mk="mk72">
									<span class="observaciones" style="color: #FFFFFF;">p/imprimir</span><br>
									<a href="javascript:ayuda('ayuda/p_imprimir_respuestas.htm');"><img border="0" src="imagessi/ayuda.gif" width="11" height="11" title="¿Que es esto?"></a><!--mk72-->
								</div>
								<!--mk68-->
							</div>
							<?php

                                // Recupero las respuestas aprendidas para este campo
                                $consulta1 = mysqlQuery("SELECT * FROM valores_campos_aprendidos WHERE ((contrato='$config[contrato]' || contrato IS NULL) && variable='$campo') ORDER BY contrato", $db_link);
    while ($row = mysql_fetch_array($consulta1)) {
        if ($row['contrato'] == $config['contrato']) {
            $color = '#000000';
        } else {
            $color = '#FF0000';
        }

        ?>
							<div class="tr" mk="mk73">
								<div class="td" mk="mk74"><span style="color: <?= $color; ?>"><?= $row['valor_esp']; ?>&nbsp;</span><!--mk74--></div>
								<div class="td" mk="mk75"><span style="color: <?= $color; ?>"><?= $row['valor_ing']; ?>&nbsp;</span><!--mk75--></div>
								<div class="td" mk="mk76">
									<?php
                    if ($row['contrato'] == $config['contrato']) {
                        ?>
									<a href="<?= "$PHP_SELF?op=bvalor&campo=$campo&vesp=" . urlencode($row['valor_esp']) . "&ving=" . urlencode($row['valor_ing']); ?>">Eliminar</a>
									<?php
                    }
        ?>
									&nbsp;<!--mk76-->
								</div>
								<div class="td" mk="mk77">
									<input type="checkbox" name="<?= "v$row[id]"; ?>" value="<?= $row['id']; ?>" <?php if ($row['para_impresion'] == 'Si') {
									    echo 'checked';
									} ?> <?php if ($row['contrato'] == 0) {
									    echo 'disabled';
									} ?> onclick="ir_a(this);">
									<!--mk77-->
								</div>
								<!--mk73-->
							</div>
							<?php

    }

    ?>

							<!--mk65-->
						</div>
						<!--mk64-->
					</div>
					<div class="tr" mk="mk78">
						<div class="td" mk="mk79">
							<form method="POST" action="<?= $PHP_SELF; ?>" onsubmit="return vnc(this);">

								<div class="tr" mk="mk80">
									<div class="td" mk="mk81"><strong>Agregar nueva respuesta</strong><!--mk81--></div>
									<!--mk80-->
								</div>
								<div class="tr" mk="mk82">
									<div class="td" mk="mk83">En espa&ntilde;ol<br>
										<input type="text" name="nresp_esp" size="15" class="form-control" maxlength="<?= $row['longitud']; ?>"><!--mk83-->
									</div>
									<div class="td" mk="mk84">En ingl&eacute;s<br>
										<input type="text" name="nresp_ing" size="15" class="form-control" maxlength="<?= $row['longitud']; ?>"><!--mk84-->
									</div>
									<!--mk82-->
								</div>
								<div class="tr" mk="mk85">
									<div class="td" mk="mk86">
										<input type="submit" value="Agregar ..."><!--mk86-->
									</div>
									<!--mk85-->
								</div>

								<input type="hidden" name="op" value="respuestas">
								<input type="hidden" name="paso" value="2">
								<input type="hidden" name="campo" value="<?= $campo; ?>">
							</form>
							<!--mk79-->
						</div>
						<!--mk78-->
					</div>

					<!--mk59-->
				</div>
				<!--mk58-->
			</div>
			<div class="tr" mk="mk87">
				<div class="td" mk="mk88">
					<i><strong><a href="<?= "$PHP_SELF?t=" . time(); ?>">&lt;-- REGRESAR</a></strong></i><!--mk88-->
				</div>
				<!--mk87-->
			</div>

			<!--mk57-->
		</div>
		<!--mk56-->
	</div>

	<!--mk53-->
</div>
<?php


    //////////////////////////////////////////////////////
    // En caso de nuevo campo
} elseif (($op == 'nuevo' || $op == 'edicion') && trim($paso) == '') {

    if ($op == 'edicion' && !empty($campo)) {
        $consulta1 = mysqlQuery("SELECT * FROM campos_inmuebles WHERE (contrato='$config[contrato]' && variable='$campo') LIMIT 1", $db_link);
        if (mysql_num_rows($consulta1) == 1) {
            $campo = mysql_fetch_array($consulta1);
        }
    }	// if

    ?>
<script language="javascript">
	<!--
	function cnv_cad(cadena) {
		var cn1 = cadena.substring(0, 1);
		var cn2 = cadena.substring(1);
		cn1 = cn1.toUpperCase() + cn2.toLowerCase();
		return (cn1);
	}

	function chk_selector(campo) {
		if (campo.value == 'mas') {
			document.getElementById('h_selector').innerHTML = '<input type="text" name="vesp" size="15" class="form-control" value="*** en español ***" disabled><br><input type="text" name="ving" size="15" class="form-control" value="*** in english ***" disabled>';
		} else {
			document.getElementById('h_selector').innerHTML = '';
		}
	}

	function chk_tipo() {

		if (allTrim(document.Formulario1.nombre_esp.value) == '') {
			var nombre_esp = '<span class="observaciones"><I>En español</I></span>';
		} else {
			var nombre_esp = '<span class="campos">' + cnv_cad(document.Formulario1.nombre_esp.value) + '</span>';
		}
		if (allTrim(document.Formulario1.nombre_ing.value) == '') {
			var nombre_ing = '<span class="observaciones"><I>In english</I></span>';
		} else {
			var nombre_ing = '<span class="campos">' + cnv_cad(document.Formulario1.nombre_ing.value) + '</span>';
		}

		if (document.Formulario1.tipo.value == 'selector') {
			document.getElementById('h_ejemplo').innerHTML = '<div class="tr"><div class="center"><i><b>Ejemplo:</b></i></div></div><div class="tr"><div class="td"><div class="tr"><div class="td">' + nombre_esp + ':<br><font color="#808080">' + nombre_ing + ':</font></div><div class="td"><div class="tr"><div class="td"><select size="1" name="prueba" class="form-control" onchange="chk_selector(this);"><option>Opción 1</option><option>Opción 2</option><option value="mas">=></option></select></div></div><div class="tr"><div class="td"><div id="h_selector"></div></div></div></div></div></div></div>';
			document.Formulario1.longitud.value = '35';
			document.Formulario1.decimales.value = '';

		} else if (document.Formulario1.tipo.value == 'numerico') {
			document.getElementById('h_ejemplo').innerHTML = '<div class="tr"><div class="center"><i><b>Ejemplo:</b></i></div></div><div class="tr"><div class="td"><div class="tr"><div class="td">' + nombre_esp + ':<br><font color="#808080">' + nombre_ing + ':</font></div><div class="td"><div class="tr"><div class="td"><input type="text" name="vesp" size="9" class="form-control" value="número ..." disabled></div></div><div class="tr"><div class="td"><div id="h_selector"></div></div></div></div></div></div></div>';
			document.Formulario1.longitud.value = '7';
			document.Formulario1.decimales.value = '0';

		} else if (document.Formulario1.tipo.value == 'caracter') {
			document.getElementById('h_ejemplo').innerHTML = '<div class="tr"><div class="center"><i><b>Ejemplo:</b></i></div></div><div class="tr"><div class="td"><div class="tr"><div class="td">' + nombre_esp + ':<br><font color="#808080">' + nombre_ing + ':</font></div><div class="td"><div class="tr"><div class="td"><input type="text" name="vesp" size="17" class="form-control" value="caracter ..." disabled></div></div><div class="tr"><div class="td"><div id="h_selector"></div></div></div></div></div></div></div>';
			document.Formulario1.longitud.value = '35';
			document.Formulario1.decimales.value = '';

		} else {
			document.getElementById('h_ejemplo').innerHTML = '';
		}
	}

	function val_nombre(campo) {
		var checkOK = "- áéíóúÁÉÍÓÚabcdefghijklmnñopqrstuvwxyzABCDEFGHIJKLMNÑOPQRSTUVWXYZ1234567890";
		var checkStr = campo.value;
		var allValid = true;
		var allNum = "";
		for (i = 0; i < checkStr.length; i++) {
			ch = checkStr.charAt(i);
			for (j = 0; j < checkOK.length; j++)
				if (ch == checkOK.charAt(j)) break;
			if (j == checkOK.length) {
				allValid = false;
				break;
			}
			allNum += ch;
		}
		if (!allValid) {
			alert('El nombre del campo solo puede tener caracteres, números y guiones(-)');
			campo.focus();
			return (false);
		}
		return (true);
	}

	function verifica_campos(theForm) {
		if (allTrim(theForm.nombre_esp.value) == '') {
			alert('Debes teclear el nombre del campo en español ...');
			theForm.nombre_esp.focus();
			return (false);
		} else if (!val_nombre(theForm.nombre_esp)) {
			alert('El nombre del campo solo puede tener caracteres, números y guiones(-)');
			theForm.nombre_esp.focus();
			return (false);
		}
		if (allTrim(theForm.tipo.value) == '') {
			alert('Debes seleccionar el tipo de campo que requieres ...');
			theForm.tipo.focus();
			return (false);
		}

		return (true);
	}

	function notra(Valor) {
		chk_tipo();
		return (val_nombre(Valor));
	}
	-->
</script>
<?php

        // Recupero los grupos de campos
        $grupos = array();
    $query = "SELECT * 
	FROM campos_inmuebles_grupos 
	WHERE contrato IS NULL OR contrato='{$config['contrato']}' 
	ORDER BY ORDEN ASC, id ASC
	";
    $sql->each($query, function ($row) use (&$grupos) {
        $idioma = 'DEFAULT';
        $name = json_decode($row['name'], true);
        $res = array(
            'id' => $row['id'],
            'name' => $name[$idioma]
        );
        $grupos[] = $res;
    });

    ?>
<main mk="mk89">
	<h1 mk="mk91">Registrando nuevo campo<!--mk91--></h1>
	<section mk="mk92">
		<div class="td" mk="mk93">
			<form method="POST" action="<?= $PHP_SELF ?>" name="Formulario1" onsubmit="return verifica_campos(this);">

				<div class="tr" mk="mk94">
					<div class="td" mk="mk95">
						<div class="tr" mk="mk96">
							<div class="px-1 py-05" mk="mk97">
								Personaliza la ficha de captura de datos para los
								inmuebles que registres en tu sistema agregando los
								campos espec&iacute;ficos que necesites de acuerdo a tus
								necesidades.
								<!--mk97-->
							</div>
							<div class="px-1 pb-1 py-05" mk="mk98">Llena a continuaci&oacute;n la configuraci&oacute;n del
								nuevo campo a registrar:<!--mk98--></div>
							<div class="d-flex" mk="mk99">
								<div class="p-1 bg-color-4 max-300" mk="mk100">
									<div class="tr" mk="mk101">
										<div class="td" mk="mk102">Grupo:</div>
										<div class="td" mk="mk103">
											<select name="grupo_id" id="grupo_id" class="form-control">
												<?php foreach ($grupos as $grupo) : ?>
												<option value="<?= $grupo['id'] ?>" <?= $campo['grupo_id'] == $grupo['id'] ? 'selected' : '' ?>>
													<?= $grupo['name'] ?>
												</option>
												<?php endforeach; ?>
											</select>
										</div>
										<!--mk101-->
									</div>
									<div class="tr" mk="mk101">
										<div class="td" mk="mk102">* Nombre del campo en espa&ntilde;ol:<!--mk102--></div>
										<div class="td" mk="mk103">
											<input type="text" name="nombre_esp" size="20" class="form-control" maxlength="35" onchange="return notra(this);" value="<?= $campo['nombre_esp']; ?>"><!--mk103-->
										</div>
										<!--mk101-->
									</div>
									<div class="tr" mk="mk104">
										<div class="td" mk="mk105">Nombre del campo en ingl&eacute;s:<!--mk105--></div>
										<div class="td" mk="mk106">
											<input type="text" name="nombre_ing" size="20" class="form-control" maxlength="35" onchange="chk_tipo(); return val_nombre(this);" value="<?= $campo['nombre_ing']; ?>"><!--mk106-->
										</div>
										<!--mk104-->
									</div>
									<div class="tr" mk="mk107">
										<div class="td" mk="mk108">Complemento al valor:<!--mk108--></div>
										<div class="td" mk="mk109">
											<input type="text" name="complemento_al_valor" size="5" class="form-control" maxlength="5" value="<?= $campo['complemento_al_valor']; ?>"><!--mk109-->
										</div>
										<!--mk107-->
									</div>
									<div class="tr" mk="mk110">
										<div class="td" mk="mk111">* Tipo de campo:<!--mk111--></div>
										<div class="td" mk="mk112">
											<select size="1" name="tipo" class="form-control" onchange="chk_tipo();">
												<option value="">selecciona ...</option>
												<?php foreach (array(
                                                        'selector' => 'Selector Dinámico',
                                                        'numerico' => 'Numerico',
                                                        'caracter' => 'Caracter'
                                                    ) as $selector => $nombre) : ?>
												<option value="<?= $selector ?>" <?= $campo['tipo'] === $selector ? 'selected' : '' ?>><?= $nombre ?></option>
												<?php endforeach; ?>
											</select><!--mk112-->
										</div>
										<!--mk110-->
									</div>
									<div class="tr" mk="mk113">
										<div class="td" mk="mk114">Longitud de la
											respuesta:<br>
											<span class="observaciones">M&aacute;ximo 250 caracteres
												o d&iacute;gitos</span><!--mk114-->
										</div>
										<div class="td" mk="mk115">
											<input type="text" name="longitud" size="7" class="form-control" maxlength="3" onchange="return val_numerico(this, 0);" value="<?= $campo['longitud']; ?>"><!--mk115-->
										</div>
										<!--mk113-->
									</div>
									<div class="tr" mk="mk116">
										<div class="td" mk="mk117">Decimales:<!--mk117--></div>
										<div class="td" mk="mk118">
											<input type="text" name="decimales" size="3" class="form-control" maxlength="1" onchange="return val_numerico(this, 0);" value="<?= $campo['decimales']; ?>"><!--mk118-->
										</div>
										<!--mk116-->
									</div>
									<div class="tr" mk="mk119">
										<div class="td" mk="mk120">Uso que tendr&aacute; el campo:<!--mk120--></div>
										<div class="td" mk="mk121">
											<select size="1" name="uso" class="form-control">
												<option value="publico" <?php if ($campo['uso'] == 'publico') {
												    echo 'selected';
												} ?>>P&uacute;blico (en pag. web)</option>
												<option value="interno" <?php if ($campo['uso'] == 'interno') {
												    echo 'selected';
												} ?>>Control interno (privado)</option>
											</select><!--mk121-->
										</div>
										<!--mk119-->
									</div>

									<!--mk100-->
								</div>
								<div class="td" mk="mk122">
									<div id="h_ejemplo" mk="mk123"><!--mk123--></div><!--mk122-->
								</div>
								<!--mk99-->
							</div>
							<div class="tr" mk="mk124">
								<div class="center" mk="mk125">
									<input type="submit" value="Registrar el campo en mi sistema" class="m-1 btn btn-primary"><!--mk125-->
								</div>
								<!--mk124-->
							</div>
							<!--mk96-->
						</div>
						<!--mk95-->
					</div>
					<div class="tr" mk="mk126">
						<div class="center" mk="mk127">
							<a href="<?= "$PHP_SELF?t=" . time(); ?>" class="btn secondary">Cancelar</a>
							<!--mk127-->
						</div>
						<!--mk126-->
					</div>

					<?php

                        if (isset($campo['variable'])) {

                            ?>
					<input TYPE="hidden" name="variable" value="<?= $campo['variable']; ?>">
					<input TYPE="hidden" name="op" value="edicion">
					<?php

                        } else {

                            ?>
					<input TYPE="hidden" name="op" value="nuevo">
					<?php

                        }	// if	// if

    ?>
					<input TYPE="hidden" name="paso" value="2">

					<!--mk94-->
				</div>
			</form><!--mk93-->
		</div>

		<!--mk92-->
	</section>
	<!--mk89-->
</main>
<?php

}


//////////////
// Desconectamos de la base de datos
mysql_close($db_link);


// Despliego el pie de página y termino la ejecución del programa
echo $mustache->render($plantilla['pie'], $render);
exit;
