<?php
session_cache_limiter('nocache');
session_start();

setlocale(LC_ALL, "es_MX");

include_once("languages/esp.php");
include_once("funciones.php");
include_once("verifica_pago.php");
// include_once("theme.php");
@mysql_select_db(DB_SI);
cm_valida_usuario('ADMIN');
cm_rec_permisos();

if ($config['inmuebles'] == -1) {
    $config['inmuebles'] = 7000;
}

if (substr($SERVER_NAME, 0, 8) == '127.0.0.') {
    $s_fotos = 'http://127.0.0.11';
} elseif (substr($SERVER_NAME, -11) == '.ycasas.com' || substr($SERVER_NAME, -10) == '.in.com.mx') {
    $s_fotos = 'http://fotos.ycasas.com';
} else {
    $s_fotos = 'http://fotos.' . str_replace('www.', '', $SERVER_NAME);
}
$s_fotos = "https://$server_photos";

$render['num_inmuebles'] = $sql->query("SELECT COUNT(*) FROM propiedades WHERE contrato='$config[contrato]'")->fetch_row()[0];

/* ------------------------------------------
    FUNCIÓN QUE DEFINE EL MENÚ SUPERIOR DE LOS INMUEBLES INDIVIDUALES
*/
function local_menu_propiedad($nuevo_inmueble = false)
{
    global $PHP_SELF, $claveprop, $propiedad, $config, $s_fotos, $sql, $render;

    if ($propiedad['aid'] > 0) {
        $consulta1 = $sql->query("SELECT nombre, apellidos FROM asesores WHERE (contrato='$config[contrato]' && id='$propiedad[aid]')");
        $dAsesor = $consulta1->fetch_assoc();
        $dAsesor = trim("$dAsesor[nombre] $dAsesor[apellidos]");
        if ($dAsesor == '') {
            $dAsesor = 'Asesor dado de baja';
        }
        $dAsesor = "Inmueble capturado por: $dAsesor";
    }	// if

    if ($claveprop == '') {
        $claveprop = $propiedad['claveprop'];
    }

    $render = array_merge($render, array(
        'dAsesor' => $dAsesor,
        'clave_sistema' => $propiedad['clave_sistema'],
        'nuevo_inmueble' => $nuevo_inmueble,
        "op1" => array(
            'active' => $_REQUEST['paso'] == 1,
            'href' => "inmueble.php?claveprop=" . urlencode($claveprop) . "&paso=1"
        ),
        "op2" => array(
            'active' => $_REQUEST['paso'] == 3,
            'href' => "{$PHP_SELF}?claveprop=" . urlencode($claveprop) . "&paso=3"
        ),
        // El paso 4 nos manda a la pantalla de multibolsa
        "op3" => array(
            'active' => $_REQUEST['paso'] == 6,
            'href' => "{$PHP_SELF}?claveprop=" . urlencode($claveprop) . "&paso=6"
        ),
        "op4" => array(
            'active' => $_REQUEST['paso'] == 5,
            'href' => "{$PHP_SELF}?claveprop=" . urlencode($claveprop) . "&paso=5"
        ),
    ));

    $menu_template = file_get_contents(__DIR__ . '/inmueble/templates/menu_superior.htm');
    return $GLOBALS['mustache']->render($menu_template, $render);
}
/*
    / Función que define el menú superior de los inmuebles individuales
*/

//////////////
// Recupero el encabezado y lo despliego, también defino variables para socios AMPI
$plantilla = cm_extrae_plantilla_mto();
include_once 'enc_mto.php';

//////////////
// En caso de desplegar la pantalla del menú principal de inmuebles
if (trim($claveprop) == '') {

    /// H T M L     inicio
    ?>

<script>
    function lTrim(sStr) {
        while (sStr.charAt(0) == " ")
            sStr = sStr.substr(1, sStr.length - 1);
        return sStr;
    }

    function rTrim(sStr) {
        while (sStr.charAt(sStr.length - 1) == " ")
            sStr = sStr.substr(0, sStr.length - 1);
        return sStr;
    }

    function allTrim(sStr) {
        return rTrim(lTrim(sStr));
    }

    function Valida1(theForm) {
        if (allTrim(theForm.claveprop.value) == '') {
            alert("Teclee una clave para el inmueble a registrar o modificar");
            theForm.claveprop.focus();
            return (false);
        }
        return val_claveprop(theForm.claveprop);
    }

    function val_claveprop(campo) {
        if (campo.name == 'claves_props') {
            var checkOK = "0123456789-_,.abcdefghijklmnñopqrstuvwxyzABCDEFGHIJKLMNÑOPQRSTUVWXYZ ";
        } else {
            var checkOK = "0123456789-_.abcdefghijklmnñopqrstuvwxyzABCDEFGHIJKLMNÑOPQRSTUVWXYZ";
        } // if	// if
        var checkStr = campo.value;
        var allValid = true;
        var allNum = "";
        for (i = 0; i < checkStr.length; i++) {
            ch = checkStr.charAt(i);
            for (j = 0; j < checkOK.length; j++)
                if (ch == checkOK.charAt(j)) break;
            if (j == checkOK.length) {
                allValid = false;
                break;
            }
            allNum += ch;
        }
        if (!allValid) {
            alert('La clave del inmueble solo puede tener letras, números y guiones(-) y sin espacios.');
            campo.focus();
            return (false);
        }
        return (true);
    }

    contaerror = 0;

    function val_numerico(campo, dec) {
        var checkStr = campo.value;
        var allValid = true;
        var allNum = "";
        if (dec > 0) {
            var checkOK = "0123456789.";
            var msg = 'Teclee sólo dígitos sin comas o deje el campo en blanco.';
        } else {
            var checkOK = "0123456789";
            var msg = 'Teclee sólo dígitos sin puntos ni comas o deje el campo en blanco.';
        }
        for (i = 0; i < checkStr.length; i++) {
            ch = checkStr.charAt(i);
            for (j = 0; j < checkOK.length; j++)
                if (ch == checkOK.charAt(j)) break;
            if (j == checkOK.length) {
                allValid = false;
                break;
            }
            allNum += ch;
        }
        if (!allValid) {
            alert(msg);
            campo.focus();
            contaerror++;
            if (contaerror > 2) campo.value = "";
            return (false);
        }
        return (true);
    }
</script>
<script src="jsc/search_history.js"></script>
<script>
    var v1 = _setup_ac([
        <?php

                                        $tmp = '';
    $consulta1 = $sql->query("SELECT claveprop FROM propiedades WHERE (contrato='$config[contrato]')");
    while ($row = $consulta1->fetch_assoc()) {
        if ($tmp != '') {
            $tmp .= ',';
        }
        $tmp .= "'$row[claveprop]'";
    }	// while
    echo $tmp;

    ?>
    ], 'claveprop');
    var lsep = "";
</script>

<?php

    include __DIR__ . '/inmueble/home/<USER>';

    ?>

<script>
    _ac_install();
</script>

<?php

} //

//////////////
// En caso de ser el primer paso donde entra $claveprop
elseif ($paso == 1 || $paso == 2) {

    // Verifico la integridad de la clave de inmueble
    if (strpos(strtoupper($claveprop), '-CMP-') !== false || strpos(strtoupper($nclaveprop), '-CMP-') !== false) {
        $sql->close();
        echo 'No es posible que la clave de un inmueble contenga la cadena "-CMP-"';
        echo $mustache->render($plantilla['pie'], $render);
        exit;
    }

    if ($ppinmueble == 'Desarrollo') {
        include_once('desarrollo.php');
        die('Programa terminado ...');
    }

    // En caso de ya estar pasando los parametros para grabar verifico que claveprop no esté registrada yá
    if ($paso == 2) {

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $CamposMC = array();
            foreach ($_POST as $key => $value) {
                if (substr($key, 0, 3) === 'MC_' && ($fm = substr($key, 3))) {
                    $CamposMC[$fm] = $value;
                }
            }

            $CamposMC = json_encode($CamposMC);
        }


        $msg_error = '';

        // Verifico la integridad de $claveprop
        //		if ($clave_sistema == '') $nclaveprop = $claveprop;
        $nclaveprop = trim($nclaveprop);
        if (strtolower($nclaveprop) == strtolower($claveprop) && $clave_sistema != '') {
            $nclaveprop = '';
        }
        if ($nclaveprop != '') {
            $consulta1 = $sql->query("SELECT clave_sistema FROM propiedades WHERE (contrato='$config[contrato]' && claveprop='$nclaveprop')");
            if ($consulta1->num_rows > 0) {
                $row = $consulta1->fetch_assoc();
                $msg_error .= "<br><span class=\"msg_error\">La clave: <a href=\"detallesnew.php?clave=$row[clave_sistema]&pp=NV\" target=\"_blank\" class=\"msg_error\">$nclaveprop</a>, ya está asignada a otro inmueble ...</span>";
                $nclaveprop = '';
            }
        }

        // Verifico la integridad de la ubicación
        $id_pais = $pais;
        $consulta1 = $sql->query("SELECT pais FROM paises WHERE (id='$pais')");
        $pais = $consulta1->fetch_assoc();
        $pais = $pais['pais'];
        $id_provincia = $provincia;
        $consulta1 = $sql->query("SELECT estado FROM estados WHERE (id='$provincia')");
        $provincia = $consulta1->fetch_assoc();
        $provincia = $provincia['estado'];
        $id_ciudad = $ciudad;
        $consulta1 = $sql->query("SELECT ciudad FROM ciudades WHERE (id='$ciudad')");
        $ciudad = $consulta1->fetch_assoc();
        $ciudad = $ciudad['ciudad'];
        $id_colonia = $colonia;
        $consulta1 = $sql->query("SELECT colonia FROM new_colonias WHERE (id='$colonia')");
        $colonia = $consulta1->fetch_assoc();
        $colonia = $colonia['colonia'];
        $zona = cm_trata_ciudad_colonia($zona);
        $muestra_colonia = cm_trata_ciudad_colonia($muestra_colonia);

        if ($esp_provincia != '') {
            $provincia = cm_trata_ciudad_colonia($esp_provincia);
        }
        if ($esp_ciudad != '') {
            $ciudad = cm_trata_ciudad_colonia($esp_ciudad);
        }
        if ($esp_colonia != '') {
            $colonia = cm_trata_ciudad_colonia($esp_colonia);
        }
        if ($esp_zona != '') {
            $zona = cm_trata_ciudad_colonia($esp_zona);
        }
        if ($zona == '---otra---') {
            $zona = '';
        }

        if ($pais == '') {
            $msg_error .= "<br><span class=\"msg_error\">Debes especificar el País donde se encuentra el inmueble ...</span>";
        }
        if ($provincia == '') {
            $msg_error .= "<br><span class=\"msg_error\">Debes especificar el Estado donde se encuentra el inmueble ...</span>";
        }
        if ($ciudad == '') {
            $msg_error .= "<br><span class=\"msg_error\">Debes especificar la Ciudad donde se encuentra el inmueble ...</span>";
        }
        if ($colonia == '') {
            $msg_error .= "<br><span class=\"msg_error\">Debes especificar la Colonia donde se encuentra el inmueble ...</span>";
        }

        // Cargo uno u otro dependiendo ...
        if ($msg_error == '') {
            include_once("inmueble_z2.php");
        } else {
            include_once("inmueble_z1.php");
        }

        // En caso de ser la pantalla de captura de información
    } else {
        include_once("inmueble_z1.php");
    }


    //////////////
    // Borrado del inmueble
} //

//
elseif (trim($claveprop) != '' && $paso == 5) {

    if ($ppinmueble == 'Desarrollo') {
        include_once('desarrollo.php');
        die('Programa terminado ...');
    } else {
        include_once("inmueble_z3.php");
    }
}

/*
    En caso de estar en desarrollos
*/
if ($ppinmueble == 'Desarrollo') {
    include_once('desarrollo.php');
    die('Programa terminado ...');
}

////////////////////////////////////////////
// Al cumplirse lo siguiente paso al área de captura de los datos internos de la propiedad
if (trim($claveprop) != '' && ($paso == 3 || $paso == 6)) {

    // Recupero la información de la propiedad, si no existe termino el programa
    $query = sprintf(
        "SELECT * FROM propiedades WHERE contrato='%s' AND claveprop='%s'",
        $config['contrato'],
        $claveprop
    );
    $consulta1 = $sql->query($query);
    if ($consulta1->num_rows == 0) {

        echo 'Ha ocurrido un error, el inmueble no existe an la base de datos.';

        // Desconectamos de la base de datos
        $sql->close();

        // Despliego el pie de página y termino la ejecución del programa
        echo $mustache->render($plantilla['pie'], $render);
        exit;
    }
    $propiedad = $consulta1->fetch_assoc();

    // Permisos de ejecución de este programa
    if ($propiedad['aid'] != $_COOKIE['cookie_asesor']) {
        cm_permiso('p_edita_todos_los_inmuebles');
    } else {
        cm_permiso('p_registra_inmuebles');
    }


    if ($paso == 3) {
        include __DIR__ . '/inmueble/inmueble_dint.php';
    } elseif ($paso == 6) {
        include __DIR__ . '/inmueble/inmueble_multibolsa.php';
    }

} //

//////////////////////////////////////////////////////////////////////
// Paso 4: Procesamiento de datos internos de la propiedad
elseif (trim($claveprop) != '' && $paso == 4) {

    include __DIR__ . '/inmueble-paso-4.php';

} //

//////////////////////////////////////////////////////////////////////
// Paso 7: Procesamiento de configuración de multibolsa inmobiliaria
elseif (trim($claveprop) != '' && $paso == 7) {
    include __DIR__ . '/inmueble-paso-7.php';
}

// Desconectamos de la base de datos
$sql->close();

// Despliego el pie de página y termino la ejecución del programa
echo $mustache->render($plantilla['pie'], $render);
exit;
