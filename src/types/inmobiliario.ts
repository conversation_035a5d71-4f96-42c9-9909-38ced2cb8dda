export interface Autor {
  firstName: string;
  lastName: string;
  avatar: string;
  company: string;
  verified: boolean;
  name?: string;
}

export interface Socio {
  _id: string;
  name: string;
  avatar: string;
  company: string;
  verified: boolean;
  phone?: string;
  email?: string;
  location?: string;
  specialties?: string[];
}

export interface Comentario {
  _id?: string;
  id: string;
  authorId: string;
  authorCache: Autor;
  text: string;
  createdAt: string;
  likes?: number;
  likedBy?: string[];
  active?: boolean;
  edited?: boolean;
  editedAt?: string;
}

export interface PostInmobiliario {
  _id: string;
  id?: string; // Alias para _id
  type: "inmueble" | "cliente" | "invitacion" | "noticia" | "mulbin";
  title: string;
  description: string;
  price: number;
  location: string;
  createdAt: string;
  authorId: string;
  authorCache: Autor;
  images: string[];
  commentsCount: number;
  showComments?: boolean;
  comments?: Comentario[];
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  propertyType?: string;
  features?: string[];
  views?: number;
  contacts?: number;
  favorites?: number; // ✅ Contador de favoritos
  isFavorited?: boolean; // ✅ Si está en favoritos del usuario actual
  sharedCount?: number;
  targetUserIds?: string[];
  isPublic?: boolean;
  active?: boolean;
  updatedAt?: string;
  externalId?: string; // 🆕 ID del sistema padre (indica inmueble relacionado)
  externalKey?: string; // 🆕 Clave interna del inmueble (indica inmueble relacionado)
  relatedInmueble?: {
    id?: number;
    key: string;
    name: string;
    image?: string;
    precio?: number;
    moneda?: string;
    operacion?: string; // venta, renta, traspaso
    tipo?: string; // casa, departamento, terreno, etc.
    colonia?: string;
    ciudad?: string;
  }; // 🆕 Inmueble relacionado para posts tipo 'inmueble' e 'invitacion'
  
  // 🏠 NUEVO: Campos para inmuebles en hilos de interés
  esInmueble?: boolean; // Flag para identificar que viene de inmuebles
  inmuebleData?: {
    inmuebleId: string;
    titulo: string;
    referenciaPrivada: string;
    inmuebleInfo: {
      titulo: string;
      descripcion: string;
      socio: {
        id: string;
        meteor_id?: string;
        nombre: string;
        empresa?: string;
      };
    };
  };
}

export interface Filtros {
  type?: string;
  location?: string;
  maxPrice?: number | null;
  minPrice?: number | null;
  authorId?: string;
  search?: string;
}

export interface NuevoPost {
  type: "inmueble" | "cliente" | "invitacion" | "noticia" | "mulbin";
  title: string;
  description: string;
  // location: string;
  images: string[];
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  propertyType?: string;
  features?: string[];
  contactPreference?: string;
  urgency?: string;
  targetUserIds?: string[];
  relatedInmueble?: {
    id?: number;
    key: string;
    name: string;
    image: string;
    precio?: number;
    moneda?: string;
    operacion?: string;
    tipo?: string;
    colonia?: string;
    ciudad?: string;
  } | null;
}

// 🆕 NUEVO: Tipos para favoritos
export interface FavoritoPost {
  _id: string;
  postId: string;
  userId: string;
  createdAt: string;
  postCache: {
    title: string;
    type: string;
    price: number;
    location: string;
    authorId: string;
    authorName: string;
    images: string[];
    active: boolean;
    lastCacheUpdate: string;
  };
  post?: PostInmobiliario; // Post completo cuando se obtiene con join
}

// 🆕 NUEVO: Respuesta de la API de favoritos
export interface FavoritosResponse {
  favorites: FavoritoPost[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

// 🆕 NUEVO: Opciones para obtener favoritos
export interface FavoritosOptions {
  page?: number;
  limit?: number;
  filters?: {
    type?: string;
    location?: string;
  };
}

// 🔧 REMOVIDO: Todo el sistema de tipos de leads eliminado
