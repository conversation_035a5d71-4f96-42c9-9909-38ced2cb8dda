declare module "simpleddp" {
  export default class SimpleDDP {
    constructor(options: any, plugins?: any[]);

    on(event: string, callback: Function): any;
    connect(): Promise<void>;
    call(methodName: string, ...args: any[]): Promise<any>;
    collection(name: string): any;
    subscribe(publicationName: string, ...args: any[]): any;
    login(credentials: any): Promise<any>;
    logout(): Promise<void>;

    connected: boolean;
  }
}

declare module "simpleddp-plugin-login" {
  export default function loginPlugin(): any;
}

declare module "isomorphic-ws" {
  const WebSocket: any;
  export default WebSocket;
}
