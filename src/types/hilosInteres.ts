// ✅ TIPOS PARA HILOS DE INTERÉS

/**
 * Cache de datos de usuario para eficiencia
 */
export interface UserCache {
  firstName: string;
  lastName: string;
  avatar: string;
  company: string;
  verified: boolean;
  lastCacheUpdate: Date;
}

/**
 * Cache de datos del post para eficiencia
 */
export interface PostCache {
  title: string;
  type: string;
  price: number;
  location: string;
  active: boolean;
  lastCacheUpdate: Date;
}

/**
 * Hilo de interés principal
 */
export interface HiloInteres {
  _id: string;
  postId: string;
  titulo: string;
  referenciaPrivada: string;

  // Participantes
  creatorId: string;
  postAuthorId: string;

  // 🆕 NUEVO: Array de socios para feed optimizado
  sociosIds: string[];

  // Cache de datos de participantes
  creatorCache: UserCache;
  postAuthorCache: UserCache;

  // Metadatos del post
  postCache: PostCache;

  // Relación con inmueble (opcional)
  postExternalId?: string;

  // Contadores
  mensajesCount: number;
  lastMessageAt: Date;

  // Estado
  active: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Campos calculados (agregados por el frontend)
  unreadMessagesCount?: number;
}

/**
 * Mensaje de un hilo
 */
export interface MensajeHilo {
  _id: string;
  hiloId: string;
  authorId: string;
  mensaje: string;

  // Cache del autor
  authorCache: UserCache;

  // Control de lectura
  readBy: string[];

  // Estado
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Datos para crear un nuevo hilo
 */
export interface NuevoHiloData {
  postId: string;
  titulo: string;
  referenciaPrivada?: string;
  mensajeInicial: string;
}

// 🆕 INTERFACES PARA CONTADORES OPTIMIZADOS

/**
 * Contador de mensajes no leídos por post (colección de apoyo)
 */
export interface UnreadCountByPost {
  _id: string; // Formato: postId_userId
  postId: string;
  userId: string;
  totalUnreadMessages: number;
  hilosCount: number;
  lastCalculatedAt: Date;

  // Desglose por hilo (opcional para debugging)
  hilosBreakdown?: {
    [hiloId: string]: number;
  };
}

/**
 * Resumen de hilos para badges
 */
export interface ResumenHilos {
  hilosCreados: number;
  hilosComoAutor: number;
  totalUnreadAsCreator: number;
  totalUnreadAsAuthor: number;
  totalUnread: number;
}

/**
 * Opciones para obtener hilos con filtros
 */
export interface HilosOptions {
  page?: number;
  limit?: number;
  sortBy?: "createdAt" | "lastMessageAt" | "mensajesCount";
  sortOrder?: "asc" | "desc";
  filters?: {
    hasUnread?: boolean;
    postType?: string;
    location?: string;
  };
}

/**
 * Respuesta paginada de hilos
 */
export interface HilosResponse {
  hilos: HiloInteres[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
}

/**
 * Contadores globales por posts (respuesta optimizada)
 */
export interface UnreadCountsByPosts {
  [postId: string]: number;
}

/**
 * Opciones para obtener contadores
 */
export interface UnreadCountsOptions {
  postIds: string[];
  forceRecalculate?: boolean;
}

/**
 * Estadísticas del sistema (para administradores)
 */
export interface HilosStats {
  totalHilos: number;
  totalMensajes: number;
  totalCounters: number;
  avgUnreadPerCounter: number;
  timestamp: Date;
}

/**
 * Estado del menú contextual de hilos
 */
export interface MenuHilosState {
  isOpen: boolean;
  postId: string | null;
  isPostAuthor: boolean;
  hilos: HiloInteres[];
  loading: boolean;
}

/**
 * Estado del modal de chat
 */
export interface ChatModalState {
  isOpen: boolean;
  hilo: HiloInteres | null;
  mensajes: MensajeHilo[];
  nuevoMensaje: string;
  loading: boolean;
  sending: boolean;
}

/**
 * Estado del modal de creación de hilo
 */
export interface CrearHiloModalState {
  isOpen: boolean;
  postId: string | null;
  titulo: string;
  referenciaPrivada: string;
  mensajeInicial: string;
  loading: boolean;
  errors: {
    titulo?: string;
    mensajeInicial?: string;
  };
}

/**
 * Props para componentes de hilos
 */
export interface HiloComponentProps {
  postId: string;
  isPostAuthor: boolean;
  onThreadSelect?: (hilo: HiloInteres) => void;
  onThreadCreate?: () => void;
}

/**
 * Eventos del sistema de hilos
 */
export interface HilosEvents {
  "thread:created": HiloInteres;
  "thread:selected": HiloInteres;
  "thread:deleted": string; // hiloId
  "message:sent": MensajeHilo;
  "messages:read": string; // hiloId
  "counters:updated": UnreadCountsByPosts; // Contadores actualizados
}

/**
 * Estado reactivo de contadores
 */
export interface UnreadCountersState {
  counters: UnreadCountsByPosts;
  loading: boolean;
  lastUpdated: Date | null;
  error: string | null;
}

// 🆕 TIPOS PARA FEED DE HILOS

/**
 * Opciones para feed de hilos
 */
export interface FeedHilosOptions {
  limit?: number;
  skip?: number;
  sortBy?: "lastMessage" | "created" | "activity" | "alphabetical";
  filterUnread?: boolean;
}

/**
 * Estadísticas del feed de hilos
 */
export interface FeedHilosStats {
  totalHilos: number;
  hilosConMensajesNoLeidos: number;
  totalMensajesNoLeidos: number;
  hilosCreados: number;
  hilosComoAutorPost: number;
  porcentajeActividad: number;
  timestamp: Date;
}

/**
 * Estado del feed de hilos
 */
export interface FeedHilosState {
  hilos: HiloInteres[];
  loading: boolean;
  stats: FeedHilosStats | null;
  loadingStats: boolean;
  filterUnread: boolean;
  sortBy: "lastMessage" | "created" | "activity" | "alphabetical";
  lastUpdated: Date | null;
  error: string | null;
}
