# INTEGRACIÓN: Eliminación Sincronizada de Inmuebles con Meteor

## 🎯 Objetivo

Implementar eliminación automática de posts inmobiliarios en Meteor cuando se elimina un inmueble desde el sistema legacy PHP (panel4-templates).

## 🏗️ Arquitectura de la Solución

### Flujo de Eliminación Sincronizada

```
[Usuario] → [inmueble_z3.php] → [Confirmación] → [Eliminar BD MySQL] → [MeteorPost::deletePostsByExternalId()] → [API Meteor] → [Feedback Usuario]
    ↓                ↓                ↓                    ↓                           ↓                            ↓              ↓
[Interface]    [Permisos]      [Doble Confirm]    [Sistema Legacy]         [Integración]                [Red Social]    [Resultado]
```

### Componentes Modificados

| **Archivo** | **Modificación** | **Responsabilidad** |
|-------------|------------------|---------------------|
| `MeteorPost.class.php` | ✅ Métodos `deletePostsByExternalId()` y `deletePostById()` | Comunicación con Meteor API para eliminación |
| `inmueble_z3.php` | ✅ Integración tras eliminación exitosa en MySQL | Orquestación del flujo de eliminación sincronizada |

---

## 📁 IMPLEMENTACIÓN DETALLADA

### 1. **MeteorPost.class.php** - Nuevos Métodos de Eliminación

#### A. `deletePostsByExternalId($externalId)`

**Propósito**: Eliminar todos los posts relacionados con un inmueble usando el externalId

**Parámetros**:
- `$externalId` (string): ID del inmueble en el sistema padre (clave_sistema)

**Respuesta**:
```php
array(
    'status' => 200,           // Código HTTP
    'success' => true,         // Booleano de éxito
    'data' => (object) array(
        'totalFound' => 2,     // Posts encontrados
        'totalDeleted' => 2,   // Posts eliminados exitosamente
        'deletedPostIds' => ['id1', 'id2'],
        'message' => 'Posts eliminados exitosamente'
    ),
    'message' => 'Posts eliminados exitosamente de Meteor'
)
```

**Casos especiales**:
- **404 Not Found**: No se encontraron posts → Se considera éxito (`success: true`)
- **403 Forbidden**: Sin permisos → Se reporta información de posts no autorizados
- **500 Error**: Excepciones → Se capturan y loggean sin detener el flujo

#### B. `deletePostById($postId)`

**Propósito**: Eliminar un post específico por su ID de Meteor

**Parámetros**:
- `$postId` (string): ID del post en Meteor

**Uso**: Para casos donde se conozca el ID específico del post a eliminar

### 2. **inmueble_z3.php** - Integración del Flujo de Eliminación

#### Punto de Integración

**Ubicación**: Líneas 175-213, justo después de la eliminación exitosa del inmueble en MySQL

```php
// En caso de que si se haya eliminado un inmueble de la BD
if ($num1 == 1) {
    // 🚀 INTEGRACIÓN CON METEOR - ELIMINAR POSTS INMOBILIARIOS
    try {
        require_once __DIR__ . '/clases/MeteorPost.class.php';
        $meteorPost = new Mulbin\MeteorPost($config['contrato']);
        
        // Eliminar todos los posts relacionados con este inmueble por externalId
        $meteorResponse = $meteorPost->deletePostsByExternalId($clave_sistema);
        
        // Procesar respuesta y generar información para el usuario
        if ($meteorResponse['success']) {
            $meteor_deletion_info = array(
                'success' => true,
                'posts_found' => $meteorResponse['data']->totalFound,
                'posts_deleted' => $meteorResponse['data']->totalDeleted
            );
        } else {
            $meteor_deletion_info = array(
                'success' => false,
                'error' => $meteorResponse['message']
            );
        }
    } catch (\Exception $e) {
        // Capturar excepciones sin detener el flujo principal
        $meteor_deletion_info = array(
            'success' => false,
            'error' => 'Excepción: ' . $e->getMessage()
        );
    }
    
    // Continúa con el flujo normal del sistema legacy...
}
```

#### Feedback Visual al Usuario

**Ubicación**: Líneas 281-310, en la pantalla de confirmación de eliminación

**Estados del feedback**:

1. **Eliminación exitosa con posts encontrados**:
   ```
   Se eliminaron 2 post(s) del Feed de Multibolsa Inmobiliaria
   ```

2. **Eliminación parcial (permisos)**:
   ```
   Se eliminaron 1 post(s) del Feed de Multibolsa Inmobiliaria
   1 post(s) no pudieron eliminarse (permisos insuficientes)
   ```

3. **Sin posts relacionados**:
   ```
   No se encontraron posts relacionados en el Feed de Multibolsa Inmobiliaria
   ```

4. **Error en la eliminación**:
   ```
   ⚠️ Error al eliminar posts del Feed: [detalle del error]
   ```

---

## 🔗 INTEGRACIÓN CON API EXISTENTE

### Uso de la Ruta DELETE /api/posts/by-external-id/:externalId

La integración utiliza la nueva ruta REST implementada anteriormente:

```
DELETE /api/posts/by-external-id/12345
Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

**Ventajas de usar externalId**:
- ✅ **Eliminación completa**: Borra todos los posts relacionados con el inmueble
- ✅ **Manejo de duplicados**: Si hay múltiples posts del mismo inmueble, los elimina todos
- ✅ **Permisos granulares**: Solo elimina posts donde el usuario tiene permisos
- ✅ **Información detallada**: Respuesta con contadores exactos de eliminación

---

## 🛡️ MANEJO DE ERRORES Y RESILIENCIA

### Principio de No-Interrupción

**Filosofía**: Los errores en Meteor **NO deben detener** la eliminación del inmueble en el sistema principal.

### Tipos de Errores Manejados

1. **Errores de Conectividad**:
   - Timeout de conexión con Meteor API
   - Servicio Meteor no disponible
   - **Resultado**: Error loggeado, eliminación MySQL continúa

2. **Errores de Autenticación**:
   - Token API inválido o expirado
   - Permisos insuficientes
   - **Resultado**: Error reportado al usuario, eliminación MySQL continúa

3. **Errores de API**:
   - Estructura de respuesta inesperada
   - Códigos HTTP no manejados
   - **Resultado**: Excepción capturada, eliminación MySQL continúa

4. **Errores de Validación**:
   - externalId inválido
   - Datos corruptos
   - **Resultado**: Error específico loggeado y mostrado

### Logging Detallado

```php
// ✅ Eliminación exitosa
error_log("✅ Eliminación sincronizada Meteor - Inmueble: {$clave_sistema}, Posts encontrados: {$totalFound}, Posts eliminados: {$deletedCount}");

// ⚠️ Errores de API
error_log("⚠️ Error en eliminación Meteor para inmueble {$clave_sistema}: {$errorMsg}");

// ❌ Excepciones críticas
error_log("❌ Excepción en eliminación Meteor para inmueble {$clave_sistema}: " . $e->getMessage());
```

---

## 🔄 FLUJO COMPLETO DE ELIMINACIÓN

### Paso a Paso

1. **Usuario confirma eliminación** en `inmueble_z3.php`
2. **Validación de permisos** del sistema legacy
3. **Eliminación de imágenes** vía servicio de fotos
4. **Eliminación en MySQL**:
   - Propiedad principal
   - Reportes de visitas
   - Citas relacionadas
   - Documentos asociados
   - Publicaciones en sitios de socios
5. **🆕 Eliminación en Meteor**:
   - Búsqueda por externalId
   - Validación de permisos por post
   - Eliminación batch de posts autorizados
6. **Feedback consolidado al usuario**

### Sincronización de Estados

```
[MySQL] ✅ Eliminado → [Meteor] ✅ Posts eliminados → [Usuario] ✅ Confirmación completa
[MySQL] ✅ Eliminado → [Meteor] ❌ Error/Sin posts → [Usuario] ⚠️ Eliminación parcial
[MySQL] ❌ Error → [Flujo se detiene] → [Usuario] ❌ Error general
```

---

## 📊 MÉTRICAS Y MONITORING

### Información Disponible en Logs

- **Contadores de eliminación**: Posts encontrados vs eliminados
- **Detalles de autorización**: Posts sin permisos suficientes
- **Tiempos de respuesta**: Performance de la integración
- **Errores específicos**: Clasificación de tipos de fallos

### Variables de Entorno Requeridas

```bash
API_URL_METEOR=http://localhost:3000/api
API_KEY_METEOR=c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

---

## 🧪 CASOS DE PRUEBA

### 1. **Eliminación Estándar**
- **Condición**: Inmueble con 1 post en Meteor
- **Resultado esperado**: Post eliminado, confirmación al usuario
- **Verificación**: Log con "Posts encontrados: 1, Posts eliminados: 1"

### 2. **Eliminación Múltiple**
- **Condición**: Inmueble con varios posts (duplicados)
- **Resultado esperado**: Todos los posts eliminados
- **Verificación**: totalFound = totalDeleted > 1

### 3. **Sin Posts Relacionados**
- **Condición**: Inmueble nunca publicado en Meteor
- **Resultado esperado**: Mensaje "No se encontraron posts relacionados"
- **Verificación**: totalFound = 0, success = true

### 4. **Permisos Parciales**
- **Condición**: Posts de diferentes autores
- **Resultado esperado**: Solo posts autorizados eliminados
- **Verificación**: totalDeleted < totalFound

### 5. **Meteor No Disponible**
- **Condición**: Servicio Meteor caído
- **Resultado esperado**: Error mostrado, inmueble eliminado de MySQL
- **Verificación**: Excepción capturada, eliminación MySQL exitosa

### 6. **Token API Inválido**
- **Condición**: Credenciales incorrectas
- **Resultado esperado**: Error de autenticación mostrado
- **Verificación**: Error 401/403 manejado correctamente

---

## 🔧 MANTENIMIENTO Y ACTUALIZACIONES

### Al Cambiar la API de Meteor

1. **Actualizar endpoint**: Modificar `makeMeteorRequest()` en `MeteorPost.class.php`
2. **Actualizar estructura de respuesta**: Revisar parsing en `deletePostsByExternalId()`
3. **Actualizar autenticación**: Modificar headers si cambia el método de auth

### Al Modificar el Flujo de Eliminación Legacy

1. **Mantener punto de integración**: Después de eliminación MySQL exitosa
2. **Preservar captura de excepciones**: No interrumpir flujo principal
3. **Actualizar variables de contexto**: Si cambian `$clave_sistema` o `$config`

### Al Cambiar el Feedback de Usuario

1. **Modificar sección de output**: Líneas 281-310 en `inmueble_z3.php`
2. **Actualizar colores y estilos**: Mantener consistencia visual legacy
3. **Localizar mensajes**: Mantener español como idioma base

---

## ⚠️ CONSIDERACIONES TÉCNICAS

### Compatibilidad PHP 5.6

- ✅ **Sintaxis legacy**: Uso de `array()` en lugar de `[]`
- ✅ **Sin type hints**: Parámetros sin declaración de tipos
- ✅ **Manejo de excepciones**: Compatible con PHP 5.6
- ✅ **Namespace**: `Mulbin\MeteorPost` compatible desde PHP 5.3

### Comunicación Cross-System

- **PHP 5.6** (panel4-templates) → **Node.js** (Meteor 3)
- **MySQL Legacy** → **MongoDB** (Meteor database)
- **cURL HTTP** → **REST API JSON**

### Seguridad

- ✅ **Validación de entrada**: `urlencode()` en externalId
- ✅ **Escape de output**: `htmlspecialchars()` en mensajes de error
- ✅ **Autenticación**: Token API para comunicación con Meteor
- ✅ **Autorización**: Permisos granulares por post en Meteor

---

## 📋 RESUMEN DE CAMBIOS

### Archivos Modificados

1. **`MeteorPost.class.php`**:
   - ✅ Método `deletePostsByExternalId($externalId)`
   - ✅ Método `deletePostById($postId)`
   - ✅ Manejo robusto de errores y respuestas
   - ✅ Logging detallado para debugging

2. **`inmueble_z3.php`**:
   - ✅ Integración después de eliminación MySQL exitosa
   - ✅ Captura de excepciones sin interrumpir flujo
   - ✅ Generación de información para usuario
   - ✅ Feedback visual con diferentes estados

### Funcionalidades Agregadas

- 🆕 **Eliminación sincronizada**: Sistema legacy ↔ Meteor
- 🆕 **Manejo de múltiples posts**: Eliminación por externalId
- 🆕 **Feedback granular**: Contadores detallados al usuario
- 🆕 **Resiliencia**: Errores no interrumpen eliminación principal
- 🆕 **Logging completo**: Tracking de todas las operaciones

---

**Fecha de implementación**: 2025-01-16  
**Versión**: 1.0  
**Compatibilidad**: PHP 5.6, Meteor 3, API REST  
**Estado**: ✅ Implementado y documentado
