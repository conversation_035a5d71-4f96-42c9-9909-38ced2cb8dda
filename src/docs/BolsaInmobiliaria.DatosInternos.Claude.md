# ARQUITECTURA MULTIBOLSA INMOBILIARIA - ANÁLISIS TÉCNICO PROFUNDO

## 🏗️ OVERVIEW ARQUITECTURAL

### ⚠️ CONSIDERACIONES TÉCNICAS CRÍTICAS

**Versiones de Tecnologías en Uso:**

- **<PERSON><PERSON><PERSON> Principal (panel4-templates)**: PHP 5.6 (Legacy)
- **Microservicio msi-v5**: PHP 8.2 + Slim Framework v4 (Moderno)
- **Red Social**: Meteor 3 (Node.js)
- **Frontend**: Vue 3 + TypeScript (Moderno)

**Implicaciones Arquitecturales:**

- **Compatibilidad**: El código PHP debe mantener compatibilidad con sintaxis PHP 5.6
- **Comunicación**: Integración cross-version entre PHP 5.6 ↔ PHP 8.2 vía HTTP APIs
- **Limitaciones**: Sin traits, sin type hints, sin namespaces modernos en sistema principal
- **Testing**: Comportamiento puede diferir entre versiones de PHP en desarrollo vs producción

### Flujo de Datos End-to-End

```
[Usuario] → [inmueble.php] → [inmueble_multibolsa.php] → [bolsa_inmobiliaria.php] → [mustache] → [Vue] → [inmueble-paso-7.php] → [MeteorPost] → [Meteor API]
    ↓              ↓                 ↓                       ↓                 ↓          ↓              ↓                ↓               ↓
[Browser]    [Router/Controller]  [Form Container]    [Logic Processor]   [Template]  [UX Layer]   [Data Processor]  [Integration]  [Social Network]
                                                                                                           ↓
                                                                                                    [MySQL Database]
                                                                                                           ↓
                                                                                                  [cm_rec_datos_prop()]
```

### Separación de Responsabilidades

| **Capa**                   | **Componente**                   | **Responsabilidad**                                     | **Tecnología**     |
| -------------------------- | -------------------------------- | ------------------------------------------------------- | ------------------ |
| **Routing**                | `inmueble.php`                   | Control de flujo, validación de pasos                   | PHP 5.6            |
| **Form Container**         | `inmueble_multibolsa.php`        | Estructura del formulario de la Multibolsa Inmobiliaria | PHP 5.6/HTML       |
| **Data Processing**        | `bolsa_inmobiliaria.php`         | Lógica de comisiones, preparación de datos              | PHP 5.6            |
| **Presentation**           | `bolsa_inmobiliaria.st.mustache` | Template rendering, estructura HTML                     | Mustache           |
| **User Interaction**       | `InmuebleBolsaInmobiliaria.vue`  | Componente interactivo, gestión de estado               | Vue 3 + TypeScript |
| **API Integration**        | `inmueble-paso-4.php`            | Procesamiento final, integración Meteor                 | PHP 5.6            |
| **External Communication** | `MeteorPost.class.php`           | Abstracción de comunicación con Meteor                  | PHP 5.6            |
| **Microservice**           | `msi-v5 API`                     | Datos de inmuebles y socios                             | PHP 8.2 + Slim v4  |
| **Social Network**         | `Meteor API`                     | Publicación de posts inmobiliarios                      | Meteor 3 (Node.js) |

---

## 📄 ANÁLISIS DETALLADO POR ARCHIVO

### 1. **inmueble.php** - Router Principal

**Ubicación**: `panel4-templates/src/entries/inmueble.php`
**Función**: Controlador central que maneja el flujo de pasos para captura de inmuebles

#### Responsabilidades Específicas:

- **Líneas 82-196**: Pantalla principal (listado de inmuebles, búsqueda)
- **Líneas 209-316**: Paso 1-2 (captura básica del inmueble)
- **Líneas 340-380**: **Paso 6** - Multibolsa (invoca `inmueble_multibolsa.php`)
- **Líneas 384-388**: **Paso 7** - Procesamiento y persistencia

#### Punto de Entrada Crítico:

```php
// Línea 372: Inclusión del formulario de la Multibolsa Inmobiliaria
<?php include __DIR__ . '/inmueble/inmueble_multibolsa.php' ?>
```

#### Dependencias:

- `funciones.php`: Funciones utilitarias del sistema
- `verifica_pago.php`: Validación de suscripciones
- Variables globales: `$propiedad`, `$claveprop`, `$config`

---

### 2. **inmueble_multibolsa.php** - Container Form

**Ubicación**: `panel4-templates/src/entries/inmueble/inmueble_multibolsa.php`
**Función**: Estructura del formulario de la Multibolsa Inmobiliaria con inclusiones modulares

#### Arquitectura Modular:

```php
// Línea 42: INTEGRACIÓN MULTIBOLSA INMOBILIARIA
include __DIR__ . '/inmueble_multibolsa/bolsa_inmobiliaria/bolsa_inmobiliaria.php';
```

#### Form Structure:

- **Formulario target**: `$PHP_SELF` con `paso=4`
- **Submit handler**: `Valida_Campos(this)`
- **Form name**: `Formulario1` (referenciado por JavaScript)

#### Campos Ocultos Críticos:

```php
<input type="hidden" name="clave_sistema" value="<?= $propiedad['clave_sistema'] ?>">
<input type="hidden" name="claveprop" value="<?= $claveprop ?>">
<input type="hidden" name="paso" value="4">
```

---

### 3. **bolsa_inmobiliaria.php** - Data Processor

**Ubicación**: `panel4-templates/src/entries/inmueble/inmueble_multibolsa/bolsa_inmobiliaria/bolsa_inmobiliaria.php`
**Función**: Procesamiento de lógica de comisiones compartidas y preparación de datos para template

#### Variables de Contexto Input:

- `$propiedad`: Array con datos del inmueble actual
- `$config`: Configuración del contrato/socio
- `$_SESSION`: Datos de sesión (meteor_user_id, meteor_auth_token)

#### Lógica de Comisiones de Venta:

```php
// Líneas 29-50: Cálculo de comisión compartida de venta
if ($m_data['enventa']) {
    $comision_vta = $propiedad['precio_venta'] * ($propiedad['i_porcentaje_comision'] / 100);

    if ($propiedad['t_comparto_comision'] == 'sobre precio') {
        $comision_compartida = $propiedad['precio_venta'] * ($propiedad['comparto_comision'] / 100);
    } elseif ($propiedad['t_comparto_comision'] == 'sobre comision') {
        $comision_compartida = $comision_vta * ($propiedad['comparto_comision'] / 100);
    }
}
```

#### Lógica de Comisiones de Renta:

```php
// Líneas 52-68: Cálculo de comisión compartida de renta
if ($propiedad['i_tipo_comision_rta'] == '1 mes') {
    $comision_rta = $propiedad['precio_renta'];
} else {
    $comision_rta = $propiedad['i_comision_rta'];
}
```

#### 🆕 Configuración de Multibolsa (Líneas 25-29):

```php
// DATOS DE MULTIBOLSA INMOBILIARIA (para precargar configuración existente)
$m_data['usar_multibolsa'] = isset($propiedad['usar_multibolsa']) ? ($propiedad['usar_multibolsa'] == 'Si' ? 'true' : 'false') : 'false';
$m_data['publicacion_publica'] = isset($propiedad['publicacion_publica']) ? ($propiedad['publicacion_publica'] == 'Si' ? 'true' : 'false') : 'false';
$m_data['socios_seleccionados'] = isset($propiedad['socios_seleccionados']) ? $propiedad['socios_seleccionados'] : '';
$m_data['solicitar_publicacion_websites'] = isset($propiedad['solicitar_publicacion_websites']) ? ($propiedad['solicitar_publicacion_websites'] == 'Si' ? 'true' : 'false') : 'false';
```

#### 🆕 Control de Duplicación de Publicaciones (Líneas 31-42):

```php
// CONTROL DE DUPLICACIÓN DE PUBLICACIONES EN FEED
$fecha_publicacion = isset($propiedad['fecha_publicacion_feed']) ? $propiedad['fecha_publicacion_feed'] : null;
$m_data['ya_publicado_en_feed'] = !empty($fecha_publicacion) ? 'true' : 'false';
$m_data['fecha_publicacion_feed'] = $fecha_publicacion;

// Formatear fecha para mostrar al usuario (si existe)
if (!empty($fecha_publicacion)) {
    $timestamp = strtotime($fecha_publicacion);
    $m_data['fecha_publicacion_formateada'] = date('d/m/Y', $timestamp);
    $m_data['hora_publicacion_formateada'] = date('H:i', $timestamp);

    // Fecha completa en español
    $meses_espanol = ['Jan' => 'ene', 'Feb' => 'feb', 'Mar' => 'mar', /* ... */];
    $fecha_base = date('d/M/Y \a \l\a\s H:i', $timestamp);
    $mes_ingles = date('M', $timestamp);
    $mes_espanol = $meses_espanol[$mes_ingles];
    $m_data['fecha_completa_formateada'] = str_replace($mes_ingles, $mes_espanol, $fecha_base);
}
```

#### Output Variables for Template:

| **Variable**                                | **Tipo**    | **Descripción**                                          |
| ------------------------------------------- | ----------- | -------------------------------------------------------- |
| `$m_data['enventa']`                        | Boolean     | Inmueble disponible para venta                           |
| `$m_data['enrenta']`                        | Boolean     | Inmueble disponible para renta                           |
| `$m_data['h_comparto_comision']`            | HTML String | Texto formateado de comisión de venta                    |
| `$m_data['h_rta_comparto_comision']`        | HTML String | Texto formateado de comisión de renta                    |
| `$m_data['meteor_user_id']`                 | String      | ID del usuario en Meteor                                 |
| `$m_data['socio_ampi']`                     | Boolean     | Si el socio pertenece a AMPI                             |
| **🆕 MULTIBOLSA VARIABLES**                 |             |                                                          |
| `$m_data['usar_multibolsa']`                | String      | "true"/"false" - Estado del checkbox "Publicar en Feed"  |
| `$m_data['publicacion_publica']`            | String      | "true"/"false" - Publicación pública vs privada          |
| `$m_data['socios_seleccionados']`           | String      | IDs de socios separados por coma                         |
| `$m_data['solicitar_publicacion_websites']` | String      | "true"/"false" - Solicitud de publicación en websites    |
| **🆕 CONTROL DE DUPLICACIÓN**               |             |                                                          |
| `$m_data['ya_publicado_en_feed']`           | String      | "true"/"false" - Si el inmueble ya fue publicado en Feed |
| `$m_data['fecha_publicacion_feed']`         | String/NULL | Fecha y hora de última publicación (formato MySQL)       |
| `$m_data['fecha_publicacion_formateada']`   | String      | Fecha formateada para mostrar (dd/mm/yyyy)               |
| `$m_data['hora_publicacion_formateada']`    | String      | Hora formateada para mostrar (HH:mm)                     |
| `$m_data['fecha_completa_formateada']`      | String      | Fecha completa en español (dd/mmm/yyyy a las HH:mm)      |

#### Condiciones de Deshabilitación:

```php
// Línea 71: AMPI Plus deshabilitado
$m_data['mostrar_ampi_plus'] = ($config['socio_ampi'] == 'Si' && false);

// Línea 91: Bolsa AMPI deshabilitada
$mostrar_bolsa_ampi = ($propiedad['tipo_promocion'] == 'EN EXCLUSIVA' && $config['socio_ampi'] == 'Si' && 'perro' == 'gato');
```

---

### 4. **bolsa_inmobiliaria.st.mustache** - Template Engine

**Ubicación**: `panel4-templates/src/entries/inmueble/inmueble_multibolsa/bolsa_inmobiliaria/bolsa_inmobiliaria.st.mustache`
**Función**: Template Mustache para rendering de HTML y carga de componente Vue

#### Estructura de Secciones:

##### A. **Comisión de Venta** (Líneas 16-44)

```mustache
{{#enventa}}
<input type="text" name="comparto_comision" value="{{comparto_comision}}" onChange="return calcula_comision_mibolsa();">
<select name="t_comparto_comision" onChange="return calcula_comision_mibolsa();">
    <option value="sobre comision"{{#t_comparto_comision_sobre_comision_selected}} selected{{/t_comparto_comision_sobre_comision_selected}}>de la comisión</option>
    <option value="sobre precio"{{#t_comparto_comision_sobre_precio_selected}} selected{{/t_comparto_comision_sobre_precio_selected}}>del valor del inmueble</option>
</select>
{{/enventa}}
```

##### B. **Comisión de Renta** (Líneas 46-62)

```mustache
{{#enrenta}}
<input type="text" name="rta_comparto_comision" value="{{rta_comparto_comision}}" onChange="return calcula_comision_rta_mibolsa();">
{{/enrenta}}
```

##### C. **🆕 Aviso de Publicación Previa** (Líneas 68-94)

```mustache
{{#ya_publicado_en_feed}}
<!-- AVISO DE PUBLICACIÓN PREVIA -->
<div class="p-4 mb-4 bg-blue-50 border-l-4 border-blue-400" id="aviso_ya_publicado">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Inmueble ya publicado en el Feed</h3>
            <div class="mt-2 text-sm text-blue-700">
                <p>Este inmueble fue publicado en el Feed de Multibolsa Inmobiliaria el <strong>{{fecha_completa_formateada}}</strong>.</p>
                <p class="mt-1">Puedes continuar editando las comisiones normalmente. Si quieres volver a publicar en el Feed, haz clic en el botón de abajo.</p>
            </div>
            <div class="mt-4">
                <button type="button" onclick="habilitarRepublicacion()"
                    class="px-4 py-2 text-sm font-medium text-blue-800 bg-blue-100 rounded transition-colors duration-200 hover:bg-blue-200">
                    Volver a publicar en el Feed
                </button>
            </div>
        </div>
    </div>
</div>
{{/ya_publicado_en_feed}}
```

##### D. **🆕 Integración Vue Component** (Líneas 97-111)

```mustache
<div class="mbi" style="display: none;">
    <input type="hidden" name="author_id" value="{{ meteor_user_id }}">
    <div
        data-inmueble-bolsa-inmobiliaria
        data-activar-multibolsa="{{ usar_multibolsa }}"
        data-is-public="{{ publicacion_publica }}"
        data-selected-socios="{{ socios_seleccionados }}"
        data-solicitar-publicacion-websites="{{ solicitar_publicacion_websites }}"
        data-ya-publicado-feed="{{ ya_publicado_en_feed }}"
        data-fecha-publicacion-feed="{{ fecha_publicacion_feed }}"
        data-fecha-publicacion-formateada="{{ fecha_completa_formateada }}"
        class="w-full"
    ></div>
    <script type="module" src="/assets/inmuebleBolsaInmobiliaria.js?t={{ tkt }}"></script>
</div>
```

#### JavaScript Functions Críticas:

##### `calcula_comision_mibolsa()` (Líneas 122-141)

- Valida campos numéricos
- Calcula comisión en tiempo real
- Actualiza DOM con formato de moneda
- **Integración Vue**: Notifica cambios via `window.notifyMultibolsaChange()`

##### `mostrar_checkbox_multibolsa()` - Función Global

- Controla visibilidad del componente Vue
- Basada en si hay comisión compartida > 0

##### **🆕 `habilitarRepublicacion()`** (Líneas 302-330)

- **Control de duplicación de publicaciones**
- Oculta el aviso de publicación previa
- Activa `window.estadoRepublicacion = true`
- Notifica al componente Vue via `habilitarModoRepublicacionVue()`
- Ejecuta cálculos iniciales de comisiones
- Muestra checkbox de multibolsa

##### `window.shouldShowMultibolsa()` (Líneas 309-360)

- **Función puente PHP-Vue con control de duplicación**
- Permite activación en modo republicación
- Bloquea auto-activación si ya fue publicado (excepto en republicación)
- Analiza contenido HTML de comisiones calculadas

---

### 5. **InmuebleBolsaInmobiliaria.vue** - Interactive Component

**Ubicación**: `panel4-templates/src/components/inmueble-bolsa-inmobiliaria/InmuebleBolsaInmobiliaria.vue`
**Función**: Componente Vue3 + TypeScript para gestión interactiva de Multibolsa

#### Estado Reactivo:

```typescript
const activarMultibolsa = ref(props.initialActivarMultibolsa); // Control principal
const isPublic = ref(props.initialIsPublic); // Público vs Privado
const selectedSocios = ref<string[]>([...props.initialSelectedSocios]); // Socios seleccionados
const availableFriends = ref<Socio[]>([]); // Lista de socios disponibles
const solicitarPublicacionWebsites = ref(
  props.initialSolicitarPublicacionWebsites
);
```

#### 🆕 Inicialización con Datos de BD:

```typescript
// Props recibidas desde index.js con datos de BD
props: {
    initialActivarMultibolsa: { type: Boolean, default: false },
    initialIsPublic: { type: Boolean, default: false },
    initialSelectedSocios: { type: Array as () => string[], default: () => [] },
    initialSolicitarPublicacionWebsites: { type: Boolean, default: false },
    // 🆕 Props para control de duplicación de publicaciones
    yaPublicadoEnFeed: { type: Boolean, default: false },
    fechaPublicacionFeed: { type: String, default: null },
    fechaPublicacionFormateada: { type: String, default: null },
}

// Estados reactivos inicializados con valores de BD
// 🆕 Si ya fue publicado, inicialmente desactivar hasta re-publicación
const activarMultibolsa = ref(
    props.yaPublicadoEnFeed ? false : props.initialActivarMultibolsa
);
const isPublic = ref(props.initialIsPublic);
const selectedSocios = ref<string[]>([...props.initialSelectedSocios]);
const solicitarPublicacionWebsites = ref(props.initialSolicitarPublicacionWebsites);

// 🆕 Estados para control de duplicación de publicaciones
const yaPublicado = ref(props.yaPublicadoEnFeed);
const enModoRepublicacion = ref(false);
```

#### Integración con PHP Backend:

```typescript
// 🆕 Función para habilitar modo de republicación
const habilitarModoRepublicacion = () => {
  enModoRepublicacion.value = true;
  activarMultibolsa.value = props.initialActivarMultibolsa;
  // Sincronizar con el estado global
  (window as any).estadoRepublicacion = true;
};

// Líneas 401-443: Auto-activación con control de duplicación
onMounted(async () => {
  // 🆕 Registrar función para habilitar republicación desde JavaScript
  (window as any).habilitarModoRepublicacionVue = habilitarModoRepublicacion;

  if (typeof (window as any).shouldShowMultibolsa === "function") {
    const shouldBeActive = (window as any).shouldShowMultibolsa();

    // 🆕 Si ya fue publicado, no auto-activar a menos que esté en modo republicación
    if (shouldBeActive && !activarMultibolsa.value && !yaPublicado.value) {
      activarMultibolsa.value = true;
    }
  }

  // Registrar callback para cambios en comisiones
  (window as any).notifyMultibolsaChange = () => {
    if (typeof (window as any).shouldShowMultibolsa === "function") {
      const shouldBeActive = (window as any).shouldShowMultibolsa();

      // 🆕 Solo activar automáticamente si no fue publicado previamente o está en modo republicación
      if (
        shouldBeActive &&
        !activarMultibolsa.value &&
        (!yaPublicado.value || enModoRepublicacion.value)
      ) {
        activarMultibolsa.value = true;
      } else if (
        !shouldBeActive &&
        activarMultibolsa.value &&
        !enModoRepublicacion.value
      ) {
        activarMultibolsa.value = false;
      }
    }
  };
});
```

#### 🆕 Control de Visibilidad del Componente:

```vue
<template>
  <div class="my-5">
    <!-- Solo mostrar el componente si no fue publicado previamente O si está en modo republicación -->
    <div
      v-if="!yaPublicado || enModoRepublicacion"
      class="p-4 bg-white rounded-lg border border-gray-200 shadow-sm"
    >
      <!-- Contenido del componente -->
    </div>

    <!-- Campos ocultos siempre presentes para compatibilidad con formularios -->
    <input
      type="hidden"
      name="usar_multibolsa"
      :value="activarMultibolsa ? 'Si' : 'No'"
    />
    <!-- ... más campos ocultos ... -->
  </div>
</template>
```

#### API Integration - Socios Loading:

```typescript
// Líneas 256-334: Carga de socios desde msi-v5
const loadUserFriends = async () => {
  const response = await axios.get("/msi-v5/owner/socios");

  // Mapeo de estructura API PHP → Vue
  availableFriends.value = response.data.data.socios
    .filter((socio: any) => socio.tipo === "directo")
    .map((socio: any) => ({
      _id: socio.id,
      name: socio.nombre,
      company: socio.empresa,
      location: socio.ubicacion,
      // ... más campos
    }));
};
```

#### Campos Ocultos para Formulario PHP:

```vue
<!-- Líneas 164-184: Compatibilidad con formularios tradicionales -->
<input
  type="hidden"
  name="usar_multibolsa"
  :value="activarMultibolsa ? 'Si' : 'No'"
/>
<input
  type="hidden"
  name="publicacion_publica"
  :value="activarMultibolsa ? (isPublic ? 'Si' : 'No') : 'No'"
/>
<input
  type="hidden"
  name="socios_seleccionados"
  :value="activarMultibolsa && !isPublic ? selectedSocios.join(',') : ''"
/>
<input
  type="hidden"
  name="solicitar_publicacion_websites"
  :value="activarMultibolsa && solicitarPublicacionWebsites ? 'Si' : 'No'"
/>
```

---

### 6. **SelectorSociosWrapper.vue** - Abstraction Layer

**Ubicación**: `panel4-templates/src/components/selector-socios-wrapper/SelectorSociosWrapper.vue`
**Función**: Wrapper que adapta datos entre diferentes contextos de uso

#### Propósito:

- Adaptar estructura de datos de Multibolsa Inmobiliaria → SelectorSocios genérico
- Mantener compatibilidad entre diferentes implementaciones
- Centralizar lógica de selección de socios

---

### 7. **inmueble-paso-4.php** - Data Persistence & Integration

**Ubicación**: `panel4-templates/src/entries/inmueble-paso-4.php`
**Función**: Procesamiento final, persistencia en MySQL e integración con Meteor

#### Flujo de Ejecución:

##### A. **Autenticación y Obtención de Datos** (Líneas 3-41)

```php
$clave_sistema = (int) $_POST['clave_sistema'];

// Token API para msi-v5
$token = $sql->row("SELECT token FROM api_tokens WHERE contrato_id = {$config['contrato']}");

// Obtener datos completos del inmueble vía API msi-v5
$curl->setUrl('http://' . $msi_v5_service . '/owner/inmuebles/' . $clave_sistema);
$response = json_decode($curl->get()->getResponse());
$apiInmueble = $response->data;
```

##### B. **Integración con Meteor** (Líneas 64-107)

```php
require_once __DIR__ . '/clases/MeteorPost.class.php';
$meteorPost = new Mulbin\MeteorPost($config['contrato']);

// Validación de datos antes de crear post
$validation = $meteorPost->validatePostData($apiInmueble, $_POST);

if ($validation['valid']) {
    $meteorResponse = $meteorPost->createPost($apiInmueble, $_POST);
}
```

##### C. **🆕 Persistencia en MySQL con Control de Fecha** (Líneas 243-264)

```php
// Líneas 186-190: Extracción de variables de multibolsa desde $_POST
$usar_multibolsa = isset($_POST['usar_multibolsa']) ? $_POST['usar_multibolsa'] : 'No';
$publicacion_publica = isset($_POST['publicacion_publica']) ? $_POST['publicacion_publica'] : 'No';
$socios_seleccionados = isset($_POST['socios_seleccionados']) ? $_POST['socios_seleccionados'] : '';
$solicitar_publicacion_websites = isset($_POST['solicitar_publicacion_websites']) ? $_POST['solicitar_publicacion_websites'] : 'No';

// 🆕 PREPARAR CAMPO DE FECHA DE PUBLICACIÓN
$fecha_publicacion_sql = '';
if ($publicacionExitosaEnFeed) {
    $fecha_publicacion_sql = ", fecha_publicacion_feed=NOW()";
    error_log("🕒 Registrando fecha de publicación en Feed para inmueble {$clave_sistema}");
}

// Líneas 250-262: Query UPDATE con campos de multibolsa + fecha condicional
$query = "UPDATE propiedades SET
    comparto_comision='$comparto_comision',
    t_comparto_comision='$t_comparto_comision',
    rta_comparto_comision='$rta_comparto_comision',
    usar_multibolsa='$usar_multibolsa',
    publicacion_publica='$publicacion_publica',
    socios_seleccionados='$socios_seleccionados',
    solicitar_publicacion_websites='$solicitar_publicacion_websites'
    $fecha_publicacion_sql $ampi_plus
    WHERE (contrato='$config[contrato]' && clave_sistema='$clave_sistema')";
```

##### D. **Actualización de Índices de Búsqueda** (Línea 339)

```php
cm_rec_datos_prop($clave_sistema, $config['contrato']);
```

---

### 8. **MeteorPost.class.php** - External Integration

**Ubicación**: `panel4-templates/src/entries/clases/MeteorPost.class.php`
**Función**: Abstracción completa de comunicación con Meteor 3 API

#### ⚠️ Limitaciones PHP 5.6:

- **Namespace**: Usa `namespace Mulbin;` (compatible desde PHP 5.3)
- **Array syntax**: Usa `array()` en lugar de `[]` para compatibilidad
- **No type hints**: Los parámetros no tienen type declarations
- **No return types**: Los métodos no declaran tipos de retorno

#### 🆕 Validación de Condiciones de Publicación:

```php
// Líneas 39-66: Verificar checkbox "Publicar en Feed" ANTES que comisiones
$usarMultibolsa = isset($formData['usar_multibolsa']) && $formData['usar_multibolsa'] === 'Si';

// Si el usuario deshabilitó "Publicar en el Feed", no publicar
if (!$usarMultibolsa) {
    return array(
        'status' => 200,
        'data' => (object) array(
            'message' => 'Post no creado - usuario deshabilitó publicación en Feed',
            'skipped' => true
        )
    );
}

// Verificar si se debe publicar (solo si comparte comisión)
$compartirVenta = isset($formData['comparto_comision']) && floatval($formData['comparto_comision']) > 0;
$compartirRenta = isset($formData['rta_comparto_comision']) && floatval($formData['rta_comparto_comision']) > 0;

if (!$compartirVenta && !$compartirRenta) {
    return array('status' => 200, 'data' => (object) array('skipped' => true));
}
```

#### Mapeo de Datos Sistema Padre → Meteor:

```php
// Líneas 163-231: Transformación completa de datos
$postData = array(
    'type' => $type,                                    // 'venta' o 'renta'
    'title' => $this->generateTitle($apiInmueble, $type),
    'price' => (float) $price,
    'location' => $this->defineLocation($apiInmueble->ubicacion),
    'bedrooms' => $this->extractCustomField($apiInmueble->campos_personalizados, 'ci_recamaras', 0),
    'bathrooms' => $this->extractCustomField($apiInmueble->campos_personalizados, 'ci_banos', 0),
    'authorId' => $formData['author_id'],               // meteor_user_id desde Vue
    'targetUserIds' => $this->extractTargetUserIds($formData), // Socios seleccionados
    'externalId' => (string) $apiInmueble->id,         // Referencia sistema padre
    'commissionData' => $this->prepareCommissionData($formData, $type)
);
```

#### Procesamiento de Destinatarios:

```php
// Líneas 100-127: Extracción de destinatarios del formulario Vue
private function extractTargetUserIds($formData) {
    if (isset($formData['socios_seleccionados']) && !empty($formData['socios_seleccionados'])) {
        return $this->parseUserIds($formData['socios_seleccionados']);
    }
    // Publicación pública: todos los socios
    elseif ($formData['publicacion_publica'] === 'Si' && isset($formData['lista_socios'])) {
        return $this->parseUserIds($formData['lista_socios']);
    }
}
```

---

## 🔄 FLUJO DE DATOS COMPLETO

### 1. **Inicialización del Formulario con Control de Duplicación**

```
[inmueble.php:paso=6] → [inmueble_multibolsa.php] → [bolsa_inmobiliaria.php]
                                                        ↓
                                              [Extrae fecha_publicacion_feed de BD]
                                                        ↓
                                              [Cálculo de comisiones PHP]
                                                        ↓
                                              [Renderizado Mustache template]
                                                        ↓
                                              [Carga componente Vue con props de duplicación]
```

### 2. **🆕 Estados Condicionales de UI**

```
¿Ya publicado? → SÍ → [Aviso + Inputs visibles + Vue oculto]
                                    ↓
                      [Botón "Volver a publicar"] → [Oculta aviso + Muestra Vue]
                                    ↓
                      [window.estadoRepublicacion = true] → [Modo republicación]

¿Ya publicado? → NO → [Inputs visibles + Vue visible + Auto-activación normal]
```

### 3. **Interacción del Usuario**

```
[Usuario modifica comisión] → [JavaScript calcula_comision_mibolsa()] → [Actualiza DOM]
                                            ↓
                              [window.notifyMultibolsaChange()] → [Vue detecta cambio]
                                            ↓
                              [🆕 Verifica: !yaPublicado || enModoRepublicacion]
                                            ↓
                              [Vue auto-activa/desactiva según comisión y estado]
```

### 4. **Selección de Socios**

```
[Vue carga socios] → [axios.get("/msi-v5/owner/socios")] → [Mapeo de datos]
                                    ↓
                    [SelectorSociosWrapper adapta estructura] → [Usuario selecciona]
                                    ↓
                    [Campos ocultos se actualizan en formulario]
```

### 5. **🆕 Envío y Procesamiento con Registro de Fecha**

```
[Submit formulario] → [inmueble-paso-4.php] → [Obtiene datos vía msi-v5 API]
                                    ↓
                    [MeteorPost.validatePostData()] → [MeteorPost.createPost()]
                                    ↓
                    [¿Publicación exitosa?] → SÍ → [$publicacionExitosaEnFeed = true]
                                    ↓                          ↓
                    [Persistencia MySQL + fecha_publicacion_feed=NOW()]
                                    ↓
                    [cm_rec_datos_prop()] → [Logging con fecha registrada]
```

---

## 🔌 DEPENDENCIAS Y INTEGRACIONES

### APIs Externas:

1. **msi-v5 API** (PHP 8.2 + Slim v4):
   - `/owner/inmuebles/{id}` - Datos completos del inmueble
   - `/owner/socios` - Lista de socios disponibles
   - Comunicación: HTTP REST desde PHP 5.6 usando cURL
2. **Meteor 3 API** (Node.js):
   - `POST /api/posts` - Creación de posts inmobiliarios
   - Autenticación: ApiKey-based authentication

### Variables de Sesión Críticas:

- `$_SESSION['meteor_user_id']`: ID del usuario en Meteor
- `$_SESSION['meteor_auth_token']`: Token de autenticación Meteor
- `$_COOKIE['cookie_asesor']`: ID del asesor activo

### Variables de Entorno:

```bash
API_URL_METEOR=http://localhost:3000/api
API_KEY_METEOR=c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
PROXY_SERVICE_MSI_V5=msi-v5-service:80
```

### Tablas de Base de Datos:

- `propiedades`: Datos principales del inmueble
  - **🆕 Campos Multibolsa**: `usar_multibolsa`, `publicacion_publica`, `socios_seleccionados`, `solicitar_publicacion_websites`
  - **🆕 Control de Duplicación**: `fecha_publicacion_feed` (DATETIME NULL)
- `api_tokens`: Tokens de autenticación para servicios
- `movpropiedades`: Log de movimientos
- `valores_campos`: Campos personalizados del inmueble

### 🆕 Script SQL de Implementación:

```sql
-- Archivo: MySQL/add_fecha_publicacion_feed.sql
ALTER TABLE propiedades
ADD COLUMN IF NOT EXISTS fecha_publicacion_feed DATETIME NULL DEFAULT NULL
COMMENT 'Fecha y hora de la última publicación exitosa en el Feed de Multibolsa Inmobiliaria';

CREATE INDEX IF NOT EXISTS idx_fecha_publicacion_feed ON propiedades(fecha_publicacion_feed);
```

---

## 🚨 PUNTOS CRÍTICOS DE FALLA

### 1. **Dependencia de Session Variables**

Si `meteor_user_id` no está en sesión, el componente Vue no puede cargar y el post no se crea en Meteor.

### 2. **Integración Asíncrona PHP ↔ Vue**

El estado se sincroniza via `window.shouldShowMultibolsa()` y `window.notifyMultibolsaChange()` - si estas funciones fallan, la sincronización se rompe.

### 3. **✅ CORREGIDO - Validación de Checkbox "Publicar en Feed"**

**PROBLEMA RESUELTO**: Ahora `MeteorPost.class.php` verifica `usar_multibolsa` ANTES que comisiones:

- ✅ **Prioridad correcta**: Checkbox se verifica primero
- ✅ **Control del usuario**: Si está desmarcado, no publica sin importar comisiones
- ✅ **Logging claro**: "Post no creado - usuario deshabilitó publicación en Feed"

### 4. **✅ CORREGIDO - Preselección de Socios**

**PROBLEMA RESUELTO**: Configuración de multibolsa se preserva entre sesiones:

- ✅ **Carga desde BD**: `bolsa_inmobiliaria.php` extrae datos existentes
- ✅ **Template dinámico**: Atributos HTML generados desde BD
- ✅ **Inicialización Vue**: Componente recibe valores correctos

### 5. **🆕 ✅ IMPLEMENTADO - Control de Duplicación de Publicaciones**

**FUNCIONALIDAD AGREGADA**: Sistema completo de prevención de duplicados:

- ✅ **Campo BD**: `fecha_publicacion_feed` registra última publicación exitosa
- ✅ **Aviso informativo**: Usuario ve cuándo fue publicado por última vez
- ✅ **Inputs siempre visibles**: Puede editar comisiones independientemente del estado
- ✅ **Vue condicional**: Checkbox oculto hasta que usuario decida re-publicar
- ✅ **Botón re-publicación**: Control explícito "Volver a publicar en el Feed"
- ✅ **Estados reactivos**: JavaScript y Vue sincronizan modo republicación
- ✅ **Fecha en español**: Formato localizado "dd/mmm/yyyy a las HH:mm"

### 6. **Mapeo de Datos API**

La transformación de datos entre msi-v5 API y Meteor API es compleja y puede fallar si cambia la estructura de respuesta de alguna API.

### 7. **⚠️ Incompatibilidades de Versiones PHP**

- **Error Handling**: PHP 5.6 tiene manejo de errores diferente a PHP 8.2
- **JSON Functions**: Comportamiento ligeramente diferente entre versiones
- **Array Functions**: Algunas funciones modernas no disponibles en PHP 5.6
- **Curl Options**: Opciones de cURL pueden diferir entre versiones

### 8. **⚠️ Comunicación Cross-Version**

- **Encoding**: Diferencias en encoding UTF-8 entre PHP 5.6 y 8.2
- **HTTP Headers**: Manejo de headers puede variar
- **SSL/TLS**: Versiones de SSL soportadas pueden causar problemas de conexión

---

## 🔧 PATRONES DE DISEÑO IDENTIFICADOS

### 1. **Template Method Pattern**

`bolsa_inmobiliaria.php` prepara datos → `mustache` renderiza → `Vue` añade interactividad

### 2. **Adapter Pattern**

`SelectorSociosWrapper` adapta datos entre diferentes contextos de uso

### 3. **Bridge Pattern**

`MeteorPost.class.php` abstrae la comunicación con Meteor API

### 4. **Observer Pattern**

Funciones JavaScript `notifyMultibolsaChange()` / `shouldShowMultibolsa()` para sincronización

---

## 📝 NOTAS PARA FUTURAS MODIFICACIONES

### Al Modificar Cálculos de Comisión:

1. Actualizar lógica en `bolsa_inmobiliaria.php`
2. Verificar JavaScript en `bolsa_inmobiliaria.st.mustache`
3. Revisar validación en `MeteorPost.class.php` (checkbox tiene prioridad)

### 🆕 Al Cambiar Configuración de Multibolsa:

1. **Base de Datos**: Agregar campos en tabla `propiedades`
2. **PHP**: Extraer en `bolsa_inmobiliaria.php` (líneas 25-42)
3. **Template**: Pasar como atributos en `bolsa_inmobiliaria.st.mustache`
4. **JavaScript**: Leer atributos en `index.js` y pasar a Vue
5. **Persistencia**: Incluir en UPDATE query de `inmueble-paso-4.php`

### 🆕 Al Modificar Control de Duplicación:

1. **Campo BD**: Ajustar tipo/formato de `fecha_publicacion_feed`
2. **PHP**: Modificar formateo en `bolsa_inmobiliaria.php` (líneas 31-42)
3. **Template**: Actualizar aviso en sección `{{#ya_publicado_en_feed}}`
4. **Vue**: Ajustar props y lógica condicional `v-if="!yaPublicado || enModoRepublicacion"`
5. **JavaScript**: Modificar función `habilitarRepublicacion()` si es necesario
6. **Persistencia**: Ajustar condición `$publicacionExitosaEnFeed` en `inmueble-paso-4.php`

### Al Cambiar Estructura de Socios:

1. Verificar mapeo en `InmuebleBolsaInmobiliaria.vue:loadUserFriends()`
2. Actualizar interfaz `Socio` en TypeScript
3. Revisar `SelectorSociosWrapper` adaptación
4. **🆕 Verificar formato en `socios_seleccionados`** (comma-separated vs JSON)

### Al Modificar Integración Meteor:

1. Actualizar `MeteorPost.class.php:preparePostData()`
2. Verificar mapeo de campos en `extractTargetUserIds()`
3. Actualizar variables de entorno si cambia endpoint
4. **🆕 Mantener validación de `usar_multibolsa` ANTES que comisiones**

### Al Cambiar UI:

1. Modificar template `bolsa_inmobiliaria.st.mustache`
2. Actualizar estilos en componente Vue
3. Verificar compatibilidad con campos ocultos del formulario
4. **🆕 Asegurar que atributos HTML se conviertan correctamente** (boolean → "true"/"false")

### ⚠️ **Al Trabajar con Código PHP 5.6:**

1. **NO usar sintaxis moderna**: No usar `[]` arrays, usar `array()`
2. **NO usar type hints**: Evitar declaraciones de tipos en parámetros
3. **Validar JSON**: Usar `json_last_error()` después de `json_decode()`
4. **Testing cross-version**: Probar especialmente comunicación con msi-v5 (PHP 8.2)
5. **Encoding explícito**: Especificar UTF-8 en todas las comunicaciones HTTP

---

**Última actualización**: 2025-01-13  
**Versión del análisis**: 2.0  
**Alcance**: Sistema completo de Multibolsa Inmobiliaria con Control de Duplicación de Publicaciones  
**Consideraciones**: PHP 5.6 legacy system con integraciones modernas

**🆕 Cambios en v1.2** (2025-01-12):

- ✅ **Fix checkbox "Publicar en Feed"**: Validación de `usar_multibolsa` implementada
- ✅ **Fix preselección de socios**: Configuración se preserva desde BD
- ✅ **Conversión de tipos mejorada**: Boolean → String para compatibilidad HTML
- ✅ **Persistencia completa**: Todos los campos de multibolsa se guardan en BD

**🎯 Cambios MAYORES en v2.0** (2025-01-13):

- 🆕 **Sistema de Control de Duplicación**: Prevención completa de publicaciones duplicadas
- 🆕 **Campo BD `fecha_publicacion_feed`**: Registro automático de fecha/hora de publicación
- 🆕 **Aviso informativo con fecha**: Usuario ve cuándo fue la última publicación
- 🆕 **Botón "Volver a publicar"**: Control explícito para re-publicación consciente
- 🆕 **Estados reactivos JavaScript↔Vue**: Sincronización modo republicación
- 🆕 **Vue condicional**: Checkbox oculto hasta decisión explícita del usuario
- 🆕 **Inputs siempre visibles**: Edición de comisiones independiente del estado
- 🆕 **Formato de fecha en español**: Localización "dd/mmm/yyyy a las HH:mm"
- 🆕 **JavaScript robusto**: Manejo de errores y elementos dinámicos
- 🆕 **UX optimizada**: Mensajes claros y flujo intuitivo
- 🆕 **Documentación completa**: Análisis técnico profundo de toda la implementación

**📁 Archivos modificados en v2.0**:

- `MySQL/add_fecha_publicacion_feed.sql` (NUEVO)
- `bolsa_inmobiliaria.php` (control duplicación + formateo fecha)
- `bolsa_inmobiliaria.st.mustache` (aviso + botón + JavaScript)
- `InmuebleBolsaInmobiliaria.vue` (props + estados + visibilidad condicional)
- `index.js` (nuevos props de duplicación)
- `inmueble-paso-4.php` (persistencia fecha condicional)
- `BolsaInmobiliaria.DatosInternos.Claude.md` (documentación actualizada)
