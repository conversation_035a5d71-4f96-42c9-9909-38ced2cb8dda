<!-- Sidebar -->
<div
  id="sidebar"
  class="flex flex-col w-64 h-screen bg-white shadow-lg sidebar-transition"
>
  <header class="flex justify-between p-3 bg-mulbin-100">
    <div class="flex items-center">
      <ion-icon name="business-outline" class="mr-2 w-7"></ion-icon>
      <h2 class="font-bold text-mulbin-800">Sistema Inmobiliario</h2>
    </div>
    <button
      id="close-sidebar"
      class="hidden text-mulbin-500 hover:text-mulbin-700"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="w-6 h-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5l7 7-7 7"
        />
      </svg>
    </button>
  </header>

  <!-- Contenido del sidebar -->
  <div class="overflow-y-auto flex-1 p-4 sidebar-content">
    <!-- Componente Vue: Estadísticas de Inmuebles -->
    <div
      data-sidebar-card-inmuebles
      data-total-properties="{{ num_inmuebles }}"
      data-api-endpoint="/msi-v5/owner/inmuebles"
      data-placeholder="Ej: CASA123, DEPTO001"
      data-status="all"
      class="sidebar-card-inmuebles-container"
    >
      <!-- El componente Vue se montará aquí -->
      <div class="flex justify-center items-center p-8">
        <div
          class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
        ></div>
        <span class="ml-2 text-sm text-gray-600">Cargando inmuebles...</span>
      </div>
    </div>

    <!-- Resumen de Actividad -->
    <div class="space-y-4">
      {{# num_citas }}
      <!-- Citas esperando respuesta -->
      <div
        class="p-4 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg border border-yellow-200"
      >
        <div class="flex justify-between items-center mb-2">
          <h4 class="text-sm font-medium text-gray-700">
            Citas esperando respuesta
          </h4>
          <ion-icon
            name="calendar-outline"
            class="text-lg text-yellow-600"
          ></ion-icon>
        </div>
        <div class="flex justify-between items-baseline">
          <span class="text-2xl font-bold text-yellow-600"
            >{{ num_citas }}</span
          >
          <button
            class="text-xs text-yellow-700 underline hover:text-yellow-800"
            onclick="location.href='/vercitas.php'"
          >
            Ver todas
          </button>
        </div>
      </div>
      {{/ num_citas }} {{# num_preguntas }}
      <!-- Preguntas esperando respuesta -->
      <div
        class="p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200"
      >
        <div class="flex justify-between items-center mb-2">
          <h4 class="text-sm font-medium text-gray-700">
            Preguntas esperando respuesta
          </h4>
          <ion-icon
            name="help-circle-outline"
            class="text-lg text-green-600"
          ></ion-icon>
        </div>
        <div class="flex justify-between items-baseline">
          <span class="text-2xl font-bold text-green-600"
            >{{ num_preguntas }}</span
          >
          <button
            class="text-xs text-green-700 underline hover:text-green-800"
            onclick="location.href='/verpreguntas.php'"
          >
            Ver todas
          </button>
        </div>
      </div>
      {{/ num_preguntas }}
    </div>

    <!-- Acciones Rápidas -->
    <div class="pt-6 mt-6 border-t border-gray-200">
      <h3
        class="mb-4 text-sm font-semibold tracking-wide text-gray-700 uppercase"
      >
        Acciones Rápidas
      </h3>
      <div class="space-y-2">
        <button
          class="p-3 w-full text-sm font-medium text-left text-gray-700 bg-gray-50 rounded-lg transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          onclick="location.href='/inmueble.php'"
        >
          <div class="flex items-center">
            <ion-icon
              name="add-circle-outline"
              class="mr-3 text-lg text-blue-600"
            ></ion-icon>
            Nuevo inmueble
          </div>
        </button>
        <button
          class="p-3 w-full text-sm font-medium text-left text-gray-700 bg-gray-50 rounded-lg transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          onclick="location.href='/inmueble.php'"
        >
          <div class="flex items-center">
            <ion-icon
              name="search-outline"
              class="mr-3 text-lg text-green-600"
            ></ion-icon>
            Buscar inmuebles
          </div>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Script del componente Vue se carga dinámicamente -->
