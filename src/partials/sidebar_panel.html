<!-- Sidebar -->
<div
  id="sidebar"
  class="flex flex-col w-64 h-screen bg-white shadow-lg sidebar-transition"
>
  <header class="flex justify-between p-3 bg-mulbin-100">
    <div class="flex items-center">
      <div class="mr-2 w-7">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 150 150"
          fill="currentColor"
          preserveAspectRatio="xMidYMid meet"
        >
          <path
            d="M60.7 145.3c-0.4-0.3-0.7-3.4-0.7-6.8 0-5.2 0.4-6.6 2.8-9.5 2.7-3.2 3.2-3.4 11.3-4 8.1-0.5 8.6-0.7 15.2-5.5l6.7-5v-5.8c0-5.4 0.3-6.2 3.4-9.3 3.3-3.3 3.7-3.4 11-3.4 13 0 16.9 3.7 16.4 15.8l-0.3 6.7-15.5 0.5-15.5 0.5-4.5 3.5c-2.5 1.9-4.7 3.6-4.9 3.7-0.2 0.1 0.4 1 1.4 1.9 0.9 0.9 2.1 2.8 2.6 4.1 1.3 3.4 1.1 10.7-0.3 12.1-1.3 1.3-27.9 1.8-29.1 0.5z"
          ></path>
          <path
            d="M16.6 129.4c-0.3-0.9-0.6-5.8-0.6-11 0-8.8-0.1-9.4-2.1-9.4-3.7 0-1.6-2.8 10-13.5 8.9-8.1 11.8-10.2 13-9.5 0.9 0.5 6.6 5.7 12.6 11.4 8.5 8.2 10.7 10.7 9.6 11.5-0.8 0.5-2 0.7-2.8 0.4-1-0.4-1.3 1.7-1.3 10.6v11.1h-18.9c-16.3 0-19-0.2-19.5-1.6z"
          ></path>
          <path
            d="M69.8 119c-2.5-2.6-2.9-3.6-2.4-6.2 0.3-1.8 1.3-3.9 2.1-4.8 1.1-1.3 1.5-4.3 1.5-12.4 0-9.3-0.2-10.7-1.7-11.3-3.4-1.2-5.3-3.7-6-7.5-0.5-3.7-1.3-4.4-10.7-10.6l-10.1-6.7-11.5-0.5-11.5-0.5-0.3-7c-0.4-12 4.2-16.9 16.2-17 8.1 0 11.4 1.5 14.6 6.8 2.2 3.5 2.8 15.5 1 17.3-0.8 0.8 1.3 2.6 6.6 6.2 7.4 4.8 7.8 5 10.1 3.5 1.6-1 3.6-1.4 6.3-1 3.6 0.5 4.2 0.2 8.9-4.8l5.1-5.3v-14c0-13.5-0.1-14-2-13.5-3.2 0.8-2.5-2.4 1.2-5.5 1.8-1.5 7.7-6.6 13.1-11.5 5.5-4.8 10.6-8.7 11.3-8.7 2 0 28.5 23.1 28.1 24.4-0.2 0.6-1.4 1.1-2.6 1-2.1-0.1-2.1 0.3-2.1 14.2 0 17 0.4 16.4-12.4 16.4h-8.6v8.3c0 7.6 0.2 8.6 2.5 10.7 5 4.7 1.6 14-5 14-3 0-8.5-4.9-8.5-7.5 0-2.7 2.9-7.3 5-8 1.8-0.6 2-1.5 2-9.1v-8.4h-9.3l-9.3 0-5.3 5.4c-4.6 4.6-5.3 5.9-4.8 8.3 0.7 3.7-1.6 8.9-4.2 9.8-2 0.6-2.1 1.4-2.1 11.1 0 9.1 0.2 10.5 1.8 11.1 7.2 2.6 8.5 11.5 2.1 14.8-4.1 2.2-5.7 1.9-9.1-1.5z"
          ></path>
          <path
            d="M104 41v-10.4c0-4.6 1.5-7.1 5-7.1 3.5 0 5 2.5 5 7.1v10.4h-10z"
          ></path>
          <path
            d="M32.3 30.5c-3.3-1.4-4.3-3.2-4.3-7.6 0-6.3 8.6-9.4 13-4.7 5.8 6.2-0.8 15.6-8.7 12.3z"
          ></path>
        </svg>
      </div>
      <h2 class="font-bold text-mulbin-800">Multibolsa Inmobiliaria</h2>
    </div>
    <button
      id="close-sidebar"
      class="hidden text-mulbin-500 hover:text-mulbin-700"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="w-6 h-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5l7 7-7 7"
        />
      </svg>
    </button>
  </header>

  <!-- Contenido del sidebar -->
  <div class="overflow-y-auto flex-1 p-4 sidebar-content">
    <div data-sidebar-multibolsa class="space-y-5">
      <!-- Placeholder mientras se monta Vue -->
      <div class="animate-pulse space-y-3">
        <div class="h-24 rounded-xl bg-gray-100"></div>
        <div class="grid grid-cols-2 gap-3">
          <div class="h-20 rounded-xl bg-gray-100"></div>
          <div class="h-20 rounded-xl bg-gray-100"></div>
        </div>
        <div class="h-32 rounded-xl bg-gray-100"></div>
      </div>
    </div>
  </div>
</div>
