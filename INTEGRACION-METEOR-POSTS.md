# Integración Meteor - Posts Inmobiliarios

## 📋 Descripción General

Esta integración permite que cuando se guarde un inmueble en el sistema padre (paso 4 de `inmueble.php`), automáticamente se cree o actualice un post correspondiente en la red social inmobiliaria de Meteor.

## 🔄 Flujo de Integración

### 1. **Trigger**: Guardado de inmueble (Paso 4)
- Se ejecuta en `panel4-templates/src/entries/inmueble.php` línea ~433
- Se activa después de obtener los datos del inmueble desde la API interna
- Utiliza los datos del formulario (`$_POST`) y del objeto `$apiInmueble`

### 2. **Validaciones**
- ✅ Verificar que existe `author_id` (meteor_user_id)
- ✅ Validar datos básicos del inmueble
- ✅ Verificar que tiene al menos un precio válido
- ✅ Comprobar que se comparte comisión (condición para publicar)

### 3. **Decisión: Crear vs Actualizar**
- Busca si ya existe un post con el mismo `externalId`
- Si existe: **actualiza** el post
- Si no existe: **crea** nuevo post

### 4. **Mapeo de Datos**

#### Del Sistema Padre → Meteor:
```php
// Datos básicos
'type' => 'venta' | 'renta'          // Basado en disponibilidad
'title' => "Casa en Venta - Colonia, Ciudad"
'description' => "Descripción limpia del HTML"
'price' => 3000000                    // precio_venta o precio_renta
'location' => 'centro'                // Mapeo de ciudad → zona

// Detalles de la propiedad
'propertyType' => 'casa'              // Basado en categorías
'bedrooms' => 3                       // Del campo ci_recamaras
'bathrooms' => 2.5                    // Del campo ci_banos (con ½)
'area' => 500.0                       // Del campo ci_construccion
'landArea' => 750.0                   // Del campo ci_terreno

// Características extraídas
'features' => ['alberca', 'estacionamiento', 'gas_estacionario']

// Metadatos
'authorId' => "meteor_user_id"        // Del formulario
'externalId' => "131351"              // ID del sistema padre
'externalKey' => "bbb"                // clave_interna
'currency' => "MXP"                   // Moneda
```

### 5. **Condiciones para Publicar**
- **Solo se publican inmuebles que comparten comisión**:
  - `comparto_comision > 0` (venta), O
  - `rta_comparto_comision > 0` (renta)
- Si no comparte: se registra en log pero no se crea post

## 🏗️ Arquitectura

### Clases Implementadas

#### `MeteorPost` (`panel4/clases/MeteorPost.class.php`)
- **Constructor**: Inicializa conexión con API de Meteor
- **`createPost()`**: Crea nuevo post inmobiliario
- **`updatePost()`**: Actualiza post existente
- **`findPostByExternalId()`**: Busca post por ID del sistema padre
- **`validatePostData()`**: Valida datos antes de enviar
- **`preparePostData()`**: Mapea datos del sistema padre → Meteor

### Métodos de Mapeo
- **`extractCustomField()`**: Extrae valores de campos personalizados
- **`generateTitle()`**: Crea títulos atractivos
- **`generateDescription()`**: Limpia HTML y genera descripción
- **`mapLocationToZone()`**: Mapea ciudades a zonas de Meteor
- **`extractFeatures()`**: Identifica características del inmueble

## 🔧 Configuración

### Variables de Entorno Requeridas
```bash
# En el sistema padre
API_URL_METEOR=http://localhost:3000/api
API_KEY_METEOR=c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

### Dependencias
- Clase `User` existente para manejo de API de Meteor
- API interna del sistema padre para obtener datos del inmueble
- Token de autorización válido para la API interna

## 📊 Ejemplo de Uso

### Datos de Entrada (Ejemplo)
```php
// $apiInmueble (desde API interna)
{
    "id": 131351,
    "clave_interna": "bbb",
    "ubicacion": {
        "colonia": "Adolfo Ruiz Cortines",
        "ciudad": "Cuernavaca"
    },
    "precios": {
        "venta": 3000000,
        "renta": 0
    },
    "disponibilidad": {
        "venta": true,
        "renta": false
    },
    "campos_personalizados": [
        {"variable": "ci_recamaras", "valor": "3"},
        {"variable": "ci_banos", "valor": "4 ½"},
        {"variable": "ci_construccion", "valor": "500.00 m²"}
    ]
}

// $_POST (desde formulario)
{
    "author_id": "meteor_user_abc123",
    "comparto_comision": "3.5",
    "t_comparto_comision": "sobre precio"
}
```

### Post Resultante en Meteor
```json
{
    "type": "venta",
    "title": "Casa en Venta - Adolfo Ruiz Cortines, Cuernavaca",
    "description": "Increíble oportunidad en Adolfo Ruiz Cortines, Cuernavaca...",
    "price": 3000000,
    "location": "centro",
    "propertyType": "casa",
    "bedrooms": 3,
    "bathrooms": 4.5,
    "area": 500.0,
    "features": [],
    "authorId": "meteor_user_abc123",
    "externalId": "131351",
    "externalKey": "bbb",
    "currency": "MXP",
    "commissionData": {
        "venta": {
            "porcentaje": 3.5,
            "tipo": "sobre precio"
        }
    }
}
```

## 📋 Logs y Monitoreo

### Logs Generados
- **🔄 Inicio**: `"Iniciando integración con Meteor: {datos}"`
- **✅ Éxito**: `"Post creado/actualizado exitosamente - Inmueble: X, Post: Y"`
- **ℹ️ Skip**: `"Post no creado - No se comparte comisión"`
- **⚠️ Error**: `"Error al crear/actualizar post: {detalle}"`
- **❌ Excepción**: `"Excepción en integración Meteor: {error}"`

### Datos Loggeados
```json
{
    "inmueble_id": 131351,
    "clave": "bbb",
    "ciudad": "Cuernavaca",
    "precio_venta": 3000000,
    "precio_renta": 0,
    "comparte_venta": true,
    "comparte_renta": false,
    "author_id": "meteor_user_abc123"
}
```

## 🛡️ Manejo de Errores

### Estrategia Resiliente
- **Errores NO interrumpen** el flujo normal del sistema padre
- Todos los errores se loggean detalladamente
- El inmueble se guarda normalmente aunque falle Meteor
- Validaciones previas evitan requests inválidos

### Tipos de Error
1. **Validación**: Datos insuficientes o incorrectos
2. **Conexión**: Error de red con Meteor
3. **API**: Error en la respuesta de Meteor
4. **Parsing**: Error al procesar respuesta JSON

## 🔄 Actualizaciones Futuras

### Posibles Mejoras
1. **Imágenes**: Integrar URLs de fotos del inmueble
2. **Geolocalización**: Agregar coordenadas lat/lng
3. **Cache**: Almacenar `meteor_post_id` en BD local
4. **Retry**: Reintentos automáticos en caso de fallo
5. **Webhook**: Notificaciones bidireccionales
6. **Batch**: Procesamiento masivo de inmuebles existentes

### Campos Opcionales por Implementar
- `images[]`: URLs de fotografías
- `virtualTour`: Link a tour virtual
- `coordinates`: {lat, lng}
- `amenities`: Amenidades del desarrollo
- `financing`: Opciones de financiamiento

## 🧪 Testing

### Pruebas Recomendadas
1. **Inmueble nuevo sin comisión**: Verificar que no se crea post
2. **Inmueble nuevo con comisión**: Verificar creación exitosa
3. **Actualización de inmueble**: Verificar update del post
4. **Error de red**: Verificar que no interrumpe el flujo
5. **Datos incompletos**: Verificar validaciones

### Comando de Prueba
```bash
# Ver logs en tiempo real
tail -f /var/log/php_errors.log | grep "Meteor"
```

---

## 🎯 Resumen

Esta integración crea una **sincronización automática y resiliente** entre el sistema padre y la red social inmobiliaria de Meteor, asegurando que:

- ✅ Los inmuebles con comisión compartida se publican automáticamente
- ✅ Los datos se mapean correctamente entre sistemas
- ✅ Los errores no afectan el funcionamiento del sistema padre
- ✅ Se mantiene trazabilidad completa mediante logs detallados
- ✅ Soporta tanto creación como actualización de posts

La integración está **lista para producción** y es **fácilmente extensible** para futuras mejoras. 