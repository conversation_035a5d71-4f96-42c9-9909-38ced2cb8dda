/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./*.html",
    "./src/**/*.{js,ts,jsx,tsx,html,htm,css,json,php,vue,mustache}",
  ],
  theme: {
    extend: {
      screens: {
        xs: "480px",
      },
      colors: {
        mulbin: {
          10: "#f0ffff",
          25: "#f0f8ff",
          50: "#e6f3fa",
          100: "#cce7f5",
          200: "#99cfeb",
          300: "#66b7e1",
          400: "#339fd7",
          500: "#0077c9", // Color base
          600: "#0069b3",
          700: "#004f86",
          800: "#003559",
          900: "#001a2d",
        },
        "color-1": "#0077c9 !important",
        "color-2": "#e6e6ff !important",
        "color-3": "#569ed0 !important",
        "color-4": "#c9dfee !important",
        "color-5": "#b0b0b0 !important",
        "color-6": "#7c7c7c !important",
        "color-7": "#ffff99 !important",
        "color-8": "#f4f4fa !important",
        "color-9": "#daf8d6 !important",
      },
      fontSize: {
        8: "0.8rem",
        9: "0.9rem",
        10: "1rem",
        11: "1.1rem",
        12: "1.2rem",
        13: "1.3rem",
        14: "1.4rem",
        15: "1.5rem",
        16: "1.6rem",
        17: "1.7rem",
        18: "1.8rem",
        19: "1.9rem",
        20: "2rem",
      },
      fontFamily: {
        monospace: ["monospace", "sans-serif"],
      },
      padding: {
        "025": "0.25rem",
        "05": "0.5rem",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0", transform: "translateY(-10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        fadeOut: {
          "0%": { opacity: "1", transform: "translateY(0)" },
          "100%": { opacity: "0", transform: "translateY(-10px)" },
        },
      },
      animation: {
        "fade-in": "fadeIn 0.3s ease-in-out forwards",
        "fade-out": "fadeOut 0.3s ease-in-out forwards",
      },
    },
  },
  plugins: [],
};
