{"name": "test-202505121947", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "NODE_ENV=development vite build --mode development", "prod": "NODE_ENV=production vite build --mode production", "pre-prod": "NODE_ENV=pre-production vite build --mode pre-production", "preview": "vite preview"}, "devDependencies": {"@types/node": "^22.15.21", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "terser": "^5.39.2", "vite": "^6.3.5", "vite-plugin-ssinc": "^1.0.11"}, "dependencies": {"@alpinejs/mask": "^3.14.9", "@floating-ui/vue": "^1.1.6", "@vitejs/plugin-vue": "^5.2.4", "alpinejs": "^3.14.9", "axios": "^1.6.2", "ionicons": "^8.0.8", "isomorphic-ws": "^5.0.0", "jquery": "^3.7.1", "marked": "^15.0.11", "photoswipe": "^5.4.4", "simpleddp": "^2.2.4", "simpleddp-plugin-login": "^4.0.2", "tailwindcss": "^3.3.3", "typescript": "^5.8.3", "vue": "^3.5.14", "vue-tsc": "^2.2.10", "ws": "^8.18.2"}}