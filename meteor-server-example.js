// Este archivo es un ejemplo para implementar en tu proyecto Meteor

import { Meteor } from "meteor/meteor";
import { Mongo } from "meteor/mongo";
import { check } from "meteor/check";

// Definición de colecciones
export const PostsInmobiliarios = new Mongo.Collection("postsInmobiliarios");
export const ComentariosPost = new Mongo.Collection("comentariosPost");
export const Notifications = new Mongo.Collection("notifications");

// Configurar índices si es necesario
if (Meteor.isServer) {
  Meteor.startup(() => {
    // Índices para mejorar el rendimiento de las consultas
    PostsInmobiliarios.createIndex({ type: 1 });
    PostsInmobiliarios.createIndex({ location: 1 });
    PostsInmobiliarios.createIndex({ price: 1 });
    PostsInmobiliarios.createIndex({ createdAt: -1 });

    ComentariosPost.createIndex({ postId: 1 });

    // Índices para notificaciones
    Notifications.createIndex({ userId: 1 });
    Notifications.createIndex({ read: 1 });
    Notifications.createIndex({ createdAt: -1 });

    console.log("🚀 Server is running!");
  });
}

// Publicaciones
if (Meteor.isServer) {
  // Publicación principal de posts inmobiliarios con filtros y paginación
  Meteor.publish(
    "postsInmobiliarios",
    function (filtros = {}, page = 1, limit = 5) {
      check(page, Number);
      check(limit, Number);

      const skip = (page - 1) * limit;
      const query = {};

      // Aplicar filtros si existen
      if (filtros.type) {
        check(filtros.type, String);
        query.type = filtros.type;
      }

      if (filtros.location) {
        check(filtros.location, String);
        query.location = filtros.location;
      }

      if (filtros.maxPrice && typeof filtros.maxPrice === "number") {
        check(filtros.maxPrice, Number);
        query.price = { $lte: filtros.maxPrice };
      }

      return PostsInmobiliarios.find(query, {
        skip,
        limit,
        sort: { createdAt: -1 },
      });
    }
  );

  // Publicación de comentarios para un post específico
  Meteor.publish("comentariosPost", function (postId) {
    // Validación más robusta
    try {
      check(postId, String);

      // Verificar que no sea un string vacío
      if (!postId.trim()) {
        throw new Meteor.Error(
          "invalid-postId",
          "El postId no puede estar vacío"
        );
      }

      return ComentariosPost.find({ postId });
    } catch (error) {
      console.error("Error en publicación comentariosPost:", error);
      // Si hay error de validación, devolver una publicación vacía
      this.error(
        new Meteor.Error(
          "validation-error",
          `Parámetro postId inválido: ${typeof postId} - ${postId}`
        )
      );
    }
  });

  //   // Publicación de notificaciones para el usuario actual
  //   Meteor.publish("userNotifications", function (limit = 20) {
  //     if (!this.userId) {
  //       return this.ready();
  //     }

  //     check(limit, Number);

  //     return Notifications.find(
  //       { userId: this.userId },
  //       {
  //         sort: { createdAt: -1 },
  //         limit: limit,
  //       }
  //     );
  //   });
  // }
  // Publicación de notificaciones para el usuario actual
  Meteor.publish("userNotifications", function (limit = 20) {
    check(limit, Number);

    return Notifications.find(
      {},
      {
        sort: { createdAt: -1 },
        limit: limit,
      }
    );
  });
}

// Métodos
Meteor.methods({
  // Crear un nuevo post inmobiliario
  crearPostInmobiliario(post) {
    // Verificar autenticación
    if (!this.userId) {
      throw new Meteor.Error(
        "not-authorized",
        "Debes iniciar sesión para publicar"
      );
    }

    // Validar datos
    check(post.type, String);
    check(post.title, String);
    check(post.description, String);
    check(post.price, Number);
    check(post.location, String);

    // Obtener datos del usuario
    const user = Meteor.users.findOne(this.userId);

    // Generar un ID único para el post
    const postId = `post-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Crear el post
    const result = PostsInmobiliarios.insertAsync({
      ...post,
      _id: postId, // Usar el ID generado
      author: {
        id: this.userId,
        name: user.profile?.name || "Usuario",
        avatar: user.profile?.avatar || "/default-avatar.png",
      },
      createdAt: new Date(),
      interestedCount: 0,
      commentsCount: 0,
    });

    console.log(`Post creado con ID: ${postId}`);
    return result;
  },

  // Marcar/desmarcar interés en un post
  toggleInteresadoPost(postId) {
    // Verificar autenticación
    if (!this.userId) {
      throw new Meteor.Error(
        "not-authorized",
        "Debes iniciar sesión para marcar interés"
      );
    }

    check(postId, String);

    // Verificar si ya está interesado
    const interesCollection = new Mongo.Collection("interesadosPost");
    const yaInteresado = interesCollection.findOne({
      postId,
      userId: this.userId,
    });

    if (yaInteresado) {
      // Eliminar interés
      interesCollection.removeAsync(yaInteresado._id);
      // Decrementar contador
      PostsInmobiliarios.updateAsync(postId, {
        $inc: { interestedCount: -1 },
      });
      return false;
    } else {
      // Agregar interés
      interesCollection.insertAsync({
        postId,
        userId: this.userId,
        createdAt: new Date(),
      });
      // Incrementar contador
      PostsInmobiliarios.updateAsync(postId, {
        $inc: { interestedCount: 1 },
      });
      return true;
    }
  },

  // Agregar comentario a un post
  agregarComentarioPost(postId, text) {
    // Verificar autenticación
    if (!this.userId) {
      throw new Meteor.Error(
        "not-authorized",
        "Debes iniciar sesión para comentar"
      );
    }

    check(postId, String);
    check(text, String);

    // Validar que existe el post
    const post = PostsInmobiliarios.findOne(postId);
    if (!post) {
      throw new Meteor.Error("post-not-found", "La publicación no existe");
    }

    // Obtener datos del usuario
    const user = Meteor.users.findOne(this.userId);

    // Crear comentario
    const comentarioId = ComentariosPost.insertAsync({
      postId,
      author: {
        id: this.userId,
        name: user.profile?.name || "Usuario",
        avatar: user.profile?.avatar || "/default-avatar.png",
      },
      text,
      createdAt: new Date(),
    });

    // Actualizar contador de comentarios
    PostsInmobiliarios.updateAsync(postId, {
      $inc: { commentsCount: 1 },
    });

    return comentarioId;
  },

  // Obtener el total de posts (para paginación)
  async getTotalPostsCount(filtros = {}) {
    const query = {};

    // Aplicar filtros si existen
    if (filtros.type) {
      check(filtros.type, String);
      query.type = filtros.type;
    }

    if (filtros.location) {
      check(filtros.location, String);
      query.location = filtros.location;
    }

    if (filtros.maxPrice && typeof filtros.maxPrice === "number") {
      check(filtros.maxPrice, Number);
      query.price = { $lte: filtros.maxPrice };
    }

    return await PostsInmobiliarios.find(query).countAsync();
  },

  // Métodos para notificaciones

  // Marcar notificación como leída
  async markNotificationAsRead(notificationId) {
    check(notificationId, String);

    if (!this.userId) {
      throw new Meteor.Error("not-authorized", "Debes iniciar sesión");
    }

    const notification = Notifications.findOne({
      _id: notificationId,
      userId: this.userId,
    });

    if (!notification) {
      throw new Meteor.Error("not-found", "Notificación no encontrada");
    }

    await Notifications.updateAsync(
      { _id: notificationId },
      { $set: { read: true } }
    );

    return true;
  },

  // Marcar todas las notificaciones como leídas
  async markAllNotificationsAsRead() {
    if (!this.userId) {
      throw new Meteor.Error("not-authorized", "Debes iniciar sesión");
    }

    await Notifications.updateAsync(
      { userId: this.userId, read: false },
      { $set: { read: true } },
      { multi: true }
    );

    return true;
  },

  // Eliminar notificación
  async deleteNotification(notificationId) {
    check(notificationId, String);

    if (!this.userId) {
      throw new Meteor.Error("not-authorized", "Debes iniciar sesión");
    }

    const notification = Notifications.findOne({
      _id: notificationId,
      userId: this.userId,
    });

    if (!notification) {
      throw new Meteor.Error("not-found", "Notificación no encontrada");
    }

    await Notifications.removeAsync({ _id: notificationId });

    return true;
  },

  // Obtener conteo de notificaciones no leídas
  async getUnreadNotificationsCount() {
    if (!this.userId) {
      return 0;
    }

    return await Notifications.find({
      userId: this.userId,
      read: false,
    }).countAsync();
  },

  // Generar datos de ejemplo para notificaciones
  async generarNotificacionesEjemplo(cantidad = 5) {
    // Verificar autenticación (o permitir en desarrollo)
    if (!this.userId && Meteor.isProduction) {
      throw new Meteor.Error(
        "not-authorized",
        "Debes iniciar sesión para generar datos de ejemplo"
      );
    }

    const userId = this.userId || "usuario-demo";

    // Datos de ejemplo para notificaciones
    const tiposNotificacion = ["info", "success", "warning", "error"];

    const titulos = [
      "Nueva propiedad disponible",
      "Comentario en tu publicación",
      "Actualización del sistema",
      "Te han enviado un mensaje",
      "Recordatorio de cita",
      "Pago realizado con éxito",
      "Aviso importante",
      "Promoción especial",
    ];

    const mensajes = [
      "Se ha agregado una nueva propiedad que coincide con tus criterios de búsqueda.",
      "Alguien ha comentado en tu publicación de propiedad en venta.",
      "El sistema estará en mantenimiento el próximo domingo.",
      "Has recibido un nuevo mensaje de un interesado en tu propiedad.",
      "Tienes una cita programada para mañana a las 10:00 AM.",
      "Tu pago mensual ha sido procesado correctamente.",
      "Es necesario actualizar tus datos personales para continuar usando el servicio.",
      "Aprovecha el 20% de descuento en nuestros servicios premium por tiempo limitado.",
    ];

    const iconos = [
      "home",
      "chatbubble",
      "warning",
      "mail",
      "calendar",
      "cash",
      "alert",
      "pricetag",
    ];

    // Limpiar notificaciones anteriores del usuario
    await Notifications.removeAsync({ userId });

    // Crear nuevas notificaciones
    const notificaciones = [];

    for (let i = 0; i < cantidad; i++) {
      const tipo =
        tiposNotificacion[Math.floor(Math.random() * tiposNotificacion.length)];
      const tituloIndex = Math.floor(Math.random() * titulos.length);
      const titulo = titulos[tituloIndex];
      const mensaje = mensajes[tituloIndex];
      const iconName = iconos[tituloIndex];

      // Determinar si estará leída o no (70% no leídas para el ejemplo)
      const read = Math.random() > 0.7;

      // Crear fecha aleatoria en los últimos 7 días
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - Math.floor(Math.random() * 7));

      const notificationId = await Notifications.insertAsync({
        _id: `notif-${Date.now()}-${i}`,
        userId,
        title: titulo,
        message: mensaje,
        type: tipo,
        read,
        createdAt,
        iconName,
        source: "demo",
      });

      notificaciones.push(notificationId);
    }

    return {
      success: true,
      message: `Se han creado ${cantidad} notificaciones de ejemplo.`,
      notificaciones,
    };
  },

  // Método para limpiar datos de notificaciones de ejemplo
  async limpiarNotificacionesEjemplo() {
    // Verificar autenticación (o permitir en desarrollo)
    if (!this.userId && Meteor.isProduction) {
      throw new Meteor.Error(
        "not-authorized",
        "Debes iniciar sesión para limpiar datos"
      );
    }

    const userId = this.userId || "usuario-demo";

    const count = await Notifications.find({
      userId,
      source: "demo",
    }).countAsync();

    await Notifications.removeAsync({ userId, source: "demo" });

    return {
      success: true,
      message: `Se han eliminado ${count} notificaciones de ejemplo.`,
    };
  },

  // Método para generar datos de prueba
  async generarDatosEjemplo(cantidad = 10) {
    // Solo permitir en desarrollo o si es administrador
    if (!this.userId && Meteor.isProduction) {
      throw new Meteor.Error(
        "not-authorized",
        "Solo administradores pueden generar datos de ejemplo en producción"
      );
    }

    // Verificar si ya hay datos
    const countActual = await PostsInmobiliarios.find().countAsync();
    if (countActual > 0) {
      throw new Meteor.Error(
        "data-exists",
        `Ya existen ${countActual} posts. Borra los datos existentes primero.`
      );
    }

    // Datos de ejemplo
    const tipos = ["venta", "renta", "socio", "intercambio"];
    const ubicaciones = ["norte", "sur", "este", "oeste", "centro"];

    const titulos = [
      "Casa con amplio jardín en zona residencial",
      "Departamento de lujo con vista panorámica",
      "Local comercial en plaza principal",
      "Terreno para desarrollo habitacional",
      "Oficina ejecutiva en zona financiera",
      "Nave industrial con andenes de carga",
      "Casa de campo con alberca",
      "Penthouse de lujo en torre exclusiva",
      "Edificio para remodelación en zona histórica",
      "Terreno con vista al mar",
    ];

    const descripciones = [
      "Excelente propiedad ubicada en zona de alta plusvalía, ideal para familias.",
      "Oportunidad de inversión única, con alta rentabilidad garantizada.",
      "Inmueble totalmente remodelado con acabados de lujo y tecnología de punta.",
      "Ubicación estratégica con fácil acceso a vías principales y transporte público.",
      "Excelentes acabados, distribución óptima y áreas verdes para su comodidad.",
      "Ideal para inversionistas que buscan diversificar su portafolio inmobiliario.",
    ];

    const usuariosDemo = [
      {
        _id: "usuario1",
        profile: {
          name: "Carlos Mendoza",
          avatar: "https://randomuser.me/api/portraits/men/34.jpg",
        },
      },
      {
        _id: "usuario2",
        profile: {
          name: "Ana Martínez",
          avatar: "https://randomuser.me/api/portraits/women/68.jpg",
        },
      },
      {
        _id: "usuario3",
        profile: {
          name: "Roberto Sánchez",
          avatar: "https://randomuser.me/api/portraits/men/45.jpg",
        },
      },
    ];

    const imagenesEjemplo = [
      "https://www.savbienesraices.com/photos/propiedades/74/alta/0f1c83107a647e0fc0339531132c638047fbabd7.JPG",
      "https://www.savbienesraices.com/photos/propiedades/74/alta/791f5bc59681b3080893e9c06525af25c65cd567.JPG",
      "https://www.savbienesraices.com/photos/propiedades/74/alta/cb0e9ad41442aad311f6ed2c468d4f8102595bf1.JPG",
      "https://www.savbienesraices.com/photos/propiedades/74/alta/3c0ed76f37da073d53b69b5965dc833168050b95.JPG",
      "https://www.savbienesraices.com/photos/propiedades/74/alta/d218473333a03c0a04f1328e17445f5a6f5c7983.JPG",
    ];

    // Insertamos posts
    const postIds = [];
    for (let i = 0; i < cantidad; i++) {
      const tipo = tipos[Math.floor(Math.random() * tipos.length)];
      const ubicacion =
        ubicaciones[Math.floor(Math.random() * ubicaciones.length)];
      const titulo = titulos[Math.floor(Math.random() * titulos.length)];
      const descripcion =
        descripciones[Math.floor(Math.random() * descripciones.length)];
      const usuario =
        usuariosDemo[Math.floor(Math.random() * usuariosDemo.length)];
      const precio = Math.floor(Math.random() * 9000000) + 1000000;
      const numImagenes = Math.floor(Math.random() * 4) + 1;

      // Seleccionamos algunas imágenes aleatorias
      const imagenes = [];
      for (let j = 0; j < numImagenes; j++) {
        const img =
          imagenesEjemplo[Math.floor(Math.random() * imagenesEjemplo.length)];
        if (!imagenes.includes(img)) {
          imagenes.push(img);
        }
      }

      // Datos opcionales para algunos posts
      const tieneDetalles = Math.random() > 0.3;
      const detallesOpcionales = tieneDetalles
        ? {
            bedrooms: Math.floor(Math.random() * 5) + 1,
            bathrooms: Math.floor(Math.random() * 3) + 1,
            area: Math.floor(Math.random() * 500) + 50,
          }
        : {};

      // Creamos una fecha aleatoria en los últimos 30 días
      const fechaCreacion = new Date();
      fechaCreacion.setDate(
        fechaCreacion.getDate() - Math.floor(Math.random() * 30)
      );

      const postId = await PostsInmobiliarios.insertAsync({
        _id: `post-${i + 1}`,
        type: tipo,
        title: `${titulo} #${i + 1}`,
        description: descripcion,
        price: precio,
        location: ubicacion,
        createdAt: fechaCreacion,
        author: {
          id: usuario._id,
          name: usuario.profile.name,
          avatar: usuario.profile.avatar,
        },
        images: imagenes,
        interestedCount: Math.floor(Math.random() * 20),
        commentsCount: 0,
        ...detallesOpcionales,
      });

      postIds.push(postId);
    }

    // Insertamos comentarios
    const comentarios = [
      "¿Aceptan crédito hipotecario?",
      "Me interesa, ¿podemos agendar una visita?",
      "¿El precio es negociable?",
      "¿Cuántos metros cuadrados de terreno son?",
      "Excelente ubicación, ¿tienen más propiedades similares?",
      "¿Incluye estacionamiento?",
      "¿Cuánto es el pago de mantenimiento?",
      "¿La cocina incluye electrodomésticos?",
      "¿Tiene alguna reforma reciente?",
      "Me encanta, quiero más información",
    ];

    let totalComentarios = 0;

    // Agregamos de 0 a 5 comentarios por post
    postIds.forEach((postId) => {
      const numComentarios = Math.floor(Math.random() * 6);

      for (let i = 0; i < numComentarios; i++) {
        const usuario =
          usuariosDemo[Math.floor(Math.random() * usuariosDemo.length)];
        const comentario =
          comentarios[Math.floor(Math.random() * comentarios.length)];

        // Creamos una fecha aleatoria en los últimos 7 días
        const fechaComentario = new Date();
        fechaComentario.setDate(
          fechaComentario.getDate() - Math.floor(Math.random() * 7)
        );

        ComentariosPost.insertAsync({
          postId,
          author: {
            id: usuario._id,
            name: usuario.profile.name,
            avatar: usuario.profile.avatar,
          },
          text: comentario,
          createdAt: fechaComentario,
        });

        totalComentarios++;
      }

      // Actualizamos el contador de comentarios del post
      if (numComentarios > 0) {
        PostsInmobiliarios.updateAsync(postId, {
          $set: { commentsCount: numComentarios },
        });
      }
    });

    return {
      success: true,
      message: `Se han creado ${cantidad} posts inmobiliarios y ${totalComentarios} comentarios de ejemplo.`,
    };
  },

  // Método para limpiar datos de ejemplo
  async limpiarDatosEjemplo() {
    // Solo permitir en desarrollo o si es administrador
    if (!this.userId && Meteor.isProduction) {
      throw new Meteor.Error(
        "not-authorized",
        "Solo administradores pueden limpiar datos en producción"
      );
    }

    const countPosts = await PostsInmobiliarios.find().countAsync();
    const countComentarios = await ComentariosPost.find().countAsync();

    // Eliminar todos los registros
    PostsInmobiliarios.removeAsync({});
    ComentariosPost.removeAsync({});

    return {
      success: true,
      message: `Se han eliminado ${countPosts} posts y ${countComentarios} comentarios.`,
    };
  },
});

// Configuración de seguridad
// Reemplazar esto con reglas reales de seguridad según tu caso de uso
PostsInmobiliarios.allow({
  insert: function (userId, doc) {
    return userId && doc.author.id === userId;
  },
  update: function (userId, doc) {
    return userId && doc.author.id === userId;
  },
  remove: function (userId, doc) {
    return userId && doc.author.id === userId;
  },
});

ComentariosPost.allow({
  insert: function (userId, doc) {
    return userId && doc.author.id === userId;
  },
  update: function (userId, doc) {
    return userId && doc.author.id === userId;
  },
  remove: function (userId, doc) {
    return userId && doc.author.id === userId;
  },
});

Notifications.allow({
  insert: function (userId, doc) {
    return userId && doc.userId === userId;
  },
  update: function (userId, doc) {
    return userId && doc.userId === userId;
  },
  remove: function (userId, doc) {
    return userId && doc.userId === userId;
  },
});
