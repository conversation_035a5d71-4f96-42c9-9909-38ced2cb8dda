# Guía para Creación de Componentes Vue en Panel4

## 📋 Índice

1. [Contexto del Sistema](#contexto-del-sistema)
2. [An<PERSON> de Empezar](#antes-de-empezar)
3. [Estructura de Archivos](#estructura-de-archivos)
4. [Proceso Paso a Paso](#proceso-paso-a-paso)
5. [Configuración de Vite](#configuración-de-vite)
6. [Integración en Panel4](#integración-en-panel4)
7. [Errores Comunes y Soluciones](#errores-comunes-y-soluciones)
8. [Testing y Debugging](#testing-y-debugging)
9. [Checklist Final](#checklist-final)

---

## 🏗️ Contexto del Sistema

### Arquitectura Actual

- **Panel4**: PHP (Orquestador principal) - Puerto 8020
- **panel4-templates**: Vue.js + TypeScript + Vite (Componentes compilados)
- **Integración**: Componentes Vue se compilan y se integran en templates PHP via `data-attributes`

### Patrón de Integración

```mermaid
graph TD
    A[Template PHP] -->|data-attributes| B[Elemento DOM]
    B -->|index.js| C[Vue App]
    C -->|createApp| D[Componente Vue]
    D -->|Events| E[DOM padre]
```

---

## ⚠️ Antes de Empezar

### ✅ Pre-requisitos

- [ ] Node.js y npm instalados
- [ ] Docker ejecutándose (para Panel4)
- [ ] Servicios de desarrollo activos:
  ```bash
  cd website2025/ && docker-compose ps
  # panel4 debe estar en puerto 8020
  ```

### 🎯 Decisiones Clave

1. **¿Es un componente nuevo o extracción?**

   - **Nuevo**: Crear desde cero
   - **Extracción**: Identificar HTML/JS a modularizar

2. **¿Necesita comunicación con el DOM padre?**

   - **Sí**: Planificar eventos y props
   - **No**: Componente autocontenido

3. **¿Requiere API externa?**
   - **Sí**: Crear servicio + tipos
   - **No**: Solo lógica interna

---

## 📁 Estructura de Archivos

### Estructura Obligatoria

```
src/components/[nombre-componente]/
├── [NombreComponente].vue          # Componente principal
├── index.js                        # Entry point para Panel4
├── types.ts                        # Interfaces TypeScript
├── services/                       # Servicios API (opcional)
├── composables/                    # Lógica reutilizable (opcional)
└── README.md                       # Documentación
```

### Ejemplo Real: `sidebar-card-inmuebles`

```
src/components/sidebar-card-inmuebles/
├── SidebarCardInmuebles.vue
├── index.js
├── types.ts
├── services/
│   └── propertyService.ts
├── composables/
│   └── usePropertySearch.ts
└── README.md
```

---

## 🚀 Proceso Paso a Paso

### Paso 1: Crear Estructura Base

```bash
# En directorio: panel4-templates/
mkdir -p src/components/[nombre-componente]/{services,composables}
cd src/components/[nombre-componente]
touch [NombreComponente].vue index.js types.ts README.md
```

### Paso 2: Definir Interfaces TypeScript

**Archivo: `types.ts`**

```typescript
// Props del componente
export interface [NombreComponente]Props {
  // Definir props obligatorias y opcionales
  totalItems: number;
  apiEndpoint: string;
  placeholder?: string;
  disabled?: boolean;
}

// Eventos que emite el componente
export interface [NombreComponente]Emits {
  (e: 'itemSelected', item: any): void;
  (e: 'itemSubmitted', item: any): void;
  (e: 'searchChanged', query: string): void;
}

// Tipos de datos internos
export interface Item {
  id: string;
  name: string;
  // ... otros campos
}

// Constantes
export const DEFAULT_PLACEHOLDER = "Buscar...";
export const MAX_RESULTS = 10;
```

### Paso 3: Crear Servicio (Si Necesita API)

**Archivo: `services/[nombre]Service.ts`**

```typescript
import type { Item } from '../types';

// Dummy data para desarrollo
const DUMMY_ITEMS: Item[] = [
  { id: '1', name: 'Item 1' },
  { id: '2', name: 'Item 2' },
];

// Función helper para simular delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class [Nombre]Service {
  static async searchItems(query: string): Promise<Item[]> {
    // Simular llamada API con dummy data
    await delay(300);

    if (!query.trim()) return [];

    return DUMMY_ITEMS.filter(item =>
      item.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  static async submitItem(item: Item): Promise<void> {
    await delay(500);
    console.log('Item enviado:', item);
    // Aquí iría la llamada real a la API
  }
}
```

### Paso 4: Crear Composable (Si Hay Lógica Compleja)

**Archivo: `composables/use[Nombre].ts`**

```typescript
import { ref, computed, watch } from 'vue';
import type { Item } from '../types';
import { [Nombre]Service } from '../services/[nombre]Service';

// Custom debounce para evitar dependencias externas
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function use[Nombre]() {
  // Estado reactivo
  const searchQuery = ref('');
  const items = ref<Item[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Función de búsqueda con debounce
  const searchItems = async (query: string) => {
    if (!query.trim()) {
      items.value = [];
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const results = await [Nombre]Service.searchItems(query);
      items.value = results;
    } catch (err) {
      error.value = 'Error al buscar items';
      console.error('Error en búsqueda:', err);
    } finally {
      loading.value = false;
    }
  };

  // Debounce de la función de búsqueda
  const debouncedSearch = debounce(searchItems, 300);

  // Watcher para búsqueda automática
  watch(searchQuery, (newQuery) => {
    debouncedSearch(newQuery);
  });

  return {
    searchQuery,
    items,
    loading,
    error,
    searchItems,
  };
}
```

### Paso 5: Crear Componente Vue

**Archivo: `[NombreComponente].vue`**

```vue
<template>
  <div class="componente-container">
    <!-- Estructura HTML aquí -->
    <div
      class="p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200"
    >
      <!-- Header -->
      <div class="flex justify-between items-center mb-2">
        <h4 class="text-sm font-medium text-gray-700">{{ title }}</h4>
        <ion-icon
          name="search-outline"
          class="text-lg text-blue-600"
        ></ion-icon>
      </div>

      <!-- Input de búsqueda -->
      <input
        v-model="searchQuery"
        :placeholder="placeholder"
        :disabled="disabled"
        class="w-full p-2 mb-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
        @input="handleInput"
      />

      <!-- Lista de resultados -->
      <div v-if="showResults" class="mt-2">
        <!-- Items -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import type { [NombreComponente]Props, [NombreComponente]Emits } from './types';
import { use[Nombre] } from './composables/use[Nombre]';

// Props
const props = withDefaults(defineProps<[NombreComponente]Props>(), {
  placeholder: 'Buscar...',
  disabled: false,
});

// Emits
const emit = defineEmits<[NombreComponente]Emits>();

// Composable
const { searchQuery, items, loading, error } = use[Nombre]();

// Estado local del componente
const showResults = ref(false);
const selectedItem = ref<Item | null>(null);

// Computed
const title = computed(() => `Items: ${props.totalItems}`);

// Methods
const handleInput = () => {
  showResults.value = true;
  emit('searchChanged', searchQuery.value);
};

const selectItem = (item: Item) => {
  selectedItem.value = item;
  searchQuery.value = item.name;
  showResults.value = false;
  emit('itemSelected', item);
};

const handleSubmit = async () => {
  if (!selectedItem.value || loading.value) return;

  try {
    // Lógica de envío
    emit('itemSubmitted', selectedItem.value);
  } catch (error) {
    console.error('Error al enviar:', error);
  }
};

// Lifecycle
onMounted(() => {
  console.log('Componente montado');
});

onUnmounted(() => {
  console.log('Componente desmontado');
});
</script>

<style scoped>
/* Estilos específicos del componente */
.componente-container {
  /* Estilos aquí */
}
</style>
```

### Paso 6: Crear Entry Point

**Archivo: `index.js`**

```javascript
// Entry point para Panel4 - VERSIÓN SIMPLE Y FUNCIONAL
import { createApp } from "vue";
import [NombreComponente] from "./[NombreComponente].vue";

console.log("🚀 Inicializando [NombreComponente]...");

function mount[NombreComponente](elementSelector = "[data-[nombre-componente]]") {
  const targetElement = document.querySelector(elementSelector);

  if (!targetElement) {
    console.error(`No se encontró el elemento: ${elementSelector}`);
    return;
  }

  // Extraer props desde data-attributes
  const props = {
    totalItems: parseInt(targetElement.getAttribute("data-total-items") || "0"),
    apiEndpoint: targetElement.getAttribute("data-api-endpoint") || "/api/items",
    placeholder: targetElement.getAttribute("data-placeholder") || "Buscar...",
    disabled: targetElement.getAttribute("data-disabled") === "true",
  };

  console.log("🏗️ Props extraídos:", props);

  // Crear contenedor
  const appContainer = document.createElement("div");
  appContainer.className = "[nombre-componente]-app";

  // Limpiar y montar
  targetElement.innerHTML = "";
  targetElement.appendChild(appContainer);

  // Montar Vue app
  const app = createApp([NombreComponente], props);
  app.mount(appContainer);

  console.log("✅ Componente montado correctamente");
  return app;
}

// Inicializar en DOM ready
document.addEventListener("DOMContentLoaded", () => {
  // Cargar iconos si es necesario
  if (!document.querySelector('script[src*="ionicons"]')) {
    const moduleScript = document.createElement("script");
    moduleScript.type = "module";
    moduleScript.src = "https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js";
    document.head.appendChild(moduleScript);
  }

  // Montar componente
  if (document.querySelector("[data-[nombre-componente]]")) {
    mount[NombreComponente]();
  } else {
    console.error("❌ No se encontró [data-[nombre-componente]]");
  }
});

// Exponer para uso externo
window.[nombre]ComponentMount = mount[NombreComponente];

export { [NombreComponente] };
```

### Paso 7: Configurar Vite

**Archivo: `vite.config.ts`**

```typescript
export default defineConfig({
  // ... configuración existente
  build: {
    rollupOptions: {
      input: {
        // ... otras entradas
        [nombreComponente]: "./src/components/[nombre-componente]/index.js",
      },
      // ... resto de configuración
    },
  },
});
```

### Paso 8: Integrar en Template PHP

**En el archivo HTML/PHP correspondiente:**

```html
<!-- Contenedor del componente -->
<div
  data-[nombre-componente]
  data-total-items="100"
  data-api-endpoint="/api/items"
  data-placeholder="Buscar items..."
  class="[nombre-componente]-container"
>
  <!-- Loading spinner mientras carga -->
  <div class="flex justify-center items-center p-8">
    <div
      class="w-8 h-8 rounded-full border-b-2 border-blue-600 animate-spin"
    ></div>
    <span class="ml-2 text-sm text-gray-600">Cargando...</span>
  </div>
</div>
```

**En `panel.html` (si aplica):**

```html
{{#if_necesario}}
<script
  type="module"
  src="./src/components/[nombre-componente]/index.js"
></script>
{{/if_necesario}}
```

---

## ⚙️ Configuración de Vite

### Entry Points Obligatorios

```typescript
// vite.config.ts
build: {
  rollupOptions: {
    input: {
      // SIEMPRE agregar nueva entrada aquí
      [nombreComponente]: "./src/components/[nombre-componente]/index.js",
    },
  },
}
```

---

## 🔗 Integración en Panel4

### Data Attributes Pattern

```html
<!-- ✅ CORRECTO -->
<div
  data-[nombre-componente]
  data-prop-name="value"
  data-api-endpoint="/api/endpoint"
  data-disabled="false"
></div>

<!-- ❌ INCORRECTO -->
<div class="component" id="component">
  <!-- Sin data-attributes para identificación -->
</div>
```

### Script Loading Pattern

```html
<!-- ✅ CORRECTO - Condicional -->
{{#if_home}}
<script type="module" src="./dist/assets/[nombre]-[hash].js"></script>
{{/if_home}}

<!-- ❌ INCORRECTO - Siempre cargado -->
<script type="module" src="./dist/assets/[nombre]-[hash].js"></script>
```

---

## 🚨 Errores Comunes y Soluciones

### Error 1: "runtime compilation is not supported"

**Causa:** Usar template strings en `index.js`

```javascript
// ❌ MAL
const ComponentWrapper = {
  template: `<MiComponente />`, // Requiere compilador
};

// ✅ BIEN
const app = createApp(MiComponente, props); // Directo
```

### Error 2: "Attempting to mutate public property '$emit'"

**Causa:** Intentar modificar `$emit` en Vue 3

```javascript
// ❌ MAL
this.$emit = function () {
  /* custom logic */
};

// ✅ BIEN
// Usar eventos directos o global properties
```

### Error 3: "Module not found: lodash-es"

**Causa:** Dependencias externas no instaladas

```javascript
// ❌ MAL
import { debounce } from "lodash-es";

// ✅ BIEN
function debounce(func, wait) {
  // Implementación propia
}
```

### Error 4: "Element not found"

**Causa:** Selector incorrecto o timing

```javascript
// ❌ MAL
const el = document.querySelector("[data-component]"); // Inmediato

// ✅ BIEN
document.addEventListener("DOMContentLoaded", () => {
  const el = document.querySelector("[data-component]");
});
```

### Error 5: "Build fails - entry not found"

**Causa:** No agregar entrada en `vite.config.ts`

```typescript
// ✅ BIEN
input: {
  miComponente: "./src/components/mi-componente/index.js", // Agregar aquí
}
```

---

## 🧪 Testing y Debugging

### Página de Prueba Standalone

Crear `test-[nombre]-component.html`:

```html
<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <title>Test [NombreComponente]</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-100 p-8">
    <div
      data-[nombre-componente]
      data-total-items="50"
      data-api-endpoint="/api/test"
      data-placeholder="Buscar en test..."
    ></div>

    <script
      type="module"
      src="./src/components/[nombre-componente]/index.js"
    ></script>
  </body>
</html>
```

### Debug Console Commands

```javascript
// En DevTools Console
window.[nombre]ComponentMount('[data-[nombre-componente]]');

// Ver estado del componente
document.querySelector('[data-[nombre-componente]]');
```

### Build Testing

```bash
# Build completo
npm run build

# Verificar assets generados
ls -la dist/assets/[nombre]*

# Test en servidor local
# Ir a: http://localhost:8020 (Panel4)
```

---

## ✅ Checklist Final

### Pre-Build

- [ ] Estructura de archivos completa
- [ ] `types.ts` con todas las interfaces
- [ ] `index.js` sin template strings
- [ ] Props extraídos de data-attributes
- [ ] Entrada agregada en `vite.config.ts`

### Build

- [ ] `npm run build` sin errores
- [ ] Assets generados en `dist/assets/`
- [ ] Tamaño de bundle razonable (< 50kB)

### Integration

- [ ] Data-attributes en template HTML
- [ ] Script loading condicional
- [ ] Loading spinner mientras carga
- [ ] Console logs funcionando

### Testing

- [ ] Componente se monta correctamente
- [ ] Props se pasan correctamente
- [ ] Funcionalidad básica funciona
- [ ] No hay errores en Console
- [ ] Responsive design funciona

---

## 🎯 Comandos de Desarrollo

```bash
# Setup inicial
mkdir -p src/components/mi-componente/{services,composables}

# Desarrollo
npm run dev     # Hot reload
npm run build   # Build completo

# Testing
npm run build && open test-mi-component.html

# Debug
docker-compose -f ../website2025/docker-compose.yml logs -f panel4
```

---

## 📚 Recursos Adicionales

- **Documentación Vue 3**: [vuejs.org](https://vuejs.org)
- **Tailwind CSS**: [tailwindcss.com](https://tailwindcss.com)
- **Ionicons**: [ionic.io/ionicons](https://ionic.io/ionicons)
- **Vite**: [vitejs.dev](https://vitejs.dev)

---

## 🔄 Flujo de Trabajo Típico

1. **Análizar** → ¿Qué extraer/crear?
2. **Planificar** → Props, eventos, servicios
3. **Estructurar** → Crear archivos base
4. **Desarrollar** → Implementar componente
5. **Configurar** → Vite + entry points
6. **Integrar** → Template PHP + data-attributes
7. **Probar** → Build + test + debug
8. **Documentar** → README + ejemplos

---

**🎉 ¡Siguiendo esta guía, deberías poder crear componentes Vue funcionales sin caer en los errores comunes!**

_Basado en la experiencia de extracción exitosa del componente `SidebarCardInmuebles`._
