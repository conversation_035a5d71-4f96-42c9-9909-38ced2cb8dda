import { defineConfig } from "vite";
import vitePluginSsinc from "vite-plugin-ssinc";
import fs from "fs";
import path from "path";
import vue from "@vitejs/plugin-vue";

// Plugin personalizado para copiar archivos Markdown y HTML después del build
const assetsPlugin = () => {
  return {
    name: "vite-plugin-assets-copy",
    closeBundle: () => {
      console.log("🚀 Build completado. Copiando archivos adicionales...");

      // Función para copiar archivos Markdown
      const copyMarkdownFiles = () => {
        const source = path.resolve("./src/entries/md");
        const destination = path.resolve("./dist/md");

        // Verificar que existe la carpeta fuente
        if (!fs.existsSync(source)) {
          console.log(
            `⚠️ La carpeta fuente ${source} no existe. No hay archivos Markdown para copiar.`
          );
          return;
        }

        // Crear la carpeta de destino si no existe
        if (!fs.existsSync(destination)) {
          fs.mkdirSync(destination, { recursive: true });
          console.log(`📁 Carpeta creada: ${destination}`);
        }

        // Leer todos los archivos de la carpeta md
        try {
          const files = fs.readdirSync(source);

          if (files.length === 0) {
            console.log("ℹ️ No hay archivos Markdown para copiar.");
            return;
          }

          let copiados = 0;
          files.forEach((file) => {
            if (file.endsWith(".md")) {
              const sourceFile = path.join(source, file);
              const destFile = path.join(destination, file);

              // Copiar el archivo
              fs.copyFileSync(sourceFile, destFile);
              copiados++;
              console.log(`📄 Archivo copiado: ${file}`);
            }
          });

          if (copiados > 0) {
            console.log(
              `✅ ${copiados} archivos Markdown copiados con éxito a ${destination}.`
            );
          } else {
            console.log("ℹ️ No se encontraron archivos .md para copiar.");
          }
        } catch (error) {
          console.error("❌ Error al copiar archivos Markdown:", error);
        }
      };

      // Ejecutar las funciones de copia
      copyMarkdownFiles();
    },
  };
};

// https://vite.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag === "ion-icon",
          // Eliminar comentarios HTML de las plantillas Vue
          comments: false,
        },
      },
    }),
    vitePluginSsinc({
      includeExtensions: ["html"],
    }),
    assetsPlugin(),
  ],
  esbuild: {
    drop: ["production", "pre-production"].includes(process.env.NODE_ENV)
      ? ["console", "debugger"]
      : [],
  },
  build: {
    minify: ["production", "pre-production"].includes(process.env.NODE_ENV),
    sourcemap: process.env.NODE_ENV === "development",
    // Configuración adicional para eliminar comentarios
    terserOptions: [
      "production",
      // "pre-production"
    ].includes(process.env.NODE_ENV)
      ? {
          format: {
            comments: false, // Eliminar todos los comentarios
          },
        }
      : undefined,
    rollupOptions: {
      input: {
        // Guest
        guest: "./guest.html",
        // Panel
        panel: "./panel.html",
        // Muro Inmobiliario Social
        muroInmobiliarioSocial:
          "./src/components/muro-inmobiliario-social/index-mbi.js",
        // "./src/components/muro-inmobiliario-social/index.js",
        // Inmueble Bolsa Inmobiliaria
        inmuebleBolsaInmobiliaria:
          "./src/components/inmueble-bolsa-inmobiliaria/index.js",
        // Dashboard
        dashboard: "./src/components/dashboard/index.js",
        // Notificaciones
        notificaciones: "./src/components/notifications/index.js",
        // Sidebar Card Inmuebles
        sidebarCardInmuebles:
          "./src/components/sidebar-card-inmuebles/index.js",
        // Sidebar Multibolsa
        sidebarMultibolsa: "./src/components/sidebar-multibolsa/index.js",
        // Multips Card
        multipsCard: "./src/components/multips/index.js",
        // MicroDash
        microdash: "./src/components/microdash/index.js",
      },
      output: {
        // assetFileNames: "assets/[name].[ext]",
        entryFileNames: (chunkInfo) => {
          // if (chunkInfo.name === "main") {
          //   return "assets/[name].js";
          // }
          // return chunkInfo.name === "panel"
          //   ? "assets/[name]-[hash].js"
          //   : "assets/[name].js";
          return chunkInfo.name === "muroInmobiliarioSocial" ||
            chunkInfo.name === "inmuebleBolsaInmobiliaria"
            ? "assets/[name].js"
            : "assets/[name]-[hash].js";
        },
      },
    },
  },
  server: {
    open: "/panel.html", // Abre panel.html automáticamente en desarrollo
  },
  resolve: {
    alias: {
      "@": "/src",
    },
  },
}));
