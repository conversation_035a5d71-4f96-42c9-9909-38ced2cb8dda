-- Agregar campo para fecha y hora de última publicación en Feed
-- Parte del sistema de control de duplicación de publicaciones

-- Columna para almacenar fecha y hora de última publicación en Feed
ALTER TABLE propiedades 
ADD COLUMN IF NOT EXISTS fecha_publicacion_feed DATETIME NULL DEFAULT NULL 
COMMENT 'Fecha y hora de la última publicación exitosa en el Feed de Multibolsa Inmobiliaria';

-- Crear índice para optimizar consultas por fecha de publicación
CREATE INDEX IF NOT EXISTS idx_fecha_publicacion_feed ON propiedades(fecha_publicacion_feed);

-- Comentario de documentación
-- Este campo se utiliza para:
-- 1. Evitar publicaciones duplicadas en el Feed
-- 2. Mostrar al usuario cuándo fue la última publicación
-- 3. Permitir re-publicación controlada por el usuario
-- 4. Rastrear actividad de publicaciones para reportes
