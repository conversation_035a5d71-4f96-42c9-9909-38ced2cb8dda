-- Script SQL para corregir el problema del checkbox "Publicar en el Feed"
-- Agrega las columnas necesarias para almacenar la configuración de Multibolsa Inmobiliaria
-- Fecha: 2025-01-12
-- Descripción: Fix para issue donde el sistema ignoraba el checkbox "Publicar en el Feed"

USE panel4si_base_datos;

-- Verificar si las columnas existen antes de agregarlas
-- Esto evita errores si el script se ejecuta múltiples veces

-- Columna para controlar si se debe publicar en el Feed de Multibolsa
ALTER TABLE propiedades 
ADD COLUMN IF NOT EXISTS usar_multibolsa ENUM('Si', 'No') DEFAULT 'Si' 
COMMENT 'Controla si el inmueble se publica en el Feed de Multibolsa Inmobiliaria';

-- Columna para controlar si la publicación es pública o privada
ALTER TABLE propiedades
ADD COLUMN IF NOT EXISTS publicacion_publica ENUM('Si', 'No') DEFAULT 'Si' 
COMMENT 'Controla si la publicación en Multibolsa es pública (todos los socios) o privada (socios seleccionados)';

-- Columna para almacenar IDs de socios seleccionados (separados por comas)
ALTER TABLE propiedades 
ADD COLUMN IF NOT EXISTS socios_seleccionados TEXT 
COMMENT 'IDs de socios seleccionados para publicación privada, separados por comas';

-- Columna para controlar solicitud de publicación en websites de socios
ALTER TABLE propiedades 
ADD COLUMN IF NOT EXISTS solicitar_publicacion_websites ENUM('Si', 'No') DEFAULT 'No' 
COMMENT 'Controla si se solicita a los socios publicar el inmueble en sus websites';

-- Crear índice para optimizar consultas por usar_multibolsa
CREATE INDEX IF NOT EXISTS idx_usar_multibolsa ON propiedades(usar_multibolsa);

-- Log de cambios
SELECT 'Columnas de Multibolsa Inmobiliaria agregadas exitosamente' AS resultado;

-- Verificar que las columnas se crearon correctamente
DESCRIBE propiedades;
