# Sistema de Tipos de Inmuebles - Documentación

## Descripción

Este sistema permite gestionar los tipos de inmuebles desde la base de datos en lugar de usar arrays hardcodeados. Proporciona soporte multiidioma (español, inglés, francés) y manejo de formas singular/plural.

## Estructura de la Base de Datos

La tabla `tipo_inmuebles` tiene la siguiente estructura:

```sql
CREATE TABLE `tipo_inmuebles` (
  `clave` int(2) UNSIGNED ZEROFILL NOT NULL DEFAULT 00,
  `id_string` varchar(23) NOT NULL,
  `ES` text NOT NULL,
  `EN` text NOT NULL,
  `tipo` text NOT NULL,
  `p_tipo` text NOT NULL,
  `tipo_esp` varchar(30) NOT NULL DEFAULT '',
  `tipo_ing` varchar(30) NOT NULL DEFAULT '',
  `tipo_fra` varchar(30) NOT NULL DEFAULT '',
  `p_tipo_esp` varchar(30) NOT NULL DEFAULT '',
  `p_tipo_ing` varchar(30) NOT NULL DEFAULT '',
  `p_tipo_fra` varchar(30) NOT NULL DEFAULT '',
  `visitas` bigint(11) NOT NULL,
  `slug` text DEFAULT NULL
);
```

## Modelos

### SITipoInmueble

Modelo principal para gestionar los tipos de inmuebles.

#### Métodos principales:

- `buscarPorClave($clave)` - Busca por clave numérica
- `buscarPorIdString($id_string)` - Busca por identificador de string
- `getNombreEspanol()`, `getNombreIngles()`, `getNombreFrances()` - Nombres por idioma
- `getPluralEspanol()`, `getPluralIngles()`, `getPluralFrances()` - Plurales por idioma
- `getNombrePorIdioma($idioma, $plural)` - Método unificado para obtener nombres
- `incrementarVisitas()` - Incrementa contador de visitas
- `obtenerTodosOrdenados()` - Todos los tipos ordenados alfabéticamente
- `obtenerMasVisitados($limite)` - Tipos más visitados

### SIInmueble

Modelo actualizado con integración a tipos de inmuebles.

#### Métodos actualizados:

- `getTipo($idioma = 'es', $plural = false)` - Obtiene el tipo del inmueble
- `tipoInmueble()` - Obtiene el objeto completo del tipo

## Uso Básico

### 1. Obtener tipo de inmueble

```php
// Buscar un inmueble
$inmueble = SIInmueble::find(123);

// Obtener tipo en español (por defecto)
echo $inmueble->getTipo(); // "Casa"

// Obtener tipo en inglés
echo $inmueble->getTipo('en'); // "House"

// Obtener tipo en francés
echo $inmueble->getTipo('fr'); // "Maison"

// Obtener tipo en plural
echo $inmueble->getTipo('es', true); // "Casas"
echo $inmueble->getTipo('en', true); // "Houses"
```

### 2. Trabajar directamente con tipos

```php
// Buscar tipo por clave
$tipo = SITipoInmueble::buscarPorClave(1);

// Obtener nombres en diferentes idiomas
echo $tipo->getNombreEspanol(); // "Casa"
echo $tipo->getNombreIngles(); // "House"
echo $tipo->getNombreFrances(); // "Maison"

// Obtener plurales
echo $tipo->getPluralEspanol(); // "Casas"
echo $tipo->getPluralIngles(); // "Houses"
echo $tipo->getPluralFrances(); // "Maisons"
```

### 3. Consultas avanzadas

```php
// Obtener todos los tipos ordenados
$tiposOrdenados = SITipoInmueble::obtenerTodosOrdenados();

// Obtener tipos más visitados
$tiposMasVisitados = SITipoInmueble::obtenerMasVisitados(10);

// Incrementar visitas
$tipo = SITipoInmueble::buscarPorClave(1);
$tipo->incrementarVisitas();
```

## Configuración

### Incluir archivos

Asegúrate de que los archivos estén incluidos correctamente:

```php
require_once __DIR__ . '/src/entries/clases/Models/Model.php';
require_once __DIR__ . '/src/entries/clases/Models/SITipoInmueble.php';
require_once __DIR__ . '/src/entries/clases/Models/SIInmueble.php';
```

### Namespace

```php
use Mulbin\Models\SIInmueble;
use Mulbin\Models\SITipoInmueble;
```

## Migración desde el sistema anterior

### Antes (hardcodeado):

```php
public function getTipo()
{
    $tipos = [
        1 => 'Casa',
        2 => 'Departamento',
        // ...
    ];
    return isset($tipos[$this->tipo]) ? $tipos[$this->tipo] : 'Inmueble';
}
```

### Después (con base de datos):

```php
public function getTipo($idioma = 'es', $plural = false)
{
    if (!$this->tipo) {
        return 'Inmueble';
    }

    $tipoInmueble = SITipoInmueble::buscarPorClave($this->tipo);
    
    if (!$tipoInmueble) {
        return 'Inmueble';
    }

    return $tipoInmueble->getNombrePorIdioma($idioma, $plural);
}
```

## Ventajas del nuevo sistema

1. **Mantenible**: Los tipos se gestionan desde la base de datos
2. **Multiidioma**: Soporte nativo para español, inglés y francés
3. **Flexible**: Soporte para singular/plural
4. **Escalable**: Fácil agregar nuevos tipos desde la BD
5. **Estadísticas**: Seguimiento de visitas por tipo
6. **SEO friendly**: Slugs para URLs amigables
7. **Consistente**: Mismo sistema para todos los inmuebles

## Ejemplos de datos

### Inserción de tipos básicos:

```sql
INSERT INTO tipo_inmuebles (clave, id_string, ES, EN, tipo, p_tipo, tipo_esp, tipo_ing, tipo_fra, p_tipo_esp, p_tipo_ing, p_tipo_fra, visitas, slug) VALUES
(1, 'casa-habitacion', 'Casa Habitación', 'House', 'Maison', 'Casas Habitación', 'Houses', 'Maisons', 'Casa', 'House', 'Maison', 0, 'casa-habitacion'),
(2, 'departamento', 'Departamento', 'Apartment', 'Appartement', 'Departamentos', 'Apartments', 'Appartements', 'Departamento', 'Apartment', 'Appartement', 0, 'departamento'),
(3, 'terreno', 'Terreno', 'Land', 'Terrain', 'Terrenos', 'Lands', 'Terrains', 'Terreno', 'Land', 'Terrain', 0, 'terreno');
```

## Solución de problemas

### Error: "Class 'Mulbin\Models\SITipoInmueble' not found"

**Causa**: El archivo no se está incluyendo correctamente.

**Solución**: Asegúrate de que el `require_once` esté presente en `SIInmueble.php`:

```php
require_once __DIR__ . '/SITipoInmueble.php';
```

### Error: "No hay conexión global a la base de datos disponible"

**Causa**: La variable global `$sql` no está definida.

**Solución**: Asegúrate de que la conexión a la base de datos esté inicializada antes de usar los modelos.

## Archivos relacionados

- `src/entries/clases/Models/SITipoInmueble.php` - Modelo de tipos de inmuebles
- `src/entries/clases/Models/SIInmueble.php` - Modelo de inmuebles (actualizado)
- `MySQL/casas_compartidas/DB_SI.tipo_inmuebles.structure.sql` - Estructura de la BD
- `ejemplo-uso-tipo-inmuebles.php` - Ejemplos de uso completos
- `test-tipo-inmueble.php` - Archivo de prueba
