# 🏗️ Panel4 Templates - Documentación Frontend

> Sistema de componentes Vue.js desacoplado que se conecta a Meteor via DDP

## 📚 Navegación de Documentación

**🔗 [Backend Meteor - Documentación Completa](../../Meteor/MulbinComponents/DOCUMENTACION-INDEX.md)** - Documentación del sistema Meteor que alimenta estos componentes

## 📋 Tabla de Contenidos

- [🎯 Concepto del Proyecto](#-concepto-del-proyecto)
- [🏗️ Arquitectura Desacoplada](#️-arquitectura-desacoplada)
- [🧩 Índice de Componentes](#-índice-de-componentes)
- [🚀 Inicio Rápido](#-inicio-rápido)
- [📂 Estructura del Proyecto](#-estructura-del-proyecto)
- [🔧 Sistema de Build](#-sistema-de-build)
- [⚡ Conexión DDP](#-conexión-ddp)
- [🧩 Desarrollo de Componentes](#-desarrollo-de-componentes)
- [🎨 Guía de Estilos](#-guía-de-estilos)
- [🔍 Debugging](#-debugging)
- [📦 Distribución](#-distribución)

---

## 🎯 Concepto del Proyecto

### ¿Qué es Panel4 Templates?

**Panel4 Templates** es un **constructor de componentes Vue.js** que opera de forma **completamente desacoplada** del backend Meteor. Su propósito es:

- 🏗️ **Construir componentes reutilizables** para diferentes sistemas
- 📦 **Generar bundles optimizados** que pueden ser integrados en cualquier aplicación
- ⚡ **Conectarse a Meteor via DDP** para datos en tiempo real
- 🎨 **Mantener consistencia visual** usando Tailwind CSS
- 🔄 **Facilitar el desarrollo** con hot-reload y herramientas modernas

### Relación con el Sistema Madre

```mermaid
graph TB
    A[Sistema Padre] --> B[Backend Meteor<br/>MulbinComponents]
    A --> C[Frontend Constructor<br/>Panel4 Templates]

    B --> D[DDP WebSocket<br/>Puerto 3000]
    B --> E[API REST<br/>Puerto 3000/api]

    C --> F[Build Development<br/>npm run build]
    C --> G[Build Production<br/>npm run prod]

    F --> H[Bundles con sourcemaps<br/>y console.log]
    G --> I[Bundles minificados<br/>sin debug]

    D --> C

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fff3e0
```

---

## 🏗️ Arquitectura Desacoplada

### Frontend No Se Ejecuta Standalone

**❌ Este frontend NO es una aplicación tradicional que se ejecuta con `npm run dev`**

**✅ Es un CONSTRUCTOR que produce componentes distribuibles**

```bash
# ❌ NO ejecutar para desarrollo de aplicación
npm run dev  # Solo para testing de componentes aislados

# ✅ SÍ ejecutar para construir componentes
npm run build  # Para desarrollo (con debug)
npm run prod   # Para producción (optimizado)
```

### Flujo de Trabajo Real

```mermaid
sequenceDiagram
    participant DEV as Desarrollador
    participant BUILD as Sistema Build
    participant DIST as Bundles Dist
    participant APP as Aplicación Padre
    participant METEOR as Backend Meteor

    DEV->>BUILD: npm run build/prod
    BUILD->>DIST: Genera bundles optimizados
    DIST->>APP: Se integran en sistema padre
    APP->>METEOR: Conecta via DDP
    METEOR->>APP: Datos en tiempo real
```

---

## 🧩 Índice de Componentes

### 🏠 **Componentes Principales**

| Componente                    | Descripción                                                   | Documentación                                                                         | Estado      |
| ----------------------------- | ------------------------------------------------------------- | ------------------------------------------------------------------------------------- | ----------- |
| **MuroInmobiliarioSocial**    | Feed social inmobiliario con notificaciones en tiempo real    | [📖 Docs](./src/components/muro-inmobiliario-social/MURO-INMOBILIARIO-SOCIAL-DOCS.md) | ✅ Completo |
| **NuevaPublicacionForm**      | Formulario desacoplado para crear publicaciones inmobiliarias | [📖 Docs](./src/components/muro-inmobiliario-social/NUEVA-PUBLICACION-FORM-DOCS.md)   | 🆕 Nuevo    |
| **MisSocios**                 | Gestión avanzada de socios y sistema de etiquetas tipo Notion | [📖 Docs](./src/components/muro-inmobiliario-social/MisSocios-DOCS.md)                | ✅ Completo |
| **SelectorSocios**            | Selector de socios con soporte para contratos y audiencia     | [📖 Docs](./src/components/selector-socios-wrapper/SELECTOR-SOCIOS-DOCS.md)           | 🆕 Nuevo    |
| **InmuebleBolsaInmobiliaria** | Control de publicación en multibolsa inmobiliaria             | [📖 Docs](./src/components/inmueble-bolsa-inmobiliaria/INMUEBLE-BOLSA-DOCS.md)        | 🆕 Nuevo    |
| **Notificaciones**            | Sistema de notificaciones en tiempo real                      | [📖 Docs](./src/components/notifications/NOTIFICACIONES-DOCS.md)                      | 🆕 Nuevo    |

### 🔧 **Componentes de Infraestructura**

| Componente              | Descripción                    | Ubicación                             | Uso                       |
| ----------------------- | ------------------------------ | ------------------------------------- | ------------------------- |
| **ddpService**          | Conexión base DDP con Meteor   | `src/services/ddpService.ts`          | Todos los componentes     |
| **inmobiliarioService** | API especializada inmobiliaria | `src/services/inmobiliarioService.ts` | Componentes inmobiliarios |
| **Panel Layout**        | Layout principal con sidebar   | `panel.html` + `src/entries/panel/`   | Sistema base              |

### 📚 **Documentación de Usuario**

| Funcionalidad               | Descripción                         | Documentación                                |
| --------------------------- | ----------------------------------- | -------------------------------------------- |
| **Multibolsa Inmobiliaria** | Sistema colaborativo de intercambio | [📖 Guía](./public/md/bolsa_inmobiliaria.md) |
| **Comisiones**              | Sistema de comisiones compartidas   | [📖 Guía](./public/md/ayuda_comisiones.md)   |

### 🔄 **Cambios Recientes**

| Fecha       | Componente             | Cambio                                              | Documentación                                                                                                                         |
| ----------- | ---------------------- | --------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------- |
| **2024-01** | NuevaPublicacionForm   | Desacoplamiento del formulario de nueva publicación | [📖 Ver docs](./src/components/muro-inmobiliario-social/NUEVA-PUBLICACION-FORM-DOCS.md)                                               |
| **2024-01** | MuroInmobiliarioSocial | Refactorización hacia arquitectura modular          | [📖 Ver cambios](./src/components/muro-inmobiliario-social/MURO-INMOBILIARIO-SOCIAL-DOCS.md#arquitectura-de-componentes-desacoplados) |
| **2024-01** | SelectorSocios         | Agregado soporte para contratos y fix de layout     | [📖 Ver cambios](./src/components/selector-socios-wrapper/SELECTOR-SOCIOS-DOCS.md#cambios-recientes)                                  |
| **2024-01** | Panel Layout           | Fix de problema de h-screen en clicks de socios     | [📖 Ver fix](./src/entries/panel/README.md#layout-fixes)                                                                              |

---

## 🚀 Inicio Rápido

### Prerequisitos

```bash
# Node.js 18+
node --version

# npm o yarn
npm --version
```

### Setup Inicial

```bash
# 1. Instalar dependencias
npm install

# 2. Para desarrollo de componentes
npm run build

# 3. Para producción
npm run prod

# 4. Los bundles estarán en ./dist/assets/
ls -la dist/assets/
```

### Verificar Backend Meteor

```bash
# El backend Meteor debe estar corriendo
curl http://localhost:3000/api/health

# DDP debe estar disponible
curl -I http://localhost:3000/websocket
```

---

## 📂 Estructura del Proyecto

```
panel4-templates/
├── src/                              # 💻 Código fuente
│   ├── components/                   # 🧩 Componentes Vue
│   │   ├── muro-inmobiliario-social/ # 🏠 Componente principal analizado
│   │   │   ├── MuroInmobiliarioSocial.vue
│   │   │   ├── MisSocios.vue           # 👥 Gestión de socios y etiquetas avanzadas
│   │   │   ├── MisSocios-DOCS.md       # 📄 Documentación técnica de gestión de socios y etiquetas
│   │   │   ├── AgregarSocioModal.vue
│   │   │   └── index.js              # 📦 Entry point del componente
│   │   ├── dashboard/                # 📊 Dashboard components
│   │   └── notifications/            # 🔔 Sistema de notificaciones
│   │
│   ├── services/                     # ⚡ Servicios DDP
│   │   ├── ddpService.ts             # 🔌 Conexión DDP base
│   │   ├── inmobiliarioService.ts    # 🏠 API inmobiliaria
│   │   └── notificationService.ts    # 🔔 API notificaciones
│   │
│   ├── types/                        # 📝 Definiciones TypeScript
│   │   └── inmobiliario.ts           # 🏠 Tipos inmobiliarios
│   │
│   ├── assets/                       # 🎨 Recursos estáticos
│   └── partials/                     # 🧱 Componentes reutilizables
│
├── dist/                             # 📦 Bundles generados
│   └── assets/                       # 🎯 Archivos distribuibles
│       ├── muroInmobiliarioSocial.js # 🏠 Bundle del muro
│       ├── dashboard-[hash].js       # 📊 Bundle dashboard
│       └── ...
│
├── public/                           # 🌐 Archivos públicos
├── panel.html                       # 🧪 Archivo de testing
├── vite.config.ts                   # ⚙️ Configuración build
├── tailwind.config.js               # 🎨 Configuración CSS
└── package.json                     # 📋 Dependencias
```

---

## 🔧 Sistema de Build

### Scripts Disponibles

```json
{
  "scripts": {
    "dev": "vite", // 🧪 Solo para testing
    "build": "NODE_ENV=development vite build --mode development",
    "prod": "NODE_ENV=production vite build --mode production",
    "preview": "vite preview" // 👀 Preview del build
  }
}
```

### Configuración de Build (vite.config.ts)

```typescript
// Configuración Multi-Entry
rollupOptions: {
  input: {
    // 🏠 Componente principal
    muroInmobiliarioSocial: "./src/components/muro-inmobiliario-social/index.js",

    // 📊 Dashboard
    dashboard: "./src/components/dashboard/index.js",

    // 🔔 Notificaciones
    notificaciones: "./src/components/notifications/index.js",
  },

  output: {
    // 📦 Naming strategy
    entryFileNames: (chunkInfo) => {
      return chunkInfo.name === "muroInmobiliarioSocial"
        ? "assets/[name].js"        // 🏠 Sin hash para integración
        : "assets/[name]-[hash].js"; // 🔧 Con hash para cache
    },
  },
}
```

### Diferencias entre Builds

| Modo            | Comando         | Características                                                            |
| --------------- | --------------- | -------------------------------------------------------------------------- |
| **Development** | `npm run build` | ✅ Source maps<br/>✅ Console.log<br/>✅ Debugging                         |
| **Production**  | `npm run prod`  | ❌ Source maps<br/>❌ Console/debugger<br/>✅ Minificado<br/>✅ Optimizado |

---

## ⚡ Conexión DDP

### Arquitectura de Servicios

```mermaid
graph TD
    A[Componente Vue] --> B[inmobiliarioService]
    B --> C[ddpService]
    C --> D[SimpleDDP]
    D --> E[WebSocket]
    E --> F[Meteor Backend<br/>Puerto 3000]

    style A fill:#e8f5e8
    style B fill:#e3f2fd
    style C fill:#f3e5f5
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#e0f2f1
```

### ddpService.ts - Conexión Base

```typescript
import SimpleDDP from "simpleddp";

class DDPService {
  private ddp: any = null;
  private serverUrl = "ws://localhost:3000/websocket";

  async connect(token?: string) {
    this.ddp = new SimpleDDP({
      endpoint: this.serverUrl,
      SocketConstructor: WebSocket,
      reconnectInterval: 5000,
    });

    await this.ddp.connect();

    // Autenticación si se proporciona token
    if (token) {
      await this.ddp.call("authenticateWithToken", token);
    }
  }

  // Suscripciones reactivas
  subscribe(publication: string, ...args: any[]) {
    return this.ddp.subscribe(publication, ...args);
  }

  // Métodos del servidor
  call(method: string, ...args: any[]) {
    return this.ddp.call(method, ...args);
  }

  // Colecciones reactivas
  collection(name: string) {
    return this.ddp.collection(name);
  }
}
```

### inmobiliarioService.ts - API Especializada

```typescript
class InmobiliarioService {
  // 🔌 Conexión
  async connectWithToken(token: string) {
    await ddpService.connect(token);
  }

  // 📡 Suscripciones
  async subscribeToPosts(filtros?: Filtros, page = 1, limit = 5) {
    const subscription = ddpService.subscribe(
      "postsInmobiliarios",
      filtros,
      page,
      limit
    );
    await subscription.ready();
  }

  // 📊 Datos reactivos
  onPostsChange(callback: (posts: PostInmobiliario[]) => void) {
    const collection = ddpService.collection("postsInmobiliarios");
    return collection.onChange(() => {
      const posts = collection.fetch();
      callback(posts);
    });
  }

  // 🚀 Métodos del servidor
  async createPost(post: NuevoPost): Promise<string> {
    return await ddpService.call("crearPostInmobiliario", post);
  }

  // 🆕 NUEVO: Crear post con destinatarios específicos
  async createPostWithTargets(
    post: NuevoPost,
    targetUserIds: string[]
  ): Promise<string> {
    return await ddpService.call(
      "crearPostInmobiliarioConDestinatarios",
      post,
      targetUserIds
    );
  }
}
```

### Publicaciones DDP Disponibles

| Publicación             | Parámetros             | Descripción                    |
| ----------------------- | ---------------------- | ------------------------------ |
| `postsInmobiliarios`    | `filtros, page, limit` | 🏠 Posts paginados con filtros |
| `postInmobiliario.byId` | `postId`               | 🏠 Post específico             |
| `comentariosPost`       | `postId`               | 💬 Comentarios de un post      |
| `notifications`         | `userId`               | 🔔 Notificaciones del usuario  |

### Métodos DDP Disponibles

| Método                                  | Parámetros                  | Retorna     | Componente             |
| --------------------------------------- | --------------------------- | ----------- | ---------------------- |
| `crearPostInmobiliario`                 | `postData`                  | `postId`    | MuroInmobiliarioSocial |
| `crearPostInmobiliarioConDestinatarios` | `postData, targetUserIds[]` | `postId`    | NuevaPublicacionForm   |
| `toggleInteresadoPost`                  | `postId`                    | `boolean`   | MuroInmobiliarioSocial |
| `agregarComentarioPost`                 | `postId, text`              | `commentId` | MuroInmobiliarioSocial |
| `authenticateWithToken`                 | `token`                     | `userId`    | Todos                  |

---

## 🧩 Desarrollo de Componentes

### Ejemplo: MuroInmobiliarioSocial.vue

```vue
<template>
  <div class="overflow-visible bg-white rounded-lg shadow-md">
    <!-- 🔔 Barra de notificaciones nueva -->
    <div
      v-if="!showMisSocios && hasNewNotifications"
      class="relative overflow-hidden border-b border-gray-200 bg-gradient-to-r from-blue-500 to-mulbin-600"
    >
      <!-- Contenido de la barra de notificaciones -->
    </div>

    <!-- 📱 Feed principal -->
    <div v-if="!showMisSocios">
      <!-- Posts inmobiliarios -->
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted } from "vue";
import inmobiliarioService from "../../services/inmobiliarioService";
import type { PostInmobiliario, Filtros } from "../../types/inmobiliario";

export default defineComponent({
  name: "MuroInmobiliarioSocial",

  props: {
    token: {
      type: String,
      default: "",
    },
  },

  setup(props) {
    // 🔄 Estados reactivos
    const posts = ref<PostInmobiliario[]>([]);
    const loading = ref(true);
    const hasNewNotifications = ref(false);

    // 📡 Conexión y suscripción
    const fetchPosts = async () => {
      try {
        // Conectar con token si está disponible
        if (props.token) {
          await inmobiliarioService.connectWithToken(props.token);
        } else {
          await inmobiliarioService.connect();
        }

        // Suscribirse a posts
        await inmobiliarioService.subscribeToPosts();

        // Obtener posts iniciales
        posts.value = inmobiliarioService.getPosts();

        // 👂 Escuchar cambios en tiempo real
        inmobiliarioService.onPostsChange((newPosts) => {
          // Verificar nuevas notificaciones
          checkForNewNotifications(newPosts);

          // Actualizar posts si no hay notificaciones pendientes
          if (!hasNewNotifications.value) {
            posts.value = newPosts;
          }
        });
      } catch (error) {
        console.error("Error al cargar posts:", error);
      }
    };

    // 🔔 Manejo de notificaciones
    const checkForNewNotifications = (newPosts: PostInmobiliario[]) => {
      // Lógica para detectar posts nuevos
      // Mostrar barra de notificación
    };

    onMounted(fetchPosts);

    return {
      posts,
      loading,
      hasNewNotifications,
      // ... otros métodos
    };
  },
});
</script>
```

### Entry Point del Componente (index.js)

```javascript
// src/components/muro-inmobiliario-social/index.js
import { createApp } from "vue";
import MuroInmobiliarioSocial from "./MuroInmobiliarioSocial.vue";

// 🚀 Auto-inicialización cuando se carga el bundle
document.addEventListener("DOMContentLoaded", () => {
  const container = document.getElementById("muro-inmobiliario-social");

  if (container) {
    const app = createApp(MuroInmobiliarioSocial, {
      // Props desde atributos del DOM
      token: container.getAttribute("data-token") || "",
    });

    app.mount(container);
  }
});

// 📦 También exportar para uso programático
export { MuroInmobiliarioSocial };
export default MuroInmobiliarioSocial;
```

### Integración en Aplicación Padre

```html
<!-- En el sistema padre -->
<!DOCTYPE html>
<html>
  <head>
    <link rel="stylesheet" href="path/to/tailwind.css" />
  </head>
  <body>
    <!-- 🎯 Container del componente -->
    <div id="muro-inmobiliario-social" data-token="user-auth-token-here"></div>

    <!-- 📦 Bundle del componente -->
    <script src="dist/assets/muroInmobiliarioSocial.js"></script>
  </body>
</html>
```

### Componentes Destacados

- **MuroInmobiliarioSocial.vue**: Feed social inmobiliario, notificaciones en tiempo real, gestión de publicaciones y socios.
- **NuevaPublicacionForm.vue**: Formulario desacoplado para crear publicaciones inmobiliarias con validación, gestión de socios y audiencia personalizada. [Ver documentación técnica](./src/components/muro-inmobiliario-social/NUEVA-PUBLICACION-FORM-DOCS.md)
- **MisSocios.vue**: Gestión avanzada de socios, incluyendo sistema completo de etiquetas tipo Notion (creación, asignación, eliminación, búsqueda y rollback optimista). [Ver documentación técnica](./src/components/muro-inmobiliario-social/MisSocios-DOCS.md)

---

## 🎨 Guía de Estilos

### Tailwind CSS Configuración

```javascript
// tailwind.config.js
module.exports = {
  content: ["./src/**/*.{vue,js,ts,jsx,tsx}", "./panel.html"],
  theme: {
    extend: {
      colors: {
        mulbin: {
          50: "#eff6ff",
          600: "#2563eb",
          700: "#1d4ed8",
        },
      },
    },
  },
};
```

### Convenciones de Clases

```vue
<template>
  <!-- ✅ Estructura base -->
  <div class="overflow-visible bg-white rounded-lg shadow-md">
    <!-- ✅ Headers con gradientes -->
    <div class="p-4 text-white bg-gradient-to-r from-mulbin-600 to-indigo-800">
      <!-- ✅ Botones principales -->
      <button
        class="flex items-center px-3 py-1 text-sm font-medium bg-white rounded-full text-mulbin-600"
      >
        <!-- ✅ Estados de loading -->
        <div
          class="w-8 h-8 mx-auto border-4 border-blue-500 rounded-full animate-spin border-t-transparent"
        >
          <!-- ✅ Cards de contenido -->
          <div class="p-4 border-b border-gray-100"></div>
        </div>
      </button>
    </div>
  </div>
</template>
```

### Iconografía

```vue
<!-- ✅ Iconos Ionicons -->
<ion-icon
  name="notifications"
  class="text-lg text-white animate-pulse"
></ion-icon>
<ion-icon name="thumbs-up-outline" class="mr-1"></ion-icon>
<ion-icon name="chatbubble-outline" class="mr-1"></ion-icon>
```

---

## 🔍 Debugging

### Logs del Sistema

```typescript
// En development mode
console.log("Posts fetched:", fetchedPosts.length);
console.log("DDP connection status:", ddpService.isConnected());

// En production mode - estos logs se eliminan automáticamente
```

### Herramientas de Debug

```javascript
// Diagnóstico de colección DDP
inmobiliarioService.diagnosticCollection();

// Estado de conexión
ddpService.getConnectionStatus();

// Verificar suscripciones activas
ddpService.getActiveSubscriptions();
```

### Debugging DDP

```javascript
// Ver datos crudos de la colección
const collection = ddpService.collection("postsInmobiliarios");
console.log("Raw collection data:", collection.fetch());

// Monitorear cambios
collection.onChange(() => {
  console.log("Collection changed:", collection.fetch().length);
});
```

### Errors Comunes

| Error                    | Causa                 | Solución                          |
| ------------------------ | --------------------- | --------------------------------- |
| `DDP connection failed`  | Backend no disponible | Verificar Meteor en puerto 3000   |
| `Post sin ID válido`     | Datos malformados     | Verificar normalización de IDs    |
| `Subscription not ready` | Suscripción async     | Esperar `subscription.ready()`    |
| `Bundle not loading`     | Ruta incorrecta       | Verificar paths en vite.config.ts |

---

## 📦 Distribución

### Bundles Generados

```bash
dist/assets/
├── muroInmobiliarioSocial.js      # 🏠 Componente principal (sin hash)
├── dashboard-a1b2c3d4.js          # 📊 Dashboard (con hash)
├── notificaciones-e5f6g7h8.js     # 🔔 Notificaciones (con hash)
└── panel-i9j0k1l2.js              # 🧪 Panel de testing (con hash)
```

### Estrategia de Naming

- **Sin hash:** Para componentes con integración directa
- **Con hash:** Para cache busting en componentes auxiliares

### Optimizaciones de Producción

```typescript
// vite.config.ts - Modo production
esbuild: {
  // 🗑️ Eliminar console.log y debugger
  drop: process.env.NODE_ENV === "production" ? ["console", "debugger"] : [],
},

build: {
  // 🗜️ Minificación activada
  minify: process.env.NODE_ENV === "production",

  // 🗺️ Source maps solo en development
  sourcemap: process.env.NODE_ENV === "development",
}
```

### Integración en Producción

```html
<!-- Sistema de producción -->
<script>
  // 🔧 Configuración global si es necesaria
  window.MULBIN_CONFIG = {
    ddpUrl: "wss://api.mulb.in/websocket",
    environment: "production",
  };
</script>

<!-- 📦 Bundle minificado -->
<script src="https://cdn.mulb.in/assets/muroInmobiliarioSocial.js"></script>
```

---

## 🤝 Flujo de Desarrollo

### 1. Desarrollar Componente

```bash
# 1. Crear/editar componente
vim src/components/mi-componente/MiComponente.vue

# 2. Crear entry point
vim src/components/mi-componente/index.js

# 3. Agregar al vite.config.ts
# input: { miComponente: "./src/components/mi-componente/index.js" }
```

### 2. Testing Local

```bash
# Build para testing
npm run build

# Preview
npm run preview

# Abrir panel.html para testing
open http://localhost:4173/panel.html
```

### 3. Integración

```bash
# Build para producción
npm run prod

# Copiar bundles al sistema padre
cp dist/assets/miComponente.js ../sistema-padre/assets/
```

### 4. Deploy

```bash
# El sistema padre maneja el deploy final
# Los bundles se integran automáticamente
```

---

## 📚 Recursos Adicionales

### Enlaces Importantes

- **[🔗 Backend Meteor Docs](../../Meteor/MulbinComponents/README_DESARROLLO.md)** - Documentación completa del backend
- **[📊 API DDP](../../Meteor/MulbinComponents/app/API-DDP-Frontend-Clients.md)** - API para frontends reactivos
- **[🌐 API REST](../../Meteor/MulbinComponents/app/API-REST-External-Backends.md)** - API para backends externos

### Dependencias Clave

| Dependencia      | Uso                | Documentación                                     |
| ---------------- | ------------------ | ------------------------------------------------- |
| **Vue 3**        | Framework reactivo | [Vue.js](https://vuejs.org/)                      |
| **SimpleDDP**    | Cliente DDP        | [SimpleDDP](https://github.com/Gregivy/simpleddp) |
| **Tailwind CSS** | Framework CSS      | [Tailwind](https://tailwindcss.com/)              |
| **Vite**         | Build tool         | [Vite](https://vitejs.dev/)                       |
| **TypeScript**   | Tipado estático    | [TypeScript](https://www.typescriptlang.org/)     |

### Herramientas de Desarrollo

```bash
# Análisis de bundles
npm run build && npx vite-bundle-analyzer dist

# Linting
npm run lint

# Type checking
npm run type-check
```

---

## 🎯 Conclusiones

**Panel4 Templates** es un sistema de construcción de componentes Vue.js que:

- ✅ **Se conecta a Meteor** via DDP para datos en tiempo real
- ✅ **Genera bundles distribuibles** optimizados
- ✅ **Mantiene separación de responsabilidades** entre frontend y backend
- ✅ **Facilita la integración** en sistemas padre
- ✅ **Optimiza para producción** con builds minificados

**Para desarrolladores:** Enfócate en crear componentes reactivos que consuman las APIs DDP del backend Meteor, y utiliza el sistema de build para generar bundles optimizados para integración.

---

**📧 Soporte:** Para dudas específicas sobre la integración DDP o desarrollo de componentes, consulta la documentación del backend Meteor o contacta al equipo de desarrollo.
