// Este script simple ayuda a limpiar completamente archivos relacionados con React
// que puedan haber quedado después de la migración a un proyecto sin React

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Lista de archivos que pueden contener configuraciones o referencias a React
const filesToCheck = [
  ".eslintrc.cjs",
  ".eslintrc.js",
  "tsconfig.node.json",
  "tsconfig.app.json",
  "tsconfig.json",
  "public/vite.svg",
  "src/vite-env.d.ts",
];

// Intenta eliminar cada archivo si existe
filesToCheck.forEach((file) => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`Eliminado: ${file}`);
    } catch (err) {
      console.error(`Error al eliminar ${file}:`, err);
    }
  }
});

console.log(
  "Limpieza completada. El proyecto ahora está libre de archivos de React no utilizados."
);
